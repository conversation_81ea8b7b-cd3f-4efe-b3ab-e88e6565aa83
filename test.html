<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Document</title>
	</head>

	<body>
		<div id="chart" style="height: 500px; width: 800px"></div>
		<!--修改注释即可看到区别-->
		<!-- <script crossorigin="anonymous" integrity="sha384-D4R8qYT6nA7nYz3VrleqV7/7JJS+n62yPyQ89i7qHnzFOEKJ6Fa6400CTuMrnWvQ"
    src="https://lib.baomitu.com/echarts/4.2.0-rc.1/echarts.min.js"></script> -->
		<script
			crossorigin="anonymous"
			integrity="sha384-mYHbpb8SNpRR9uL7PfZoWk1rI3/VXsAkIC5Sy7+Aa7a79JKqZ19qg4OcgvgsCx36"
			src="https://lib.baomitu.com/echarts/4.0.0/echarts.min.js"
		></script>
		<script>
			const chart = echarts.init(document.querySelector('#chart'))
			const option = {
				xAxis: {
					type: 'category',
					data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
					axisLine: {
						onZero: false,
					},
				},
				yAxis: {
					type: 'value',
				},
				series: [
					{
						data: [-120, 200, 150, 80, 70, 110, 130],
						type: 'bar',
					},
				],
			}
			chart.setOption(option)
		</script>
	</body>
</html>
