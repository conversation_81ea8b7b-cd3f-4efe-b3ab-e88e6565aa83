// uno.config.ts
import { defineConfig } from 'unocss'

export default defineConfig({
	// ...UnoCSS选项
	presets: [],
	rules: [
		[/^w-([\.\d]+)$/, ([, num]) => ({ width: `${num}px !important` })],
		[/^h-([\.\d]+)$/, ([, num]) => ({ height: `${num}px !important` })],
		[/^m-([\.\d]+)$/, ([, num]) => ({ margin: `${num}px !important` })],
		[/^m-top-([\.\d]+)$/, ([, num]) => ({ 'margin-top': `${num}px` })],
		[/^m-right-([\.\d]+)$/, ([, num]) => ({ 'margin-right': `${num}px` })],
		[/^m-bottom-([\.\d]+)$/, ([, num]) => ({ 'margin-bottom': `${num}px` })],
		[/^m-left-([\.\d]+)$/, ([, num]) => ({ 'margin-left': `${num}px` })],
		[/^p-([\.\d]+)$/, ([, num]) => ({ padding: `${num}px` })],
		[/^p-top-([\.\d]+)$/, ([, num]) => ({ 'padding-top': `${num}px` })],
		[/^p-right-([\.\d]+)$/, ([, num]) => ({ 'padding-right': `${num}px` })],
		[/^p-bottom-([\.\d]+)$/, ([, num]) => ({ 'padding-bottom': `${num}px` })],
		[/^p-left-([\.\d]+)$/, ([, num]) => ({ 'padding-left': `${num}px` })],
	],
})
