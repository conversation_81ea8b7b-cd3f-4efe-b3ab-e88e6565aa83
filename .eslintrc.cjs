/*
 * @Author: lhl
 * @Date: 2023-05-18 17:27:30
 * @LastEditTime: 2023-07-04 16:54:23
 * @LastEditors: lhl
 * @Description:
 * @FilePath: \portrait-of-cadres\.eslintrc.cjs
 */
module.exports = {
	//env属性来指定你的代码运行的环境
	env: {
		browser: true,
		es2021: true,
	},
	extends: ['plugin:vue/vue3-essential', 'plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended'],
	overrides: [],
	parser: 'vue-eslint-parser', // 新增
	parserOptions: {
		ecmaVersion: 'latest',
		sourceType: 'module',
		ecmaFeatures: true,
		parser: '@typescript-eslint/parser', // 解析Ts
		extraFileExtensions: ['.vue'], // 新增
	},
	plugins: ['vue', 'prettier', '@typescript-eslint'],
	rules: {
		'prettier/prettier': 'error',
		'arrow-body-style': 'off',
		'prefer-arrow-callback': 'off',
		'no-multi-spaces': 0,
		'no-multi-str': 0,
		'prettier/prettier': ['error', { endOfLine: 'auto' }],
		'vue/multi-word-component-names': 'off',
	},
}
