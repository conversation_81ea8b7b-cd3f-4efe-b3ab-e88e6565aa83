// import './mock/index'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import 'uno.css'
import './style.less'
import 'echarts'
import router from './router'
import ECharts from 'vue-echarts'
import directives from './directives'
import App from './App.vue'
import VueLazyLoad from 'vue3-lazyload'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(directives)
app.use(VueLazyLoad)

app.component('v-chart', ECharts)

app.mount('#app')
