import { pmsRequest, userRequest } from '@/utils/axios'
//班子画像所有接口地址： https://wiki.aidangqun.com/project/8?p=2105
import { ENV_OFFLINE } from '@/config/env'

const getTeamStatistics = (params: any) => {
	return pmsRequest.get('/team-waring/team-statistics', params)
}
const getTeamWaring = (params: any) => {
	return pmsRequest.get('/team-waring/team-term', params)
}
const getTotalCadres = (params: any) => {
	return pmsRequest.get('/team-waring/total-cadres', params)
}

const getTeamTownshipSequence = (params: any) => {
	return pmsRequest.get('/team-waring/team-township-sequence', params)
}
// 单班子基础信息http://wiki.aidangqun.com/project/8?p=2144
const getTeamBasicInfo = (params: any) => {
	return pmsRequest.get('/team-waring/team-basic', params)
}
//  班子金字塔/
const getTeamPyramid = (params: any) => {
	return pmsRequest.get('/team-waring/team-pyramid', params)
}
//  班子生态/班子业绩散点图
const getTeamScatter = (params: any) => {
	return pmsRequest.get('/team-waring/team-scatter', params)
}
//   班子运行指数列表
const getTeamIndex = (params: any) => {
	return pmsRequest.get('/team-waring/team-index', params)
}
//   班子运行指数列表
const getTeamIndexLine = (params: any) => {
	return pmsRequest.get('/team-waring/team-index-line', params)
}
//   巡查审计评级
const getTeamPatrol = (params: any) => {
	return pmsRequest.get('/team-waring/team-patrol', params)
}
//   个人事项报告
const getTeamPersonal = (params: any) => {
	return pmsRequest.get('/team-waring/team-personal', params)
}
//    清正廉洁
const getTeamHonest = (params: any) => {
	return pmsRequest.get('/team-waring/team-honest', params)
}
//     班子年度考核
const getTeamAnnual = (params: any) => {
	return pmsRequest.get('/team-waring/team-annual', params)
}

const queryTeam = (params: any) => {
	if (ENV_OFFLINE) {
		params.org_ids = params.org_ids?.join(',')
	}
	return pmsRequest[ENV_OFFLINE ? 'get' : 'post']('/team-waring/team-query', params)
}

const getTeamTermDetail = (params: any) => {
	return pmsRequest.get('/team-waring/team-term-detail', params)
}
//  干部职数
const getCadresjob = (params: any) => {
	return pmsRequest.get('/team-waring/cadres-job', params)
}

//  班子结构分析表/team-waring/team-structure https://wiki.aidangqun.com/project/8?p=2161
const getTeamStructure = (params: any) => {
	return pmsRequest.get('/team-waring/team-structure', params)
}
//  单个班子结构分析 https://wiki.aidangqun.com/project/8?p=2708
const getTeamStructureAnalyze = (params: any) => {
	return pmsRequest.get('/team-waring/team-structure-analyze', params)
}
//  单个班子结构分析 https://wiki.aidangqun.com/project/8?p=2708
const getCadreIndex = (params: any) => {
	return pmsRequest.get('/team-waring/cadre-index', params)
}
//  单个班子结构分析 https://wiki.aidangqun.com/project/8?p=2876
const getListByScope = (params: any) => {
	return userRequest.get('/pms-leader-team/get-list-by-scope', params)
}
//  班子巡察指数(金字塔，列表 /patrol/org_pyramid_list ) https://wiki.aidangqun.com/project/8?p=3315
export const getPatrolOrgPyramidList = (params: any) => {
	return pmsRequest.get('/patrol/org_pyramid_list', params)
}
// 班子巡察,班子巡察扣分汇总(/patrol/org_inspection)
export const getPatrolOrgInspection = (params: any) => {
	return pmsRequest.get('/patrol/org_inspection', params)
}
// 一把手（/patrol/main_user）
export const getPatrolMainUser = (params: any) => {
	return pmsRequest.get('/patrol/main_user', params)
}
// 其他班子成员（/patrol/other_user）
export const getPatrolOtherUser = (params: any) => {
	return pmsRequest.get('/patrol/other_user', params)
}
// 班子成员测评对比（/patrol/org_eval_compare）
export const getPatrolOrgEvalCompare = (params: any) => {
	return pmsRequest.get('/patrol/org_eval_compare', params)
}

//  班子谈话等次（/patrol/org_speech_rating）
export const getPatrolOrgSpeechRating = (params: any) => {
	return pmsRequest.get('/patrol/org_speech_rating', params)
}

export {
	getTeamStatistics,
	getTeamWaring,
	getTotalCadres,
	getTeamTownshipSequence,
	getTeamBasicInfo,
	getTeamPyramid,
	getTeamScatter,
	getTeamIndex,
	getTeamIndexLine,
	getTeamPatrol,
	getTeamPersonal,
	getTeamHonest,
	getTeamAnnual,
	queryTeam,
	getCadreIndex,
	getTeamTermDetail,
	getCadresjob,
	getTeamStructure,
	getTeamStructureAnalyze,
	getListByScope,
}
