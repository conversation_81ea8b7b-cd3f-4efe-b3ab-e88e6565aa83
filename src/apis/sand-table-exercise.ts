import { padAxios } from '@/utils/axios'
// 沙盘推演接口参数需要手动规定顺序
// 401020214001-沙盘推演主页  https://wiki.aidangqun.com/project/8?p=2295
export const getHomePage = (sequence = '', mockId = '', adminId = '') => {
	return padAxios.get(`/job/home_page?sequence=${sequence}&mockId=${mockId}&adminId=${adminId}`)
}
export const getLackjob = (sequence = '', mockId = '', adminId = '') => {
	return padAxios.get(`/job/lack_job?sequence=${sequence}&mockId=${mockId}&adminId=${adminId}`)
}
//401020214002--岗找人-班子队伍 https://wiki.aidangqun.com/project/8?p=2296
export const getOrgTeam = (orgId = '', mockId = '') => {
	return padAxios.get(`/job/org_team?orgId=${orgId}&mockId=${mockId}`)
}
//  401020214004-提交调整方案 https://wiki.aidangqun.com/project/8?p=2313
export const submitMock = (submitMock = '') => {
	return padAxios.get(`/job/submit_mock?submitMock=${submitMock}`)
}
// 401020214003-沙盘模拟主页删除某个人 delete_user_mock https://wiki.aidangqun.com/project/8?p=2314
export const deleteUserMock = (mockId = '', orgId = '', userId = '', pmsJobId = '', adminId = '') => {
	return padAxios.get(`/job/delete_user_mock?mockId=${mockId}&orgId=${orgId}&userId=${userId}&pmsJobId=${pmsJobId}&adminId=${adminId}`)
}
// 401020214005-待配名单 to_submit_list https://wiki.aidangqun.com/project/8?p=2315
export const toSubmitList = (mockId = '') => {
	return padAxios.get(`/job/to_submit_list?mockId=${mockId}`)
}
// 401020214006-拟任职务（职务列表） job_list https://wiki.aidangqun.com/project/8?p=2316
export const jobList = (orgId = '', jobName = '') => {
	return padAxios.get(`/job/job_list?orgId=${orgId}&jobName=${jobName}`)
}
// 401020214007-拟免职务 user_job https://wiki.aidangqun.com/project/8?p=2317
export const userJob = (userId = '', mockId = '') => {
	return padAxios.get(`/job/user_job?userId=${userId}&mockId=${mockId}`)
}
//  401020214008-历史方 mock_list案 https://wiki.aidangqun.com/project/8?p=2318
export const mockList = (adminId = '') => {
	return padAxios.get(`/job/mock_list?adminId=${adminId}`)
}
// 401020214009-确认为最终方案 confirm_mock https://wiki.aidangqun.com/project/8?p=2319
export const confirmMock = (mockId = '', adminId = '') => {
	return padAxios.get(`/job/confirm_mock?mockId=${mockId}&adminId=${adminId}`)
}
// 401020214010-一键智选/job/user_list https://wiki.aidangqun.com/project/8?p=2320
export const getUserList = (type = '', filter = '') => {
	return padAxios.get(`/job/user_list?type=${type}&filter=${filter}`)
}
// 401020214015-拉取沙盘推演数据 https://wiki.aidangqun.com/project/8?p=2359
export const syncJob = (params: any) => {
	return padAxios.get(`/sync/job`, {
		params,
	})
}
// 401020214013-方案分析（全区/序列） https://wiki.aidangqun.com/project/8?p=2323
export const planAnalyze = (tab = '', type = '', mockId = '') => {
	return padAxios.get(`/job/mock_analyze?tab=${tab}&type=${type}&mockId=${mockId}`)
}
// 401020214013-方案分析 (班子) https://wiki.aidangqun.com/project/8?p=2323
export const orgAnalyze = (mockId = '') => {
	return padAxios.get(`/job/mock_analyze_org?mockId=${mockId}`)
}
// 401020214012-班子结构分析 https://wiki.aidangqun.com/project/8?p=2322
export const getTeamStructure = (orgId = '', mockId = '') => {
	return padAxios.get(`/job/team_structure?orgId=${orgId}&mockId=${mockId}`)
}
// 401020214016-班子结构对比 https://wiki.aidangqun.com/project/8?p=2365
export const getTeamStructureCompare = (orgId = '', mockId = '', userIds = '', type = '') => {
	return padAxios.get(`/job/team_structure_compare?orgId=${orgId}&mockId=${mockId}&userIds=${userIds}&type=${type}`)
}
// 401020214017-重置方案 https://wiki.aidangqun.com/project/8?p=2378
export const resetMock = (mockId = '', adminId = '') => {
	return padAxios.get(`/job/reset_mock?mockId=${mockId}&adminId=${adminId}`)
}
// 401020214018-保存方案 https://wiki.aidangqun.com/project/8?p=2379
export const saveMock = (mockId = '', adminId = '', mockName = '', type = '') => {
	return padAxios.get(`/job/save_mock?mockId=${mockId}&adminId=${adminId}&mockName=${mockName}&type=${type}`)
}
// 401020214018-创建方案 https://wiki.aidangqun.com/project/8?p=2379
export const createMock = (mockName = '', adminId = '') => {
	return padAxios.get(`/job/create_mock?mockName=${mockName}&adminId=${adminId}`)
}
// 401020214011-调整人员名单 https://wiki.aidangqun.com/project/8?p=2321
export const mockUserList = (mockId = '') => {
	return padAxios.get(`/job/mock_user_list?mockId=${mockId}`)
}
// 401020214011-预警人员名单 https://wiki.aidangqun.com/project/8?p=2380
export const warnList = (mockId = '') => {
	return padAxios.get(`/job/warn_list?mockId=${mockId}`)
}
// 401020214011-研判名单 https://wiki.aidangqun.com/project/8?p=2397
export const estimate = (mockId = '') => {
	return padAxios.get(`/job/estimate?mockId=${mockId}`)
}
// 401020214021-添加备选人 https://wiki.aidangqun.com/project/8?p=2397
export const addAlternativeUser = (alternativeUser = '') => {
	return padAxios.get(`/job/add_alternative_user?alternativeUser=${alternativeUser}`)
}
// 401020214022-删除备选人 https://wiki.aidangqun.com/project/8?p=2397
export const deleteAlternativeUser = (alternativeUser = '') => {
	return padAxios.get(`/job/remove_alternative_user?alternativeUser=${alternativeUser}`)
}
//主页相关数量 https://wiki.aidangqun.com/project/8?p=2682
export const getHomePageCount = (mockId = '', adminId: any) => {
	return padAxios.get(`/job/home_page_count?mockId=${mockId}&adminId=${adminId}`)
}
