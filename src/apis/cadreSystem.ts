import { ENV_OFFLINE } from '@/config/env'
import { pmsRequest, userRequest } from '@/utils/axios'

export const getUserListByOrgId = (org_id: number, params = {}) => {
	// return userRequest.get(`/pms-leader-team/get-list/${id}`, params)
	return userRequest.get(ENV_OFFLINE ? `/pms-leader-team/get-list` : `/pms-leader-team/get-list/${org_id}`, { org_id, ...params })
}
export const getOrgList = (params: any) => {
	return userRequest.get('/org/get-all-org', params)
}
/**
 * @description: 动议名单 http://wiki.aidangqun.com/project/8?p=1982
 * @param {any} params
 * @return {*}
 */
export const getMotionList = () => {
	return userRequest.get('/user-expand/get-motion-list')
}
//  401020214031-批量查询干部头像/cadre_select/batch_get_avatar
export const batchGetAvatar = (params: any) => {
	return pmsRequest.get(`/cadre_select/batch_get_avatar`, params)
}
// 查询用户类型  /leader-screen/user_type?user_id=
// 返回值 1-市管 2-区管 3-中层
export const getUserType = (params: any) => {
	return userRequest.get('/leader-screen/user_type', params)
}
