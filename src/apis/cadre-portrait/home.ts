import { userRequest, pmsRequest } from '@/utils/axios'
import { UserInfo, RadarDataType, CoordinateDataType, PersonalDataType, AbilityDataType } from '@/types/user'
import { ResponesType } from '@/types/http'
type requestParams = { user_id: number; flag?: number }
type requestParamsTwo = {}
type ResultType<T> = Promise<ResponesType<T>>
/**
 * @description: 干部基本信息 http://wiki.aidangqun.com/project/8?p=1949
 * @return {*}
 */
export const getLeaderBaseInfo = (params: requestParams): ResultType<UserInfo> => {
	return userRequest.get('/leader-screen/baseinfo', params)
}
/**
 * @description: 干部评价雷达图 http://wiki.aidangqun.com/project/8?p=1950
 * @param {requestParams} params
 * @return {*}
 */
export const getAssetsData = (params: requestParams): ResultType<RadarDataType> => {
	return userRequest.get('/leader-screen/assert', params)
}

/**
 * @description: 能力业绩坐标分布 http://wiki.aidangqun.com/project/8?p=1984&keyword=performance-point
 * @param {requestParams} params
 * @return {*}
 */
// export const getPointData = (params: any): ResultType<CoordinateDataType> => {
// 	return userRequest.get('/leader-screen/point', params)
// }
// TODO 坐标分布
export const getPointData = (params: any): ResultType<CoordinateDataType> => {
	return pmsRequest.get('/leader-screen/performance-point-screen', params)
}

/**
 * @description: 干部指数坐标 http://wiki.aidangqun.com/project/8?p=1983
 * @param {requestParams} params
 * @return {*}
 */
export const getLeaderIndex = (params: any): ResultType<CoordinateDataType> => {
	return userRequest.get('/leader-screen/coord-cadre-index', params)
}
/**
 * @description: 个人测评分析 http://wiki.aidangqun.com/project/8?p=1951
 * @param {requestParams} params
 * @return {*}
 */
export const getPersonelResult = (params: requestParams): ResultType<PersonalDataType> => {
	return userRequest.get('/leader-screen/result', params)
}
/**
 * @description: 能力测评分析 http://wiki.aidangqun.com/project/8?p=1953
 * @param {requestParams} params
 * @return {*}
 */
export const getAblityResult = (params: requestParams): ResultType<AbilityDataType> => {
	return userRequest.get('/leader-screen/all-result', params)
}
/**
 * @description: 历史业绩 http://wiki.aidangqun.com/project/8?p=1952
 * @param {requestParams} params
 * @return {*}
 */
export const getAchievement = (params: requestParams): ResultType<any> => {
	return userRequest.get('/leader-screen/achievement', params)
}
/**
 * @description: 年度考核 http://wiki.aidangqun.com/project/8?p=1955
 * @param {requestParams} params
 * @return {*}
 */
export const getEvalData = (params: requestParams): ResultType<any> => {
	return userRequest.get('/leader-screen/eval', params)
}
/**
 * @description: 荣誉表彰 http://wiki.aidangqun.com/project/8?p=1956
 * @param {requestParams} params
 * @return {*}
 */
export const getGloryData = (params: requestParams): ResultType<any> => {
	return userRequest.get('/leader-screen/glory', params)
}
/**
 * @description: 负面清单 http://wiki.aidangqun.com/project/8?p=1956
 * @param {requestParams} params
 * @return {*}
 */
export const getNegativeData = (params: requestParams): ResultType<any> => {
	return userRequest.get('/leader-screen/negative', params)
}

/**
 *
 * @description:干部评价条形图 http://wiki.aidangqun.com/project/8?p=1979
 * @param {requestParams} params
 * @return {*}
 */

export const getBarData = (params: requestParams): ResultType<any> => {
	return userRequest.get('/leader-screen/assert-two', params)
}
/**
 *
 * @description:干部指数 http://wiki.aidangqun.com/project/8?p=1981&keyword=cadre-index
 * @param {requestParams} params
 * @return {*}
 */

export const getCadreIndex = (params: any): ResultType<any> => {
	return pmsRequest.get('/leader-screen/cadre-index-screen', params)
}
/**
 * @description: 查询维度配置 http://wiki.aidangqun.com/project/8?p=1980
 * @param { requestParams } params
 * @return {*}
 */
export const getDimensionConfig = (params: requestParamsTwo): ResultType<any> => {
	return pmsRequest.get('/eval/find-dimensionality-config', params)
}
/**
 * @description: 树形图 http://wiki.aidangqun.com/project/8?p=1994
 * @return {*}
 */
export const getTreediagram = (params: requestParams) => {
	return pmsRequest.get('/pms-leader-screen/tree_diagram', params)
}
/**
 * @description: 树形图 http://wiki.aidangqun.com/project/8?p=1994
 * @return {*}
 */
export const getOrgTreediagram = (params: requestParams) => {
	return pmsRequest.get('/team-waring/tree-diagram', params)
}
/**
 * @description: 获取金字塔数据 http://wiki.aidangqun.com/project/8?p=1995
 * @param {requestParams} params
 * @return {*}
 */
export const getPyramid = (params: requestParams) => {
	return pmsRequest.get('/leader-screen/pyramid-screen', params)
}
/**
 * @description: 获取个人事项
 * @param {requestParams} params
 * @return {*}
 */
export const getPeronelMatter = (user_id: any) => {
	return pmsRequest.get(`/person-matters/get?user_id=${user_id}`)
}
/**
 * @description: 负面清单 https://wiki.aidangqun.com/project/8?p=2570
 * @param {requestParams} params
 * @return {*}
 */
export const getPunishment = (user_id: any) => {
	return pmsRequest.get(`/leader-screen/punishment?user_id=${user_id}`)
}
/**
 * @description: 个人监督–个人事项核查报告 https://wiki.aidangqun.com/project/8?p=2580
 * @param {requestParams} params
 * @return {*}
 */
export const mattersCheck = (user_id: any) => {
	return pmsRequest.get(`/person-matters/matters-check?user_id=${user_id}`)
}
/**
 * @description: 个人监督-信访举报情况 https://wiki.aidangqun.com/project/8?p=2579
 * @param {requestParams} params
 * @return {*}
 */
export const complaintReport = (user_id: any) => {
	return pmsRequest.get(`/person-matters/complaint-report?user_id=${user_id}`)
}
/**
 * @description: 个人监督–其他监督 https://wiki.aidangqun.com/project/8?p=2579
 * @param {requestParams} params
 * @return {*}
 */
export const getSupervision = (user_id: any) => {
	return pmsRequest.get(`/person-matters/supervision?user_id=${user_id}`)
}
/**
 * @description: 考察评价-班子回访调研评价 https://wiki.aidangqun.com/project/8?p=2629
 * @param {requestParams} params
 * @return {*}
 */
export const returnVisit = (user_id: any) => {
	return pmsRequest.get(`/person-matters/return-visit?user_id=${user_id}`)
}
/**
 * @description: 考察评价-巡视巡察政治生态报告评价 https://wiki.aidangqun.com/project/8?p=2630
 * @param {requestParams} params
 * @return {*}
 */
export const ecologicalReport = (user_id: any) => {
	return pmsRequest.get(`/person-matters/ecological-report?user_id=${user_id}`)
}
//
export const getCadreLine = (user_id: any) => {
	return pmsRequest.get(`?user_id=${user_id}`)
}
// 干部指数和测评分数/leader-screen/cadre-index-and-test-score https://wiki.aidangqun.com/project/8?p=2954
export const getCadreIndexAndTestScore = (params: any) => {
	return userRequest.get('/leader-screen/cadre-index-and-test-score', params)
}
// 个人测评分析结果详情/leader-screen/test-result-detail https://wiki.aidangqun.com/project/8?p=2961
export const getTestResultDetail = (params: any) => {
	return userRequest.get(`/leader-screen/test-result-detail`, params)
}
export const getRadio = async () => {
	const params = {}
	const { data, code } = await getDimensionConfig(params)
	if (code == 0) {
		const ids: any = []
		data.map((item: any) => {
			if (item.name == '基础信息') {
				item.children.forEach((item: any) => {
					ids.push(item.config_id)
				})
			} else {
				item.children.forEach((item: any) => {
					ids.push(item.config_id)
				})
			}
		})

		return ids
	}
}

/**
 * @description: 特征标签 https://wiki.aidangqun.com/project/8?p=2861
 * @param {any} params
 * @return {*}
 */
export const getFeature = (params: any, config = {} as any) => {
	return pmsRequest.get(`/pms-leader-screen/feature`, params, config)
}

// 重大表现数据查询 https://wiki.aidangqun.com/project/8?p=2867
export const getPerformance = (params: any) => {
	return pmsRequest.get(`/pms-leader-screen/user_major_performace`, params)
}
// 4010202120550-预警干部列表 https://wiki.aidangqun.com/project/8?p=3115&keyword=person-matters%252Falert_user
export const getAlertUser = (params: any) => {
	return pmsRequest.get(`/person-matters/alert_user`, params)
}
//  培训情况（/person-matters/user_train）
export const getUserTrain = (params: any) => {
	return pmsRequest.get(`/person-matters/user_train`, params)
}

//
export const getAnalysis = (params: any) => {
	return pmsRequest.get(`/leader-screen/result_title`, params)
}
//  年度述职（/person-matters/annual_report）https://wiki.aidangqun.com/project/8?p=3523
export const getAnualReport = (params: any) => {
	return pmsRequest.get(`/person-matters/annual_report`, params)
}