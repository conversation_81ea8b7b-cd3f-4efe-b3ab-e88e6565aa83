import { padAxios, pmsRequest, userRequest } from '@/utils/axios'
// 收藏夹 列表
export const getCollectionList = (adminId: any, page: any, content: string) => {
	return padAxios.get(`/favorites/findList/${adminId}/${page}/${content}`)
}
// 收藏夹弹窗列表
export const getCollectionList1 = (adminId: any, sourceId: any, page: number) => {
	return padAxios.get(`/favorites/findList2/${adminId}/${sourceId}/${page}`)
}

// 收藏夹 新增
export const addCollection = (adminId: any, name: any, description: any) => {
	return padAxios.get(`/favorites/add/${adminId}/${name}/${description}`)
}
// 收藏夹 删除
export const deleteCollection = (favoritesId: any) => {
	return padAxios.get(`/favorites/delete/${favoritesId}`)
}
// 收藏夹 删除
export const updateCollection = (favoritesId: any, name: any, description: any) => {
	return padAxios.get(`/favorites/update/${favoritesId}/${name}/${description}`)
}
// 收藏列表
export const getFavoriteList = (favoritesId: any) => {
	return padAxios.get(`/favorite/findList/${favoritesId}`)
}
// 收藏删除
export const deleteFavorite = (favoritesId: any, sourceId: any) => {
	return padAxios.get(`/favorite/cancel/${favoritesId}/${sourceId}`)
}
// 收藏新增
export const addFavorite = (favoritesId: any, sourceId: any) => {
	return padAxios.get(`/favorite/add/${favoritesId}/${sourceId}`)
}
// 标注列表
export const getCalloutList = (adminId: any, page: any, content: any) => {
	return padAxios.get(`/callout/findList/${adminId}/${page}/${content}`)
}
// 更新标注
export const updateCallout = (calloutId: any, data: any) => {
	return padAxios.get(`/callout/update/${calloutId}/${data}`)
}
// 新增标注
export const addCallout = (sourceId: any, sourceName: any, adminId: any, data: any) => {
	return padAxios.get(`/callout/add/${sourceId}/${sourceName}/${adminId}/${data}`)
}
// 删除标注
export const deleteCallout = (calloutId: any) => {
	return padAxios.get(`/callout/delete/${calloutId}`)
}
// 获取标注详情
export const getCalloutDetail = (adminId: any, calloutId: any) => {
	return padAxios.get(`/callout/get/${adminId}/${calloutId}`)
}
// 获取用户信息
// export const getUsersByIds = (params: any) => {
// 	return userRequest.get(`/uc/user/get-user-by-ids`, params)
// }
// 获取用户信息
export const getUsersByIds = (params: any) => {
	return pmsRequest.get(`/cadre_select/favorite`, params)
}
// 批量收藏
export const addBatchCollection = (favoritesId: any, sourceIds: any) => {
	return padAxios.get(`/favorite/addBatch/${favoritesId}/${sourceIds}`)
}
// 40102021804-登录个人中心（/sys_user/login_personal）
export const loginPersonal = (params: any) => {
	return pmsRequest.get(`/sys_user/login_personal`, params)
}
//  40102021805-修改个人中心密码 /sys_user/update_personal_pwd
export const updatePersonalPassword = (params: any) => {
	return pmsRequest.get(`/sys_user/update_personal_pwd`, params)
}
