import { pmsRequest, padAxios } from '@/utils/axios'
import { ENV_OFFLINE } from '@/config/env'
const paramsTransform = (params: any) => {
	return ENV_OFFLINE ? { param: JSON.stringify(params) } : params
}

const postTransformGet = () => {
	return pmsRequest[ENV_OFFLINE ? 'get' : 'post']
}
//  干部指数，标签 （ /patrol/index_tag）
export const getInspectionIndexTag = (params: any) => {
	return pmsRequest.get('/patrol/index_tag', params)
}
// 干部巡察指数(金字塔，列表)（ /patrol/pyramid_list）
export const getInspectionIndexPyramidList = (params: any) => {
	return pmsRequest.get('/patrol/pyramid_list', params)
}
// 巡察测评 （/patrol/eval_item）https://wiki.aidangqun.com/project/8?p=3282
export const getInspectionIndexEvalItem = (params: any) => {
	return pmsRequest.get('/patrol/eval_item', params)
}
// 测评对比（/patrol/eval_compare）
export const getInspectionIndexEvalCompare = (params: any) => {
	return pmsRequest.get('/patrol/eval_compare', params)
}
// 巡察指数（/patrol/index https://wiki.aidangqun.com/project/8?p=3287
export const getInspectionIndex = (params: any) => {
	return pmsRequest.get('/patrol/index', params)
}
//  谈话等次(/patrol/speech_rating) https://wiki.aidangqun.com/project/8?p=3288
export const getInspectionIndexSpeechRating = (params: any) => {
	return pmsRequest.get('/patrol/speech_rating', params)
}
//   巡察结果运用(/patrol/org_inspection_use)
export const getInspectionIndexOrgInspectionUse = (params: any) => {
	return pmsRequest.get('/patrol/org_inspection_user', params)
}
// 班子巡察及其详情（/patrol/org_inspection）
export const getInspectionIndexOrgInspection = (params: any) => {
	return pmsRequest.get('/patrol/org_inspection', params)
}
// 班子谈话等次（/patrol/org_speech_rating）
export const getInspectionIndexOrgSpeechRating = (params: any) => {
	return pmsRequest.get('/patrol/org_speech_rating', params)
}
// 班子成员巡察指数（/patrol/org_user_patrol_index）
export const getOrgUserPatrolIndex = (params: any) => {
	return pmsRequest.get('/patrol/org_user_patrol_index', params)
}
