import { loginRequest, pmsRequest } from '@/utils/axios'
//登录
export const login = ({ password, phone, uuid, captcha, flag }: any) => {
	return loginRequest.post(`/login`, {
		phone,
		password,
		uuid,
		captcha,
		flag,
	})
	// update dingxing 2018-05-03 14:58 调用node登录接口路由
}
export const chooseOrg = (params: any) => {
	return loginRequest.post(`/choose`, params)
	// update dingxing 2018-05-03 14:58 调用node登录接口路由
}
// 40102021802-登录（/login/login）
export const offlineLogin = (params: any) => {
	return pmsRequest.get(`/login/login`, params)
}
// 40102021801-同步系统用户（/login/sync_sys_user）
export const syncSysUser = () => {
	return pmsRequest.get(`/login/sync_sys_user`)
}
// 40102021803-修改系统用户密码 https://wiki.aidangqun.com/project/8?p=3101
export const updateUserPassword = (params: any, config: any) => {
	return pmsRequest.get(`/sys_user/update_user_pwd`, params, config)
}
// 获取数据同步进度（/sync/percent）https://wiki.aidangqun.com/project/8?p=3612
export const getSyncPercent = (params: any) => {
	return pmsRequest.get(`/sync/percent`, params)
}
