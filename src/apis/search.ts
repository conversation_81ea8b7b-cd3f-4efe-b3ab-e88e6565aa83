import { ENV_OFFLINE } from '@/config/env'
import { pmsRequest } from '@/utils/axios'

export const getPmsLeader = (params: any) => {
	return pmsRequest.post('/pms-leader/query', params)
}
export const getPmsLeader1 = (params: any) => {
	if (ENV_OFFLINE) {
		params.query = JSON.stringify(params)
	}
	return pmsRequest[ENV_OFFLINE ? 'get' : 'post']('/pms-leader/query', params)
}
// http://wiki.aidangqun.com/project/8?p=2253
export const fetchInfoByType = (params: any) => {
	return pmsRequest.get('/pms-leader/associate', params)
}
