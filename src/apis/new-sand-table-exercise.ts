import { pmsRequest, padAxios } from '@/utils/axios'
import { ENV_OFFLINE } from '@/config/env'
const paramsTransform = (params: any) => {
	return ENV_OFFLINE ? { param: JSON.stringify(params) } : params
}

const postTransformGet = () => {
	return pmsRequest[ENV_OFFLINE ? 'get' : 'post']
}

// 沙盘推演接口参数需要手动规定顺序
// 401020214001-沙盘推演主页  https://wiki.aidangqun.com/project/8?p=2295
export const getHomePage = (params: any) => {
	return pmsRequest.get(`/cadre_select/home_data`, params)
}
// https://wiki.aidangqun.com/project/8?p=2787
export const getOrgTeam = (params: any) => {
	return pmsRequest.get(`/cadre_select/org_team`, params)
}
export const getPlanList = () => {
	return pmsRequest.get('/cadre_select/mock_list')
}
// 新增方案
export const addPlan = (params: any) => {
	return pmsRequest.get('/cadre_select/create_mock', params)
}
// 修改方案
export const editPlan = (params: any) => {
	return pmsRequest.get('/cadre_select/update_mock', params)
}
// 复制方案
export const copyPlan = (params: any) => {
	return pmsRequest.get('/cadre_select/copy_mock', params)
}
// 确定最终方案
export const submitFinalPlan = (params: any) => {
	return pmsRequest.get('/cadre_select/confirm_mock', params)
}
// 确定上会方案
export const submitUpMeetPlan = (params: any) => {
	return pmsRequest.get('/cadre_select/meet_mock', params)
}
// 获取调整名单
export const getAdjustList = (params: any) => {
	if (ENV_OFFLINE) {
		const _params: any = {}
		_params['param'] = JSON.stringify(params)

		return pmsRequest.get('/cadre_select/motion_list', _params)
	}
	return pmsRequest.post('/cadre_select/motion_list', params)
}
// 撤销调整
export const cancalAdjust = (params: any) => {
	return pmsRequest.get('/cadre_select/cancel_mock', params)
}
// https://wiki.aidangqun.com/project/8?p=2800
export const getlackJob = (params: any) => {
	return postTransformGet()(`/cadre_select/lack_job`, paramsTransform(params))
}
// https://wiki.aidangqun.com/project/8?p=2800
export const getSubmitList = (params: any) => {
	return postTransformGet()(`/cadre_select/to_submit_list`, paramsTransform(params))
}
// https://wiki.aidangqun.com/project/8?p=2800
export const getJobList = (params: any) => {
	return postTransformGet()(`/cadre_select/job_list`, params)
}
// https://wiki.aidangqun.com/project/8?p=2811 岗找人-干部查询
export const getOrgTeamDetail = (params: any) => {
	return postTransformGet()(`/cadre_select/find_user`, paramsTransform(params))
}
// https://wiki.aidangqun.com/project/8?p=2803 待配名单
export const getOrgTeamDetail2 = (params: any) => {
	return pmsRequest.post(`/cadre_select/to_submit_list`, params)
}
// https://wiki.aidangqun.com/project/8?p=2803 待配名单
export const getTeamLeaderList = (params: any) => {
	return pmsRequest.post(`/cadre_select/team_leader_list`, params)
}
// https://wiki.aidangqun.com/project/8?p=2788 加入候选
export const addCandidate = (params: any) => {
	return postTransformGet()(`/cadre_select/add_candidate`, paramsTransform(params))
}
// code https://wiki.aidangqun.com/project/8?p=2819
export const getCode = (params: any) => {
	return pmsRequest.get(`/cadre_select/code`, params)
}
// https://wiki.aidangqun.com/project/8?p=2804 岗位预警
export const getJobWarning = (params: any) => {
	return postTransformGet()(`/cadre_select/warn_list`, paramsTransform(params))
}
// 首页 https://wiki.aidangqun.com/project/8?p=2818
export const getHomeData = (params: any) => {
	return pmsRequest.get(`/cadre_select/team_leader_list`, params)
}
// 取消候选 https://wiki.aidangqun.com/project/8?p=2808
export const cancelMock = (params: any) => {
	return pmsRequest.get(`/cadre_select/cancel_mock`, params)
}
// 取消候选 https://wiki.aidangqun.com/project/8?p=2808
export const changeAdjustType = (params: any) => {
	return pmsRequest.get(ENV_OFFLINE ? '/cadre_select/update_adjust_type' : `/cadre_select/update/adjust_type`, params)
}
// 班子结构异常 https://wiki.aidangqun.com/project/8?p=2830#1%E3%80%81%E6%8E%A5%E5%8F%A3%E5%AE%9A%E4%B9%89
export const teamStructureList = (params: any) => {
	return postTransformGet()(`/cadre_select/team_structure_list`, paramsTransform(params))
}
// 班子结构分析 https://wiki.aidangqun.com/project/8?p=2833
export const teamMockAnalyze = (params: any) => {
	if (ENV_OFFLINE) {
		const _params: any = {}
		_params['param'] = JSON.stringify(params)

		return pmsRequest.get('/cadre_select/mock_analyze', _params)
	}
	return pmsRequest.post(`/cadre_select/mock_analyze`, params)
}
// 班子结构分析-候选人 https://wiki.aidangqun.com/project/8?p=2832
export const teamFindCandidate = (params: any) => {
	return pmsRequest.get(`/cadre_select/find_candidate`, params)
}
//收藏夹 https://wiki.aidangqun.com/project/8?p=2844
export const favorite = (params: any) => {
	return pmsRequest.post(`/cadre_select/favorite`, params)
}
// 获取收藏夹，收藏人员 https://wiki.aidangqun.com/project/8?p=2835
export const getFavoriteList = (adminId: any) => {
	return padAxios.get(`/favorites/find/${adminId}`)
}

// 修改拟任职务文本名称 https://wiki.aidangqun.com/project/8?p=2853
export const updateJobName = (params: any) => {
	return pmsRequest.get(`/cadre_select/modify_job_name`, params)
}

// 修改序号（调整名单） https://wiki.aidangqun.com/project/8?p=2809
export const updateSerialNumber = (params: any) => {
	return pmsRequest.post(`/cadre_select/modify_seq`, paramsTransform(params))
}

// 查询中层干部 https://wiki.aidangqun.com/project/8?p=2806
export const getMiddleLayer = (params: any) => {
	return pmsRequest.get(`/cadre_select/find_middle_level_cadres`, params)
}

// 获取拟任职务文本名称 https://wiki.aidangqun.com/project/8?p=2856
export const getJobName = (params: any) => {
	return pmsRequest.get(`/cadre_select/get_job_name`, params)
}

// 添加候选人（中层） https://wiki.aidangqun.com/project/8?p=2857
export const addCandidateMiddle = (params: any) => {
	if (ENV_OFFLINE) {
		const _params: any = {}
		_params['param'] = JSON.stringify(params)

		return pmsRequest.get('/cadre_select/add_middle_candidate', _params)
	}

	return pmsRequest.post(`/cadre_select/add_middle_candidate`, params)
}

// 获取用户的拟任职务 https://wiki.aidangqun.com/project/8?p=2858
export const getUserDesiredJob = (params: any) => {
	return pmsRequest.get(`/cadre_select/get_user_candidate`, params)
}
//获取一个方案 https://wiki.aidangqun.com/project/8?p=2877
export const getMock = () => {
	return pmsRequest.get(`/cadre_select/get_default_mock`)
}

// 修改调整理由 https://wiki.aidangqun.com/project/8?p=2879
export const adjustReason = (params: any) => {
	return pmsRequest.get(`/cadre_select/adjust_reason`, params)
}
//调整对比分析 https://wiki.aidangqun.com/project/8?p=2880
export const adjustCompareOrg = (params: any) => {
	return pmsRequest.get(`/cadre_select/adjust_compare`, params)
}
//  首页领导列表 https://wiki.aidangqun.com/project/8?p=2818
export const getTeamUserList = (params: any) => {
	return pmsRequest.get(`/cadre_select/team_leader_list`, params)
}
//  删除方案/cadre_select/delete_mock
export const deleteMock = (params: any) => {
	return pmsRequest.get(`/cadre_select/delete_mock`, params)
}
//  首页根据干部姓名查询（/cadre_select/home_page_user_search） https://wiki.aidangqun.com/project/8?p=2952
export const homePageUserSearch = (params: any) => {
	return pmsRequest.get(`/cadre_select/home_page_user_search`, params)
}
// 首页根据班子名称查询（/cadre_select/home_page_org_search） https://wiki.aidangqun.com/project/8?p=2953
export const homePageOrgSearch = (params: any) => {
	return pmsRequest.get(`/cadre_select/home_page_org_search`, params)
}
