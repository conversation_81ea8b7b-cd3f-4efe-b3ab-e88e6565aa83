import { pmsRequest, padAxios } from '@/utils/axios'
import { ENV_OFFLINE, XUNCHA_VERSION } from '@/config/env'
// 巡查离线版
const XUNCHA_LEVEL = ENV_OFFLINE && XUNCHA_VERSION

const replaceLargeOffline = (path: string) => {
	if (XUNCHA_LEVEL) {
		return path.replace('/large', '')
	}
	return path
}

// 0102021908-点击班子(/large/screen/teamInspectionList) https://wiki.aidangqun.com/project/8?p=3445
export const getOrgDetails = (params: any) => {
	return pmsRequest.get(replaceLargeOffline('/large/screen/teamInfo'), params)
}
// 40102021901-班子巡察（/large/screen/teamInspection）https://wiki.aidangqun.com/project/8?p=3432

export const getTeamInspectionList = (params?: any) => {
	return pmsRequest.post(replaceLargeOffline('/large/screen/teamInspection'), params)
}
// 40102021902-班子问题（/large/screen/teamSummary https://wiki.aidangqun.com/project/8?p=3433
export const getTeamSummary = () => {
	return pmsRequest.post(replaceLargeOffline('/large/screen/teamSummary'))
}
// 40102021903-一把手/其他成员巡察指数（/large/screen/aHand）https://wiki.aidangqun.com/project/8?p=3434
export const getAHand = (params: any) => {
	return pmsRequest.get(replaceLargeOffline('/large/screen/aHand'), params)
}
// 获取树tab切换 40102021905-班子评测机构序列号(/large/screen/screenList)  https://wiki.aidangqun.com/project/8?p=3436
export const apiGetTreeTab = () => {
	return pmsRequest.post(replaceLargeOffline('/large/screen/screenList'))
}
// 40102021904-班子评测树(/large/screen/orgScreen)  https://wiki.aidangqun.com/project/8?p=3435
export const apiGetTreeData = (sequence: any[]) => {
	if (XUNCHA_LEVEL) {
		return pmsRequest.get(replaceLargeOffline('/large/screen/orgScreen'), {
			sequence: sequence ? JSON.stringify(sequence) : undefined,
		})
	}
	return pmsRequest.post(replaceLargeOffline('/large/screen/orgScreen'), sequence)
}
//  40102021906-一把手/其他成员巡察指数列表(/large/screen/aHandList)

export const getAHandList = (params: any) => {
	return pmsRequest.get(replaceLargeOffline('/large/screen/aHandList'), params)
}
// 0102021907-班子扣分列表(/large/screen/teamInspectionList)

export const getTeamInspectionListForScore = (params: any) => {
	return pmsRequest.get(replaceLargeOffline('/large/screen/teamInspectionList'), params)
}
//  班子巡察指数(金字塔，列表 /patrol/org_pyramid_list )
export const getOrgInspectionList = (params: any) => {
	return pmsRequest.get('/patrol/org_pyramid_list', params)
}
// 班子同序列排名（/patrol/org_index_rank）
export const getOrgIndexRank = (params: any) => {
	return pmsRequest.get('/patrol/org_index_rank', params)
}
//  个人同序列排名（/patrol/user_index_rank）
export const getUserIndexRank = (params: any) => {
	return pmsRequest.get('/patrol/user_index_rank', params)
}
// 干部政治画像（/patrol/user_political_screen）
export const getUserPoliticalScreen = (params: any) => {
	return pmsRequest.get('/patrol/user_political_screen', params)
}
//  班子政治画像（/patrol/org_political_screen）
export const getOrgPoliticalScreen = (params: any) => {
	return pmsRequest.get('/patrol/org_political_screen', params)
}
