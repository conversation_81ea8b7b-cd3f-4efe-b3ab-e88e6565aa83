import { ENV_OFFLINE } from '@/config/env'
import { pmsRequest } from '@/utils/axios'
/**
 * @description: 查看评测结果 http://wiki.aidangqun.com/project/8?p=1982
 * @param {any} params
 * @return {*}
 */
export const getComparisonResult = (params: any) => {
	return pmsRequest.post('/eval/get-compare-result', params)
}
export const getComparisonResult1 = (params: any) => {
	if (!ENV_OFFLINE) {
		params.user_ids = params.user_ids?.split(',')
	}
	return pmsRequest[ENV_OFFLINE ? 'get' : 'post']('/eval/get-compare-result', params)
}
