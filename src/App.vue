<template>
	<a-config-provider :locale="zhCN">
		<RouterView v-slot="{ Component }">
			<keep-alive :include="keepalive_store.keepalive">
				<component :is="Component" :key="$route.meta.routerViewKey ? $route.query.user_id : ''" />
			</keep-alive>
		</RouterView>
		<!-- <RouterView /> -->
	</a-config-provider>
	<ProgressTips :visible="startLoad" :percentage="syncDataProgress" />
</template>
<script setup lang="ts">
import { provide, ref } from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { Base64, getUserInfoItem } from '@/utils/utils'
import { useComparative } from '@/store/comparative'
import ProgressTips from '@/components/ProgressTips.vue'
import { getSyncPercent } from '@/apis/login'
// import { getLeaderBaseInfo } from '@/apis/cadre-portrait/home'
dayjs.locale('zh-cn')
import useKeepAlive from '@/store/keepalive'
import { message } from 'ant-design-vue'
const keepalive_store = useKeepAlive()

const _tk = getUserInfoItem('_tk')

const comparative = useComparative()

_tk && comparative.initConfigIds()

const URI = window.location.href
if (URI.includes('?')) {
	// const params = getParam()
	const params = new URLSearchParams(URI.split('?')[1])
	// console.log('_h', params)
	const _h = params.get('_h')
	const user_id = params.get('user_id')
	if (_h) {
		const userInfo = JSON.parse(Base64.decode(_h) || '{}')
		// 保存用户信息
		user_id && (userInfo.user_id = user_id)
		window.sessionStorage.setItem('userInfo', JSON.stringify(userInfo))
	}
}
// 数据加载进度
const syncDataProgress = ref(0)
// 开始同步数据
const startLoad = ref(false)

const loadSyncDataProgress = (flag: any) => {
	let timer: any

	startLoad.value = true

	const load = async (flag: any) => {
		const res = await getSyncPercent({
			flag,
		})

		if (res.code === 0) {
			const { data } = res

			syncDataProgress.value = data

			if (data >= 100) {
				// 同步完成

				clearInterval(timer)

				timer = undefined

				setTimeout(() => {
					message.success('数据同步完成')

					syncDataProgress.value = 0

					startLoad.value = false
				}, 1000)
			}
		} else {
			syncDataProgress.value = 0

			startLoad.value = false

			message.error(res.message)

			clearInterval(timer)

			timer = undefined
		}
	}

	timer = setInterval(() => {
		load(flag)
	}, 500)
}

provide('progress', {
	startLoad,
	start: (flag: any) => {
		loadSyncDataProgress(flag)
	},
	close: (flag: any) => {
		startLoad.value = false
	},
})
</script>
