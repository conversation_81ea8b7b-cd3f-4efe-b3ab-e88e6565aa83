@import './assets/less/reset.less';
@import './styles/font.less';
@import './styles/variable.less';

:root {
	font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
	line-height: 1.5;
	font-weight: 400;

	color-scheme: light dark;
	color: rgba(255, 255, 255, 0.87);

	font-synthesis: none;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-webkit-text-size-adjust: 100%;
}

body {
	margin: 0;
	display: flex;
	place-items: center;
	min-width: 320px;
}

h1 {
	font-size: 3.2em;
	line-height: 1.1;
}

#app {
	height: 100%;
	width: 100%;
}

.ant-popover {
	width: 300px;
}
.ant-message {
	svg {
		font-size: 20px !important;
	}
	.ant-message-custom-content {
		font-size: 20px !important;
	}
}
