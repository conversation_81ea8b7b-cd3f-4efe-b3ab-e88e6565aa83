<template>
	<div class="layout-wrap">
		<div class="header-wrap">
			<div class="left">
				<div v-if="back" class="back-box" @click="$router.go(-1)">
					<span class="back-icon"></span>
					<span class="line"></span>
					<span class="back-text">返回</span>
				</div>
			</div>
			<div class="center">{{ title }}</div>
			<div class="right"></div>
		</div>
		<div class="content-wrap">
			<slot></slot>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
export default defineComponent({
	props: {
		title: String,
		back: {
			type: Boolean,
			default: false,
		},
	},
	setup() {
		const route = useRoute()
		const showHeader = ref(true)

		watchEffect(() => {
			if (route.meta.header !== undefined) {
				showHeader.value = route.meta.header as boolean
			}
		})
		return { showHeader }
	},
})
</script>
<style lang="less" scoped>
.layout-wrap {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background-color: #f6f8fc;
	color: #333333;
	.content-wrap {
		flex: 1;
		overflow: hidden;
		font-family: 'SourceHanSansCN-Medium';
	}
	.header-wrap {
		width: 100%;
		height: 100px;
		text-align: center;
		// background: url('@/assets/images/header-bg.png') no-repeat center / cover;
		background: rgba(210, 49, 34, 1);
		display: flex;
		align-items: center;
		.center {
			margin: 0px 41px;
			font-family: YouSheBiaoTiHei;
			font-size: 62px;
			line-height: 62px;
			font-weight: 400;
			color: #ffffff;
		}
		.left {
			background: url('@/assets/images/layout-left.png') center right / 528px 22px no-repeat;
			flex: 1;
			height: 100%;
			.back-box {
				padding-left: 16px;
				display: flex;
				align-items: center;
				height: 100%;
				cursor: pointer;
				.back-icon {
					display: inline-block;
					width: 42px;
					height: 42px;
					background: url('@/assets/images/back-header.png') center / cover no-repeat;
				}
				.line {
					width: 1px;
					height: 24px;
					margin: 0px 20px;
					background-color: #ffffff;
				}
				.back-text {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					font-size: 36px;
					color: #ffffff;
				}
			}
		}
		.right {
			background: url('@/assets/images/layout-right.png') center left / 747px 22px no-repeat;
			flex: 1;
			height: 100%;
		}
	}
}
</style>
