<template>
	<!-- <slot name="avatar" v-bind="{ avatar: head_url ? prefixCls + head_url : defaultAvatar }"></slot> -->
	<img :src="head_url ? URLCDN + '/' + head_url : defaultAvatar" alt="" class="avatar" @error="loadError" />
	<!-- <img name="avatar" :src="head_url" /> -->
</template>
<script lang="ts">
import { batchGetAvatar } from '@/apis/cadreSystem'
import { throttle } from '@/utils/utils'
import { CDN_URL, ENV_OFFLINE, ENV_PRO, XUNCHA_PRO } from '@/config/env'

const cacheMap = new Map()
const callMap = new Map()

let timer: any = null

const pushTask = (task: any) => {
	if (timer) {
		clearTimeout(timer)

		timer = null
	}

	const { user_id, cb = () => undefined } = task
	if (callMap.has(user_id)) {
		return
	}

	callMap.set(user_id, cb)

	timer = setTimeout(() => {
		if (callMap.size) {
			const user_ids: any = []

			callMap.forEach((value, key) => {
				user_ids.push(key)
			})

			// 拆分10个ID一组进行请求
			const chunkSize = 2
			const chunkedArray = []
			for (let i = 0; i < user_ids.length; i += chunkSize) {
				const chunk = user_ids.slice(i, i + chunkSize)
				chunkedArray.push(chunk)
			}

			chunkedArray.forEach((item) => {
				batchGetAvatar({
					user_ids: item.join(','),
				}).then((res: any) => {
					if (res.code === 0) {
						res.data.forEach((item: any) => {
							callMap.get(item.user_id)?.(item)

							callMap.delete(item.user_id)
						})
					}
				})
			})
		}
	}, 50)
}
</script>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import defaultAvatar from '@/assets/images/avatar.png'

const props = defineProps({
	user_id: String || Number,
	head_url: String,
})
const prefixCls = ENV_OFFLINE ? `file:///${window.js_kit?.getHeadPhotoPath?.()}/` : CDN_URL + '/fr_img/'
// 巡察CDN地址不一样
const URLCDN = XUNCHA_PRO ? CDN_URL : prefixCls

const avatar = ref(defaultAvatar)

const show = ref(false)

const requestAvatar = () => {
	if (!props.user_id) {
		return
	}

	if (!ENV_OFFLINE && props.head_url) {
		return (avatar.value = props.head_url.includes('data:image') ? props.head_url : `${CDN_URL}/fr_img/${props.head_url}`)
	}

	if (cacheMap.size > 100) {
		cacheMap.clear()
	}

	if (cacheMap.has(props.user_id)) {
		avatar.value = cacheMap.get(props.user_id)
		return
	}

	// batchGetAvatar({
	// 	user_ids: props.user_id,
	// }).then((res) => {
	// 	if (res.code === 0) {
	// 		avatar.value = res.data[0]?.avatar

	// 		cacheMap.set(props.user_id, avatar.value)
	// 	}
	// })
	pushTask({
		user_id: props.user_id,
		cb: (item: any) => {
			const { avatar: _avatar } = item
			// base64ToBlob({ b64data: _avatar }).then((blob: any) => {
			// 	avatar.value = blob.preview

			// 	cacheMap.set(props.user_id, _avatar)
			// })
			setTimeout(() => {
				avatar.value = _avatar

				show.value = true

				cacheMap.set(props.user_id, _avatar)
			}, Math.random() * 100)
		},
	})
}

const loadError = (e) => {
	e.target.src = defaultAvatar
}
// watch(() => props.user_id, requestAvatar, { immediate: true })
</script>

<style lang="scss" scoped>
img {
	background: url('v-bind(defaultAvatar)') no-repeat center / 100%;
}
</style>
