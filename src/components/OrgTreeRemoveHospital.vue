<template>
	<div :class="`tree-list`">
		<div class="left">
			<div class="top-org" @click="onTopSelect"><img class="org-icon" src="@/assets/images/org-icon.png" alt="" />{{ treeData[0]?.short_name }}</div>
			<a-tree
				blockNode
				show-icon
				v-model:expandedKeys="expandedKeys"
				v-model:selectedKeys="selectedKeys"
				v-model:checkedKeys="checkedKeys"
				:tree-data="treeData[0]?.children"
				@select="onTreeSelect"
				:field-names="fieldNames"
			>
				<template #title="{ short_name }">
					<span>{{ short_name }}</span>
				</template>
				<template #switcherIcon="{ defaultIcon }">
					<component :is="defaultIcon" />
				</template>
				<template #icon="{ dataRef }">
					<img v-if="dataRef?.children.length" class="sub-org-icon" src="@/assets/images/sub-org-icon.png" />
					<span v-else class="sub-org-icon"></span>
				</template>
			</a-tree>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { getOrgList } from '@/apis/cadreSystem'
import { message } from 'ant-design-vue'
import { onMounted, ref, shallowRef, watch } from 'vue'

const emit = defineEmits(['select', 'bread-crumb'])

const props = defineProps({
	initKey: {
		type: Array<any>,
	},
	type: {
		type: Number,
		default: 0,
	},
})
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const checkedKeys = ref<string[]>([])
const treeData = shallowRef<any>([])
const isShowSearch = ref(false)
const fieldNames = {
	children: 'children',
	title: 'short_name',
	key: 'org_id',
}
// 新增获取组织树方法
async function fetchOrgTreeData() {
	try {
		const { data } = await getOrgList({ type: props.type })
		if (data?.length) {
			const processedData = addIndexToChildren(data)
			treeData.value = processedData
			// 扁平化处理，但不影响原始数据
			const flattenTree = (tree: any[]): any[] => {
				return tree.reduce((acc: any[], node: any) => {
					acc.push({
						org_id: node.org_id,
						short_name: node.short_name,
						name: node.name,
						index: node.index,
					})
					if (node.children?.length) {
						acc.push(...flattenTree(node.children))
					}
					return acc
				}, [])
			}
			// 存储扁平化的数据，但不修改原始树形结构
			const flattenedData = flattenTree(processedData)
			return flattenedData
		}
	} catch (error) {
		message.error('获取组织树数据失败')
		return []
	}
}
function addIndexToChildren(data: any) {
	// 定义递归函数
	function addIndexRecursive(children: any, index: any) {
		children?.forEach((child: any, i: any) => {
			// 为当前对象添加 index 属性
			child.index = index + '-' + i
			// 如果当前对象的 children 属性存在且是一个数组，则继续递归调用 addIndexRecursive
			if (child?.children && Array.isArray(child?.children)) {
				addIndexRecursive(child?.children, child?.index)
			}
		})
	}

	// 遍历顶层对象，为其 children 添加 index
	data?.forEach((item: any, i: any) => {
		item.index = String(i)
		if (item.children && Array.isArray(item.children)) {
			addIndexRecursive(item.children, item.index)
		}
	})

	return data
}
function findPath(orgTree: any, targetOrgId: any, targetName: any) {
	let path: any = []

	// 定义递归函数
	function findPathRecursive(node: any, currentPath: any) {
		// 将当前节点添加到当前路径中
		currentPath.push({ org_id: node.org_id, index: node.index, children: node.children, name: node.name })

		// 如果当前节点的 org_id 和 name 匹配目标值，则将当前路径作为结果路径
		if (node.org_id === targetOrgId && node.name === targetName) {
			path = currentPath.slice() // 复制当前路径
			return
		}

		// 遍历当前节点的子节点
		if (node?.children) {
			for (const child of node?.children) {
				// 递归调用
				findPathRecursive(child, currentPath.slice())
				// 如果找到了路径，则直接返回
				if (path.length > 0) {
					return
				}
			}
		}
	}

	// 调用递归函数
	findPathRecursive(orgTree, [])

	return path
}


const onTopSelect = () => {
	expandedKeys.value = []
	selectedKeys.value = [treeData?.value[0]?.org_id]
	emit('select', treeData?.value[0]?.org_id, treeData?.value[0], [])

	emit(
		'bread-crumb', // 寻路并生成结果
		findPath(treeData?.value[0], treeData?.value[0].org_id, treeData?.value[0].name)
	)
}
const currentSelect = ref()

const onTreeSelect = (_selectedKeys: any, { node }: any, type = 'click') => {
	if (currentSelect.value == _selectedKeys[0]) {
		return
	}
	currentSelect.value = _selectedKeys[0]

	// 如果点击的是顶级节点，直接调用 onTopSelect
	if (node?.dataRef?.org_id === 1) {
		return onTopSelect()
	}

	// 如果点击的是某个节点，判断是否需要展开或收起
	const isExpanded = expandedKeys.value.includes(node?.dataRef.org_id)

	if (node?.dataRef.children && node?.dataRef.children.length > 0) {
		// 如果当前节点有子节点
		if (isExpanded) {
			// 如果当前节点已经展开，则收起
			expandedKeys.value = expandedKeys.value.filter((key) => key !== node.dataRef.org_id)
		} else {
			// 如果当前节点未展开，则展开
			expandedKeys.value = [...expandedKeys.value, node.dataRef.org_id]
		}
	}

	// 如果有选中值，则触发 select 事件
	if (_selectedKeys.length > 0) {
		emit('select', _selectedKeys[0], node?.dataRef, expandedKeys.value)
		// 触发 bread-crumb 事件
		emit('bread-crumb', findPath(treeData?.value[0], _selectedKeys[0], node?.dataRef.name))
	}
}
// 找到节点的所有父级org_id
const findParentIds = (tree: any, targetOrgId: number): number[] => {
	const parentIds: number[] = []

	function findParentsRecursive(node: any, currentPath: number[]): boolean {
		// 如果找到目标节点，返回当前路径
		if (node.org_id === targetOrgId) {
			parentIds.push(...currentPath)
			return true
		}

		// 遍历子节点
		if (node.children && node.children.length > 0) {
			for (const child of node.children) {
				if (findParentsRecursive(child, [...currentPath, node.org_id])) {
					return true
				}
			}
		}

		return false
	}

	// 从根节点开始查找
	if (tree && tree.length > 0) {
		findParentsRecursive(tree[0], [])
	}

	return parentIds
}

// 修改onSelectApi方法
const onSelectApi = async (org_id: number) => {
	console.log('🚀 ~ onSelectApi 被调用，org_id:', org_id)
	const flattenedData = await fetchOrgTreeData()
	const current = flattenedData?.find((item: any) => item.org_id == org_id)

	if (current) {
		console.log('🚀 ~ 找到目标节点:', current)

		// 确保树数据已加载
		if (treeData.value && treeData.value.length > 0) {
			// 找到所有父级节点并展开
			const parentIds = findParentIds(treeData.value, org_id)
			console.log('🚀 ~ 需要展开的父级节点:', parentIds)

			// 展开所有父级节点
			if (parentIds.length > 0) {
				expandedKeys.value = [...new Set([...expandedKeys.value, ...parentIds.map((id) => String(id))])]
				console.log('🚀 ~ 更新后的展开节点:', expandedKeys.value)
			}
		}

		onTreeSelect(
			[Number(org_id)],
			{
				node: {
					dataRef: current,
				},
			},
			'api'
		)
	} else {
		console.log('🚀 ~ 未找到目标节点，org_id:', org_id)
	}
}

// 添加初始化调用
onMounted(async () => {
	await fetchOrgTreeData()
	if (treeData.value.length > 0) {
		// 默认选中第一个节点
		const firstNodeId = treeData?.value[0]?.org_id
		selectedKeys.value = [firstNodeId]

		// 展开所有节点
		expandedKeys.value = getAllNodeIds(treeData?.value)
		// 触发选中事件
		onTreeSelect([firstNodeId], { node: { dataRef: treeData?.value[0] } })
	}
})

// 获取所有节点的 org_id
function getAllNodeIds(tree: any[]): string[] {
	let ids: string[] = []
	tree?.forEach((node: any) => {
		ids.push(node.org_id)
		if (node?.children && node?.children?.length > 0) {
			ids = ids.concat(getAllNodeIds(node?.children))
		}
	})
	return ids
}

defineExpose({
	treeData,
	checkedKeys,
	selectedKeys,
	expandedKeys,
	onSelectApi,
	onTreeSelect,
})
</script>
<style scoped lang="less">
.top-org {
	margin-bottom: 14px;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 500;
	font-size: 24px;
	color: rgba(0, 0, 0, 0.85);
	line-height: 28px;
	font-style: normal;
	cursor: pointer;
	.org-icon {
		margin-right: 15px;
		width: 54px;
		height: 54px;
	}
}
.sub-org-icon {
	width: 25px;
	height: 23px;
}
.search-box {
	padding: 10px;
	margin-bottom: 10px;
}

:deep(.ant-input) {
	width: 100%;
	height: 32px;
	line-height: 1.5;
	padding: 4px 11px;
	font-size: 14px;
	border-radius: 4px;
	border: 1px solid #d9d9d9;
	box-sizing: border-box;
}
:deep(.ant-tree-iconEle) {
	margin: 0px 9px 0px 0px;
	width: 25px;
	height: 23px;
}
.tree-list {
	display: flex;
	position: relative;
	min-height: 100%;
	background-color: #fff;
	:deep(.ant-tree) {
		.ant-tree-switcher {
			align-self: center;
		}

		.ant-tree-node-selected {
			background: rgba(0, 142, 255, 0.06);
		}
		.ant-tree-node-content-wrapper {
			padding: 12px 0px;
		}
		.ant-tree-node-content-wrapper {
			display: flex;
			align-items: center;
		}
		.ant-tree-switcher-icon {
			font-size: 13px;
			line-height: 13px;
			vertical-align: middle;
		}
		.ant-tree-title {
			// font-size: 22px;
			font-size: 24px;
			line-height: 24px;
		}
	}
	.left {
		width: 351px;
	}
}
</style>
