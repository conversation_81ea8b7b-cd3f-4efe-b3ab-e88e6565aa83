<template>
	<div class="tag-box" v-if="data.length">
		<PopTag v-for="(item, index) in data" :color="color" :key="index" :tag="item" />
	</div>
</template>

<script lang="ts" setup>
import PopTag from './Tag.vue'

const props = defineProps({
	data: {
		type: Array<any>,
		default: () => [],
	},
	color: {
		type: String || Function,
	},
})
</script>

<style lang="less" scoped>
.tag-box {
	display: flex;
	flex-wrap: wrap;
	gap: 20px;
}
</style>
