<template>
	<div style="height: 100%; width: 100%" ref="chartRef"></div>
</template>

<script lang="ts" setup>
// 自定义4.0.0版本echarts
import { onMounted, ref, watchEffect } from 'vue'
import { echart4 } from '@/assets/js/echarts.4.0.0'
const echarts: any = {}

echart4(echarts)

const props = defineProps({
	option: {},
})

const chartRef = ref()

const echarts_instance = ref()

const initEcharts = () => {
	if (chartRef.value && chartRef.value) {
		echarts_instance.value = echarts.init(chartRef.value)
	}
}

onMounted(() => {
	initEcharts()
})

watchEffect(() => {
	if (echarts_instance.value) {
		echarts_instance.value.setOption(props.option)
	} else {
		initEcharts()
	}
})

defineExpose({
	instance: echarts_instance,
})
</script>

<style lang="scss" scoped></style>
