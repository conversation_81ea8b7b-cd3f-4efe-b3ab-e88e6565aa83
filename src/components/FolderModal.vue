<template>
	<a-modal
		class="folder-modal"
		:afterClose="onAfterClose"
		:closable="false"
		:visible="collectVisible"
		width=""
		destroyOnClose
		:footer="null"
		@cancel="onClose"
	>
		<Folder @close="onClose" @success="onSuccess" modal-type="collect" :source-ids="selectedRowKeys.join(',')" />
	</a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
defineProps({
	selectedRowKeys: {
		type: Array,
		default: () => [],
	},
})

const emits = defineEmits(['close'])

const collectVisible = ref(true)

const onClose = () => {
	collectVisible.value = false
}

const onSuccess = () => {
	collectVisible.value = false
}

const onAfterClose = () => {
	emits('close')
}
</script>
<style lang="less">
.folder-modal {
	width: 752px;
	.ant-modal-body {
		padding: 0px !important;
	}
}

.search-modal {
	margin-top: 24px;
	padding: 24px;
	width: 100%;
	.ant-checkbox-wrapper {
		font-size: 20px;
	}
	button {
		font-size: 19px;
		height: 40px;
		width: 80px;
	}
}
</style>
