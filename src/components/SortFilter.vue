<template>
	<div class="filter-menu">
		<div class="filter-item" v-for="(filter, index) in filters" :key="index">
			<div class="label">{{ filter.label }}：</div>
			<div class="filter-option">
				<div
					class="f-o-item"
					@click="onActive(filter, 'up')"
					:class="{
						'up-select': active.key === filter.key && active.type === 'up',
					}"
				>
					<span class="filter-icon sort-up"></span>
					<span class="filter-text">升序</span>
				</div>
				<div
					class="f-o-item"
					:class="{
						'down-select': active.key === filter.key && active.type === 'down',
					}"
					@click="onActive(filter, 'down')"
				>
					<span class="filter-icon sort-down"></span>
					<span class="filter-text">降序</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, PropType, unref } from 'vue'

interface filterOptions {
	label: string
	key: any
	defaultSelect?: boolean
}
const props = defineProps({
	filters: { type: Array<filterOptions>, required: true },
	onSelect: {
		type: Function as PropType<(params: any) => void>,
		required: false,
	},
})

const active = ref<any>({})

const onActive = (item: filterOptions, type: string) => {
	const { key } = item
	const preActive = unref(active)
	if (preActive.key === key && preActive.type === type) {
		active.value = {}
	} else {
		active.value = { key, type }
	}

	props.onSelect?.(active.value)
}
</script>

<style lang="less" scoped>
.align-center {
	display: flex;
	align-items: center;
}
.filter-menu {
	display: flex;
	gap: 0px 51px;
	.filter-item {
		.align-center;
		.label {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 21px;
			color: #000000;
			line-height: 25px;
		}
		.filter-option {
			.align-center;
			gap: 0px 15px;
			.f-o-item {
				.align-center;
				cursor: pointer;
				justify-content: center;
				width: 128px;
				height: 48px;
				background: #f7f8fa;
				border-radius: 38px 38px 38px 38px;
				.filter-icon {
					margin-right: 9px;
					display: inline-block;
					width: 27px;
					height: 27px;
					background-position: center center;
					background-repeat: no-repeat;
					background-size: 100% 100%;
				}
				.filter-text {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 21px;
					line-height: 25px;
				}
				.sort-up {
					background-image: url('@/assets/images/sort-filter/sort-up.png');
				}
				.sort-down {
					background-image: url('@/assets/images/sort-filter/sort-down.png');
				}
			}
			.up-select {
				.filter-text {
					color: #0088ff !important;
				}
				.filter-icon {
					background-image: url('@/assets/images/sort-filter/sort-up-active.png') !important;
				}
			}
			.down-select {
				.filter-icon {
					background-image: url('@/assets/images/sort-filter/sort-down-active.png') !important;
				}
				.filter-text {
					color: #0088ff !important;
				}
			}

			.sort-down-active {
			}
		}
	}
}
</style>
