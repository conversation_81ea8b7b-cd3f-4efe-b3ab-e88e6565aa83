<template>
	<div class="poc-check">
		<span
			class="check"
			:class="{ active: isActive(item.value, props.value) }"
			v-for="(item, index) in checkOptions"
			:key="index"
			@click="onActive(item.value, $event)"
		>
			{{ item.label }}
		</span>
	</div>
</template>

<script lang="ts" setup>
import { ref, PropType, watchEffect, unref, watch } from 'vue'
import { useInjectFormItemContext } from 'ant-design-vue/lib/form/FormItemContext'

const props = defineProps({
	value: {},
	type: {
		default: 'check',
		type: String as PropType<'radio' | 'check'>, // 单选复选框
	},
	options: {
		type: Array<{
			label: string
			value: any
		}>,
		required: true,
	},
})

const emits = defineEmits(['change', 'update:checked', 'update:value'])

const formItemContext = useInjectFormItemContext()

const checkOptions = ref<any>([])

watchEffect(() => {
	checkOptions.value = props.options.map((item: any) => {
		return {
			...item,
			check: false,
		}
	})
})

const onActive = (value: any, e: any) => {
	const componentType = props.type

	let _value: any = undefined

	if (componentType === 'radio') {
		_value = value === props.value ? undefined : value
	} else {
		_value = Array.isArray(props.value) ? [...props.value] : []

		const index = _value.findIndex((item: any) => item === value)

		if (index !== -1) {
			_value.splice(index, 1)
		} else {
			_value.push(value)
		}
	}

	emits('change', _value)

	emits('update:checked', _value)

	emits('update:value', _value)

	formItemContext.onFieldChange()
}

const isActive = (value: any, _values) => {
	return props.type === 'radio' ? value === props.value : (props.value || ([] as any)).includes(value)
}
</script>

<style lang="less" scoped>
.poc-check {
	display: flex;
	flex-wrap: wrap;
	gap: 10px 15px;

	.check {
		display: inline-block;
		padding: 0px 15px;
		min-width: 168px;
		height: 54px;
		background: #ffffff;
		border-radius: 6px 6px 6px 6px;
		opacity: 1;
		text-align: center;
		font-size: 21px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: rgba(0, 122, 255, 1);
		line-height: 54px;
		user-select: none;
		border: 1px solid rgba(0, 122, 255, 1);
	}
	.active {
		background: #008eff;
		border: 1px solid #008eff;
		color: #ffffff;
	}
}
</style>
