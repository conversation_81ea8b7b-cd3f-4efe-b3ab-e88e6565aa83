<template>
	<div class="pop-tag">
		<div class="inner" :style="{ backgroundColor: color?.call ? color(tag.label) : color }">{{ tag.label }}</div>
		<div class="count">
			<span class="icon">x</span> <span class="number">{{ tag.count }}</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

defineProps({
	tag: {
		type: Object,
		default: () => ({}),
	},
	color: {
		type: String || Function,
	},
})
</script>

<style lang="scss" scoped>
.pop-tag {
	display: flex;
	.inner {
		padding: 4px 12px !important;
		font-size: 20px;
		text-align: center;
		font-family: Source <PERSON>, Source Han Sans CN;
		font-weight: 400;
		color: #ffffff;
		border-radius: 5px 5px 5px 5px;
	}
	.count {
		margin-left: 4px;
		display: flex;
		align-items: center;
		.icon {
			margin-right: 5px;
			padding-bottom: 2px;
			font-size: 23px;
			font-family: Source <PERSON>, Source <PERSON>;
			font-weight: 400;
			color: #f02a2a;
			line-height: 23px;
		}
		.number {
			font-size: 26px;
			font-family: Rany, Rany;
			font-weight: normal;
			color: #f02a2a;
			line-height: 30px;
		}
	}
}
</style>
