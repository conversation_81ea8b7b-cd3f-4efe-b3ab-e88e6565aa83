<template>
	<a-form ref="formRef" v-bind="layout" name="advanced_search" class="search-form" :model="formState">
		<a-card :bordered="false">
			<a-form-item>
				<a-radio-group v-model:value="formState.cadre_type">
					<a-radio value="2">区管干部</a-radio>
					<a-radio value="3">中层干部</a-radio>
				</a-radio-group>
			</a-form-item>
		</a-card>
		<a-card :bordered="false">
			<div class="search-title">基本信息</div>
			<a-row :gutter="24">
				<a-col :span="12">
					<a-form-item label="姓名" name="name">
						<a-input v-model:value="formState.name" placeholder="请输入" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="性别" name="gender">
						<a-checkbox-group v-model:value="formState.gender">
							<a-checkbox value="1">男性</a-checkbox>
							<a-checkbox value="2">女性</a-checkbox>
						</a-checkbox-group>
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="24">
				<a-col :span="12">
					<a-form-item label="民族" name="ethic">
						<a-checkbox-group v-model:value="formState.ethic">
							<a-checkbox value="1">汉族</a-checkbox>
							<a-checkbox value="2">少数民族</a-checkbox>
						</a-checkbox-group>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="出生年月">
						<div class="date-box">
							<a-form-item name="birthday_start">
								<a-date-picker
									v-model:value="formState['birthday_start']"
									value-format="YYYY-MM"
									format="YYYY-MM"
									inputReadOnly
									picker="month"
									@openChange="onDataPickerChange"
								/>
							</a-form-item>
							<div class="line"></div>
							<a-form-item name="birthday_end">
								<a-date-picker
									v-model:value="formState['birthday_end']"
									value-format="YYYY-MM"
									inputReadOnly
									@openChange="onDataPickerChange"
									picker="month"
								/>
							</a-form-item>
						</div>
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="24">
				<a-col :span="12">
					<a-form-item label="入党时间">
						<div class="date-box">
							<a-form-item name="join_time_start">
								<a-date-picker v-model:value="formState['join_time_start']" inputReadOnly value-format="YYYY-MM-DD" />
							</a-form-item>
							<div class="line"></div>
							<a-form-item name="join_time_end">
								<a-date-picker v-model:value="formState['join_time_end']" inputReadOnly value-format="YYYY-MM-DD" />
							</a-form-item>
						</div>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="政治面貌" name="political">
						<a-checkbox-group v-model:value="formState.political">
							<a-checkbox value="1">中共党员</a-checkbox>
							<a-checkbox value="2">非党员</a-checkbox>
						</a-checkbox-group>
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="24">
				<a-col :span="12">
					<a-form-item label="熟悉专业和特长" name="profession_specialty">
						<a-input v-model:value="formState.profession_specialty" placeholder="请输入" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="专业技术职务" name="technical_position">
						<a-input v-model:value="formState.technical_position" placeholder="请输入" />
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="24">
				<div class="tip-link">
					<div class="link" @click="onExpand">
						<svg
							v-if="searchVisible"
							t="1695798910365"
							class="icon"
							viewBox="0 0 1024 1024"
							version="1.1"
							xmlns="http://www.w3.org/2000/svg"
							p-id="20054"
							width="16"
							height="16"
						>
							<path
								d="M526.78880522 465.76944925c-3.97682157-3.97682157-9.32067555-5.96523235-14.54025387-5.96523236-5.34385398 0-10.5634323 1.98841078-14.54025387 5.96523236L197.20971757 766.14375348c-7.95364315 7.95364315-7.95364315 20.87831325 0 28.83195639 7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0l286.2068774-286.20687739 286.33115307 286.33115307c7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0 7.95364315-7.95364315 7.95364315-20.87831325 0-28.8319564L526.78880522 465.76944925z m0 0"
								p-id="20055"
								fill="#1296db"
							></path>
							<path
								d="M197.0854419 558.35482643c7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0L512.12427568 272.14794903l286.33115307 286.33115307c7.95364315 7.95364315 20.87831325 7.95364315 28.83195637 0 7.95364315-7.95364315 7.95364315-20.87831325 0-28.83195638L526.78880522 229.02429013c-3.97682157-3.97682157-9.32067555-5.96523235-14.54025387-5.96523235-5.34385398 0-10.5634323 1.98841078-14.54025387 5.96523235L197.20971757 529.52287005c-7.95364315 7.82936747-7.95364315 20.87831325-0.12427567 28.83195638z m0 0"
								p-id="20056"
								fill="#1296db"
							></path>
						</svg>
						<svg
							v-else
							t="1695796990911"
							class="icon"
							viewBox="0 0 1024 1024"
							version="1.1"
							xmlns="http://www.w3.org/2000/svg"
							p-id="18534"
							width="16"
							height="16"
						>
							<path
								d="M497.3568 558.592c3.9936 3.9936 9.3184 6.0416 14.6432 5.9392 5.3248 0 10.6496-1.9456 14.6432-5.9392l302.1824-302.1824c7.9872-7.9872 7.9872-20.992 0-28.9792-7.9872-7.9872-20.992-7.9872-28.9792 0L512 515.2768 224.0512 227.328c-7.9872-7.9872-20.992-7.9872-28.9792 0-7.9872 7.9872-7.9872 20.992 0 28.9792l302.2848 302.2848z"
								p-id="18535"
								fill="#1296db"
							></path>
							<path
								d="M828.928 465.408c-7.9872-7.9872-20.992-7.9872-28.9792 0L512 753.3568 224.0512 465.408c-7.9872-7.9872-20.992-7.9872-28.9792 0-7.9872 7.9872-7.9872 20.992 0 28.9792L497.3568 796.672c3.9936 3.9936 9.3184 6.0416 14.6432 5.9392 5.3248 0 10.6496-1.9456 14.6432-5.9392l302.1824-302.1824c7.9872-7.9872 7.9872-21.0944 0.1024-29.0816z"
								p-id="18536"
								fill="#1296db"
							></path>
						</svg>
						<span class="text"> 高级查询 </span>
					</div>
				</div>
			</a-row>
		</a-card>
		<Transition name="moresearch">
			<div class="more-seach" v-if="searchVisible">
				<a-card :bordered="false">
					<div class="search-title" click-number="5">职务信息</div>
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="干部类别" name="cadre_category" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
								<a-checkbox-group v-model:value="formState.cadre_category" class="cadre_category">
									<a-checkbox value="1">市管领导</a-checkbox>
									<a-checkbox value="2">区管正职</a-checkbox>
									<a-checkbox value="3">区管副职</a-checkbox>
									<a-checkbox value="200108">乡镇正职</a-checkbox>
									<a-checkbox value="200101">乡镇副职</a-checkbox>
									<a-checkbox value="200102">部门正职</a-checkbox>
									<a-checkbox value="200103">部门副职</a-checkbox>
									<a-checkbox value="200104">企业正职</a-checkbox>
									<a-checkbox value="200105">企业副职</a-checkbox>
									<a-checkbox value="200106">街道正职</a-checkbox>
									<a-checkbox value="200107">街道副职</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="现任职务" name="current_job">
								<a-input v-model:value="formState.current_job" placeholder="请输入" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="任现职务时间">
								<a-row :gutter="24" class="item-h">
									<span>大于&nbsp;</span>
									<a-form-item name="current_job_time_gte">
										<a-input-number v-model:value="formState.current_job_time_gte" />
									</a-form-item>
									<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
									<a-form-item name="current_job_time_lte">
										<a-input-number v-model:value="formState.current_job_time_lte" />
									</a-form-item>
									<span>&nbsp;年</span>
								</a-row>
							</a-form-item>
						</a-col>
					</a-row>
					<!-- <a-row :gutter="24"> </a-row> -->
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="干部职级" name="current_rank">
								<a-checkbox-group v-model:value="formState.current_rank">
									<a-checkbox value="1">正处</a-checkbox>
									<a-checkbox value="200301">副处</a-checkbox>
									<a-checkbox value="200302">保留副处</a-checkbox>
									<a-checkbox value="20030">正科</a-checkbox>
									<a-checkbox value="5">保留正科</a-checkbox>
									<a-checkbox value="6">副科</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="现职级任职时间">
								<a-row :gutter="24" class="item-h">
									<span>大于&nbsp;</span>
									<a-form-item name="current_rank_time_gte">
										<a-input-number v-model:value="formState.current_rank_time_gte" />
									</a-form-item>
									<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
									<a-form-item name="current_rank_time_lte">
										<a-input-number v-model:value="formState.current_rank_time_lte" />
									</a-form-item>
									<span>&nbsp;年</span>
								</a-row>
							</a-form-item>
						</a-col>
					</a-row>
					<!-- <a-row :gutter="24">
								<a-col :span="24">
									<a-form-item label="现职级任职时间" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
										<a-row :gutter="24" class="item-h">
											<span>大于&nbsp;</span>
											<a-form-item name="current_rank_time_gte">
												<a-input-number v-model:value="formState.current_rank_time_gte" />
											</a-form-item>
											<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
											<a-form-item name="current_rank_time_lte">
												<a-input-number v-model:value="formState.current_rank_time_lte" />
											</a-form-item>
											<span>&nbsp;年</span>
										</a-row>
									</a-form-item>
								</a-col>
							</a-row> -->
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="干部身份" name="identity">
								<a-checkbox-group v-model:value="formState.identity">
									<a-checkbox value="200201">行政</a-checkbox>
									<a-checkbox value="200202">事业</a-checkbox>
									<a-checkbox value="200203">参公</a-checkbox>
									<a-checkbox value="200204">国企</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
						<!-- <a-col :span="12">
							<a-form-item label="简历时间" name="work_resume_start" v-if="clickNumber >= 5">
								<a-date-picker
									v-model:value="formState['work_resume_start']"
									value-format="YYYY-MM"
									format="YYYY-MM"
									picker="month"
									inputReadOnly
									@openChange="onDataPickerChange"
								/>
							</a-form-item>
						</a-col> -->
					</a-row>
				</a-card>
				<a-card :bordered="false">
					<div class="search-title">教育信息</div>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="初始学历" name="full_time_education">
								<a-checkbox-group v-model:value="formState.full_time_education">
									<a-checkbox value="1">研究生</a-checkbox>
									<a-checkbox value="2">大学本科</a-checkbox>
									<a-checkbox value="3">大学专科</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="最高学历" name="on_job_education">
								<a-checkbox-group v-model:value="formState.on_job_education">
									<a-checkbox value="1">研究生</a-checkbox>
									<a-checkbox value="2">大学本科</a-checkbox>
									<a-checkbox value="3">大学专科</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="全日制院校" name="full_time_school">
								<a-input v-model:value="formState.full_time_school" placeholder="请输入" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="专业" name="major">
								<a-input v-model:value="formState.major" placeholder="请输入" />
							</a-form-item>
						</a-col>
					</a-row>
				</a-card>
				<a-card :bordered="false">
					<div class="search-title">其他信息</div>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="年度考核">
								<div class="year-aprove form-common-style">
									<a-form-item name="year_examine_start">
										<a-input-number v-model:value="formState.year_examine_start" />
									</a-form-item>
									<span class="line"></span>
									<a-form-item name="year_examine_end">
										<a-input-number v-model:value="formState.year_examine_end" />
									</a-form-item>
									<span class="text-style">年有</span>
									<a-form-item name="examine_count">
										<a-input-number v-model:value="formState.examine_count" />
									</a-form-item>
									<span class="text-style">次</span>
									<a-form-item name="examine_count">
										<a-select placeholder="考核等次" v-model:value="formState.examine_level">
											<a-select-option value="1">优秀</a-select-option>
											<a-select-option value="2">称职</a-select-option>
											<a-select-option value="4">基本称职</a-select-option>
											<a-select-option value="6">不称职</a-select-option>
										</a-select>
									</a-form-item>
								</div>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="基层年限">
								<div class="base-year-aprove form-common-style">
									<a-form-item name="base_year_start">
										<a-input-number v-model:value="formState.base_year_start" />
									</a-form-item>
									<span class="line"></span>
									<a-form-item name="base_year_end">
										<a-input-number v-model:value="formState.base_year_end" />
									</a-form-item>
									<span class="text-style">年</span>
								</div>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="干部指数排名">
								<div class="cadre-index form-common-style">
									<a-form-item name="department_type">
										<a-select placeholder="请选择" v-model:value="formState.department_type" allow-clear>
											<!-- <a-select-option value="1">同序列</a-select-option> -->
											<a-select-option value="2">乡镇（部门）</a-select-option>
											<!-- <a-select-option value="3">本单位</a-select-option> -->
										</a-select>
									</a-form-item>
									<span class="text-style">排名</span>
									<a-form-item name="rank_start"> <a-input-number v-model:value="formState.rank_start" /> </a-form-item>
									<span class="text-style-percent">%</span>
									<span class="line"></span>
									<a-form-item name="rank_end">
										<a-input-number v-model:value="formState.rank_end" />
									</a-form-item>
									<span class="text-style-percent">%</span>
								</div>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="干部来源" name="source">
								<a-radio-group v-model:value="formState.source">
									<a-radio :value="item.value" :key="item.label" v-for="item in options" @click="onRadioClick(item)">{{ item.label }}</a-radio>
								</a-radio-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="简历信息" name="information">
								<div class="no-wrap-box">
									<a-select
										v-model:value="formState.information"
										placeholder=""
										label-in-value
										style="width: 100%"
										:filter-option="false"
										:default-active-first-option="false"
										:not-found-content="fetchState.fetching ? undefined : null"
										:options="fetchState.data1"
										show-search
										:show-arrow="false"
										allow-clear
										@search="fetchInfo($event, 1)"
									>
										<template v-if="fetchState.fetching" #notFoundContent>
											<a-spin size="small" />
										</template>
									</a-select>
									<span class="text-style">(包含)</span>
								</div>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="分管领域" name="responsibilities">
								<div class="no-wrap-box">
									<a-select
										v-model:value="formState.responsibilities"
										placeholder=""
										label-in-value
										style="width: 100%"
										:filter-option="false"
										:default-active-first-option="false"
										:not-found-content="fetchState.fetching ? undefined : null"
										:options="fetchState.data2"
										:show-arrow="false"
										show-search
										allow-clear
										@search="fetchInfo($event, 2)"
									>
										<template v-if="fetchState.fetching" #notFoundContent>
											<a-spin size="small" />
										</template>
									</a-select>
									<span class="text-style">(包含)</span>
								</div>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="熟悉领域和特长领域" name="speciality">
								<div class="no-wrap-box">
									<a-select
										v-model:value="formState.speciality"
										placeholder=""
										label-in-value
										style="width: 100%"
										:filter-option="false"
										:default-active-first-option="false"
										:not-found-content="fetchState.fetching ? undefined : null"
										:options="fetchState.data3"
										show-search
										:show-arrow="false"
										allow-clear
										@search="fetchInfo($event, 3)"
									>
										<template v-if="fetchState.fetching" #notFoundContent>
											<a-spin size="small" />
										</template>
									</a-select>
									<span class="text-style">(包含)</span>
								</div>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="特征标签" name="label">
								<div class="no-wrap-box">
									<a-select
										v-model:value="formState.label"
										placeholder=""
										label-in-value
										style="width: 100%"
										:filter-option="false"
										:default-active-first-option="false"
										:not-found-content="fetchState.fetching ? undefined : null"
										:options="fetchState.data4"
										show-search
										:show-arrow="false"
										allow-clear
										@search="fetchInfo($event, 4)"
									>
										<template v-if="fetchState.fetching" #notFoundContent>
											<a-spin size="small" />
										</template>
									</a-select>
									<span class="text-style">(包含)</span>
								</div>
							</a-form-item>
						</a-col>
					</a-row>
				</a-card>
			</div>
		</Transition>
		<!-- <a-card :bordered="false">
			<a-row>
				<a-col :span="24" style="text-align: center" class="btn-wrap">
					<a-button style="margin-right: 60px" value="large" @click="resetForm">重置</a-button>
					<a-button type="primary" value="large" html-type="submit">查询</a-button>
				</a-col>
			</a-row>
		</a-card> -->
	</a-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue'
import { debounce } from '@/utils/utils'
import { fetchInfoByType } from '@/apis/search'

interface FormState {
	work_resume_start: string
	cadre_type: string
	sort_type: string
	name: string
	birthday: [string, string]
	birthday_start: string
	join_time_start: string
	join_time_end: string
	birthday_end: string
	ethic: [string, string]
	gender: [string, string]
	political: [string, string]
	join_time: [string, string]
	cadre_category: [string, string]
	identity: [string, string]
	full_time_education: [string, string]
	on_job_education: [string, string]
	full_time_school: [string, string]
	current_rank: [string, string]
	major: [string, string]
	profession_specialty: string
	technical_position: string
	current_job: string
	current_job_time_gte: string
	current_job_time_lte: string
	current_rank_time_gte: string
	current_rank_time_lte: string
	year_examine_start: string
	year_examine_end: string
	base_year_start: string
	base_year_end: string
	source: number | undefined
	information: string
	responsibilities: string
	speciality: string
	label: string
	examine_count: string
	examine_level: string
}

const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 18 },
}

const options = [
	{ label: '选调生', value: '1' },
	{ label: '村官', value: '2' },
	{ label: '五方面人才', value: '3' },
]

// 联想查询
const fetchState: any = reactive({
	data1: [],
	data2: [],
	data3: [],
	data4: [],
	type: undefined,
	fetching: false,
})

const searchVisible = ref<boolean>(false)

const formRef = ref()

const formState = reactive({ cadre_type: '2' } as FormState)

const onRadioClick = (info: any) => {
	if (formState.source === info.value) {
		formState.source = undefined
	}
}

const fetchInfo = debounce(async (name: any, type: number) => {
	fetchState.type = type

	fetchState.fetching = true
	const res = await fetchInfoByType({
		name,
		type,
	})

	if (res.code === 0) {
		fetchState[`data${type}`] = res.data?.map((item: string) => ({ label: item, value: item }))
	}
	fetchState.fetching = false
}, 300)

const onDataPickerChange = (status: any) => {
	if (status) {
		nextTick(() => {
			const el: any = document.querySelector('.ant-picker-year-btn')

			el?.click()
		})
	}
}

const onExpand = () => {
	searchVisible.value = !searchVisible.value
}
// 重置表单
const resetForm = () => {
	formRef?.value?.resetFields()

	/* setTimeout(() => {
		onFinish({})
	}, 100) */
}
// const resetForm = () => {
// 	formRef?.value?.resetFields()

// 	/* setTimeout(() => {
// 		onFinish({})
// 	}, 100) */
// }

defineExpose({
	formState,
	resetForm,
})
</script>

<style lang="less" scoped>
.search-form {
	user-select: none;
	::v-deep(.ant-card-body) {
		padding: 24px 24px 5px !important;
	}
	width: 100%;
	.ant-card {
		margin-bottom: 16px;
		.ant-row {
			margin-bottom: 10px;
			.ant-col .ant-row {
				margin-bottom: 0px;
			}
		}
		.item-h {
			align-items: center;
			margin: 0 !important;
			.ant-form-item {
				margin-bottom: 0;
			}
		}
	}
	.date-box {
		display: flex;
		align-items: center;
	}
	.ant-checkbox-group {
		.ant-checkbox-wrapper {
			margin-left: 0;
			margin-right: 10px;
		}
	}
	.cadre_category {
		::v-deep(.ant-checkbox-wrapper) {
			// margin-bottom: 10px;
		}
	}
}
.search-title {
	font-size: 20px;
	font-weight: bold;
	display: flex;
	align-items: center;
	&::before {
		margin-right: 8px;
		content: '';
		display: inline-block;
		width: 8px;
		height: 24px;
		background: url('@/assets/images/left-header-icon.png') center / cover no-repeat;
	}
}
.text-style {
	margin: 0px 12px;
	white-space: nowrap;
}
.base-year-aprove {
	display: flex;
	align-items: center;
	.ant-input-number {
		width: 100px;
	}
}

.tip-link {
	width: 100%;
	display: flex;
	justify-content: center;
	.link {
		display: flex;
		align-items: center;
		cursor: pointer;
		.text {
			margin-left: 10px;
			color: #1296db;
		}
	}
}

.base-year-aprove {
	display: flex;
	align-items: center;
	.ant-input-number {
		width: 100px;
	}
}
.cadre-index {
	.ant-select {
		width: 190px;
	}
	.text-style-percent {
		margin-left: 5px;
		font-size: 18px;
	}
	.line {
		margin: 0 10px;
	}
	.ant-input-number {
		width: 70px;
	}
}

.line {
	display: inline-block;
	width: 15px;
	height: 1px;
	background: #d9d9d9;
	margin: 0 3px;
}
.form-common-style {
	display: flex;
	align-items: center;
	.ant-input-number {
		width: 120px;
	}
}
.text-style {
	margin: 0px 12px;
	white-space: nowrap;
}
.base-year-aprove {
	display: flex;
	align-items: center;
	.ant-input-number {
		width: 100px;
	}
}

.no-wrap-box {
	display: flex;
	align-items: flex-end;
}

.moresearch-enter-active {
	transition: opacity 0.5s ease;
}

.moresearch-enter-from {
	opacity: 0;
}
</style>
