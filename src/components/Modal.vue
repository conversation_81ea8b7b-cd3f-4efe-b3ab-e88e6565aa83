<template>
	<div :class="['modal', visible && 'show-modal']" @click="onModal">
		<div class="modal-content" @click.stop="($e) => $e">
			<slot></slot>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
	name: 'Modal',
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
	},
	setup(_props, { emit }) {
		const onModal = () => {
			emit('close')
		}
		return {
			onModal,
		}
	},
})
</script>

<style scoped lang="less">
.modal {
	position: fixed;
	inset: 0;
	background-color: rgba(0, 0, 0, 0.5);
	transform: scale(0);
	transition: all 0.2s ease-in-out;
	z-index: 9;

	.modal-content {
		position: absolute;
		top: 40%;
		left: 50%;
		transform: translate(-50%, -40%);
		background-color: #f6f8fc;
		overflow: auto;
	}

	.modal-content::-webkit-scrollbar {
		display: none;
	}
}

.show-modal {
	transform: scale(1);
}
</style>
