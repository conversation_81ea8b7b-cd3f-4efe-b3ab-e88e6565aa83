<template>
	<div :class="`fixed-menu ${size}`">
		<div :class="`menu ${activeKey && item.key === activeKey ? 'active' : ''}`" v-for="item in menu" :key="item.label" @click="item.click">
			<i class="icon" :style="`background-image: url(${item.icon})`" v-if="item.icon"></i>
			<span class="text">{{ item.label }}</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'

interface Menu {
	label: string
	icon?: any
	key?: string
	click: () => void
}

defineProps({
	menu: {
		type: Array<Menu>,
		default: () => [],
	},
	size: {
		type: String as PropType<'small' | 'large'>,
		default: 'large',
	},
	activeKey: {
		type: String,
	},
})
</script>

<style lang="scss" scoped>
.small {
	.menu {
		padding: 28px 0px;
		// margin-bottom: 40px;
		border-bottom: 1px solid rgba(0, 0, 0, 0.08);
		// &:not(:first-child) {
		// 	margin-top: 40px;
		// }
	}
}
.large {
	.menu {
		padding: 30px 0px;
	}
}
.fixed-menu {
	margin-bottom: 16px;
	width: 84px;
	padding: 16px 16px;
	background-color: #ffffff;
	border-radius: 8px 8px 8px 8px;
	.menu {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		.icon {
			display: inline-block;
			width: 32px;
			height: 32px;
			background: no-repeat center / 100% 100%;
		}
		.text {
			font-size: 26px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #222222;
			line-height: 30px;
		}
	}
	.active {
		border-bottom: 4px solid #e5251b;
		.text {
			font-weight: bold;
			color: #e5251b;
		}
	}
}
</style>
