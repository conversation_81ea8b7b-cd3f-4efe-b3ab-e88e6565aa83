<template>
	<a-modal title="修改密码" :visible="visible" @cancel="handleCancel" @ok="handleSubmit" v-bind="$attrs" class="reset-password-modal">
		<a-form ref="formRef" layout="vertical" :model="form" :rules="rules">
			<a-form-item name="password_old" label="旧密码:" colon :rules="rules.password_old">
				<a-input v-model:value="form.password_old" />
			</a-form-item>
			<a-form-item name="newPassword" label="新密码:" colon :rules="rules.newPassword">
				<a-input-password v-model:value="form.newPassword" />
			</a-form-item>
			<a-form-item name="confirmPassword" label="确认新密码:" colon :rules="rules.confirmPassword">
				<a-input-password v-model:value="form.confirmPassword" />
			</a-form-item>
		</a-form>
	</a-modal>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import router from '@/router/index'
import { message } from 'ant-design-vue'
import { updateUserPassword } from '@/apis/login'
import { getUserInfoItem } from '@/utils/utils'
import { emptyHeader, createHeaders } from '@/utils/axios'
import axios from 'axios'
import md5 from 'js-md5'
const props = defineProps({
	visible: {
		type: Boolean,
		required: true,
	},
	onClose: {
		type: Function,
		required: true,
	},
	headers: {
		type: Object,
		default: () => ({}),
	},
	user_id: Number,
})
const formRef = ref(null)

const form = reactive({
	password_old: '',
	newPassword: '',
	confirmPassword: '',
})

const validateByRegex = (password) => {
	// 定义正则表达式
	const passwordRegex =
		/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,30}$/

	return passwordRegex.test(password)
}

const validatePassword = async (_rule, value: string, text: string) => {

	if (value === '') {
		return Promise.reject(text || '请输入')
	} else {
		const res = validateByRegex(value)

		if (res) {
			return Promise.resolve()
		} else {
			return Promise.reject('密码应包含大小写字母、数字和特殊字符中的至少三种，且长度为8-30个字符。')
		}
	}
}

const rules = {
	password_old: [{ required: true, message: '请输入旧密码!' }],
	newPassword: [
		{
			required: true,
			trigger: 'change',
			validator: (_rule, value) => {
				return validatePassword(_rule, value, '请输入新密码!')
			},
		},
	],
	confirmPassword: [
		{
			required: true,
			trigger: 'change',
			validator: (_rule, value) => {
				if (!value.trim()) {
					return Promise.reject('再次确认新密码!')
				} else {
					return value !== form.newPassword ? Promise.reject('两次密码不一致!') : Promise.resolve()
				}
			},
		},
	],
}

const handleCancel = () => {
	props.onClose()

	formRef.value.resetFields()
}

const handleSubmit = () => {
	formRef.value
		.validate()
		.then(async () => {
			const { newPassword, password_old, confirmPassword } = form

			if (newPassword !== confirmPassword) {
				message.error('两次密码不一致')
				return
			}

			// 提交重置密码
			console.log('提交重置密码')
			const _uid = props.user_id || getUserInfoItem('_uid')

			const res = await updateUserPassword(
				{ sys_user_id: _uid, password_old: md5(password_old), password: md5(newPassword) },
				{
					headers: props.headers,
				}
			)

			if (res.code === 0) {
				message.success('密码重置成功', 1, () => {
					sessionStorage.clear()

					router.replace('/login')

					handleCancel?.()
				})
			} else {
				message.error(res.message)
			}
		})
		.catch((error) => {
			console.log('Validate Failed:', error)
		})
}
</script>

<style lang="less">
.reset-password-modal {
	width: 557px !important;
	.ant-modal-content {
		padding: 0;
		.ant-modal-title {
			font-weight: bold;
			font-size: 24px;
			color: rgba(0, 0, 0, 0.9);
			line-height: 24px;
		}
		.ant-form-item-control-input-content {
			height: 42px;
			input {
				height: 100%;
				font-size: 21px;
			}
			.ant-input-password {
				height: 100%;
			}
		}
		button {
			padding: 10px 29px !important;
			border-radius: 3px 3px 3px 3px;
			font-size: 18px;
			line-height: 18px;
			height: unset !important;
		}

		.ant-form-item-required {
			font-weight: 400;
			font-size: 21px;
			line-height: 21px;
		}
	}
}
</style>
