<template>
	<Modal :visible="visible" @close="onClose">
		<div class="rule-box">
			<div class="modal-close" @click="onClose"></div>
			<div class="content-box">
				<div class="title" v-if="title">{{ title }}</div>
				<div class="content">
					<div class="img">
						<img :src="url" alt="" />
					</div>
				</div>
			</div>
		</div>
	</Modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import Modal from './Modal.vue'
export default defineComponent({
	emits: ['close'],
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
		url: {
			type: String,
			default: '',
		},
		title: {
			type: String,
			default: '',
		},
	},
	methods: {
		onClose() {
			this.$emit('close')
		},
	},
})
</script>

<style scoped lang="less">
.rule-box {
	position: relative;
	width: 1362px;
	background: #ffffff;
	box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.04);
	border-radius: 8px 8px 8px 8px;
	opacity: 1;
	user-select: none;

	.content-box {
		.title {
			height: 81px;
			text-align: center;
			line-height: 81px;
			font-size: 24px;
			font-family: PingFang SC-Heavy, PingFang SC;
			font-weight: 800;
			color: #000000;
			border-bottom: 1px solid #e5231a;
		}
		.content {
			height: 745px;
			padding: 24px 52px 53px;

			.img {
				height: 100%;
				width: 100%;
				overflow-y: auto;
				img {
					width: 100%;
					object-fit: contain;
				}
				&::-webkit-scrollbar {
					display: none;
				}
			}
		}
	}
}
.modal-close {
	position: absolute;
	top: 24px;
	right: 30px;
	background: url('@/assets/images/modalClose.png') no-repeat center / 100% 100%;
	width: 23px;
	height: 23px;
	cursor: pointer;
}
::v-deep(.modal-content) {
	// .modal-content {
	background-color: transparent !important;
	// }
}
</style>
