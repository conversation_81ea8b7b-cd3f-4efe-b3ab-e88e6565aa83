<template>
	<div class="p-auto-complete" @click.stop="">
		<div ref="inputRef">
			<a-input suffix-icon="search" allow-clear placeholder="点击输入" :value="value" @input="handleChange" v-bind="$attrs" @focus="onFocus" />
		</div>
		<div class="p-search-list" v-if="dropVisible && options?.length" ref="dropRef">
			<div class="p-search-item" v-for="(option, index) in options" @click="onSelect(option)" :key="index">
				<slot :data="option">
					<span class="p-search-item-label">{{ option.label }}</span>
				</slot>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref, watch, toRefs, onMounted, onUnmounted } from 'vue'
const props = defineProps({
	value: String, // v-model 绑定的值
	placeholder: String,
	focu: {
		default: true,
		type: Boolean,
	},
	options: {
		type: Array<{ label: string; value: string }>,
		default: () => [],
	},
})

const emits = defineEmits(['search', 'update:value', 'change', 'input'])

const dropVisible = ref(false)

const valueIn = ref('')

const closeStatus = ref(false)

const close = () => {
	closeStatus.value = false
}

defineExpose({
	close,
})

const handleChange = (value: any) => {
	// 处理输入框值的变化
	emits('search', value.target.value)

	emits('update:value', value.target.value)

	emits('input', value.target.value)
}

const onFocus = (event: any) => {
	// 处理输入框聚焦事件
	props.focu && emits('search', event.target.value)

	dropVisible.value = true
}

const onBlur = () => {
	dropVisible.value = false
}

const onSelect = (value: any) => {
	console.log('🚀 ~ onSelect ~ value:', value)
	// 处理搜索按钮点击事件
	emits('update:value', value.value, value)

	emits('change', value.value, value)

	dropVisible.value = false
}
const inputRef = ref()
const dropRef = ref()

watch(
	() => dropRef.value,
	(value) => {
		const drop = dropRef.value
		if (drop) {
			drop.style.top = inputRef.value.clientHeight + 'px'
		}
	}
)
const onClickCloseDrop = () => {
	dropVisible.value = false
}
onMounted(() => {
	window.addEventListener('click', onClickCloseDrop)
})

onUnmounted(() => {
	// document.body.style.height = '100%'
	// document.documentElement.style.height = '100%'
	window.removeEventListener('click', onClickCloseDrop)
})
</script>

<style lang="less" scoped>
.p-auto-complete {
	position: relative;
	.p-search-list {
		position: absolute;
		top: 0px;
		left: 0px;
		max-height: 500px;
		overflow: hidden;
		background: #fff;
		z-index: 9999;
		overflow: auto;
	}
}
</style>
