import { createApp } from 'vue'
import FolderModal from './FolderModal.vue'

const mountComponent = (component: any, props = {}) => {
	let dismissEle: any = document.createElement('div')

	let app: any = createApp(component, {
		onClose: () => {
			app.unmount()
			dismissEle.remove()

			app = null
			dismissEle = null
		},
		...props,
	})

	app.mount(dismissEle)

	return app
}
/**
 * @description: 收藏夹modal
 * @return {*}
 */
export const folderModal = (props: any) => {
	return mountComponent(FolderModal, props)
}
