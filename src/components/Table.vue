<template>
	<div class="table">
		<div v-if="showHead">
			<table cellpadding="0">
				<thead>
					<tr>
						<td
							v-for="item in columns"
							:key="item.key"
							class="header-td"
							:style="{
								width: item.width,
								textAlign: item.align,
							}"
						>
							<div class="table-header-td" :style="{ justifyContent: 'center', width: '100%' }">
								<span>{{ item.title }}</span>
								<span
									:class="[
										'sort-icon',
										sortCurrent.currentKey === item.key && (sortCurrent.currentStatus ? 'sort-active-top' : 'sort-active-bottom'),
									]"
									v-if="item.sort"
									@click="onSort(item.key)"
								></span>
							</div>
						</td>
					</tr>
				</thead>
			</table>
		</div>
		<div :class="`table-body ${bodyScroll ? 'body-scroll' : ''}`" ref="bodyRef" v-if="expand ? is_expand : true">
			<table>
				<colgroup>
					<col
						v-for="row in columns"
						:key="row.key"
						:style="{
							width: row.width,
							textAlign: row.align || 'center',
						}"
					/>
				</colgroup>
				<tbody>
					<tr
						v-for="(item, index) in innerData"
						:key="index"
						:data-index="index"
						:data-id="rowId ? tableId + '_' + item[rowId] : ''"
						:class="`${rowId && item[rowId] === activeRow ? 'active' : ''}`"
						@click="rowClick && rowClick(item, index)"
					>
						<td
							v-for="col in columns"
							:key="col.key"
							:class="col.colClass ? col.colClass : ''"
							@click="col.colClick && col.colClick(item, $event)"
							:style="{
								textAlign: col.align || 'center',
								height: col.height,
								...tdStyle,
							}"
							:rowspan="col.rowSpan"
						>
							<slot :name="col.key" :data="item" :value="item[col.key]">
								<template v-if="col.customization">
									<component :is="col.customization" :value="item[col.key]" />
								</template>
								<span
									v-else
									:href="col.src || 'javascript:void (0)'"
									:style="{
										color: (col.color && col.color(item[col.key])) || (rowColor && rowColor?.(item, index)) || '',
									}"
									:class="`table-body-td ${
										showMaxMin
											? columnsMax[col.key] === item.name
												? 'table-column-max'
												: columnsMin[col.key] === item.name
												? 'table-column-min'
												: ''
											: ''
									}`"
								>
									{{ item[col.key] }}
								</span>
							</slot>
						</td>
					</tr>
				</tbody>
			</table>
			<p className="table-body-nodata" v-if="!dataSource?.length">暂无数据</p>
		</div>
		<div className="expand-click" v-if="expand" @click="onExpand">
			<span class="expand-text">点击{{ !is_expand ? '展开' : '收起' }}</span>
			<span class="expand-icon" :class="{ rotate: is_expand }"></span>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType, reactive, ref, toRefs, watchEffect, createVNode, unref } from 'vue'
// dataSource 中的key根据columns中的key推断

interface DataSourceType {
	[key: string]: any
}
interface ColumnsType {
	title: string
	key: string
	width?: any
	align?: any
	ellipsis?: boolean
	colClass?: string
	height?: string
	src?: string
	sort?: boolean
	showMax?: boolean
	showMin?: boolean
	rowSpan?: number
	// 传入定义的组件名，组件在下方定义
	customization?: string
	color?: (value: string) => string
	colClick?: (data: any, $event: any) => void
}
const colorList = ['#00CBFF', '#15FED1', '#FDA22C', 'yellow', 'rgb(78,136,94)', 'rgb(176,76,199)', '#ffffff']
// 自定义列组件列表，需要再自定义列可以在下面添加
// 块组件
export const CustomBlock = {
	name: 'CustomBlock',
	props: {
		value: {
			default: 0,
		},
	},
	render() {
		let { value } = this as { [key in string]: unknown }
		let _value = Number(value)
		let color = '#FF9900'
		if (_value >= 80) {
			color = '#60CA71'
		}
		// else if (_value < 90 && _value >= 80) {
		// 	color = '#FDD100'
		// }
		else if (_value < 80 && _value >= 70) {
			color = '#FFA300'
		}
		return !value
			? ''
			: createVNode(
					'span',
					{
						class: `custom-block`,
						style: {
							backgroundColor: color,
						},
					},
					null
			  )
	},
}

const Portrait = {
	name: 'Portrait',
	props: {
		value: {
			type: String,
			default: '',
		},
	},
	render() {
		// let { value } = this as { [key in string]: unknown }
		return createVNode(
			'span',
			{
				class: `custom-portrait`,
			},
			null
		)
	},
}
export default defineComponent({
	components: {
		CustomBlock,
		Portrait,
	},
	props: {
		columns: {
			default: () => [],
			type: Array as PropType<ColumnsType[]>,
		},
		// 每一行的数据取哪个字段作为唯一标识
		rowId: {
			type: String,
			default: null,
			required: false,
		},
		// 每行颜色是否不同
		rowColor: {
			type: Function as PropType<(item: DataSourceType, index: number) => string>,
			default: () => String,
		},
		colColor: {
			type: Function as PropType<(value: any) => string>,
			default: () => String,
		},
		showHead: {
			type: Boolean,
			default: true,
		},
		dataSource: {
			type: Array<DataSourceType>,
			default: () => [],
		},
		tabStyle: {
			type: Object,
			default: () => ({}),
		},
		tdStyle: {
			type: Object,
			default: () => ({}),
		},
		headerStyle: {},
		bodyScroll: {
			type: Boolean,
			default: false,
		},
		rowClick: {
			type: Function as PropType<(data: any, index: number) => void>,
			default: () => void 0,
			required: false,
		},
		// 展示每一列的最大值
		showMax: {
			type: Boolean,
			default: false,
		},
		// 展示每一列的最小值
		showMaxMin: {
			type: Boolean,
			default: false,
		},
		// 某一行不参与最大值最小值计算， 多个用逗号隔开
		filterRowNameInMaxMin: {
			type: String,
			default: undefined,
		},
		// 是否开启合并
		rowspan: {
			type: Boolean,
			default: false,
			required: false,
		},
		expand: {
			type: Boolean,
			default: false,
			required: false,
		},
	},
	emits: ['table', 'sort'],
	setup(props, { emit }) {
		const { dataSource, showMaxMin, columns, filterRowNameInMaxMin } = toRefs(props)
		// 列表容器
		const bodyRef = ref()
		const columnsMax = ref<{ [key in string]: any }>({})
		const columnsMin = ref<{ [key in string]: any }>({})
		// 存放data数据
		const innerData = ref<Array<DataSourceType>>([])
		// 排序相关
		const sortCurrent = reactive({
			currentKey: '',
			currentStatus: false,
		})
		const is_expand = ref(false)

		// 当前高亮的行
		const activeRow = ref<any>()
		// 随机生成table唯一标识
		const tableId = Math.random().toString().substr(2)
		watchEffect(() => {
			const _data = [...dataSource.value]
			if (showMaxMin.value) {
				let __data = [..._data]
				if (filterRowNameInMaxMin.value) {
					__data = _data.filter((item) => {
						const filterContainer = filterRowNameInMaxMin.value?.split(',')

						return !filterContainer?.includes(item.name)
					})
				}
				columns.value.forEach((col) => {
					if (col.showMax || col.showMin) {
						const key = col.key
						const max = Math.max(...__data.map((item) => Number(item[key])))
						const min = Math.min(...__data.map((item) => Number(item[key])))
						__data.forEach((item) => {
							if (col.showMax && Number(item[key]) === max) {
								columnsMax.value[key] = item.name
							} else if (col.showMin && Number(item[key]) === min) {
								columnsMin.value[key] = item.name
							}
						})
					}
				})
			}
			innerData.value = _data
		})
		const onTable = () => {
			emit('table')
		}
		const onSort = (key: string) => {
			emit('sort', key)

			if (key !== sortCurrent.currentKey) {
				sortCurrent.currentStatus = true
			} else {
				sortCurrent.currentStatus = !sortCurrent.currentStatus
			}
			sortCurrent.currentKey = key

			innerData.value = sortDataByKey(key, sortCurrent.currentStatus)
		}
		const sortDataByKey = (key: string, sortType: boolean) => {
			let tempList: any = []
			let tempData = [...innerData.value]
			if (unref(filterRowNameInMaxMin)) {
				const filterList = filterRowNameInMaxMin.value?.split(',')
				tempData = tempData.filter((item) => {
					if (filterList?.includes(item.name)) {
						tempList.push(item)
					}
					return !filterList?.includes(item.name)
				})
			}
			tempData = tempData.sort((a: any, b: any) => {
				return sortType ? a[key] - b[key] : b[key] - a[key]
			})

			return tempData.concat(tempList)
		}
		/**
		 * @description: 滚动到指定元素
		 * @param {*} id
		 * @return {*}
		 */
		const scrollToElementById = (id: number | string) => {
			// 获取到元素下标
			const dataId = `tr[data-id="${tableId}_${id}"]`
			const element: any = document.querySelector(dataId)

			if (element && bodyRef.value) {
				bodyRef.value.scrollTo(0, element.offsetTop)
			} else {
				bodyRef.value?.scrollTo(0, 0)
			}
		}
		const rowHighLight = (id: number | string) => {
			activeRow.value = id
		}
		const onExpand = () => {
			console.log(is_expand.value)
			is_expand.value = !is_expand.value
		}
		return {
			is_expand,
			bodyRef,
			tableId,
			colorList,
			activeRow,
			innerData,
			columnsMin,
			columnsMax,
			sortCurrent,
			onSort,
			onTable,
			scrollToElementById,
			rowHighLight,
			onExpand,
		}
	},
})
</script>

<style scoped lang="less">
.table {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	// font-size: 14px;
	border-spacing: 0px;
	box-sizing: border-box;
	overflow: hidden;
	font-family: PingFang SC-Regular;
	.expand-click {
		margin-top: 10px;
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;
		.expand-text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 22px;
			line-height: 1;
			color: #d33625;
			cursor: pointer;
		}
		.expand-icon {
			margin-top: 4px;
			width: 20px;
			height: 20px;
			background: url('@/assets/images/data-screen/expand.png') no-repeat center center / 100% 100%;
			cursor: pointer;
		}
		.rotate {
			transform: rotateZ(-180deg);
		}
	}
	table {
		width: 100%;
		border-collapse: collapse;
		// border-collapse: ;
		// border-spacing: 0;
		// border-collapse: collapse;
		th,
		td {
			border: 1px solid #e9e9e9;
			text-align: center;
		}
	}
	.table-header-td {
		display: flex;
		align-items: center;
		height: 100%;
		span {
			position: relative;
		}
	}
	td,
	col {
		padding: 14px 10px;
	}

	colgroup {
		display: table-row;
		vertical-align: inherit;
		border-color: inherit;

		col {
			padding: 0 10px;
		}
	}

	col {
		display: table-cell;
		vertical-align: inherit;
	}

	thead {
		// background: rgba(0, 203, 255, 0.1);

		td {
			background: #f3f3f3;
			text-align: center;
			font-size: 20px;
			line-height: 23px;
			font-weight: 400;
			color: #3d3d3d;
		}
	}

	tbody {
		font-weight: 400;
		& > tr {
			margin: 2px;
			border-top: 15px;
			border-color: transparent;
			background-color: #ffffff;
			font-size: 18px;
			color: #3d3d3d;
		}
		.ellipsis {
			display: -webkit-box;
			text-overflow: -o-ellipsis-lastline;
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 1;
		}
	}
	.body-scroll {
		overflow: scroll;
	}
	&-body {
		// height: calc(100% - 31%);
		// height: 100%;
		flex: 1;
		// margin-top: -4px;
		table {
			margin-top: -4px;
			// border-color: #b6ff00;
		}
		a {
			text-decoration: none;
			outline: none;
			cursor: text;
			&:hover,
			&:visited,
			&:link,
			&:active {
				color: inherit;
			}
		}
		.a-pointer {
			cursor: pointer;
		}
		&-nodata {
			padding-top: 20px;
			text-align: center;
			vertical-align: middle;
			color: #999;
		}
		.table-column-max {
			color: #209b34 !important;
			font-weight: bold;
		}
		.table-column-min {
			color: #ee391f !important;
			font-weight: bold;
		}
	}

	&::-webkit-scrollbar {
		/* 滚动条整体样式 */
		width: 5px;
		/*高宽分别对应横竖滚动条的尺寸*/
		height: 10px;
		cursor: pointer;
	}

	&::-webkit-scrollbar-thumb {
		/* 滚动条里面小方块 */
		border-radius: 10px;
		-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: #cbf9ff;
	}

	&::-webkit-scrollbar-track {
		/* 滚动条里面轨道 */
		-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
		border-radius: 10px;
	}

	& ::-webkit-scrollbar {
		/* 组件内滚动条不显示 */
		display: none;
	}

	// &:hover ::-webkit-scrollbar {
	//   /* 悬停时滚动条才显示 */
	//   display: block;
	// }

	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
}

.sort-icon {
	position: relative;
	display: inline-block;
	width: 16px;
	height: 13px;
	cursor: pointer;
}

.sort-icon::before {
	content: '';
	position: absolute;
	height: 0px;
	width: 0px;
	border-width: 0 7px 7px 7px;
	border-style: solid;
	border-color: transparent transparent #999999 transparent;
}

.sort-icon::after {
	margin-top: 2px;
	position: absolute;
	top: 50%;
	content: '';
	height: 0px;
	width: 0px;
	border-width: 7px 7px 0 7px;
	border-style: solid;
	border-color: #999999 transparent transparent transparent;
}
.sort-active-top {
	&::before {
		border-color: transparent transparent #2462ff transparent;
	}
}
.sort-active-bottom {
	&::after {
		border-color: #2462ff transparent transparent transparent;
	}
}
// .sort-icon[data-sort='desc']::before {
// 	border-width: 0 4px 4px 4px;
// 	border-color: transparent transparent #000 transparent;
// }
.active {
	background-color: rgba(235, 91, 84, 0.07) !important;
}
</style>
<style>
.custom-block {
	display: inline-block;
	width: 32px;
	height: 12px;
}
</style>
