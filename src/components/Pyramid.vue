<template>
	<div class="pyramid">
		<div :class="`pyramid-item pyramid-${item.key}`" v-for="item in pyramidList" :key="item.key">
			<div class="pyramid-img">
				<div class="pyramid-label">{{ item.label }}</div>
				<img :src="item.img" alt="" />
				<div class="select" v-if="activeInfo.pyramid_index === item.key - 1">
					<div class="select-line"></div>
					<div class="text">{{ activeInfo.username }} {{ activeInfo.inspection_index }}</div>
				</div>
				<div class="splitLine">
					<div class="line-box">
						<div class="left-line"></div>
						<div class="right-line"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, PropType } from 'vue'

import ImgPyramid1 from '@/assets/images/pyramid-1.png'
import ImgPyramid2 from '@/assets/images/pyramid-2.png'
import ImgPyramid3 from '@/assets/images/pyramid-3.png'
import ImgPyramid4 from '@/assets/images/pyramid-4.png'
defineProps({
	activeInfo: Object as PropType<any>,
})
const pyramidList = computed(() => {
	const list = [
		{
			img: ImgPyramid1,
			label: '10%',
			key: 1,
		},
		{
			img: ImgPyramid2,
			label: '20%',
			key: 2,
		},
		{
			img: ImgPyramid3,
			label: '30%',
			key: 3,
		},
		{
			img: ImgPyramid4,
			label: '40%',
			key: 4,
		},
	]
	return list
})
</script>

<style lang="less" scoped>
.pyramid {
	width: 377px;
	display: flex;
	flex-direction: column;
	align-items: center;
	user-select: none;
	.pyramid-item {
		position: relative;
		transform: translate(-60px);

		.pyramid-img {
			position: relative;
			margin: 0 auto;
			.pyramid-label {
				position: absolute;
				top: 40%;
				left: -20px;
				font-size: 22px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #666666;
				line-height: 26px;
			}
			img {
				width: 100%;
				height: 100%;
			}
			.select {
				position: absolute;
				top: 0;
				left: 100%;
				display: flex;
				align-items: center;
				width: 441px;
				height: 99px;
				-webkit-clip-path: polygon(0 0, 100% 0%, 100% 100%, 7% 100%);
				clip-path: polygon(0 0, 100% 0%, 100% 100%, 12% 100%);
				.select-line {
					height: 1px;
					background: #278236;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
				.text {
					margin-left: 10px;
					font-size: 22px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #278236;
					line-height: 26px;
				}
			}
			.splitLine {
				position: absolute;
				inset: 0;
				// background-color: rgba(0, 0, 0, 0.9);
				.line-box {
					.left-line {
					}
					.right-line {
					}
				}
			}
		}
		&:nth-child(2) {
			margin-top: -10px;
		}
		&:nth-child(3) {
			margin-top: -20px;
		}
		&:nth-child(4) {
			margin-top: -20px;
		}
	}
	.pyramid-1 {
		.pyramid-img {
			width: 73px;
			height: 84px;

			.pyramid-label {
				transform: translateX(-15px);
			}
		}
		.select {
			width: 341px;
			height: 60px !important;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-40px, 6px);
			-webkit-clip-path: polygon(0 0, 100% 0%, 100% 100%, 8% 100%) !important;
			clip-path: polygon(0 0, 100% 0%, 100% 100%, 8% 100%) !important;
			.select-line {
				width: 191px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-2 {
		.pyramid-img {
			width: 159px;
			height: 92px;
			.pyramid-label {
				left: -30px !important;
			}
		}
		.select {
			width: 271px;
			height: 70px !important;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-45px);
			-webkit-clip-path: polygon(0 0, 100% 0%, 100% 100%, 8% 100%) !important;
			clip-path: polygon(0 0, 100% 0%, 100% 100%, 9% 100%) !important;
			.select-line {
				width: 146px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-3 {
		.pyramid-img {
			width: 260px;
			height: 110px;
		}
		.select {
			width: 271px;
			height: 90px !important;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-49px, 7px);
			.select-line {
				width: 126px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-4 {
		.pyramid-img {
			width: 377px;
			height: 137px;
		}
		.select {
			width: 251px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-55px, 13px);
			.select-line {
				width: 66px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
}
</style>
