<template>
	<div class="folder">
		<div class="header">
			<div class="title">{{ type === 'insert' ? '新建收藏夹' : type === 'editor' ? '编辑收藏夹' : '添加收藏到' }}</div>
			<div class="close" @click="onClose"></div>
		</div>
		<div class="list" v-if="type === 'collect'" @scroll="onScroll" ref="scrollContainer">
			<div class="item" v-for="(item, index) in list" :key="index">
				<div class="left">{{ item.name }}</div>
				<div class="right">
					<button @click="onCollection(item)" :class="item.hasFavorite ? 'a-collect' : 'no-collect'">
						{{ item.hasFavorite ? '取消收藏' : '收藏' }}
					</button>
				</div>
			</div>
		</div>
		<div class="insert" v-else>
			<a-form
				:model="formState"
				name="basic"
				:label-col="{ span: 24 }"
				:wrapper-col="{ span: 24 }"
				autocomplete="off"
				@finish="onFinish"
				@finishFailed="onFinishFailed"
			>
				<a-form-item
					class="collection-name"
					label="收藏夹名称（20字以内）"
					name="name"
					:rules="[{ required: true, message: '请输入收藏夹名称' }]"
					required
				>
					<a-input v-model:value="formState.name" :maxLength="20" />
				</a-form-item>

				<a-form-item class="collection-description" label="收藏夹描述（100字以内）" name="description">
					<a-input v-model:value="formState.description" :maxLength="100" />
				</a-form-item>
			</a-form>
		</div>
		<div :class="`footer ${type !== 'collect' ? 're-margin' : ''}`">
			<div class="inner-box" @click="onInsert" v-if="type === 'collect'">
				<span class="icon"></span>
				<span class="text">创建收藏夹</span>
			</div>
			<div class="inner-box" v-else>
				<div class="button" @click="onCancel">取消</div>
				<div class="button confirm" @click="onConfirm">确认</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, PropType, watchEffect } from 'vue'
import {
	addCollection,
	getCollectionList1,
	getCollectionList,
	addFavorite,
	deleteFavorite,
	updateCollection,
	addBatchCollection,
} from '@/apis/user-center'
import { getUserInfoItem, debounce } from '@/utils/utils'

interface FormState {
	name: string
	description: string
	favoritesId?: number
}

interface FolderData extends FormState {
	favoritesId: number
	hasFavorite: boolean // 收藏状态
}

const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	modalType: {
		type: String as PropType<'collect' | 'insert' | 'editor'>,
		required: true,
	},
	sourceId: {
		type: String,
		required: false,
	},
	sourceIds: {
		type: String,
		required: false,
	},
	editor: {
		type: Object as PropType<{ editor: boolean; editorInfo: any }>,
	},
})
const user_id = getUserInfoItem('_uid')

const page = ref(1)

const loadEnd = ref(false)

const type = ref<'collect' | 'insert' | 'editor'>('collect')

const list = ref<Array<FolderData>>([])

const scrollContainer = ref()

const formState = reactive<FormState>({
	name: '',
	description: '',
	favoritesId: undefined,
})

const emits = defineEmits(['close', 'success'])

const onClose = () => {
	emits('close')

	resetFields()
}

const resetFields = () => {
	formState.description = ''
	formState.name = ''
	formState.favoritesId = undefined

	setTimeout(() => {
		type.value = 'collect'
	}, 200)
}

const onFinish = (values: any) => {
	console.log('Success:', values)
}

const onFinishFailed = (errorInfo: any) => {
	console.log('Failed:', errorInfo)
}

const onInsert = () => {
	type.value = 'insert'
}
const submiting = ref(false)
const onConfirm = async () => {
	if (submiting.value) return

	submiting.value = true

	const { modalType } = props
	try {
		if (!formState.name) {
			return (submiting.value = false)
		}

		// 编辑状态
		if (modalType === 'editor') {
			const { editorInfo } = props.editor as any

			await updateCollection(editorInfo.favoritesId, formState.name, formState.description)
		} else {
			await addCollection(user_id, formState.name, formState.description)
		}

		// 新增后重新打开
		modalType === 'insert' && (loadEnd.value = false)
		;['insert', 'editor'].includes(modalType) ? emits('success') : loadDataByPage(page.value)

		resetFields()
	} catch (err) {
		console.log(err)
	}
	submiting.value = false
}

const onCancel = () => {
	if (props.modalType !== 'collect') {
		onClose()
	} else {
		type.value = 'collect'
	}
}

const onScroll = debounce(() => {
	const container = scrollContainer.value
	// 如果滚动到底部
	if (!loadEnd.value && container.scrollTop + container.clientHeight + 50 >= container.scrollHeight) {
		// 在这里可以执行滚动到底部后的操作
		page.value = page.value + 1

		initCollectionList(1)
	}
}, 200)

/**
 * @description: 添加收藏
 * @param {*} record
 * @return {*}
 */
const onCollection = async (record: FolderData) => {
	if (record.hasFavorite) {
		await deleteFavorite(record.favoritesId, props.sourceId)
	} else {
		props.sourceIds ? await addBatchCollection(record.favoritesId, props.sourceIds) : await addFavorite(record.favoritesId, props.sourceId)
	}
	list.value.map((item: any) => {
		if (item.favoritesId === record.favoritesId) {
			item.hasFavorite = !item.hasFavorite
		}
	})
}

const initCollectionList = async (type = 0) => {
	const res = props.sourceId ? await getCollectionList1(user_id, props.sourceId, page.value) : await getCollectionList(user_id, page.value, '')
	if (!res.data.length) {
		return (loadEnd.value = true)
	}
	if (type === 1) {
		list.value.push(...res.data)
	} else {
		if (res.data.length) {
			list.value = res.data
		}
	}
}
// 加载所有数据
const loadDataByPage = async (page: number) => {
	list.value = []
	for (let i = 1; i <= page; i++) {
		const res = props.sourceId ? await getCollectionList1(user_id, props.sourceId, i) : await getCollectionList(user_id, page, '')

		list.value.push(...res.data)
	}
}
initCollectionList()

watchEffect(() => {
	type.value = props.modalType
})
watchEffect(() => {
	if (props.modalType === 'editor' && props.editor) {
		const { editorInfo } = props.editor
		const { name, description, favoritesId } = editorInfo

		formState.description = description
		formState.favoritesId = favoritesId
		formState.name = name
	}
})
</script>

<style lang="scss" scoped>
.folder {
	display: flex;
	flex-direction: column;
	padding: 24px;
	width: 100%;
	height: 618px;
	background: #ffffff;
	border-radius: 8px 8px 8px 8px;
	opacity: 1;

	.header {
		display: flex;
		justify-content: space-between;
		.title {
			font-size: 36px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
		}
		.close {
			width: 40px;
			height: 40px;
			background: url('@/assets/images/modalClose.png') no-repeat center / cover;
			cursor: pointer;
		}
	}
	.list {
		flex: 1;
		overflow-y: auto;
		&::-webkit-scrollbar {
			display: none;
		}
		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24px 0px;
			.left {
				display: flex;
				align-items: center;
				font-size: 28px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #000000;
				line-height: 33px;
				color: #000000;
				&::before {
					margin-right: 12px;
					content: '';
					display: inline-block;
					width: 32px;
					height: 32px;
					background: url('@/assets/images/folder.png') no-repeat center / cover;
				}
			}
			.right {
				button {
					width: 128px;
					height: 56px;
					border-radius: 4px 4px 4px 4px;
					opacity: 1;
					font-size: 26px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					outline: none;
					border: none;
					cursor: pointer;
				}
				.a-collect {
					color: #e5251b;
					background: #fce9e8;
				}
				.no-collect {
					color: #ffffff;
					background: #e5251b;
				}
			}
		}
	}
	.insert {
		flex: 1;
		input {
			height: 72px;
		}
		.collection-name {
			margin-top: 57px;
		}
		.collection-description {
			margin-top: 20px;
		}
	}
	.footer {
		.inner-box {
			display: flex;
			align-items: center;
			justify-content: center;
			.icon {
				display: inline-block;
				width: 44px;
				height: 44px;
				background: url('@/assets/images/add.png') no-repeat center / cover;
			}
			.text {
				font-size: 26px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #000000;
			}

			.button {
				flex: 1;
				height: 88px;
				line-height: 88px;
				text-align: center;
				font-size: 26px;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
				background: #ffffff;
				opacity: 1;
				cursor: pointer;
			}
			.confirm {
				color: #e5251b;
			}
		}
	}
	.re-margin {
		border-top: 1px solid #f0f0f0;
		margin: 0px -24px -24px;
	}
}
</style>
