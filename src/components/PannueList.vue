<template>
	<div :class="`pannue-list ${pannue ? 'type-pannue' : 'type-list'}`">
		<div class="pannue" @click="onActive(true)">平铺</div>
		<div class="line"></div>
		<div class="list" @click="onActive(false)">列表</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, toRefs } from 'vue'

const emits = defineEmits(['active', 'update:value'])

const pannue = ref(true)

const onActive = (type: boolean) => {
	pannue.value = type
	emits('active', type ? '2' : '1')
}
</script>

<style lang="less" scoped>
.pannue-list {
	display: flex;
	align-items: center;
	& > div {
		display: flex;
		align-items: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 21px;
		color: #000000;
		line-height: 25px;
		cursor: pointer;
		&::before {
			margin-right: 8px;
			content: '';
			display: inline-block;
			width: 27px;
			height: 27px;
			font-family: Source <PERSON>, Source <PERSON>N;
			font-weight: 400;
			font-size: 21px;
			color: #000000;
		}
	}
	.line {
		width: 1px;
		height: 27px;
		background: rgba(0, 0, 0, 0.4);
		margin: 0px 25px;
	}
}
.type-pannue {
	.pannue {
		color: #008eff;
		&::before {
			background: url('@/assets/images/pinpu.png') center / cover;
		}
	}
	.list {
		&::before {
			background: url('@/assets/images/list01.png') center / cover;
		}
	}
}

.type-list {
	.pannue {
		&::before {
			background: url('@/assets/images/pinpu01.png') center / cover;
		}
	}
	.list {
		color: #008eff;
		&::before {
			background: url('@/assets/images/list.png') center / cover;
		}
	}
}
</style>
