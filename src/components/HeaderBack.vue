<template>
	<div class="header-back">
		<span class="back-icon" @click="onBack"></span>
		<span class="title">{{ title }}</span>
	</div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

defineProps({
	title: String,
})

const router = useRouter()

const onBack = () => {
	router.back()
}
</script>

<style lang="less" scoped>
.header-back {
	display: flex;
	align-items: center;
	padding: 32px 33px;
	background-color: #fff;
	border-bottom: 2px solid #f5f5f5;
	.back-icon {
		display: inline-block;
		width: 23px;
		height: 23px;
		background: #252525;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		background: url('@/assets/images/arrow-left.png') center / 100% no-repeat;
		cursor: pointer;
	}
	.title {
		margin-left: 19px;
		font-size: 24px;
		font-family: <PERSON> <PERSON>, Source <PERSON> CN;
		font-weight: 500;
		color: #222222;
		line-height: 28px;
	}
}
</style>
