<template>
	<div class="time-line">
		<div class="time-list">
			<div class="time-item" v-for="time in times" :key="time">
				<span
					class="time"
					:class="{
						'active-time': time === activeKey,
					}"
					@click="onActive(time)"
				>
				</span>
				<span
					class="text"
					:class="{
						'active-text': time === activeKey,
					}"
				>
					{{ time }}
				</span>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, toRef } from 'vue'

const emits = defineEmits(['update:value'])

const props = defineProps({
	value: {
		type: String || Number,
	},
	times: {
		type: Array<any>,
		default: () => [],
	},
})

const times = toRef(props, 'times')

const value = toRef(props, 'value')

const activeKey = ref<any>()

const onActive = (key: any) => {
	activeKey.value = key

	emits('update:value', key)
}

watch(
	value,
	() => {
		activeKey.value = value.value
	},
	{
		immediate: true,
	}
)
</script>

<style lang="less" scoped>
.time-line {
	width: 100%;
	overflow-x: auto;
	height: 60px;
	.time-list {
		display: flex;
		align-items: center;

		position: relative;
		&::before {
			position: absolute;
			min-width: 100%;
			content: '';
			display: inline-block;
			border-bottom: 1px dashed #008eff;
		}
	}
	.time-item {
		position: relative;
		font-size: 0px;
		&:not(:first-child) {
			margin-left: 247px;
		}
		&:first-child {
			margin-left: 61px;
		}
		.time {
			display: inline-block;
			width: 26px;
			height: 26px;
			background: #ffffff;
			border: 1px solid rgba(0, 0, 0, 0.31);
			border-radius: 50%;
			overflow: hidden;
		}
		.text {
			position: absolute;
			bottom: 0px;
			left: 50%;
			font-size: 20px;
			color: rgba(0, 0, 0, 0.85);
			transform: translate(-50%, 90%);
			white-space: nowrap;
		}
		.active-time {
			border: 6px solid #008eff;
		}
		.active-text {
			color: #008eff;
		}
	}
}
</style>
