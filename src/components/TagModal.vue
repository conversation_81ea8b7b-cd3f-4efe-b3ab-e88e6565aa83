<template>
	<a-modal class="tag-ant-modal" :visible="tagVisible" @cancel="onTagModalClose" title="特征标签" :footer="null">
		<div class="modal-content">
			<div class="tag-content">
				<div class="inner">
					<!-- <template v-for="item in userInfo.feature_list">
							<TagBox color="#008EFF" :data="item.positive_feature_map" />
							<TagBox color="#FF6A16" :data="item.negative_feature_map" />
						</template> -->
					<TagBox color="#008EFF" :data="positive_feature || []" />
					<TagBox color="#FF6A16" :data="negative_feature || []" />
					<TagBox color="#FF6A16" :data="integrity_tag || []" />
				</div>
				<div class="legend">
					<div class="tag-1">干部特点</div>
					<div class="tag-2">主要不足</div>
				</div>
			</div>
			<div class="other-content">
				<div class="title">其他标签</div>
				<div class="inner" style="border-bottom: none">
					<TagBox color="#60CA71" :data="other_feature || []" />
				</div>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

defineProps({
	tagVisible: Boolean,
	onTagModalClose: Function,
	other_feature: Array,
	positive_feature: Array,
	negative_feature: Array,
	integrity_tag: Array,
})
</script>

<style lang="less">
.tag-ant-modal {
	width: 1300px !important;
	.ant-modal-title {
		font-size: 24px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		text-align: center;
	}
	.tag-content,
	.other-content {
		background: #f7fbff;
		.inner {
			padding: 24px 24px 50px;
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
		}
		.legend {
			padding: 12px 0px;
			display: flex;
			justify-content: center;
			gap: 30px;
			.tag-1,
			.tag-2 {
				display: flex;
				align-items: center;
				font-size: 16px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				&::before {
					content: '';
					display: inline-block;
					width: 8px;
					height: 8px;
					margin-right: 4px;
				}
			}
			.tag-1 {
				&::before {
					background: #008eff;
				}
			}
			.tag-2 {
				&::before {
					background: #ff6a16;
				}
			}
		}
	}
	.other-content {
		padding: 24px;
		margin-top: 20px;
		height: 220px;
		background: #f6fff7;
		border-radius: 8px 8px 8px 8px;
		.title {
			display: flex;
			align-items: center;
			font-size: 22px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			line-height: 22px;
			&::before {
				content: '';
				margin-right: 7px;
				display: inline-block;
				width: 8px;
				height: 24px;
				background: url('@/assets/images/card-icon.png') no-repeat center / cover;
			}
		}
	}
}
</style>
