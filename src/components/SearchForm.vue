<script lang="ts" setup>
import { reactive, ref, onMounted, unref, onDeactivated, nextTick, onActivated } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import Folder from './Folder.vue'
import { MeScrollInit } from '@/utils/utils'
import { getPmsLeader, fetchInfoByType } from '@/apis/search'
import { useRouter } from 'vue-router'
import { Base64, getUserInfo, appendParamsToUrl, debounce } from '@/utils/utils'
import { getDimensionConfig } from '@/apis/cadre-portrait/home'

import SearchInput from './SearchInput.vue'

const CDN_URL = import.meta.env.VITE_CDN_URL

const router = useRouter()

interface FormState {
	work_resume_start: string
	cadre_type: string
	sort_type: string
	name: string
	birthday: [string, string]
	birthday_start: string
	join_time_start: string
	join_time_end: string
	birthday_end: string
	ethic: [string, string]
	gender: [string, string]
	political: [string, string]
	join_time: [string, string]
	cadre_category: [string, string]
	identity: [string, string]
	full_time_education: [string, string]
	on_job_education: [string, string]
	full_time_school: [string, string]
	current_rank: [string, string]
	major: [string, string]
	profession_specialty: string
	technical_position: string
	current_job: string
	current_job_time_gte: string
	current_job_time_lte: string
	current_rank_time_gte: string
	current_rank_time_lte: string
	year_examine_start: string
	year_examine_end: string
	base_year_start: string
	base_year_end: string
	source: number
	information: string
	responsibilities: string
	speciality: string
	label: string
	examine_count: string
	examine_level: string
}
const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 18 },
}

const options = [
	{ label: '选调生', value: '1' },
	{ label: '村官', value: '2' },
	{ label: '五方面人才', value: '3' },
]

const formRef = ref<FormInstance>()
const selectedRowKeys = ref<any[]>([])
const formState = reactive({ cadre_type: '2' } as FormState)
const pagination = reactive({
	showSizeChanger: false,
	showQuickJumper: true,
	showTotal: (total: number) => `共${total}条数据`,
} as any)
const visible = ref<boolean>(false)
const collectVisible = ref(false)
const searchVisible = ref<boolean>(false)
const drawerVisible = ref<boolean>(false)
const basicMessage = ref<any>([])
const cadreMessage = ref<any>([])
const dataSource = ref<any>([])
const quotaList = ref<any>([])
const tableLoading = ref<boolean>(false)
const tableBoxRef = ref<any>(null)
// 上拉加载
const currentPage = ref(1)
// 滚动实例
const mescroll1 = ref()

// 联想查询
const fetchState = reactive({
	data1: [],
	data2: [],
	data3: [],
	data4: [],
	type: undefined,
	fetching: false,
})

const columns = [
	{
		title: '序号',
		dataIndex: 'index',
		key: 'index',
		width: '5%',
		align: 'center',
	},
	{
		title: '姓名',
		dataIndex: 'username',
		key: 'username',
		width: '13%',
		align: 'center',
	},
	{
		title: '头像',
		dataIndex: 'avatar',
		key: 'avatar',
		width: '13%',
		align: 'center',
	},
	{
		title: '现任职务',
		dataIndex: 'current_job',
	},
	{
		title: '出生年月(年龄)',
		dataIndex: 'birthday',
		key: 'birthday',
		width: '15%',
		sorter: true,
		defaultSortOrder: 'ascend',
	},
	{
		title: '全日制学历',
		dataIndex: 'diploma',
		width: '10%',
		align: 'center',
	},
	{
		title: '全日制学历毕业院校及专业',
		dataIndex: 'school',
	},
	{
		title: '干部指数',
		dataIndex: 'cadre_index',
		width: '10%',
		align: 'center',
		sorter: true,
	},
	{
		title: '排序',
		dataIndex: 'cadre_index_rank',
		width: '10%',
		align: 'center',
	},
	// {
	// 	title: '操作',
	// 	key: 'action',
	// 	align: 'center',
	// },
]

onMounted(() => {
	// onFinish({})
	getDimensionConfig({}).then(({ data, code }: any) => {
		if (code == 0) {
			data.map((item: any) => {
				if (item.name == '基础信息') {
					basicMessage.value = item.children
				} else {
					cadreMessage.value = item.children
				}
			})
		}
	})
})
onDeactivated(() => {
	visible.value = false
})
const resetForm = () => {
	resetFiledsSelf()

	formRef?.value?.resetFields()

	/* setTimeout(() => {
		onFinish({})
	}, 100) */
}

const showModal = () => {
	if (unref(selectedRowKeys).length > 10) {
		return message.error('最多可选择10人')
	}
	visible.value = true
}
const onRadioClick = (info: any) => {
	if (formState.source === info.value) {
		formState.source = undefined
	}
}

const onCloseDrawer = () => {
	drawerVisible.value = false
	selectedRowKeys.value = []
}

const handleOk = () => {
	const config_ids: any[] = []
	basicMessage.value.forEach((element: any) => {
		if (element.is_select) {
			config_ids.push(element.config_id)
		}
	})
	cadreMessage.value.forEach((element: any) => {
		if (element.is_select) {
			config_ids.push(element.config_id)
		}
	})
	goPage({ path: '/comparison-results', user_id: selectedRowKeys.value.join(','), config_ids: config_ids.join(',') })
}

const goPage = ({ path, ...parmas }: any) => {
	const userInfo = JSON.stringify(getUserInfo())

	const _h = Base64.encode(userInfo)

	router.push({
		path,
		query: {
			...parmas,
			_h,
		},
	})
}
const updateDebounce = debounce((formState: any, key: any) => {
	formState[key] = name
}, 100)
const fetchInfo = debounce(async (name: any, type: number) => {
	const keyMap: any = {
		1: 'information',
		2: 'responsibilities',
		3: 'speciality',
		4: 'label',
	}
	fetchState.type = type

	fetchState.fetching = true
	const res = await fetchInfoByType({
		name,
		type,
		cadre_type: formState.cadre_type,
	})

	if (res.code === 0) {
		const _data = res.data?.map((item: string) => ({ label: item, value: item }))

		// _data.unshift({ label: name, value: name })

		fetchState[`data${type}`] = _data
	}
	const key: string = keyMap[type]
	// updateDebounce(formState, key)
	// console.log('🚀 ~ file: SearchForm.vue:268 ~ fetchInfo ~ formState[key]:', formState[key])

	fetchState.fetching = false
}, 300)

const onFocus = (type: number) => {
	if (type !== fetchState.type) {
		fetchState.data = []
	}
}

const handlePaginationChange = (params: any, filter: any, sorter: any) => {
	const sortMap: any = {
		ascend: 'ASC',
		descend: 'DESC',
	}

	switch (sorter.field) {
		case 'cadre_index':
			formState.sort_type = undefined
			formState.cadre_sort_type = sortMap[sorter.order]
			break
		case 'birthday':
			formState.sort_type = sortMap[sorter.order]
			formState.cadre_sort_type = undefined
			break
	}

	onSearch()
}

const onSelectChange = (rowKeys: any[]) => {
	selectedRowKeys.value = rowKeys
}
const onSearch = async () => {
	// 手动点击查询，排序
	currentPage.value = 1

	dataSource.value = []

	await onFinish({})
}

const getParams = () => {
	const params: any = { ...formState }
	if (formState.birthday) {
		params.birthday_start = formState.birthday[0]
		params.birthday_end = formState.birthday[1]
		delete params.birthday
	}
	if (formState.join_time) {
		params.join_time_start = formState.join_time[0]
		params.join_time_end = formState.join_time[1]
		delete params.join_time
	}
	if (formState.join_time) {
		params.join_time_start = formState.join_time[0]
		params.join_time_end = formState.join_time[1]
		delete params.join_time
	}
	if (formState.information) {
		params.information = formState.information
	}
	if (formState.responsibilities) {
		params.responsibilities = formState.responsibilities
	}
	if (formState.speciality) {
		params.speciality = formState.speciality
	}
	if (formState.label) {
		params.label = formState.label
	}

	if (!formState.sort_type && !formState.cadre_sort_type) {
		params.sort_type = 'ASC'
	}
	const _params: any = {}
	Object.entries(params).forEach(([key, value]) => {
		if (Array.isArray(value)) {
			if (value[0] !== undefined) {
				_params[key] = value
			}
			return false
		}
		_params[key] = value
	})

	return _params
}
/**
 * @description: 选中项
 * @param {*} record
 * @return {*}
 */
let _selectedRowKeys: any = []

const onSelect = (record: any) => {
	const index = _selectedRowKeys.findIndex((item: any) => {
		return item === record.user_id
	})
	if (index == -1) {
		_selectedRowKeys.push(record.user_id)
		quotaList.value.push(record)
	} else {
		if (index == -1) {
			return void 0
		}
		_selectedRowKeys.splice(index, 1)
		quotaList.value.splice(index, 1)
	}
	selectedRowKeys.value = [..._selectedRowKeys]
}
// 全选
const onSelectAll = (selected, selectedRows, changeRows) => {
	// 全选
	if (selected) {
		// 过滤掉undefine
		const filterUser = selectedRows.filter((item) => item)
		// // 存在反选逻辑，正常的全选逻辑两个长度应该相等
		// if (filterUser.length !== changeRows.length) {
		// 	// changeRows.forEach((item) => {
		// 	// 	if (res.findIndex((item1) => item1.user_id === item.user_id) === -1) {
		// 	// 		res.push(item)
		// 	// 	}
		// 	// })
		// 	// 过滤出两个数组中的不同项
		// 	// const diff = filterUser.filter((item) => changeRows.findIndex((_item) => item.user_id === _item.user_id) === -1).map((item) => item.user_id)
		// 	// 去除反选的数据
		// 	const rowData = quotaList.value.filter((item) => {
		// 		return !selectedRows.map((item) => item.user_id).includes(item.user_id)
		// 	})

		// 	// keys
		// 	_selectedRowKeys = rowData.map((item) => item.user_id)

		// 	quotaList.value = rowData

		// 	selectedRowKeys.value = _selectedRowKeys
		// } else {
		const filterData = filterUser.filter((item) => !_selectedRowKeys.includes(item.user_id))

		quotaList.value = quotaList.value.concat(filterData)
		// user_id
		let keys = quotaList.value.map((item) => item.user_id)

		_selectedRowKeys = keys

		selectedRowKeys.value = keys

		// }
	} else {
		// 取消全选
		const changeKeys = changeRows.map((item) => item.user_id)
		// 过滤掉取消的数据
		const rowData = quotaList.value.filter((item) => !changeKeys.includes(item.user_id))
		// 列表
		quotaList.value = rowData
		// 选中key
		_selectedRowKeys = rowData.map((item) => item.user_id)
		selectedRowKeys.value = _selectedRowKeys
	}
}
// 下拉框input发生变化
const onSelectInput = (value: any) => {
	console.log(value)
}

// 删除选中项中的数据
const deleteName = (item: any) => {
	// 获取下标
	const index = selectedRowKeys.value.findIndex((item1: any) => {
		return item1 === item.user_id
	})

	const index1 = quotaList.value.findIndex((item1: any) => item1.user_id === item.user_id)

	_selectedRowKeys.splice(index, 1)

	selectedRowKeys.value = [..._selectedRowKeys]

	quotaList.value.splice(index1, 1)
}

const onClear = () => {
	_selectedRowKeys = []
	selectedRowKeys.value = []
	quotaList.value = []
}
const onExpand = () => {
	searchVisible.value = !searchVisible.value
}

const onClose = () => {
	collectVisible.value = false
}
const onSuccess = () => {
	collectVisible.value = false
}
const onCollect = () => {
	collectVisible.value = true
}

/**
 * @description: 数据加载
 * @param {*} page
 * @param {*} scroll
 * @return {*}
 */
const onFinish = async ({ scroll = true }: any) => {
	drawerVisible.value = true
	const formState = getParams()

	const _params: any = { ...formState, page: currentPage.value }

	tableLoading.value = true
	const res = await getPmsLeader(_params)

	tableLoading.value = false
	if (res.code === 0) {
		const { content, totalElements }: any = res.data || {}
		dataSource.value = content || []
		pagination.total = totalElements

		mescroll1.value?.resetUpScroll()
	}
	if (tableBoxRef.value && scroll) {
		// 获取距离顶部得距离
		nextTick(() => {
			const top = tableBoxRef.value?.offsetTop
			// 滚动到指定位置
			document.querySelector('.search-wrap')?.scrollTo({
				top,
				behavior: 'smooth',
			})
		})
	}
}
const bindScrollLoad = () => {
	const scrollBody: any = document.querySelector('.search-res-table .ant-table-body')

	mescroll1.value = MeScrollInit(scrollBody, {
		auto: false,
		callback: async () => {
			const formState = getParams()

			const _params: any = { ...formState, page: ++currentPage.value }

			const res = await getPmsLeader(_params)

			if (res.code === 0) {
				const { content, totalPages }: any = res.data || {}

				dataSource.value.push(...content)

				mescroll1.value.endByPage(content, totalPages)
			}
		},
	})
}
onMounted(() => {
	bindScrollLoad()
})
const onDataPickerChange = (status: any) => {
	if (status) {
		nextTick(() => {
			const el: any = document.querySelector('.ant-picker-year-btn')

			el?.click()
		})
	}
}

const onBlur = (type: any) => {
	const keyMap = {
		1: 'information',
		2: 'responsibilities',
		3: 'speciality',
		4: 'label',
	}
	// 获取值
	const value = formState[keyMap[type]]

	// console.log()

	// fetchState[`data${type}`] =
}

// 点击次数，用于隐藏研究时间
const clickNumber = ref(0)

const docClick = (e: any) => {
	const attrsClickNumber = e.target.getAttribute('click-number')
	if (clickNumber.value >= 5) {
		return
	}

	if (attrsClickNumber) {
		clickNumber.value++
	} else {
		clickNumber.value = 0
	}
}

const updateInformation = (value: any) => {
	formState.information = value
}
const updateResponsibilities = (value: any) => {
	formState.responsibilities = value
}
const updateSpeciality = (value: any) => {
	formState.speciality = value
}
const updateLabel = (value: any) => {
	formState.label = value
}
// 重置
const resetFiledsSelf = () => {
	formState.information = undefined
	formState.responsibilities = undefined
	formState.speciality = undefined
	formState.label = undefined
}

onActivated(() => {
	// 连续点击触发研究时间字段
	document.addEventListener('click', docClick)
})

onDeactivated(() => {
	document.removeEventListener('click', docClick)
	if (clickNumber.value < 5) {
		clickNumber.value = 0
	}
})
</script>

<template>
	<div class="search-wrap">
		<div class="form-wrap">
			<a-form ref="formRef" v-bind="layout" name="advanced_search" class="search-form" :model="formState" @finish="onSearch">
				<a-card :bordered="false">
					<a-form-item>
						<a-radio-group v-model:value="formState.cadre_type">
							<a-radio value="2">区管干部</a-radio>
							<a-radio value="3">中层干部</a-radio>
						</a-radio-group>
					</a-form-item>
				</a-card>
				<a-card :bordered="false">
					<div class="search-title">基本信息</div>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="姓名" name="name">
								<a-input v-model:value="formState.name" placeholder="请输入" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="性别" name="gender">
								<a-checkbox-group v-model:value="formState.gender">
									<a-checkbox value="1">男性</a-checkbox>
									<a-checkbox value="2">女性</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="民族" name="ethic">
								<a-checkbox-group v-model:value="formState.ethic">
									<a-checkbox value="1">汉族</a-checkbox>
									<a-checkbox value="2">少数民族</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="出生年月">
								<div class="date-box">
									<a-form-item name="birthday_start">
										<a-date-picker
											v-model:value="formState['birthday_start']"
											value-format="YYYY-MM"
											format="YYYY-MM"
											inputReadOnly
											picker="month"
											@openChange="onDataPickerChange"
										/>
									</a-form-item>
									<div class="line"></div>
									<a-form-item name="birthday_end">
										<a-date-picker
											v-model:value="formState['birthday_end']"
											value-format="YYYY-MM"
											inputReadOnly
											@openChange="onDataPickerChange"
											picker="month"
										/>
									</a-form-item>
								</div>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="入党时间">
								<div class="date-box">
									<a-form-item name="join_time_start">
										<a-date-picker v-model:value="formState['join_time_start']" inputReadOnly value-format="YYYY-MM-DD" />
									</a-form-item>
									<div class="line"></div>
									<a-form-item name="join_time_end">
										<a-date-picker v-model:value="formState['join_time_end']" inputReadOnly value-format="YYYY-MM-DD" />
									</a-form-item>
								</div>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="政治面貌" name="political">
								<a-checkbox-group v-model:value="formState.political">
									<a-checkbox value="1">中共党员</a-checkbox>
									<a-checkbox value="2">非党员</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="熟悉专业和特长" name="profession_specialty">
								<a-input v-model:value="formState.profession_specialty" placeholder="请输入" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="专业技术职务" name="technical_position">
								<a-input v-model:value="formState.technical_position" placeholder="请输入" />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<div class="tip-link">
							<div class="link" @click="onExpand">
								<svg
									v-if="searchVisible"
									t="1695798910365"
									class="icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="20054"
									width="16"
									height="16"
								>
									<path
										d="M526.78880522 465.76944925c-3.97682157-3.97682157-9.32067555-5.96523235-14.54025387-5.96523236-5.34385398 0-10.5634323 1.98841078-14.54025387 5.96523236L197.20971757 766.14375348c-7.95364315 7.95364315-7.95364315 20.87831325 0 28.83195639 7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0l286.2068774-286.20687739 286.33115307 286.33115307c7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0 7.95364315-7.95364315 7.95364315-20.87831325 0-28.8319564L526.78880522 465.76944925z m0 0"
										p-id="20055"
										fill="#1296db"
									></path>
									<path
										d="M197.0854419 558.35482643c7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0L512.12427568 272.14794903l286.33115307 286.33115307c7.95364315 7.95364315 20.87831325 7.95364315 28.83195637 0 7.95364315-7.95364315 7.95364315-20.87831325 0-28.83195638L526.78880522 229.02429013c-3.97682157-3.97682157-9.32067555-5.96523235-14.54025387-5.96523235-5.34385398 0-10.5634323 1.98841078-14.54025387 5.96523235L197.20971757 529.52287005c-7.95364315 7.82936747-7.95364315 20.87831325-0.12427567 28.83195638z m0 0"
										p-id="20056"
										fill="#1296db"
									></path>
								</svg>
								<svg
									v-else
									t="1695796990911"
									class="icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="18534"
									width="16"
									height="16"
								>
									<path
										d="M497.3568 558.592c3.9936 3.9936 9.3184 6.0416 14.6432 5.9392 5.3248 0 10.6496-1.9456 14.6432-5.9392l302.1824-302.1824c7.9872-7.9872 7.9872-20.992 0-28.9792-7.9872-7.9872-20.992-7.9872-28.9792 0L512 515.2768 224.0512 227.328c-7.9872-7.9872-20.992-7.9872-28.9792 0-7.9872 7.9872-7.9872 20.992 0 28.9792l302.2848 302.2848z"
										p-id="18535"
										fill="#1296db"
									></path>
									<path
										d="M828.928 465.408c-7.9872-7.9872-20.992-7.9872-28.9792 0L512 753.3568 224.0512 465.408c-7.9872-7.9872-20.992-7.9872-28.9792 0-7.9872 7.9872-7.9872 20.992 0 28.9792L497.3568 796.672c3.9936 3.9936 9.3184 6.0416 14.6432 5.9392 5.3248 0 10.6496-1.9456 14.6432-5.9392l302.1824-302.1824c7.9872-7.9872 7.9872-21.0944 0.1024-29.0816z"
										p-id="18536"
										fill="#1296db"
									></path>
								</svg>
								<span class="text"> 高级查询 </span>
							</div>
						</div>
					</a-row>
				</a-card>
				<Transition name="moresearch">
					<div class="more-seach" v-show="searchVisible">
						<a-card :bordered="false">
							<div class="search-title" click-number="5">职务信息</div>
							<a-row :gutter="24">
								<a-col :span="24">
									<a-form-item label="干部类别" name="cadre_category" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
										<a-checkbox-group v-model:value="formState.cadre_category" class="cadre_category">
											<a-checkbox value="1">市管领导</a-checkbox>
											<a-checkbox value="2">区管正职</a-checkbox>
											<a-checkbox value="3">区管副职</a-checkbox>
											<a-checkbox value="200108">乡镇正职</a-checkbox>
											<a-checkbox value="200101">乡镇副职</a-checkbox>
											<a-checkbox value="200102">部门正职</a-checkbox>
											<a-checkbox value="200103">部门副职</a-checkbox>
											<a-checkbox value="200104">企业正职</a-checkbox>
											<a-checkbox value="200105">企业副职</a-checkbox>
											<a-checkbox value="200106">街道正职</a-checkbox>
											<a-checkbox value="200107">街道副职</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="现任职务" name="current_job">
										<a-input v-model:value="formState.current_job" placeholder="请输入" />
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="任现职务时间">
										<a-row :gutter="24" class="item-h">
											<span>大于&nbsp;</span>
											<a-form-item name="current_job_time_gte">
												<a-input-number v-model:value="formState.current_job_time_gte" />
											</a-form-item>
											<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
											<a-form-item name="current_job_time_lte">
												<a-input-number v-model:value="formState.current_job_time_lte" />
											</a-form-item>
											<span>&nbsp;年</span>
										</a-row>
									</a-form-item>
								</a-col>
							</a-row>
							<!-- <a-row :gutter="24"> </a-row> -->
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="干部职级" name="current_rank">
										<a-checkbox-group v-model:value="formState.current_rank">
											<a-checkbox value="1">正处</a-checkbox>
											<a-checkbox value="200301">副处</a-checkbox>
											<a-checkbox value="200302">保留副处</a-checkbox>
											<a-checkbox value="20030">正科</a-checkbox>
											<a-checkbox value="5">保留正科</a-checkbox>
											<a-checkbox value="6">副科</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="现职级任职时间">
										<a-row :gutter="24" class="item-h">
											<span>大于&nbsp;</span>
											<a-form-item name="current_rank_time_gte">
												<a-input-number v-model:value="formState.current_rank_time_gte" />
											</a-form-item>
											<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
											<a-form-item name="current_rank_time_lte">
												<a-input-number v-model:value="formState.current_rank_time_lte" />
											</a-form-item>
											<span>&nbsp;年</span>
										</a-row>
									</a-form-item>
								</a-col>
							</a-row>
							<!-- <a-row :gutter="24">
								<a-col :span="24">
									<a-form-item label="现职级任职时间" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
										<a-row :gutter="24" class="item-h">
											<span>大于&nbsp;</span>
											<a-form-item name="current_rank_time_gte">
												<a-input-number v-model:value="formState.current_rank_time_gte" />
											</a-form-item>
											<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
											<a-form-item name="current_rank_time_lte">
												<a-input-number v-model:value="formState.current_rank_time_lte" />
											</a-form-item>
											<span>&nbsp;年</span>
										</a-row>
									</a-form-item>
								</a-col>
							</a-row> -->
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="干部身份" name="identity">
										<a-checkbox-group v-model:value="formState.identity">
											<a-checkbox value="200201">行政</a-checkbox>
											<a-checkbox value="200202">事业</a-checkbox>
											<a-checkbox value="200203">参公</a-checkbox>
											<a-checkbox value="200204">国企</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="简历时间" name="work_resume_start" v-if="clickNumber >= 5">
										<a-date-picker
											v-model:value="formState['work_resume_start']"
											value-format="YYYY-MM"
											format="YYYY-MM"
											picker="month"
											inputReadOnly
											@openChange="onDataPickerChange"
										/>
									</a-form-item>
								</a-col>
							</a-row>
						</a-card>
						<a-card :bordered="false">
							<div class="search-title">教育信息</div>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="初始学历" name="full_time_education">
										<a-checkbox-group v-model:value="formState.full_time_education">
											<a-checkbox value="1">研究生</a-checkbox>
											<a-checkbox value="2">大学本科</a-checkbox>
											<a-checkbox value="3">大学专科</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="最高学历" name="on_job_education">
										<a-checkbox-group v-model:value="formState.on_job_education">
											<a-checkbox value="1">研究生</a-checkbox>
											<a-checkbox value="2">大学本科</a-checkbox>
											<a-checkbox value="3">大学专科</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="全日制院校" name="full_time_school">
										<a-input v-model:value="formState.full_time_school" placeholder="请输入" />
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="专业" name="major">
										<a-input v-model:value="formState.major" placeholder="请输入" />
									</a-form-item>
								</a-col>
							</a-row>
						</a-card>
						<a-card :bordered="false">
							<div class="search-title">其他信息</div>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="年度考核">
										<div class="year-aprove form-common-style">
											<a-form-item name="year_examine_start">
												<a-input-number v-model:value="formState.year_examine_start" />
											</a-form-item>
											<span class="line"></span>
											<a-form-item name="year_examine_end">
												<a-input-number v-model:value="formState.year_examine_end" />
											</a-form-item>
											<span class="text-style">年有</span>
											<a-form-item name="examine_count">
												<a-input-number v-model:value="formState.examine_count" />
											</a-form-item>
											<span class="text-style">次</span>
											<a-form-item name="examine_level">
												<a-select placeholder="考核等次" v-model:value="formState.examine_level">
													<a-select-option value="1">优秀</a-select-option>
													<a-select-option value="2">称职</a-select-option>
													<a-select-option value="4">基本称职</a-select-option>
													<a-select-option value="6">不称职</a-select-option>
												</a-select>
											</a-form-item>
										</div>
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="基层年限">
										<div class="base-year-aprove form-common-style">
											<a-form-item name="base_year_start">
												<a-input-number v-model:value="formState.base_year_start" />
											</a-form-item>
											<span class="line"></span>
											<a-form-item name="base_year_end">
												<a-input-number v-model:value="formState.base_year_end" />
											</a-form-item>
											<span class="text-style">年</span>
										</div>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="干部指数排名">
										<div class="cadre-index form-common-style">
											<a-form-item name="department_type">
												<a-select placeholder="请选择" v-model:value="formState.department_type" allow-clear>
													<!-- <a-select-option value="1">同序列</a-select-option> -->
													<a-select-option value="2">乡镇（部门）</a-select-option>
													<!-- <a-select-option value="3">本单位</a-select-option> -->
												</a-select>
											</a-form-item>
											<span class="text-style">排名</span>
											<a-form-item name="rank_start"> <a-input-number v-model:value="formState.rank_start" /> </a-form-item>
											<span class="text-style-percent">%</span>
											<span class="line"></span>
											<a-form-item name="rank_end">
												<a-input-number v-model:value="formState.rank_end" />
											</a-form-item>
											<span class="text-style-percent">%</span>
										</div>
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="干部来源" name="source">
										<a-radio-group v-model:value="formState.source">
											<a-radio :value="item.value" v-for="item in options" @click="onRadioClick(item)">{{ item.label }}</a-radio>
										</a-radio-group>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="简历信息" name="information">
										<div class="no-wrap-box">
											<SearchInput
												v-model:value="formState.information"
												placeholder=""
												@search="fetchInfo($event, 1)"
												@change="updateInformation"
												:options="fetchState.data1"
												allow-clear
											/>
											<!-- <a-select
												v-model:value="formState.information"
												:searchValue="formState.information"
												placeholder=""
												label-in-value
												style="width: 100%"
												:filter-option="false"
												:default-active-first-option="false"
												:not-found-content="fetchState.fetching ? undefined : null"
												:options="fetchState.data1"
												show-search
												:show-arrow="false"
												allow-clear
												@change="onSelectInput"
												@search="fetchInfo($event, 1)"
											>
												<template v-if="fetchState.fetching" #notFoundContent>
													<a-spin size="small" />
												</template>
											</a-select> -->
											<span class="text-style">(包含)</span>
										</div>
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="分管领域" name="responsibilities">
										<div class="no-wrap-box">
											<SearchInput
												v-model:value="formState.responsibilities"
												placeholder=""
												@search="fetchInfo($event, 2)"
												@change="updateResponsibilities"
												:options="fetchState.data2"
												allow-clear
											/>
											<!-- <a-select
												v-model:value="formState.responsibilities"
												placeholder=""
												label-in-value
												style="width: 100%"
												:filter-option="false"
												:default-active-first-option="false"
												:not-found-content="fetchState.fetching ? undefined : null"
												:options="fetchState.data2"
												:show-arrow="false"
												show-search
												allow-clear
												@search="fetchInfo($event, 2)"
											>
												<template v-if="fetchState.fetching" #notFoundContent>
													<a-spin size="small" />
												</template>
											</a-select> -->
											<span class="text-style">(包含)</span>
										</div>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="熟悉领域和特长领域" name="speciality">
										<div class="no-wrap-box">
											<SearchInput
												v-model:value="formState.speciality"
												placeholder=""
												@search="fetchInfo($event, 3)"
												@change="updateSpeciality"
												:options="fetchState.data3"
												allow-clear
											/>
											<!-- <a-select
												v-model:value="formState.speciality"
												placeholder=""
												label-in-value
												style="width: 100%"
												:filter-option="false"
												:default-active-first-option="false"
												:not-found-content="fetchState.fetching ? undefined : null"
												:options="fetchState.data3"
												show-search
												:show-arrow="false"
												allow-clear
												@search="fetchInfo($event, 3)"
											>
												<template v-if="fetchState.fetching" #notFoundContent>
													<a-spin size="small" />
												</template>
											</a-select> -->
											<span class="text-style">(包含)</span>
										</div>
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="特征标签" name="label">
										<div class="no-wrap-box">
											<SearchInput
												v-model:value="formState.label"
												placeholder=""
												@search="fetchInfo($event, 4)"
												@change="updateLabel"
												:options="fetchState.data4"
												allow-clear
											/>
											<!-- <a-select
												v-model:value="formState.label"
												placeholder=""
												label-in-value
												style="width: 100%"
												:filter-option="false"
												:default-active-first-option="false"
												:not-found-content="fetchState.fetching ? undefined : null"
												:options="fetchState.data4"
												show-search
												:show-arrow="false"
												allow-clear
												@search="fetchInfo($event, 4)"
											>
												<template v-if="fetchState.fetching" #notFoundContent>
													<a-spin size="small" />
												</template>
											</a-select> -->
											<span class="text-style">(包含)</span>
										</div>
									</a-form-item>
								</a-col>
							</a-row>
						</a-card>
					</div>
				</Transition>
				<a-card :bordered="false">
					<a-row>
						<a-col :span="24" style="text-align: center" class="btn-wrap">
							<a-button style="margin-right: 60px" value="large" @click="resetForm">重置</a-button>
							<a-button type="primary" value="large" html-type="submit">查询</a-button>
						</a-col>
					</a-row>
				</a-card>
			</a-form>
			<div ref="tableBoxRef" />

			<a-card :bordered="false">
				<a-row :gutter="24">
					<a-col :span="19">
						<a-table
							class="search-res-table"
							rowKey="user_id"
							:loading="tableLoading"
							:columns="columns"
							:data-source="dataSource"
							:pagination="false"
							@change="handlePaginationChange"
							:scroll="{ y: '37.395833vw' }"
							:row-selection="{ selectedRowKeys: selectedRowKeys, onSelect: onSelect, onSelectAll: onSelectAll, hideDefaultSelections: true }"
						>
							<template #bodyCell="{ column, record, index }">
								<template v-if="column.key === 'index'">
									{{ index + 1 }}
								</template>
								<template v-if="column.key === 'username'">
									<a @click="goPage({ path: '/cadre-portrait/home', user_id: record.user_id })">
										{{ record.username }}
									</a>
								</template>
								<template v-if="column.key === 'birthday'">
									<span>{{ record.birthday }}（{{ record.age }}岁）</span>
								</template>
								<template v-if="column.key === 'action'">
									<a @click="goPage({ path: '/cadre-portrait/cadre-table', user_id: record.user_id })" class="table-a">干部任免审批表</a>
									<a-divider type="vertical" />
									<a @click="goPage({ path: '/cadre-portrait/home', user_id: record.user_id })" class="table-a">干部画像</a>
								</template>
								<template v-if="column.key === 'avatar'">
									<img :src="`${CDN_URL}/fr_img/${record.head_url}`" class="avatar" />
								</template>
							</template>
						</a-table>
					</a-col>
					<a-col :span="5">
						<div class="already-have">
							<div class="have-nubmer">
								<span class="label">已选: </span>
								<span class="select-number">{{ quotaList.length }}</span>
								<span class="tips">（对比分析最多可选择10人）</span>
							</div>
							<div class="clear">
								<a-popconfirm title="确认清空?" ok-text="确认" cancel-text="取消" @confirm="onClear">
									<a>一键清空</a>
								</a-popconfirm>
							</div>
							<div class="have-name">
								<div class="inner-box">
									<div v-for="(item, index) in quotaList" :key="index" class="select-box">
										<span>{{ item.username }}</span
										><span class="have-delete" @click.stop="deleteName(item)"></span>
									</div>
								</div>
							</div>
							<div class="button-box">
								<a-button type="primary" class="my-button" :disabled="selectedRowKeys.length < 2 || selectedRowKeys.length > 10" @click="showModal"
									>对比分析</a-button
								>
								<a-button type="primary" class="my-button" :disabled="selectedRowKeys.length < 1" @click="onCollect">批量收藏</a-button>
							</div>
						</div>
					</a-col>
				</a-row>
			</a-card>
		</div>
		<a-modal v-model:visible="visible" width="70%" title="分析维度" @ok="handleOk" class="search-modal">
			<div class="veidoo">
				<div class="veidoo-item">
					<div class="title">基础信息</div>
					<div class="data">
						<div class="check-item" v-for="(item, index) in basicMessage" :key="index">
							<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
						</div>
					</div>
				</div>
				<div class="veidoo-item">
					<div class="title">干部画像</div>
					<div class="data">
						<div class="check-item" v-for="(item, index) in cadreMessage" :key="index">
							<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
						</div>
					</div>
				</div>
			</div>
		</a-modal>

		<a-modal class="folder-modal" :closable="false" :visible="collectVisible" width="" destroyOnClose :footer="null" @cancel="onClose">
			<Folder @close="onClose" @success="onSuccess" modal-type="collect" :source-ids="selectedRowKeys.join(',')" />
		</a-modal>
	</div>
</template>

<style lang="less" scoped>
.search-wrap {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 100%;
	overflow-y: auto;
	font-size: 16px;

	::-webkit-scrollbar {
		display: none;
	}
	.form-wrap {
		position: relative;
		width: 90%;
	}
	.search-form {
		user-select: none;
		::v-deep(.ant-card-body) {
			padding: 24px 24px 5px !important;
		}
		width: 100%;
		.ant-card {
			margin-bottom: 16px;
			.ant-row {
				margin-bottom: 10px;
				.ant-col .ant-row {
					margin-bottom: 0px;
				}
			}
			.item-h {
				align-items: center;
				margin: 0 !important;
				.ant-form-item {
					margin-bottom: 0;
				}
			}
		}
		.date-box {
			display: flex;
			align-items: center;
		}
		.ant-checkbox-group {
			.ant-checkbox-wrapper {
				margin-left: 0;
				margin-right: 10px;
			}
		}
		.cadre_category {
			::v-deep(.ant-checkbox-wrapper) {
				// margin-bottom: 10px;
			}
		}
	}
	.search-title {
		font-size: 20px;
		font-weight: bold;
		display: flex;
		align-items: center;
		&::before {
			margin-right: 8px;
			content: '';
			display: inline-block;
			width: 8px;
			height: 24px;
			background: url('@/assets/images/left-header-icon.png') center / cover no-repeat;
		}
	}

	.line {
		display: inline-block;
		width: 15px;
		height: 1px;
		background: #d9d9d9;
		margin: 0 3px;
	}
	.form-common-style {
		display: flex;
		align-items: center;
		.ant-input-number {
			width: 120px;
		}
	}
	.text-style {
		margin: 0px 12px;
		white-space: nowrap;
	}
	.base-year-aprove {
		display: flex;
		align-items: center;
		.ant-input-number {
			width: 100px;
		}
	}
	.cadre-index {
		.ant-select {
			width: 190px;
		}
		.text-style-percent {
			margin-left: 5px;
			font-size: 18px;
		}
		.line {
			margin: 0 10px;
		}
		.ant-input-number {
			width: 70px;
		}
	}
	.no-wrap-box {
		display: flex;
		align-items: flex-end;
	}
	.btn-wrap {
		margin-bottom: 10px;
		.ant-btn {
			height: 48px;
			padding: 0 64px;
			font-size: 16px;
		}
	}

	.already-have {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		.clear {
			padding: 5px 20px;
			font-size: 16px;
			background: #f7f8fa;
			a {
				color: #2462ff;
			}
		}
		.have-nubmer {
			display: flex;
			align-items: center;
			padding: 28px 20px;
			width: 100%;
			height: 76px;
			background: #f7f8fa;
			border-radius: 4px 4px 4px 4px;
			opacity: 1;
			font-size: 14px;
			font-weight: 400;
			.label {
				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #222222;
				line-height: 24px;
			}
			.select-number {
				margin-left: 10px;
				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #2462ff;
			}
		}

		.have-name {
			padding: 10px;
			flex: 1;
			background: #f7f8fa;
			border-radius: 4px 4px 4px 4px;
			opacity: 1;
			.inner-box {
				padding: 10px;
				height: 100%;
				min-height: 461px;
				max-height: 718px;
				overflow-y: auto;
				background-color: #ffffff;
				.select-box {
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
			div {
				margin-bottom: 13px;
			}

			span {
				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #222222;
			}

			.have-delete {
				width: 20px;
				height: 20px;
				display: inline-block;
				background-image: url('@/assets/images/delete.png');
				background-size: 100% 100%;
				margin-left: 15px;
				vertical-align: middle;
				cursor: pointer;
			}
		}
		.button-box {
			width: 100%;
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;
			.my-button {
				padding: 0px;
				width: 48%;
			}
		}
	}
	.tip-link {
		width: 100%;
		display: flex;
		justify-content: center;
		.link {
			display: flex;
			align-items: center;
			cursor: pointer;
			.text {
				margin-left: 10px;
				color: #1296db;
			}
		}
	}
}
.search-modal {
	margin-top: 24px;
	padding: 24px;
	width: 100%;
	background: #ffffff;
	.veidoo {
		margin-top: 10px;
		width: 100%;
		.top-content {
			display: flex;
			margin-top: 49px;
			margin-bottom: 31px;

			.label {
				display: flex;
				align-items: center;
				font-size: 18px;
				font-weight: bold;
				color: #00fff6;
				line-height: 18px;
				text-shadow: 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2),
					0 0 4px rgba(35, 219, 252, 0.2);
				background: linear-gradient(0deg, #3bdeff 6.1279296875%, #d1fbff 55.4443359375%, #ddf9ff 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;

				&::after {
					margin-left: 7px;
					content: '';
					display: inline-block;
					width: 24px;
					height: 16px;
					background: url('@/assets/images/label-icon.png') no-repeat center / cover;
				}
			}
		}
		.top-content-two {
			margin-bottom: 22px;
		}
		.veidoo-item {
			display: flex;
			border: 1px solid #ebebeb;
			.title {
				display: flex;
				align-items: center;
				justify-content: center;

				width: 100px;
				background: #f3f3f3;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;

				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #222222;
				line-height: 24px;
			}

			.data {
				flex: 1;
				padding: 28px 84px;

				display: flex;
				flex-wrap: wrap;
				.check-item {
					width: 25%;
				}
			}

			.two-title {
				display: flex;
				width: 169px;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 68px 0;
			}

			div {
				font-size: 14px;
				font-family: Source Han Sans CN;
				font-weight: 500;
				color: #00d2ff;
			}
		}
	}

	.ant-checkbox-wrapper {
		font-size: 20px;
	}
	button {
		font-size: 30px;
	}
}

.moresearch-enter-active {
	transition: opacity 0.5s ease;
}

.moresearch-enter-from {
	opacity: 0;
}
.table-a {
}

.avatar {
	width: 100px;
	height: 130px;
	object-fit: cover;
}
</style>
<style lang="scss">
.folder-modal {
	width: 752px;
	.ant-modal-body {
		padding: 0px !important;
	}
}

.search-modal {
	margin-top: 24px;
	padding: 24px;
	width: 100%;
	.ant-checkbox-wrapper {
		font-size: 20px;
	}
	button {
		font-size: 19px;
		height: 40px;
		width: 80px;
	}
}
</style>
