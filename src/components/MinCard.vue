<template>
	<div :class="['card-box']" ref="cardRef">
		<div class="card-header header" v-if="header">
			<div class="card-bg">
				<div class="left">
					<span class="title">{{ title }}</span>
					<span class="sub_title" v-if="subTitle"> {{ subTitle }}</span>
				</div>
				<slot name="right"></slot>
			</div>
		</div>
		<div class="content">
			<slot></slot>
		</div>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	header: {
		type: Boolean,
		default: false,
	},
	type: {
		type: String,
	},
	title: String,
	subTitle: String,
})
</script>

<style scoped lang="less">
.card-box {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	// background: no-repeat center / 100% 100%;
	background-color: #ffffff;
	border-radius: 8px;
	overflow: hidden;
	.header {
		flex-shrink: 0;
	}
	.content {
		padding: 20px;
		flex: 1 0;
		overflow: hidden;
		background: #fbfcff;
	}
}

.card-header {
	padding: 0 24px;
	width: 100%;
	height: 52px;
	display: flex;
	align-items: center;
	background-color: #daf4dd;
	.card-bg {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 15px;
		width: 100%;
		.title {
			font-size: 18px;
			line-height: 18px;
			font-family: PingFang SC-Heavy, PingFang SC;
			font-weight: 800;
			color: #000000;
		}
		.sub_title {
			font-size: 14px;
			color: #000000;
		}
	}
}
</style>
