<template>
	<div :class="`report-table-vertical ${border ? 'table-border' : ''}`">
		<div class="fixed-box" ref="fixedBoxRef">
			<div :class="`table-row ${rowClass(index, column.data)}`" v-for="(column, index) in customData" :key="index">
				<div class="table-header" ref="fixedHeadTable">
					<div
						:class="`table-td align-${column.align || 'center'} ${column.headerClassName || ''}`"
						:style="column.style ? column.style : {}"
						ref="fixedHeadRef"
					>
						{{ column.name }}
					</div>
				</div>
			</div>
		</div>
		<div class="fixed-row"></div>
		<div class="table-box" ref="tableBoxRef">
			<div class="innner-box">
				<div class="table-top" ref="topSlotRef">
					<slot name="top"></slot>
				</div>
				<div :class="`table-row ${rowClass(index, column.data)}`" v-for="(column, index) in customData">
					<div class="table-header" ref="tableHeaderRef">
						<div :class="`table-td align-${column.align || 'center'} ${column.headerClassName || ''}`" :style="column.style ? column.style : {}">
							{{ column.name }}
						</div>
					</div>
					<div class="table-body">
						<template v-if="column.merge">
							<div class="table-td row-merge">
								<slot :name="column.key" :data="column.data" :value="column.value"></slot>
							</div>
						</template>
						<template v-else>
							<div
								:class="`table-td align-${column.align || 'center'}`"
								v-for="(data, _index) in column.data"
								:key="_index"
								:style="column.contentStyle ? column.contentStyle : {}"
							>
								<slot :name="column.key" :data="data" :value="data[column.key]" :index="_index">
									<span :style="`text-align: ${column.align || 'center'}`">{{ data[column.key] }}</span>
								</slot>
							</div>
						</template>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, toRefs, watchEffect, ref, CSSProperties, unref, onUpdated, onUnmounted, onMounted, nextTick, inject, watch } from 'vue'
type Columns = {
	key: string
	name: string
	dataIndex: string
	// 是否合并当前行
	merge?: boolean
	style?: CSSProperties
	align?: 'left' | 'center' | 'right'
	headerClassName?: string
}
type DataSource = {
	[key: string]: any
}

interface CustomData extends Columns {
	value?: any
	data?: DataSource
}
export default defineComponent({
	props: {
		columns: {
			type: Array<Columns>,
			default: () => [],
		},
		dataSource: {
			type: Array<DataSource>,
			default: () => [],
		},
		border: {
			type: Boolean,
			default: false,
			required: false,
		},
		rowClass: {
			type: Function,
			default: () => '',
		},
		drag: {
			type: Boolean,
			default: () => false,
		},
	},
	emits: ['scroll'],
	setup(props, { emit }) {
		const { dataSource, columns, drag } = toRefs(props)
		// 表头列表
		const tableHeaderRef = ref([])
		// 固定表头列表
		const fixedHeadTable = ref([])
		// 固定表头父元素
		const fixedBoxRef = ref(undefined)
		// 插槽高度
		const topSlotRef = ref(undefined)
		//
		const tableBoxRef = ref(undefined)

		const customData = ref<Array<CustomData>>()
		watchEffect(() => {
			customData.value = columns.value.map((item) => {
				let _data: Array<DataSource> = []
				const _dataSource = unref(dataSource)
				const value = _dataSource.flatMap((data) => {
					_data.push(data)
					if (data[item.key] !== undefined) {
						return [data[item.key]]
					}
					return []
				})
				return {
					...item,
					value,
					data: _data,
				}
			})
		})
		const setFixedHeader = () => {
			const headList = tableHeaderRef.value
			// 固定表头
			const fixedHeaderList = fixedHeadTable.value
			// 表格顶部
			const tableTop = topSlotRef.value
			//
			const fixedBox = fixedBoxRef.value
			headList.forEach((el: any, index: number) => {
				// 单个表头
				const fixEl = fixedHeaderList[index]

				const height = el.getBoundingClientRect().height
				// 顶部表格顶部
				const tableHeader = tableTop.getBoundingClientRect().height
				fixEl.style.height = height + 'px'
				fixedBox.style.paddingTop = tableHeader + 'px'
			})
		}

		let func: any = {}

		const point = inject('point')

		const updatePoint = inject('updatePoint')

		const dragRegistry = () => {
			if (!unref(drag)) {
				return
			}

			const startPoint = {
				x: 0,
				y: 0,
			}
			let canMove = false

			const el = tableBoxRef.value

			el.style.cursor = 'grab'

			el.addEventListener('mousedown', (event) => {
				startPoint.x = event.x
				startPoint.y = event.y

				canMove = true

				document.body.style.cursor = 'grabbing'
				el.style.cursor = 'grabbing'
			})

			const mouseMove = (event: MouseEvent) => {
				if (!canMove) return

				const { x: moveX, y: moveY } = event
				const { x: startX } = startPoint

				// 容器滚动多少
				const elScrollLeft = el.scrollLeft

				emit('scroll', elScrollLeft)

				// 需要移动的距离
				const moveInstanceX = moveX - startX

				// 左移动
				if (moveInstanceX < 0) {
					let instance = elScrollLeft + Math.abs(moveInstanceX)
					let maxInstance = el.scrollWidth - el.offsetWidth

					if (instance >= maxInstance) {
						instance = maxInstance
					}
					updatePoint({ x: instance })
				} else {
					let instance = elScrollLeft - moveInstanceX

					if (instance <= 0) {
						instance = 0
					}
					updatePoint({ x: instance })
				}
				startPoint.x = moveX
				startPoint.y = moveY
			}

			const mouseUp = () => {
				canMove = false
				document.body.style.cursor = 'auto'
				el.style.cursor = 'grab'
			}

			document.addEventListener('mousemove', mouseMove)
			document.addEventListener('mouseup', mouseUp)

			func = {
				mouseMove,
				mouseUp,
			}
		}

		const touchRegistry = () => {
			if (!unref(drag)) {
				return
			}

			const startPoint = {
				x: 0,
				y: 0,
			}
			let canMove = false

			const el = tableBoxRef.value

			el.style.cursor = 'grab'

			el.addEventListener('touchstart', (event) => {
				startPoint.x = event.changedTouches[0].pageX
				startPoint.y = event.changedTouches[0].pageY

				canMove = true
			})
			let touchType = ''

			const touchMove = (event: MouseEvent) => {
				if (!canMove) return

				const { pageX, pageY } = event.changedTouches[0]
				const { x: startX, y: startY } = startPoint

				// 容器滚动多少
				const elScrollLeft = el.scrollLeft

				emit('scroll', elScrollLeft)

				// 需要移动的距离
				const moveInstanceX = pageX - startX
				// y轴移动的距离
				const moveInstanceY = pageY - startY
				if (!touchType) {
					touchType = Math.abs(moveInstanceX) < Math.abs(moveInstanceY) ? 'y' : 'x'
				}

				if (touchType === 'y') {
					return (canMove = false)
				}
				if (touchType === 'x') {
					event.preventDefault()
				}
				// 左移动
				if (moveInstanceX < 0) {
					let instance = elScrollLeft + Math.abs(moveInstanceX)
					let maxInstance = el.scrollWidth - el.offsetWidth

					if (instance >= maxInstance) {
						instance = maxInstance
					}
					updatePoint({ x: instance })
				} else {
					let instance = elScrollLeft - moveInstanceX
					if (instance <= 0) {
						instance = 0
					}
					updatePoint({ x: instance })
				}
				startPoint.x = pageX
				startPoint.y = pageY
			}

			const touchEnd = () => {
				canMove = false
				touchType = ''
			}

			document.addEventListener('touchmove', touchMove, { passive: false })
			document.addEventListener('touchend', touchEnd)

			func = {
				touchMove,
				touchEnd,
			}
		}
		onMounted(() => {
			dragRegistry()
			touchRegistry()
		})
		// 实时获取高度
		const interval = setInterval(() => {
			setFixedHeader()
		}, 500)

		onUnmounted(() => {
			window.removeEventListener('resize', setFixedHeader)
			document.removeEventListener('mousemove', func.mouseMove)
			document.removeEventListener('mouseup', func.mouseUp)

			clearInterval(interval)
		})

		window.addEventListener('resize', setFixedHeader)

		watch(
			point,
			() => {
				tableBoxRef.value?.scroll(point.x, point.y)
			},
			{
				immediate: true,
			}
		)
		return {
			customData,
			fixedBoxRef,
			topSlotRef,
			tableBoxRef,
			tableHeaderRef,
			fixedHeadTable,
		}
	},
})
</script>

<style scoped lang="less">
.table-box {
	width: 100%;
	overflow-x: hidden;
	user-select: none;
	&::-webkit-scrollbar {
		display: none;
	}
}

.table-border {
	.table-header {
		border-right: 1px solid #ebebeb;
		border-bottom: 1px solid #ebebeb;
	}
	.table-body {
		.table-td {
			border-right: 1px solid #ebebeb;
			border-bottom: 1px solid #ebebeb;
		}
	}
	.table-row {
		margin-top: 0 !important;
	}
}
.report-table-vertical {
	position: relative;
	display: flex;
	flex-direction: column;
	font-size: 14px;
	font-family: Source Han Sans CN Regular;
	font-weight: 400;
	color: #3d3d3d;

	.fixed-box {
		position: absolute;
		top: 0;
		left: 0;
		width: 140px;
		height: 100%;
		z-index: 100;
		background-color: #ffffff;
	}
	.table-row {
		display: flex;
		margin-top: 4px;
		width: fit-content;
		min-width: 100%;
		.table-header {
			.table-td {
				padding: 14px 28px;
				width: 140px;
				font-size: 20px;
				text-align-last: justify;
			}
		}
		.table-body {
			display: flex;
			color: #000000;
			flex: 1;
			.table-td {
				padding: 13px 25px;
				flex: 1;
				min-width: 666px;
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
				min-height: 40px;
				font-size: 18px;
				// padding: 10px 20px;
			}
			.align-left {
				justify-content: flex-start;
			}
			.align-center {
				justify-content: center;
			}
			.align-right {
				justify-content: flex-end;
			}
		}
		.table-header {
			display: flex;
			align-items: center;
			justify-content: center;
			text-align: center;
			// padding: 16px;
		}
	}
	.table-td {
		padding: 14px 10px;
	}
	.table-columns {
		min-width: 150px;
	}
}
.fixed-row {
	position: absolute;
	top: 0;
	left: 0;
}
</style>
