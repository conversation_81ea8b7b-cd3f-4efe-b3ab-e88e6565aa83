<template>
	<div :class="['card-box', `size-${sizeType}`]" ref="cardRef">
		<div class="card-header header" v-if="header">
			<div class="card-bg">
				<div class="left">
					<span class="title">{{ title }}</span>
					<span class="sub_title" v-if="subTitle"> {{ subTitle }}</span>
				</div>
				<slot name="right"></slot>
			</div>
		</div>
		<div class="content">
			<slot></slot>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue'
type SizeType = '1' | '2' | '3' | '4' | '5' | '6' // 卡片大小
// 宽高比例
// const ratio = 1.5
defineProps({
	header: {
		type: Boolean,
		default: false,
	},
	type: {
		type: String,
	},
	sizeType: {
		type: String as PropType<SizeType>,
		default: () => '1',
	},
	title: String,
	subTitle: String,
})
const cardRef = ref<HTMLElement>()
</script>

<style scoped lang="less">
.card-box {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	// background: no-repeat center / 100% 100%;
	background-color: #ffffff;
	border-radius: 8px;
	overflow: hidden;
	.header {
		flex-shrink: 0;
	}
	.content {
		padding: 20px;
		flex: 1 0;
		overflow: hidden;
	}
}
.sizeType {
	background-image: url('~@/assets/images/card-vertical-border.png');
}

.card-header {
	padding: 0 20px;
	width: 100%;
	height: 70px;
	display: flex;
	align-items: center;
	background-color: #daf4dd;
	.card-bg {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 15px;
		width: 100%;
		background: url('@/assets/images/card-icon.png') no-repeat left center / 8px 24px;
		font-size: 24px;
		line-height: 24px;
		font-family: PingFang SC-Heavy, PingFang SC;
		font-weight: 800;
		color: #000000;
		.sub_title {
			font-size: 18px;
			color: #000000;
		}
		// &::after {
		// 	content: '';
		// 	position: absolute;
		// 	bottom: 0;
		// 	left: 50px;
		// 	display: block;
		// 	height: 1px;
		// 	width: 100%;
		// 	background-color: #055368;
		// }
	}
}

// $prefix-class: 'size';
// $url-prefix: '@/assets/images/';
// @for $n from 1 through 6 {
// 	.#{$prefix-class}-#{$n} {
// 		background-image: url('#{$url-prefix}size-#{$n}.png');
// 	}
// }
</style>
