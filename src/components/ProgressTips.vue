<template>
	<transition name="progress" appear>
		<div class="progress-tips" v-if="visible">
			<div class="progress-box">
				<div class="inner-box" :style="widthPercent">
					<div class="text">
						<span class="num">{{ percentage || 0 }}</span> <span class="short-icon">%</span>
					</div>
					<div class="icon"></div>
				</div>
			</div>
		</div>
	</transition>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'

const props = defineProps({
	percentage: { type: Number, required: true, default: 0 },
	visible: { type: Boolean, required: true, default: true },
})

const widthPercent = computed(() => {
	return {
		width: props.percentage < 9 ? 'fit-content' : props.percentage + '%',
	}
})
</script>

<style lang="less" scoped>
.progress-tips {
	position: fixed;
	top: 76px;
	left: 50%;
	transform: translate(-50%);
	z-index: 999;

	.progress-box {
		width: 590px;
		height: 18px;
		background: rgba(25, 106, 171, 0.2);
		filter: drop-shadow(0 10px 10px rgba(0, 0, 0, 0.3));
		border-radius: 70px;
		.inner-box {
			height: 100%;
			text-align: right;
			position: relative;
			display: flex;
			align-items: center;
			transition: all 0.2s linear;
			background: linear-gradient(90deg, #5db4f3 0%, #0987e3 100%);
			border-radius: 70px;
			position: relative;
			&::before {
				position: absolute;
				top: 0px;
				left: 0px;
				content: '';
				display: inline-block;
				height: 100%;
				width: 5px;
				filter: blur(10px);
				background: #ffffff;
				animation: bgChange 4s ease-in-out infinite;
			}
			.text {
				padding: 10px 0px;
				display: flex;
				align-items: center;
				transition: all 0.2s linear;
				border-radius: 70px;
				flex: 1;
				padding-left: 10px;
				width: auto;
				font-variant-numeric: tabular-nums;
				vertical-align: baseline;
				color: #ffffff;

				.num {
					font-size: 16px;
					line-height: 1;
					text-align: right;
					transform: translateY(1px);
				}
				.short-icon {
					font-size: 16px;
					line-height: 1;
					transform: translateY(1px);
				}
			}
			.icon {
				flex-shrink: 0;
				background: url('@/assets/images/progress-icon.png') center center/ 100% 100% no-repeat;
				width: 30px;
				height: 30px;
				transform: translateX(2px);
			}

			@keyframes bgChange {
				0% {
					left: -5%;
					opacity: 0;
				}
				100% {
					opacity: 1;
					left: calc(100% - 4px);
				}
			}
		}
	}
}

.progress-leave-to {
	opacity: 0;
	transform: translate(-50%, -50px) scale(0.5);
}
.progress-enter-from {
	opacity: 0;
	transform: translate(-50%, -50px) scale(1);
}
.progress-enter-active,
.progress-leave-active {
	transition: all 0.3s ease-in-out;
}
</style>
