<template>
	<a-dropdown>
		<a-input suffix-icon="search" placeholder="点击输入" :value="value" @input="handleChange" v-bind="$attrs" @focus="onFocus" />
		<template #overlay>
			<a-menu>
				<a-menu-item v-for="item in options" :key="item.value" @click="onSelect(item)">
					<span>{{ item.label }}</span>
				</a-menu-item>
			</a-menu>
		</template>
	</a-dropdown>
</template>

<script lang="ts" setup>
import { ref, watch, toRefs } from 'vue'
const props = defineProps({
	value: String, // v-model 绑定的值
	placeholder: String,
	options: {
		type: Array<{ label: string; value: string }>,
		default: () => [],
	},
})

const emits = defineEmits(['search', 'update:value', 'change', 'input'])

const valueIn = ref('')

const handleChange = (value: any) => {
	// 处理输入框值的变化
	emits('search', value.target.value)

	emits('update:value', value.target.value)

	emits('input', value.target.value)

	emits('change', value.target.value)
}

const onFocus = (event: any) => {
	// 处理输入框聚焦事件
	emits('search', event.target.value)
}

const onSelect = (value: any) => {
	console.log(value)
	// 处理搜索按钮点击事件
	emits('update:value', value.value)

	emits('change', value.value)
}
</script>

<style scoped>
/* 添加样式，根据需要自定义样式 */
</style>
