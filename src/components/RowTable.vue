<!-- 横向表格 -->
<template>
	<div class="row-table">
		<div class="row-table-container">
			<!-- 表格内容 -->
			<div class="row-table-content">
				<div :class="`table-row`" v-for="col in columns" :key="col.dataIndex || col.dataIndex">
					<!-- 横向title -->
					<div :class="`table-title ${col.titleClass || ''}`">{{ col.title }}</div>

					<!-- 横向数据 -->
					<div :class="`table-data`">
						<div
							:class="`data-item ${col.rowClass} text-${col.align || 'center'}`"
							:style="`width: ${col.width || 100 / datasource.length + '%'}`"
							v-for="(item, index) in datasource"
							:key="index"
						>
							<slot :name="col.dataIndex" :value="item[col.dataIndex]" :data="item">{{ item[col.dataIndex] }}</slot>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'

type ColumnsType = {
	title: string
	dataIndex: string
	key?: string
	align?: string
	width?: string
	titleClass?: string
	rowClass?: string
}

const props = defineProps({
	columns: {
		type: Array<ColumnsType>,
		default: () => [],
	},
	datasource: {
		type: Array<any>,
		default: () => [],
	},
})
</script>
<style lang="less" scoped>
.row-table {
	--padding: 23px 29px;
	width: 100%;
	.row-table-container {
		.row-table-content {
			.table-row {
				width: 100%;
				display: flex;
				&:nth-child(1) {
					.table-title {
						border-top: 1px solid #e9e9e9;
					}
					.table-data {
						.data-item {
							border-top: 1px solid #e9e9e9;
						}
					}
				}
				.table-title {
					display: flex;
					align-items: center;
					padding: var(--padding);
					width: 133px;
					text-align: center;
					border-right: 1px solid #e9e9e9;
					border-left: 1px solid #e9e9e9;
					border-bottom: 1px solid #e9e9e9;
					background: #f5f5f5;

					font-size: 18px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #3d3d3d;
				}
				.table-data {
					display: flex;
					flex: 1;
					.data-item {
						display: flex;
						align-items: center;
						justify-content: center;
						padding: var(--padding);
						text-align: center;
						border-right: 1px solid #e9e9e9;
						border-bottom: 1px solid #e9e9e9;
						font-size: 18px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #3d3d3d;
					}
					.text-left {
						justify-content: left;
					}
					.text-center {
						justify-content: center;
					}
					.text-right {
						justify-content: right;
					}
				}
			}
		}
	}
}
</style>
