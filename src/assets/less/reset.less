/************************** reset ************************/
html,
body {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
	color: #333;
	background-color: #f5f5f5;
	-webkit-overflow-scrolling: touch;
	font-family: 'Source Han Sans CN;', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'Microsoft YaHei', 'WenQuanYi Micro Hei',
		'SourceHanSansCN-Medium', sans-serif;
}

body * {
	outline: 0;
	box-sizing: border-box;
	-webkit-text-size-adjust: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	font-size: inherit;
}

button,
input,
textarea {
	border: none;
	line-height: 1.5;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
	color: #ccc;
}

input:read-only {
	-webkit-touch-callout: none;
}
em,
strong,
i {
	font-style: normal;
}
a:hover,
a:visited,
a:link,
a:active {
	text-decoration: none;
	color: inherit;
}

// li {
// 	list-style: none;
// }

ul,
ol,
input,
button,
h6,
h3,
p {
	margin: 0;
	padding: 0;
}

h6,
h3,
input,
button {
	font: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0;
}
