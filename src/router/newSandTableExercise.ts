// 沙盘推演，路由全部同级
export const newSandTableExercise = {
	path: '/new-sand-table-exercise',
	component: () => import('@/views/NewSandTableExercise/index.vue'),
	name: 'newSandTableExercise',
	children: [
		{
			path: '/new-sand-table-exercise/home',
			component: () => import('@/views/NewSandTableExercise/Home/index.vue'),
			name: 'newSandTableExerciseHome',
		},
		{
			path: '/new-sand-table-exercise/candidate-org-team',
			name: 'newSandTableExerciseCandidateOrgTeam',
			component: () => import('@/views/NewSandTableExercise/CandidateOrgTeam/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/org-team',
			name: 'newSandTableExerciseOrgTeam',
			component: () => import('@/views/NewSandTableExercise/OrgTeam/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/look-for-by-job',
			name: 'newSandTableExerciseLookForByJob',
			component: () => import('@/views/NewSandTableExercise/LookForByJob/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/structural-analysis',
			name: 'newSandTableExerciseStructuralAnalysis',
			component: () => import('@/views/NewSandTableExercise/StructuralAnalysis/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/structure-abnormal',
			name: 'newSandTableExerciseStructureAbnormal',
			component: () => import('@/views/NewSandTableExercise/StructureAbnormal/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/lack-of-matching',
			name: 'newSandTableExerciseLackOfMatching',
			component: () => import('@/views/NewSandTableExercise/LackOfMatching/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/wait-list',
			name: 'newSandTableExerciseWaitList',
			component: () => import('@/views/NewSandTableExercise/WaitList/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/early-warning',
			name: 'newSandTableExerciseEarlyWarning',
			component: () => import('@/views/NewSandTableExercise/EarlyWarning/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/plan-list',
			name: 'newSandTableExercisePlanList',
			component: () => import('@/views/NewSandTableExercise/PlanList/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/adjust-list',
			name: 'newSandTableExerciseAdjustList',
			component: () => import('@/views/NewSandTableExercise/PlanList/AdjustList/index.vue'),
		},
		{
			path: '/new-sand-table-exercise/adjust-analysis',
			name: 'newSandTableExerciseAdjustAnalysis',
			component: () => import('@/views/NewSandTableExercise/AdjustmentAnalysis/index.vue'),
		},
	],
}
