// 沙盘推演，路由全部同级
export const sandTableExercise = {
	path: '/sand-table-exercise',
	component: () => import('@/views/SandTableExercise/index.vue'),
	name: 'sandTableExercise',
	children: [
		{
			path: '/sand-table-exercise/home',
			name: 'SandTableExerciseHome',
			component: () => import('@/views/SandTableExercise/NewHome/index.vue'),
		},
		{
			path: '/sand-table-exercise/smart-selection',
			name: 'SandTableExerciseSmartSelection',
			component: () => import('@/views/SandTableExercise/SmartSelection/index.vue'),
		},
		{
			path: '/sand-table-exercise/scheme-analysis',
			name: 'SandTableExerciseSchemeAnalysis',
			component: () => import('@/views/SandTableExercise/SchemeAnalysis/index.vue'),
		},
		{
			path: '/sand-table-exercise/early-warning',
			name: 'SandTableExerciseEarlyWarning',
			component: () => import('@/views/SandTableExercise/EarlyWarning/index.vue'),
		},
		{
			path: '/sand-table-exercise/wait-matched',
			name: 'SandTableExerciseWaitMatched',
			component: () => import('@/views/SandTableExercise/WaitMatched/index.vue'),
		},
		{
			path: '/sand-table-exercise/historical-plan',
			name: 'SandTableExerciseHistoricalPlan',
			component: () => import('@/views/SandTableExercise/HistoricalPlan/index.vue'),
		},
		{
			path: '/sand-table-exercise/look-for',
			name: 'SandTableExerciseLookFor',
			component: () => import('@/views/SandTableExercise/LookFor/index.vue'),
		},
		{
			path: '/sand-table-exercise/cadre-allocation',
			name: 'SandTableExerciseCadreAllocation',
			component: () => import('@/views/SandTableExercise/CadreAllocation/index.vue'),
		},
		{
			path: '/sand-table-exercise/job-vacancy',
			name: 'SandTableExerciseJobVacancy',
			component: () => import('@/views/SandTableExercise/JobVacancy/index.vue'),
		},
	],
}
