export const cadreSystem = {
	path: '/cadre-system',
	component: () => import('@/views/CadreSystem/CadreSystem.vue'),
	name: 'cadre-system',
	meta: {
		keepAlive: true,
		aliveTo: ['/cadre-system', '/cadre-portrait'], // 跳转其中某个页面开启页面缓存，可以是完整路径，也可以是顶级路由前缀
	},
	children: [
		{
			path: '/cadre-system/information',
			component: () => import('@/views/CadreSystem/Information/information.vue'),
			name: 'cadre-system-information',
		},
		{
			path: '/cadre-system/search',
			// component: () => import('@/views/CadreSearch/index.vue'),
			// component: () => import('@/views/CadreSystem/Search/index.vue'),
			component: () => import('@/views/CadreSystem/Search/newSearch.vue'),
			name: 'cadre-system-search',
		},
		{
			path: '/cadre-system/statistics',
			component: () => import('@/views/CadreSystem/Statistics/index.vue'),
			name: 'cadre-system-statistics',
		},
		{
			path: '/cadre-system/search-result',
			component: () => import('@/views/CadreSystem/SearchResult/SearchResult.vue'),
			// params 参数
			name: 'cadre-system-search-result',
			props: true,
		},
		{
			path: '/cadre-system/user-center',
			component: () => import('@/views/CadreSystem/UserCenter/UserCenter.vue'),
			// params 参数
			name: 'cadre-system-user-center',
		},
		{
			path: '/cadre-system/list-motion',
			name: 'cadre-system-list-motion',
			meta: {
				hiddenMenu: true,
			},
			component: () => import('@/views/CadreSystem/ListMotion/ListMotion.vue'),
		},
	],
}
