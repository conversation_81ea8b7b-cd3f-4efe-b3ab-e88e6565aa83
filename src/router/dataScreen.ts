// 巡察指数大屏
export const dataScreen = {
	path: '/data-screen',
	component: () => import('@/views/DataScreen/index.vue'),
	name: 'data-screen',
	meta: {
		routerViewKey: `data-screen`,
	},
	children: [
		{
			path: '/data-screen',
			redirect: '/data-screen/login',
		},
		{
			path: '/data-screen/login',
			name: 'data-screen-login',
			component: () => import('@/views/DataScreen/Login/index.vue'),
		},
		{
			path: '/data-screen/home',
			name: 'data-screen-home',
			component: () => import('@/views/DataScreen/Home/index.vue'),
		},
		{
			path: '/data-screen/org-detail',
			name: 'data-screen-detail',
			component: () => import('@/views/DataScreen/OrgDetail/index.vue'),
		},
		{
			path: '/data-screen/party-inspection-detail',
			name: 'data-screen-party-inspection-detail',
			component: () => import('@/views/DataScreen/PartyInspectionDetail/index.vue'),
		},
		{
			path: '/data-screen/party-inspection-detail/CommunicateLevel.vue',
			name: 'data-screen-party-inspection-detail/CommunicateLevel.vue',
			component: () => import('@/views/DataScreen/PartyInspectionDetail/component/CommunicateLevel.vue'),
		},
	],
}
