// 干部画像
const cadrePortrait = {
	path: `/cadre-portrait`,
	name: `cadre-portrait`,
	component: () => import('@/views/CadrePortrait/CadrePortrait.vue'),
	meta: {
		// 存在多个引用时，不复用组件
		routerViewKey: `cadre-portrait`,
	},
	children: [
		{
			path: `/cadre-portrait/home`,
			name: `cadre-portrait-home`,
			component: () => import('@/views/CadrePortrait/Home/index.vue'),
		},
		{
			path: `/cadre-portrait/cadre-table`,
			name: `cadre-portrait-cadre-table`,
			meta: {
				layoutFull: true, // 当前上级路由下的所有路由是否需要左侧用户信息板块儿
			},
			component: () => import('@/views/CadrePortrait/CadreTable/index.vue'),
		},
		{
			path: `/cadre-portrait/search-comparison`,
			name: `cadre-portrait-search-comparison`,
			meta: {
				layoutFull: true, // 当前上级路由下的所有路由是否需要左侧用户信息板块儿
			},
			// component: () => import('@/views/CadrePortrait/SearchComparison/SearchComparison.vue'),
			component: () => import('@/views/CadreSystem/Search/newSearch.vue'),
		},
		{
			path: `/cadre-portrait/relationship-graph`,
			name: `relationship-graph`,
			meta: {
				layoutFull: true, // 当前上级路由下的所有路由是否需要左侧用户信息板块儿
			},
			component: () => import('@/views/CadrePortrait/RelationshipGraph/index.vue'),
		},
	],
}
// 干部指数
const cadreIndex = {
	path: `/cadre-index`,
	component: () => import('@/views/CadreIndex/index.vue'),
	name: `cadre-index`,
}

export { cadrePortrait, cadreIndex }
