import { defineStore } from 'pinia'

import { getCode } from '@/apis/new-sand-table-exercise'

// 保存页面状态
const code = defineStore('code', {
	state: () => {
		return { codeMap: {} as any }
	},
	actions: {
		async updateCode(code: string | number) {
			if (this.codeMap[code]) {
				return
			}
			const res = await getCode({ code })
			if (res.code === 0) {
				this.codeMap[code] = res.data.map((item) => ({ label: item.op_value, value: item.op_key, ...item }))
			}
		},
	},
	getters: {
		getCode() {
			return (code: any) => {
				return this.codeMap[code] || []
			}
		},
	},
})
export default code
