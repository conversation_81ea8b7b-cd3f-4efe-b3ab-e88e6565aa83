import { defineStore } from 'pinia'

export const UNIT = 1
export const SAME = 2
export const TOWN = 3
export const COUNTY = 0
export type MenuKey = typeof UNIT | typeof SAME | typeof TOWN | typeof COUNTY

export const useHomeStore = defineStore('home', {
	state: () => {
		return {
			// 散点图点击后的user_id
			coor_user_id: '-1',
			// 坐标分布图选中的菜单
			coor_menu_selected: 1,
			// 坐标分布菜单
			menu: [
				{
					label: '本单位',
					key: UNIT,
				},
				{
					label: '同序列',
					key: SAME,
				},
				{
					label: '乡镇/部门',
					key: TOWN,
				},
				{
					label: '全区',
					key: COUNTY,
				},
			],
		}
	},
	getters: {
		// 人大、政协、纪委、公安、检察院、法院人员 去掉同序列
		menuFilterSame(): Array<{ label: string; key: number }> {
			return this.menu.filter((item) => [2, 0].includes(item.key))
		},
		//特殊十二人
		menuFilterSpecial(): Array<{ label: string; key: number }> {
			return this.menu.filter((item) => ![1, 2].includes(item.key))
		},
		menuList(): Array<{ label: string; key: number }> {
			return this.menu
		},
	},
	actions: {
		updateCoorUserId(user_id: string) {
			this.coor_user_id = user_id
		},
		updateCoorMenuSelected(key: number) {
			this.coor_menu_selected = key
		},
	},
})
