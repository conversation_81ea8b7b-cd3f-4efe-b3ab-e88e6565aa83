import { defineStore } from 'pinia'
// 保存页面状态
const mock = defineStore('sandbox', {
	state: () => {
		return {
			mockId: undefined, // 当前模拟方案id
			mockName: undefined,
			mockList: [] as any,
			singleMock: [] as any, // 不存在组织id, 通过一键智选调整的用户
		}
	},
	actions: {
		setMockId(mockId: any) {
			this.mockId = mockId
		},
		update(mock: any) {
			this.mockList.push(mock)
		},
		findByOrgId(orgId: any) {
			const _mock: any = this.mockList

			const index = _mock.findIndex((item: any) => item.orgId === orgId)

			return index
		},
		// 是否原本已存在
		hasPreMock(mock: any) {
			return this.findByOrgId(mock.orgId) !== -1
		},
		replaceMock(mock: any) {
			const index = this.findByOrgId(mock.orgId)
			if (index !== -1) {
				this.mockList.splice(index, 1, mock)
			} else {
				this.update(mock)
			}
		},
		// 找到单个用户Id
		findSingleMockByUserId(userId: any) {
			return this.singleMock.findIndex((item: any) => item.userId === userId)
		},
		// 单个用户
		updatesingleMock(mockUser: any) {
			const index = this.findSingleMockByUserId(mockUser.userId)

			if (index !== -1) {
				this.singleMock.splice(index, 1, mockUser)
			} else {
				this.singleMock.push(mockUser)
			}
		},
		// 多个
		updateMultiMockUser(mockList: any) {
			mockList.forEach((mockUser: any) => {
				this.updatesingleMock(mockUser)
			})
		},
		setMockName(mockName: any) {
			this.mockName = mockName
		},
	},
	getters: {
		getMockList(): any {
			const mockList = this.mockList.flatMap((item: any) => item.mockUser)
			const singleMock = this.singleMock
			return [...mockList, ...singleMock]
		},
	},
})
export default mock
