import { defineStore } from 'pinia'
// 保存页面状态
const keepalive = defineStore('keepalive', {
	state: () => {
		return { keepalive: ['CadreSystem'] as any }
	},
	actions: {
		clear() {
			this.keepalive = []
		},
		push(val: string) {
			const index = this.keepalive.findIndex((v) => v === val)
			if (index === -1) {
				this.keepalive.push(val)
			}
		},
		remove(val: string) {
			const index = this.keepalive.findIndex((v) => v === val)
			if (index !== -1) {
				this.keepalive.splice(index, 1)
			}
		},
	},
	getters: {
		keepaliveString(): string {
			return this.keepalive.join(',')
		},
	},
})
export default keepalive
