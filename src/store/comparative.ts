import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { getDimensionConfig } from '@/apis/cadre-portrait/home'
export const UNIT = 1
export const SAME = 2
export const TOWN = 3
export const COUNTY = 0
export type MenuKey = typeof UNIT | typeof SAME | typeof TOWN | typeof COUNTY

export const useComparative = defineStore('comparative', {
	state: () => {
		return {
			comparative: [] as Array<any>, // 对比分析
			config_ids: [],
		}
	},
	actions: {
		updateComparative(data: any) {
			if (Array.isArray(data)) {
				this.comparative = data
			} else {
				const index = this.getComparativeIndex(data.user_id)
				// 存在就删除, 反之保存
				if (index !== -1) {
					this.comparative.splice(index, 1)
				} else {
					if (this.comparative.length < 10) {
						this.comparative.push(data)
					} else {
						message.warn('最多添加10人')
					}
				}
			}
		},
		// 传入数据是否存在于列表中
		getComparativeIndex(user_id: any) {
			return this.comparative.findIndex((item: any) => {
				return item.user_id === user_id
			})
		},
		// 获取configid
		initConfigIds() {
			if (this.config_ids.length) return

			getDimensionConfig({}).then(({ data, code }: any) => {
				if (code == 0) {
					const ids: any = []
					data.map((item: any) => {
						if (item.name == '基础信息') {
							item.children.forEach((item: any) => {
								ids.push(item.config_id)
							})
						} else {
							item.children.forEach((item: any) => {
								ids.push(item.config_id)
							})
						}
					})

					this.config_ids = ids
				}
			})
		},
	},
})
