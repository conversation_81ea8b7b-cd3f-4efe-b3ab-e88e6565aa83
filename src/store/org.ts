import { getOrgList } from '@/apis/cadreSystem'
import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'

const org = defineStore('org', {
	state: () => {
		const orgList = sessionStorage.getItem('org_list')
		const flatData = sessionStorage.getItem('flat_data')

		return { orgTree: JSON.parse(orgList || '[]') as any, flatData: JSON.parse(flatData || '[]') }
	},
	actions: {
		updateDetail(orgTree: any) {
			sessionStorage.setItem('org_list', JSON.stringify(orgTree))
			this.orgTree = orgTree || {}
		},
		updateFlatData(orgTree: any) {
			const flatData = (data = []) => {
				const _data: any = data.flatMap((org: any) => {
					const { children, ...other } = org
					if (Array.isArray(children) && children.length) {
						return [org, ...flatData(children as any)]
					}
					return [org]
				})
				return _data
			}
			// 数据扁平化
			this.flatData = flatData(orgTree)
			sessionStorage.setItem('flat_data', JSON.stringify(this.flatData))
		},
		/**
		 * 加载组织机构树
		 * @param type 数据类型 0-显示所有数据(默认) 1-显示隐藏数据
		 */
		async loadOrgTree(type: number = 0) {
			const res = await getOrgList({ type })

			if (res.code === 0) {
				this.updateDetail(res.data)
			} else {
				message.error(res.message)
			}
		},
	},
})
export default org
