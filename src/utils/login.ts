import { message } from 'ant-design-vue'
import { getQueryVariable, Base64 } from './utils'
export const loginByQuery = () => {
	const query = getQueryVariable()

	try {
		const params = Base64.decode(query)

		const keys = Object.keys(params)

		keys.forEach((key: any) => {
			sessionStorage.setItem(key, JSON.stringify(params[key]))
		})
	} catch (err) {
		console.log('err')

		message.error('用户信息转换错误')
	}
}
