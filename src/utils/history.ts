import router from '@/router/index'
/**
 * @description: 路由跳转
 * @param {any} params {path: }  'url'
 * @return {*}
 */
export const historyPush = (params: any) => {
	// if (typeof params === 'string' && ENV_PAD) {
	// 	const pathList = params.split('?')
	// 	const url = pathList[0]
	// 	const query = pathList[1]

	// 	// 将query 转换为对象
	// 	const queryList = query.split('&')

	// 	const _query: any = {}

	// 	queryList.forEach((item: any) => {
	// 		const queryItem = item.split('=')
	// 		_query[queryItem[0]] = queryItem[1]
	// 	})

	// 	params = { path: url, query: _query, force: true, replace: false }
	// }

	// console.log(params)

	// ENV_PAD ? router.push(params) : window.open(params)

	router.push(params)
}
