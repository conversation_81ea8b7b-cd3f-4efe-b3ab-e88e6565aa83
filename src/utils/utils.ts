import MeScroll from 'mescroll.js'

export const Base64 = {
	_keyStr: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
	encode(e: string) {
		let t = ''
		let n
		let r
		let i
		let s
		let o
		let u
		let a
		let f = 0
		e = Base64._utf8_encode(e)
		while (f < e.length) {
			n = e.charCodeAt(f++)
			r = e.charCodeAt(f++)
			i = e.charCodeAt(f++)
			s = n >> 2
			o = ((n & 3) << 4) | (r >> 4)
			u = ((r & 15) << 2) | (i >> 6)
			a = i & 63
			if (isNaN(r)) {
				u = a = 64
			} else if (isNaN(i)) {
				a = 64
			}
			t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
		}
		return t
	},
	decode(e: string) {
		let t = ''
		let n
		let r
		let i
		let s
		let o
		let u
		let a
		let f = 0
		e = e.replace(/[^A-Za-z0-9\+\/\=]/g, '')
		while (f < e.length) {
			s = this._keyStr.indexOf(e.charAt(f++))
			o = this._keyStr.indexOf(e.charAt(f++))
			u = this._keyStr.indexOf(e.charAt(f++))
			a = this._keyStr.indexOf(e.charAt(f++))
			n = (s << 2) | (o >> 4)
			r = ((o & 15) << 4) | (u >> 2)
			i = ((u & 3) << 6) | a
			t += String.fromCharCode(n)
			if (u != 64) {
				t += String.fromCharCode(r)
			}
			if (a != 64) {
				t += String.fromCharCode(i)
			}
		}
		t = Base64._utf8_decode(t)
		return t
	},
	_utf8_encode(e: string) {
		e = e.replace(/\r\n/g, '\n')
		let t = ''
		for (let n = 0; n < e.length; n++) {
			const r = e.charCodeAt(n)
			if (r < 128) {
				t += String.fromCharCode(r)
			} else if (r > 127 && r < 2048) {
				t += String.fromCharCode((r >> 6) | 192)
				t += String.fromCharCode((r & 63) | 128)
			} else {
				t += String.fromCharCode((r >> 12) | 224)
				t += String.fromCharCode(((r >> 6) & 63) | 128)
				t += String.fromCharCode((r & 63) | 128)
			}
		}
		return t
	},
	_utf8_decode(e: string) {
		let t = ''
		let n = 0
		let r = 0
		let c3 = 0
		let c2 = 0
		while (n < e.length) {
			r = e.charCodeAt(n)
			if (r < 128) {
				t += String.fromCharCode(r)
				n++
			} else if (r > 191 && r < 224) {
				c2 = e.charCodeAt(n + 1)
				t += String.fromCharCode(((r & 31) << 6) | (c2 & 63))
				n += 2
			} else {
				c2 = e.charCodeAt(n + 1)
				c3 = e.charCodeAt(n + 2)
				t += String.fromCharCode(((r & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63))
				n += 3
			}
		}
		return t
	},
}
export const getParam = () => {
	const qs = (function (a) {
		if (!a) return {}
		const b: any = {}
		for (let i = 0; i < a.length; ++i) {
			const p = a[i].split('=')
			if (p.length != 2) continue
			b[p[0]] = decodeURIComponent(p[1].replace(/\+/g, ' '))
		}
		return b
	})(window.location.search.substr(1).split('&'))
	return qs
}

export const throttle = <T extends (...args: any[]) => void>(func: T, delay: number): T => {
	let timeoutId: ReturnType<typeof setTimeout> | undefined
	let lastExecTime = 0

	return function (...args: Parameters<T>) {
		const currentTime = Date.now()

		if (currentTime - lastExecTime > delay) {
			lastExecTime = currentTime
			func(...args)
		} else {
			if (timeoutId) {
				clearTimeout(timeoutId)
			}

			timeoutId = setTimeout(() => {
				lastExecTime = Date.now()
				func(...args)
			}, delay - (currentTime - lastExecTime))
		}
	} as T
}

export function debounce<T>(func: T, delay: number): T {
	let timer: ReturnType<typeof setTimeout> | null

	return function (...args: Parameters<T>): void {
		if (timer) {
			clearTimeout(timer)
		}

		timer = setTimeout(() => {
			func.apply(window, args)
			timer = null
		}, delay)
	} as T
}
// js节流

export function decreaseOpacity(color: string, amount: number) {
	// 解析颜色值
	const isHex = color.indexOf('#') === 0
	const isRgb = color.indexOf('rgb') === 0

	let r, g, b, a

	if (isHex) {
		color = color.slice(1) // 移除 #
		r = parseInt(color.substr(0, 2), 16)
		g = parseInt(color.substr(2, 2), 16)
		b = parseInt(color.substr(4, 2), 16)
		a = 1 // 默认不透明
	} else if (isRgb) {
		const rgba = color.substring(color.indexOf('(') + 1, color.lastIndexOf(')')).split(',')
		r = parseInt(rgba[0].trim())
		g = parseInt(rgba[1].trim())
		b = parseInt(rgba[2].trim())
		a = parseFloat(rgba[3].trim())
	} else {
		r = g = b = a = 0
	}

	// 降低透明度
	a = Math.max(0, Math.min(1, a - amount))

	return 'rgba(' + r + ', ' + g + ', ' + b + ', ' + a + ')'
}

export const convertPxToRem = (px: number) => {
	// 获取屏幕宽度
	const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth

	// 计算vw值
	const rem = (screenWidth / 1920) * px

	// 返回转换后的vw值
	return rem
}

export const convertPixelsToVw = (pixelWidth: any) => {
	// 获取视口宽度
	const viewportWidth = (window.innerWidth || document.documentElement.clientWidth) * 2

	// 计算vw单位
	const vwWidth = (pixelWidth / viewportWidth) * 100

	return vwWidth + 'vw'
}

/**
 * @description: 获取用户信息
 * @return {*}
 */
export const getUserInfo = () => {
	const userInfo = sessionStorage.getItem('userInfo')
	return userInfo ? JSON.parse(userInfo) : null
}
/**
 * @description: 获取用户信息中的某一项
 * @param {string} key
 * @return {*}
 */
export const getUserInfoItem = (key: string) => {
	const userInfo = getUserInfo()
	return userInfo ? userInfo[key] : null
}
/**
 * @description: 将url 和参数拼接， 返回完整的字符窜
 * @param {string} url
 * @param {object} params
 * @return {*}
 */
export const appendParamsToUrl = (url: string, params: { [key in string]: any }) => {
	// 将对象参数转换为字符串
	let paramsStr = Object.entries(params)
		.map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
		.join('&')

	// 拼接 URL 和参数
	let urlWithParams = url + '?' + paramsStr

	return urlWithParams
}
/**
 * @description: 生成 UUID
 * @return {*}
 */
export const generateUUID = () => {
	// 生成 RFC4122 版本 4 随机 UUID
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		const r = (Math.random() * 16) | 0
		const v = c === 'x' ? r : (r & 0x3) | 0x8

		return v.toString(16)
	})
}

// 弧度计算
export const getRadian = (angle: number) => {
	return (Math.PI / 180) * angle
}
// 传入dom元素，直接滚动到顶部
export const scrollToTop = (el: HTMLElement, behavior: 'smooth' | 'auto' = 'smooth') => {
	el.scrollTo({
		top: 0,
		behavior,
	})
}
// MeScroll 配置
export const MeScrollInit = (el: HTMLElement, props: any) => {
	const mescroll = new (MeScroll as any)(el, {
		down: {
			use: false,
		},
		up: {
			htmlNodata: ' ',
			htmlLoading: ' ',
			...props,
		},
	})
	return mescroll
}

export const functionMap = {
	determineUserType(job: string): number {
		return 1
	},
	transformData(input: any) {
		const userData = {
			userId: input.user_id,
			userName: input.username,
			headUrl: input.head_url,
			currentJob: input.current_job,
			birthday: input.birthday,
			age: parseInt(input.age),
			initDegree: input.diploma,
			initSchool: input.school,
			cadreIndex: input.cadre_index,
			userType: functionMap.determineUserType(input.current_job),
			highestDegree: input.diploma, // Adjust as needed based on your data
		}
		return userData
	},
}

export function convertCamelToUnderscore(obj: any): any {
	if (typeof obj !== 'object' || obj === null) {
		return obj
	}

	if (Array.isArray(obj)) {
		return obj.map((item: any) => convertCamelToUnderscore(item))
	}

	const result: any = {}
	for (const key in obj) {
		if (obj.hasOwnProperty(key)) {
			const convertedKey = key.replace(/([a-z0-9])([A-Z])/g, '$1_$2').toLowerCase()
			result[convertedKey] = convertCamelToUnderscore(obj[key])
		}
	}

	return result
}
// 高度更改
export const setEqualHeightInGroups = (parentElements: any) => {
	// 祖元素
	const heightMap: any = {}

	parentElements?.forEach((item: any) => {
		Array.from(item.children || []).forEach((el: any, index: number) => {
			heightMap[index] = heightMap[index] > el.clientHeight ? heightMap[index] : el.clientHeight
		})
	})
	parentElements?.forEach((item: any) => {
		Array.from(item.children || [])?.forEach?.((el: any, index: number) => {
			el.style.height = heightMap[index] + 'px'
		})
	})
}
/**
 * @description: 合并表格行，计算相同项的方法
 * @param {any} data
 * @param {any} key
 * @return {*}
 */
export const calculateRowSpan = (data: any, key: any) => {
	const calculatedData: any = []
	let currentGroup: any = []
	let currentRowCount = 0

	for (let i = 0; i < data.length; i++) {
		const currentValue = data[i][key]

		if (currentGroup.length === 0 || currentValue !== currentGroup[0][key]) {
			if (currentGroup.length > 0) {
				currentGroup[0].rowSpan = currentRowCount
				calculatedData.push(...currentGroup)
			}
			currentGroup = [data[i]]
			currentRowCount = 1
		} else {
			currentGroup.push(data[i])
			currentRowCount++
		}

		if (i === data.length - 1) {
			currentGroup[0].rowSpan = currentRowCount
			calculatedData.push(...currentGroup)
		}
	}

	return calculatedData
}

// 获取路径参数
export const getQueryVariable = () => {
	const query = window.location.href.split('?')[1]

	const urlParams = new URLSearchParams(query)

	const paramsMap: any = {}

	for (const pair of urlParams.entries()) {
		console.log(pair[0] + ', ' + pair[1])
		paramsMap[pair[0]] = pair[1]
	}

	return paramsMap
}
// 特征标签转换
export const convertMap = (data: Array<any>, sort = false) => {
	let positive_feature_map: any = []
	data?.map?.((item: any) => {
		const _index = positive_feature_map.find((item1: any) => item1.label === item)
		if (!_index) {
			positive_feature_map.push({
				label: item,
				count: 1,
			})
		} else {
			_index.count++
		}
	})
	if (sort) {
		positive_feature_map = positive_feature_map.sort((a: any, b: any) => b.count - a.count)
	}

	return positive_feature_map
}
