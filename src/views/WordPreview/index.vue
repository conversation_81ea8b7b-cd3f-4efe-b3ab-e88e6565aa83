<template>
	<div class="docWrap">
		<!-- 预览文件的地方（用于渲染） -->
		<div ref="file" id="wordView"></div>
		<div class="btn-box">
			<a-button type="primary" @click="onDownLoad">下载</a-button>
			<a-button type="primary" @click="onBack">返回</a-button>
			<template v-if="type == 5">
				<a-button type="primary" @click="activeKey = item.key" v-for="item in panes" :key="item.key">{{ item.title }}</a-button>
			</template>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as docx from 'docx-preview'
import { estimate } from '@/apis/sand-table-exercise'
import { createHeaders } from '@/utils/axios'
import axios from 'axios'
const padUrl = import.meta.env.VITE_APP_BASE_API
const route = useRoute()
const router = useRouter()

// const urlMap: any = {
// 	1: `/job/estimate`,
// 	2: `/job/candidate`,
// 	3: `/job/vote`,
// 	4: `/job/adjust`,
// 	5: ``,
// }
// const subUrl: any = {
// 	1: `/job/count_dept`,
// 	2: `/job/count_town`,
// }

const urlMap: any = {
	1: `/sand/estimate`,
	2: `/sand/candidate`,
	3: `/sand/vote`,
	4: `/sand/adjust`,
	5: ``,
}
const subUrl: any = {
	1: `/sand/count-dept`,
	2: `/sand/count-town`,
}

const panes = ref<{ title: string; key: string; closable?: boolean }[]>([
	{ title: '部门', key: '1' },
	{ title: '乡镇', key: '2' },
])
// mockid
const { mock_id, type } = route.query as any

const activeKey = ref('1')

const file = ref()

async function getWordText(data: string) {
	nextTick(() => {
		docx.renderAsync(data, file.value).catch((err: any) => {
			console.log(err)
		})
	})
}
const loadData = async (_type?: any, isDown?: boolean) => {
	let url = urlMap[type]

	if (type == '5') {
		url = subUrl[_type]
	}

	axios
		.get(`${padUrl}/pms${url}`, {
			params: {
				mock_id,
				type: 1,
			},
			responseType: 'arraybuffer',
			headers: createHeaders(),
		})
		.then((res: any) => {
			console.log('🚀 ~ loadData ~ res:', res)

			getWordText(res.data)

			if (!isDown) {
				return
			}
			// 转a标签下载
			const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })

			const downloadElement = document.createElement('a')

			const href = window.URL.createObjectURL(blob)

			downloadElement.href = href

			downloadElement.download = `预览文件.docx`

			downloadElement.click()
		})
}

const onBack = () => {
	router.back()
}

const onDownLoad = () => {
	loadData(activeKey.value, true)
}

watch(
	activeKey,
	() => {
		loadData(activeKey.value)
	},
	{
		immediate: true,
	}
)
</script>

<style lang="less" scoped>
.docWrap {
	width: 100%;
	height: 100%;
	#wordView {
		width: 100%;
		height: 100%;
	}
	.btn-box {
		position: fixed;
		top: 40vh;
		right: 10vw;
		display: flex;
		flex-direction: column;

		button {
			font-size: 21px;
			width: 120px;
			height: 54px;
			&:not(:first-child) {
				margin-top: 38px;
			}
		}
	}
	.tabs {
		position: fixed;
		bottom: 0px;
		left: 2vw;
		:deep(.ant-tabs-nav) {
			margin: 0px;
		}
	}
}
</style>
