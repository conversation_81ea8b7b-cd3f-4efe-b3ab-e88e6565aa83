<template>
	<Layout back title="干部巡察指数">
		<div class="inspection-index">
			<div class="top-base_info">
				<div class="base_info">
					<CodeAvatar :head_url="baseInfo.head_url" />
					<div class="info">
						<div class="name">{{ baseInfo.username }}</div>
						<div class="position">{{ baseInfo.current_job }}</div>
					</div>
				</div>
				<div class="inspection-box" :style="`background-image: ${determineColor(baseInfo.inspection_index).backgroundColor}`">
					<div class="top top-1">
						<div class="left">
							<span>巡察指数</span>
							<span class="icon"></span>
						</div>
					</div>
					<div class="middle">
						<div class="inline-box">
							<div class="index" :style="`color: ${determineColor(baseInfo.inspection_index).color};`">{{ baseInfo.inspection_index }}</div>
							<div class="rank" :style="`background: ${determineColor(baseInfo.inspection_index).color}`">
								<div class="text">{{ baseInfo.leader === 1 ? '同序列' : '班子内' }}</div>
								<div class="index_rank">{{ baseInfo.inspection_index_rank }}</div>
							</div>
						</div>
					</div>
					<div class="bottom">
						<div
							class="progress"
							:style="`width:${baseInfo.inspectionIndexPercent}%;background: ${determineColor(baseInfo.inspection_index).color}`"
						></div>
					</div>
				</div>
				<div class="tag_box">
					<div class="tag-box_title">特征标签</div>
					<div class="tag-content" @click="onTagModalOpen">
						<template v-for="(item, index) in baseInfo.positive_feature?.slice?.(0, 3)" :key="index">
							<PopTag :tag="item" :color="'rgba(0, 142, 255, 1)'" />
						</template>
						<template v-for="(item, index) in baseInfo.negative_feature?.slice?.(0, 3)" :key="index">
							<PopTag :tag="item" :color="'rgba(255, 106, 22, 1)'" />
						</template>
						<template v-for="(item, index) in baseInfo.other_feature?.slice?.(0, 3)" :key="index">
							<PopTag :tag="item" :color="'rgba(255, 163, 0, 1)'" />
						</template>
						<!-- <template v-for="(item, index) in baseInfo.integrity_tag" :key="index">
							<PopTag :tag="item" :color="'rgba(255, 163, 0, 1)'" />
						</template> -->
					</div>
				</div>
			</div>
			<LeaderIndexCharts class="h-533 m-top-20" />
			<PersonalAnalysis class="m-top-20" />
			<EvaluationComparison class="m-top-20" />
			<ApplicationInspectionResult title="巡察结果运用" header class="m-top-20" />
			<ConversationLevel title="谈话等次" header class="m-top-20"></ConversationLevel>
			<IntegrityRisk title="廉政风险" header class="m-top-20" :data="baseInfo.integrity_tag" />

			<a-modal class="tag-ant-modal" :visible="tagVisible" @cancel="onTagModalClose" title="特征标签" :footer="null">
				<div class="modal-content">
					<div class="tag-content">
						<div class="inner">
							<!-- <template v-for="item in userInfo.feature_list">
							<TagBox color="#008EFF" :data="item.positive_feature_map" />
							<TagBox color="#FF6A16" :data="item.negative_feature_map" />
						</template> -->
							<TagBox color="#008EFF" :data="baseInfo.positive_feature || []" />
							<TagBox color="#FF6A16" :data="baseInfo.negative_feature || []" />
						</div>
						<div class="legend">
							<div class="tag-1">干部特点</div>
							<div class="tag-2">主要不足</div>
						</div>
					</div>
					<div class="other-content">
						<div class="title">其他标签</div>
						<div class="inner" style="border-bottom: none">
							<TagBox color="#60CA71" :data="baseInfo.other_feature || []" />
						</div>
					</div>
				</div>
			</a-modal>
			<Echarts$4 style="width: 100%; height: 400px" />
		</div>
	</Layout>
</template>

<script lang="ts" setup>
import { ref, provide } from 'vue'
import { useRoute } from 'vue-router'
import Layout from '@/layout/index.vue'
import TagBox from '@/components/TagBox.vue'
import PopTag from '@/components/Tag.vue'
import Card from '@/components/Card.vue'
import LeaderIndexCharts from './components/LeaderIndexCharts.vue'
import PersonalAnalysis from './components/InspectionAndEvaluation.vue'
import EvaluationComparison from './components/EvaluationComparison.vue'
import ApplicationInspectionResult from './components/ApplicationInspectionResults.vue'
import ConversationLevel from './components/ConversationLevel.vue'
import IntegrityRisk from './components/IntegrityRisk.vue'
import Echarts$4 from '@/components/Echarts$4.vue'
import { getInspectionIndexTag } from '@/apis/inspection-index'
import { convertMap } from '@/utils/utils'

const route = useRoute()

const { user_id } = route.query

const baseInfo = ref({
	username: '',
	head_url: '',
	current_job: '',
	leader: 1,
	inspection_index: 0,
	inspection_index_rank: '0/0',
	positive_feature: [],
	negative_feature: [],
	other_feature: [],
	integrity_tag: [],
	inspectionIndexPercent: 0,
})

const tagVisible = ref(false)

const onTagModalClose = () => {
	tagVisible.value = false
}

const onTagModalOpen = () => {
	tagVisible.value = true
}

const determineColor = (value: number) => {
	const colorMap = {
		color: '',
		backgroundColor: '',
	}

	if (value >= -10) {
		colorMap.color = 'rgba(96, 202, 113, 1)'
		colorMap.backgroundColor = 'linear-gradient(270deg, #EAFFEB 0%, #FFFFFF 100%);'
	} else if (value >= -15) {
		colorMap.color = 'rgba(255, 128, 0, 1)'
		colorMap.backgroundColor = 'linear-gradient( 270deg,#FFF6ED 0%, #FFFFFF 100%);'
	} else {
		colorMap.color = 'rgba(180, 105, 0, 1)'
		colorMap.backgroundColor = 'linear-gradient( 270deg, #F6EDE0 0%, rgba(255,255,255,0) 100%);'
	}
	console.log(colorMap)
	return colorMap
}

const initData = async () => {
	console.log(user_id)

	const res = await getInspectionIndexTag({ user_id })

	if (res.code === 0) {
		const { inspection_index, positive_feature, negative_feature, other_feature, integrity_tag } = res.data
		// 几个标签转换

		baseInfo.value = {
			...res.data,
			inspectionIndexPercent: inspection_index ? (inspection_index > 100 ? 100 : inspection_index) : 0,
			positive_feature: convertMap(positive_feature),
			negative_feature: convertMap(negative_feature),
			other_feature: convertMap(other_feature),
			integrity_tag: convertMap(integrity_tag),
		}
	}
}

initData()
</script>

<style lang="scss" scoped>
.inspection-index {
	padding: 16px;
	height: 100%;
	overflow: auto;
	.top-base_info {
		display: flex;
		gap: 0px 20px;
		height: 216px;
		.base_info {
			flex: 1;
			height: 100%;
			display: flex;
			padding: 23px 15px;
			background: linear-gradient(90deg, #ffffff 0%, #ecf5ff 100%);
			box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
			border-radius: 8px 8px 8px 8px;
			.info {
				margin-left: 20px;
				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 28px;
					color: #000000;
					line-height: 33px;
					text-align: left;
				}
				.position {
					margin-top: 6px;
					font-size: 20px;
					color: #333333;
					// 三行
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 3;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}
		.inspection-box {
			flex: 1;
			padding: 33px 32px;
			width: 49%;
			height: 100%;
			background: linear-gradient(305deg, #e5fee6 0%, #ffffff 100%);
			border-radius: 8px 8px 8px 8px;
			opacity: 1;
			box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
			.top {
				font-size: 24px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: 28px;

				display: flex;
				align-items: center;
				.right {
					font-size: 22px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #2462ff;
					cursor: pointer;
				}
				.warn-right {
					display: flex;
					justify-content: space-between;
					align-items: center;
					min-width: 113px;
					padding: 10px;
					height: 40px;
					background: #ffa300;
					border-radius: 6px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					font-size: 20px;
					color: #ffffff;
					line-height: 23px;
					&::before {
						content: '';
						display: inline-block;
						height: 24px;
						width: 24px;
						background: url('../../images/cadre-card-icon.png') center / cover no-repeat;
						transition: all linear 0.3s;
					}
				}
				.warn-right-4 {
					background: #d23122 !important;
					// &::before {
					// 	background: url('../../images/cadre-card-icon2.png') center / cover no-repeat !important;
					// }
				}
				.drop-icon-rotate {
					&::after {
						transform: rotateZ(180deg);
					}
				}
				.drop-icon {
					position: relative;
					&::after {
						content: '';
						display: inline-block;
						width: 24px;
						height: 24px;
						background: url('../../images/cadre-card-icon1.png') center / cover no-repeat;
					}
					.drop-menu {
						position: absolute;
						top: 100%;
						right: 0px;
						display: flex;
						flex-direction: column;
						gap: 14px 0px;
						width: 208px;
						min-height: 131px;
						padding: 18px 12px;
						background: #ffffff;
						box-shadow: 0px 0 8px 0px rgba(0, 0, 0, 0.1);
						border-radius: 6px 6px 6px 6px;
						.menu-item {
							padding: 8px 9px;
							background: rgba(255, 163, 0, 0.1);
							border-radius: 6px 6px 6px 6px;
						}
						.level {
							display: flex;
							align-items: center;
							width: 90%;
							&::before {
								margin-right: 5px;
							}
						}

						.level-4 {
							font-size: 20px;
							color: #d23122 !important;
							background: rgba(210, 49, 34, 0.1) !important;
							&::before {
								content: '';
								display: inline-block;
								width: 22px;
								height: 22px;
								background: url('../../images/cadre-card-icon2.png') center / cover no-repeat !important;
								vertical-align: top;
							}
						}
						.level-normal {
							font-size: 20px;
							color: #ffa300;
							&::before {
								content: '';
								display: inline-block;
								width: 22px;
								height: 22px;
								background: url('../../images/cadre-card-icon3.png') center / cover no-repeat;
								vertical-align: top;
							}
						}
					}
				}
			}
			.top-1 {
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
			.top-2 {
				display: flex;
				justify-content: space-between;
			}
			.middle {
				width: 100%;
				height: 50px;
				margin-top: 26px;
				.inline-box {
					display: flex;
					align-items: flex-end;
					width: 100%;
					height: 100%;
					span {
					}
				}
				.index {
					line-height: 48px;
					font-size: 50px;
					font-weight: bold;
					font-family: 'BAKHUM';
					color: rgba(210, 49, 34, 1);
				}
				.rank {
					display: flex;
					flex-direction: column;
					justify-content: center;
					margin-left: 27px;
					padding: 4px 0px;
					width: 96px;
					text-align: center;
					background-color: rgba(210, 49, 34, 1);
					color: rgba(255, 255, 255, 1);
					border-radius: 2px 2px 2px 2px;
					opacity: 1;
					.text {
						font-size: 14px;
						line-height: 1;
					}
					.index_rank {
						margin-top: 4px;
						font-size: 20px;
						line-height: 20px;
					}
				}
			}
			.bottom {
				margin-top: 16px;
				width: 100%;
				height: 12px;
				background: #f5f5f5;
				border-radius: 30px 30px 30px 30px;
				opacity: 1;
				overflow: hidden;
				.progress {
					height: 12px;
					width: 50%;
					background-color: rgba(210, 49, 34, 1);
					border-radius: 30px 30px 30px 30px;
					opacity: 1;
				}
			}
		}
		.tag_box {
			width: 768px;
			height: 100%;
			background: linear-gradient(305deg, #fff3de 0%, #ffffff 100%);
			box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
			border-radius: 8px 8px 8px 8px;
			padding: 32px 24px;
			overflow: hidden;
			.tag-box_title {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 24px;
				color: #333333;
			}
			.tag-content {
				margin-top: 24px;
				display: flex;
				flex-wrap: wrap;
				gap: 24px;
			}
		}
	}
	:deep(.table-header-td) {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 20px;
		line-height: 20px;
		color: #3d3d3d;
	}

	:deep(.table-body-td) {
		font-weight: 400;
		font-size: 18px;
		color: rgba(0, 0, 0, 0.9);
	}
}
</style>
<style lang="less">
.tag-ant-modal {
	width: 1300px !important;
	.ant-modal-title {
		font-size: 24px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		text-align: center;
	}
	.tag-content,
	.other-content {
		background: #f7fbff;
		.inner {
			padding: 24px 24px 50px;
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
		}
		.legend {
			padding: 12px 0px;
			display: flex;
			justify-content: center;
			gap: 30px;
			.tag-1,
			.tag-2 {
				display: flex;
				align-items: center;
				font-size: 16px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				&::before {
					content: '';
					display: inline-block;
					width: 8px;
					height: 8px;
					margin-right: 4px;
				}
			}
			.tag-1 {
				&::before {
					background: #008eff;
				}
			}
			.tag-2 {
				&::before {
					background: #ff6a16;
				}
			}
		}
	}
	.other-content {
		padding: 24px;
		margin-top: 20px;
		height: 220px;
		background: #f6fff7;
		border-radius: 8px 8px 8px 8px;
		.title {
			display: flex;
			align-items: center;
			font-size: 22px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			line-height: 22px;
			&::before {
				content: '';
				margin-right: 7px;
				display: inline-block;
				width: 8px;
				height: 24px;
				background: url('@/assets/images/card-icon.png') no-repeat center / cover;
			}
		}
	}
}
</style>
