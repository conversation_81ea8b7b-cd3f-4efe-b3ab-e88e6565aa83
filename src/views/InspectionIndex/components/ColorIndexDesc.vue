<template>
	<div class="color-index-desc">
		<div class="color-index" :class="{ clickClass: click }" @click="onHandle(item)" v-for="(item, index) in data || colorList" :key="index">
			<span
				class="icon"
				:style="{
					'--color': item.color,
				}"
			></span>
			<span class="label">{{ item.label }}</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue'
defineProps({
	data: Array as PropType<any>,
	click: Boolean,
})
const emits = defineEmits(['select'])
const colorList = [
	{
		color: 'rgba(96, 202, 113, 1)',
		label: '-10（含）以上',
	},
	{
		color: 'rgba(255, 128, 0, 1)',
		label: '-15（含）~-10',
	},
	{
		color: 'rgba(180, 105, 0, 1)',
		label: '-15以下',
	},
]

const onHandle = (item: any) => {
	emits('select', item)
}
</script>

<style lang="less" scoped>
.color-index-desc {
	display: flex;
	flex-wrap: wrap;
	gap: 0px 30px;
	.clickClass {
		cursor: pointer;
	}
	.color-index {
		display: flex;
		align-items: center;
		.icon {
			margin-right: 4px;
			display: inline-block;
			background-color: var(--color);
			width: 12px;
			height: 12px;
		}
		.label {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 18px;
			line-height: 1;
			color: rgba(0, 0, 0, 0.65);
		}
	}
}
</style>
