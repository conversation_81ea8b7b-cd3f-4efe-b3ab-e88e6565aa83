<template>
	<Card title="谈话等次" header style="height: auto">
		<div class="conversation-level">
			<DataTable :data-source="data" :columns="columns">
				<template v-slot:down="{ value }">
					<div class="table-tag-box">
						<TagBox :data="value" :color="'rgba(0, 142, 255, 1)'" />
					</div>
				</template>
				<template v-slot:same="{ value }">
					<div class="table-tag-box">
						<TagBox :data="value" :color="'rgba(0, 142, 255, 1)'" />
					</div>
				</template>
				<template v-slot:upp="{ value }">
					<div class="table-tag-box">
						<TagBox :data="value" :color="'rgba(0, 142, 255, 1)'" />
					</div>
				</template>
				<template v-slot:score="{ value }">
					<span class="score">{{ value }}</span>
				</template>
			</DataTable>
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Card from '@/components/Card.vue'
import DataTable from '@/components/Table.vue'
import TagBox from '@/components/TagBox.vue'
import { getInspectionIndexSpeechRating } from '@/apis/inspection-index'
import { useRoute, useRouter } from 'vue-router'
import { convertMap } from '@/utils/utils'
const { user_id } = useRoute().query
const data = ref<any>([])

const columns = ref([
	{
		title: '巡察组',
		dataIndex: 'down',
		key: 'down',
		align: 'center',
		width: '30%',
	},
	{
		title: '班子成员',
		dataIndex: 'same',
		key: 'same',
		align: 'center',
		width: '20%',
	},
	{
		title: '其他干部',
		dataIndex: 'upp	',
		key: 'upp',
		align: 'center',
		width: '20%',
	},
	{
		title: '综合得分（25）',
		dataIndex: 'score',
		key: 'score',
		align: 'center',
		width: '20%',
	},
])

const formartData = (data: any) => ({
	down: convertMap(data.down),
	same: convertMap(data.same),
	upp: convertMap(data.upp),
	score: data.score,
})

const loadData = async () => {
	// data.value = [
	// 	formartData({
	// 		user_id: 123,
	// 		score: 1.0,
	// 		down: ['A', 'A', 'B', 'B'],
	// 		same: ['A', 'B'],
	// 		upp: ['A', 'B'],
	// 	}),
	// ]

	const res = await getInspectionIndexSpeechRating({ user_id })
	if (res.code === 0 && res.data) {
		data.value = [formartData(res.data)]
	}
}

loadData()
</script>

<style lang="scss" scoped>
.conversation-level {
	.bkoufen {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 18px;
		color: #ee391f;
	}

	.table-tag-box {
		display: flex;
		justify-content: center;
	}

	.score {
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 40px;
		color: #d23122;
		line-height: 47px;
		text-align: center;
	}
}
</style>
