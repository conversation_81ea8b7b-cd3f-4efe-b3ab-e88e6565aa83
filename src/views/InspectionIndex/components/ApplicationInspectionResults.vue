<template>
	<Card title="巡察结果运用" header style="height: auto">
		<div class="application-inspection-results">
			<DataTable :data-source="tableData" :columns="columns">
				<template v-slot:my_score="{ value }">
					<span class="my_score">{{ value }}</span>
				</template>
				<template v-slot:type="{ value }">
					<span class="type">{{ getTypeText(value) }}</span>
				</template>
			</DataTable>
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Card from '@/components/Card.vue'
import DataTable from '@/components/Table.vue'
import { getInspectionIndexOrgInspectionUse } from '@/apis/inspection-index'
import { useRoute } from 'vue-router'

const { user_id } = useRoute().query

const tableData = ref([])
const typeMap = [
	{
		id: 1,
		type: '直接责任',
	},
	{
		id: 2,
		type: '主要领导责任',
	},
	{
		id: 3,
		type: '重要领导责任',
	},
	{
		id: 4,
		type: '一把手',
	},
	{
		id: 5,
		type: '乡镇长',
	},
]

// 根据type获取文本
const getTypeText = (type: number) => {
	return typeMap.find((item) => item.id === type)?.type
}

const columns = ref([
	{
		title: '扣分指标',
		dataIndex: 'item',
		key: 'item',
		align: 'center',
		width: '30%',
	},
	{
		title: '具体问题',
		dataIndex: 'question',
		key: 'question',
		align: 'center',
		width: '20%',
	},
	{
		title: '班子扣分',
		dataIndex: 'org_score',
		key: 'org_score',
		align: 'center',
		width: '10%',
	},
	{
		title: '承担责任',
		dataIndex: 'type',
		key: 'type',
		align: 'center',
		width: '20%',
	},
	{
		title: '本人扣分',
		dataIndex: 'my_score',
		key: 'my_score',
		align: 'center',
		width: '10%',
	},
])

const loadData = async () => {
	const res = await getInspectionIndexOrgInspectionUse({ user_id })
	if (res.code === 0) {
		tableData.value = res.data
	}
}

loadData()
</script>

<style lang="scss" scoped>
.application-inspection-results {
	.my_score {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 18px;
		color: #ee391f;
	}
}
</style>
