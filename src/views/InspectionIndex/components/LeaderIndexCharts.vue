<template>
	<Card style="height: auto" title="干部巡察指数" header size-type="2">
		<template v-slot:right>
			<div class="data-menu" v-if="user.detail.promotion_type !== 4">
				<div :class="['menu-item', coor_menu_selected === item.key && 'menu-active']" v-for="item in menu" :key="item.key" @click="onMenu(item.key)">
					{{ item.label }}
				</div>
			</div>
		</template>
		<div class="inspection-inner-box">
			<div class="pyramid">
				<div :class="`pyramid-item pyramid-${item.key}`" v-for="item in pyramidList" :key="item.key" @click="onActive(item.key)">
					<div class="pyramid-img">
						<div class="pyramid-label">{{ item.label }}</div>
						<img :src="item.img" alt="" />
						<div class="select" v-if="activeInfo.pyramid_index === item.key - 1">
							<div class="select-line"></div>
							<div class="text">{{ activeInfo.username }} {{ activeInfo.inspection_index }}</div>
						</div>
						<div class="splitLine">
							<div class="line-box">
								<div class="left-line"></div>
								<div class="right-line"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="table-box">
				<DataTable :data-source="data" :columns="columns" ref="table" row-id="user_id" :row-click="onRowClick" body-scroll>
					<template v-slot:rank="{ value }">
						<span v-if="value > 3">{{ value }}</span>
						<span v-else :class="`rank-icon rank-${value}`"></span>
					</template>
					<template v-slot:portrait="{ data }">
						<span class="custom-portrait" @click="onLocation(data)"></span>
					</template>

					<template v-slot:same_rank="{ value }">{{ value }} </template>
				</DataTable>
			</div>
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { defineComponent, ref, computed, watch, toRefs, onMounted, inject } from 'vue'
import { useRoute } from 'vue-router'
import { getPyramid } from '@/apis/cadre-portrait/home'
import { useHomeStore } from '@/store/home'
import useUser from '@/store/user'
import DataTable from '@/components/Table.vue'
import { historyPush } from '@/utils/history'

import ImgPyramid1 from '@/assets/images/pyramid-1.png'
import ImgPyramid2 from '@/assets/images/pyramid-2.png'
import ImgPyramid3 from '@/assets/images/pyramid-3.png'
import ImgPyramid4 from '@/assets/images/pyramid-4.png'
import { debounce, Base64, getQueryVariable } from '@/utils/utils'

import { getInspectionIndexPyramidList } from '@/apis/inspection-index'

const urlParams = getQueryVariable()
console.log('🚀 ~ urlParams:', urlParams)
const { user_id } = useRoute().query
// 0-全区, 1-本单位, 2-同序列, 3-全部乡镇/部门
const menu = ref([
	{
		label: '本单位',
		key: 1,
	},
	{
		label: '同序列',
		key: 2,
	},
	{
		label: '乡镇/部门',
		key: 3,
	},
	{
		label: '全区',
		key: 4,
	},
])

const columns = [
	{
		key: 'rank',
		align: 'center',
		width: '6%',
		title: '排名',
	},
	{
		key: 'username',
		align: 'center',
		width: '9%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (data: any, event: any) => {
			event.stopPropagation()

			onLocation(data)
		},
	},
	{
		key: 'current_job',
		align: 'left',
		width: '25%',
		title: '职务',
	},
	{
		key: 'inspection_index',
		align: 'center',
		width: '12%',
		title: '巡察指数',
	},
]
const coor_menu_selected = ref<any>(1)
const store = useHomeStore()
const user = useUser()
const table = ref()
const activeRow = ref(1)
const activeInfo = ref<any>({
	username: '',
	inspection_index: 0,
	pyramid_index: 1,
})
const { coor_user_id } = toRefs(store)
// 用户信息

const data = ref<any>([])

const pyramidList = computed(() => {
	const list = [
		{
			img: ImgPyramid1,
			label: '10%',
			key: 1,
		},
		{
			img: ImgPyramid2,
			label: '20%',
			key: 2,
		},
		{
			img: ImgPyramid3,
			label: '30%',
			key: 3,
		},
		{
			img: ImgPyramid4,
			label: '40%',
			key: 4,
		},
	]
	return list
})

const onMenu = (key: number) => {
	// currentIndex.value = key
	coor_menu_selected.value = key
}
const onRowClick = async (data: any) => {
	const flag = coor_menu_selected.value

	const res = await getInspectionIndexPyramidList({ user_id: data.user_id, flag })

	if (res.code === 0 && res.data) {
		activeInfo.value = res.data

		table.value.rowHighLight?.(data.user_id)
	}
}
/**
 * @description: 加载数据
 * @return {*}
 */
const loadData = async (user_id: any) => {
	console.log('🚀 ~ loadData ~ user_id:', user_id)
	if (!user_id) return

	const flag = coor_menu_selected.value

	const res = await getInspectionIndexPyramidList({ user_id, flag })

	if (res.code === 0 && res.data) {
		activeInfo.value = res.data

		data.value = res.data.user_list
	}
}
const onActive = (key: number) => {
	activeRow.value = key
}
const onLocation = (data: any, event?: any) => {
	const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
	userInfo.user_id = data.user_id

	historyPush({
		path: `/inspection-index`,
		query: {
			...urlParams,
			user_id: data.user_id,
		},
	})
}
// 监听currentIndex变化 更新图表数据
watch(
	coor_menu_selected,
	debounce<any>((newValue, oldValue) => {
		console.log(newValue, oldValue)
		newValue !== oldValue && loadData(user_id)
	}, 500),
	{
		immediate: true,
	}
)
/**
 * @description:  人员切换响应
 * @return {*}
 */
watch(coor_user_id, (newValue, oldValue) => {
	if (coor_user_id.value !== '-1' && newValue !== oldValue) {
		loadData(coor_user_id.value)
	}
})

onMounted(() => {
	// 角度
	const angle = 30
	// 规定邻边的长度
	const adjacent = 100
	// 获取对应容器中心点
	const primary1 = document.querySelector('.pyramid-1')
	// 存放斜边坐标点
	const axisArray = []
	// 获取宽度
	if (primary1) {
		const width = primary1.clientWidth
		// 获取中心点
		const center = width / 2
		// 计算正切
		const tan = Math.tan((angle * Math.PI) / 180)
		// 计算对边的长度
		const opposite = tan * adjacent

		axisArray.push([center + opposite, adjacent])
	}

	// function getPointIn
})
</script>

<style scoped lang="less">
.data-menu {
	display: flex;
	align-items: center;
	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		font-weight: 500;
		color: #666666;
		// line-height: 28px;
		cursor: pointer;
	}
	.menu-active {
		color: #333333;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #ff2121;
		border-radius: 2px 2px 2px 2px;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translate(-50%, 0);
	}
}

::v-deep(.pyramid) {
	padding: 0px 29px 27px 10px;
}
.inspection-inner-box {
	display: flex;
	padding: 0px 15px;
	.pyramid {
		width: 634px;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		user-select: none;
		.pyramid-item {
			position: relative;
			transform: translate(-60px);

			.pyramid-img {
				position: relative;
				margin: 0 auto;
				.pyramid-label {
					position: absolute;
					top: 40%;
					left: -20px;
					font-size: 22px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #666666;
					line-height: 26px;
				}
				img {
					width: 100%;
					height: 100%;
				}
				.select {
					position: absolute;
					top: 0;
					left: 100%;
					display: flex;
					align-items: center;
					width: 341px;
					height: 75.5px;
					-webkit-clip-path: polygon(0 0, 100% 0%, 100% 100%, 7% 100%);
					clip-path: polygon(0 0, 100% 0%, 100% 100%, 12% 100%);
					.select-line {
						height: 1px;
						background: #278236;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
					}
					.text {
						margin-left: 10px;
						font-size: 22px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #278236;
						line-height: 26px;
					}
				}
				.splitLine {
					position: absolute;
					inset: 0;
					// background-color: rgba(0, 0, 0, 0.9);
					.line-box {
						.left-line {
						}
						.right-line {
						}
					}
				}
			}
			&:nth-child(2) {
				margin-top: -10px;
			}
			&:nth-child(3) {
				margin-top: -20px;
			}
			&:nth-child(4) {
				margin-top: -20px;
			}
		}
		.pyramid-1 {
			.pyramid-img {
				width: 74px;
				height: 86px;

				.pyramid-label {
					transform: translateX(-15px);
				}
			}
			.select {
				width: 341px;
				background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
				transform: translate(-40px);
				.select-line {
					width: 191px;
					background-color: #278236;
				}
				.text {
					color: #278236;
				}
			}
		}
		.pyramid-2 {
			.pyramid-img {
				width: 162.23px;
				height: 93.9px;
				.pyramid-label {
					left: -30px !important;
				}
			}
			.select {
				width: 271px;
				background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
				transform: translate(-40px);
				.select-line {
					width: 146px;
					background-color: #278236;
				}
				.text {
					color: #278236;
				}
			}
		}
		.pyramid-3 {
			.pyramid-img {
				width: 265.23px;
				height: 112px;
			}
			.select {
				width: 271px;
				background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
				transform: translate(-45px, 10px);
				.select-line {
					width: 126px;
					background-color: #278236;
				}
				.text {
					color: #278236;
				}
			}
		}
		.pyramid-4 {
			.pyramid-img {
				width: 384.87px;
				height: 140.51px;
			}
			.select {
				width: 271px;
				background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
				transform: translate(-50px, 20px);
				.select-line {
					width: 66px;
					background-color: #278236;
				}
				.text {
					color: #278236;
				}
			}
		}
	}

	.table-box {
		flex: 1;
		display: flex;
		flex-direction: column;
		// margin-top: 16px;
		overflow: auto;
		padding: 0px 0px;
		max-height: 414.5px;
		// 隐藏滚动条
		&::-webkit-scrollbar {
			display: none;
		}

		.box-content__line {
			height: 414.5px;
		}

		.box-content__table {
			flex: 1;
		}
		.collection-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0px auto;
			width: 72px;
			height: 32px;
			background: #ffffff;
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #e5251b;

			font-weight: 400;
			font-size: 18px;
			color: #e5251b;
			line-height: 21px;
		}
		.gray-status {
			border: 1px solid rgba(0, 0, 0, 0.25);
			color: #999999;
		}

		.rank-icon {
			display: inline-block;
			width: 28px;
			height: 28px;
			vertical-align: middle;
		}
		.rank-1 {
			background: url('@/assets/images/rank-1.png') no-repeat center / contain;
		}
		.rank-2 {
			background: url('@/assets/images/rank-2.png') no-repeat center / contain;
		}
		.rank-3 {
			background: url('@/assets/images/rank-3.png') no-repeat center / contain;
		}
	}
}
</style>
