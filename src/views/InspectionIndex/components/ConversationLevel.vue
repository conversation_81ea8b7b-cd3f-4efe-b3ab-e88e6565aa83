<template>
	<Card title="谈话等次" header style="height: auto">
		<div class="conversation-level">
			<div class="table-tag-box">
				<div class="tag-item">
					<CircleTitle title="综合计分" />
					<div class="score-box">
						<div class="sc-b-score">
							{{ data.score || '-' }}
						</div>
					</div>
				</div>
				<div class="tag-item">
					<CircleTitle title="巡察组" />
					<div class="tag-box">
						<PopTag :tag="tag" v-for="(tag, index) in data.down" :key="index" :color="getColor" />
					</div>
				</div>
				<div class="tag-item">
					<CircleTitle title="班子成员" />
					<div class="tag-box">
						<PopTag :tag="tag" v-for="(tag, index) in data.same" :key="index" :color="getColor" />
					</div>
				</div>
				<div class="tag-item">
					<CircleTitle title="其他干部" />
					<div class="tag-box">
						<PopTag :tag="tag" v-for="(tag, index) in data.upp" :key="index" :color="getColor" />
					</div>
				</div>
			</div>
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Card from '@/components/Card.vue'
import DataTable from '@/components/Table.vue'
import TagBox from '@/components/TagBox.vue'
import PopTag from '@/components/Tag.vue'
import CircleTitle from './CircleTitle.vue'
import { getInspectionIndexSpeechRating } from '@/apis/inspection-index'
import { useRoute, useRouter } from 'vue-router'
import { convertMap } from '@/utils/utils'
const { user_id } = useRoute().query
const data = ref<any>([])

const columns = ref([
	{
		title: '巡察组',
		dataIndex: 'down',
		key: 'down',
		align: 'center',
		width: '30%',
	},
	{
		title: '班子成员',
		dataIndex: 'same',
		key: 'same',
		align: 'center',
		width: '20%',
	},
	{
		title: '其他干部',
		dataIndex: 'upp	',
		key: 'upp',
		align: 'center',
		width: '20%',
	},
	{
		title: '综合得分（25）',
		dataIndex: 'score',
		key: 'score',
		align: 'center',
		width: '20%',
	},
])
const getColor: any = (text: any) => {
	const colorMap: any = {
		A: 'rgba(96, 202, 113, 1)',
		B: 'rgba(255, 128, 0, 1)',
		C: 'rgba(180, 105, 0, 1)',
		D: 'rgba(230, 177, 39, 1)',
	}
	return colorMap[text]
}

const formartData = (data: any) => ({
	down: convertMap(data.down),
	same: convertMap(data.same),
	upp: convertMap(data.upp),
	score: data.score,
})

const loadData = async () => {
	// data.value = [
	// 	formartData({
	// 		user_id: 123,
	// 		score: 1.0,
	// 		down: ['A', 'A', 'B', 'B'],
	// 		same: ['A', 'B'],
	// 		upp: ['A', 'B'],
	// 	}),
	// ]

	const res = await getInspectionIndexSpeechRating({ user_id })
	if (res.code === 0 && res.data) {
		data.value = formartData(res.data)
		console.log(data.value)
	}
}

loadData()
</script>

<style lang="scss" scoped>
.conversation-level {
	.bkoufen {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 18px;
		color: #ee391f;
	}

	.table-tag-box {
		display: flex;
		gap: 0px 36px;
		.tag-item {
			display: flex;
			flex-direction: column;
			padding: 20px 24px;
			flex: 1;
			height: 216px;
			background: #f7f7f7;

			.tag-box {
				padding: 26px 0px;
				flex: 1;
				display: flex;
				flex-wrap: wrap;
				align-items: flex-start;
				gap: 10px 50px;
			}
		}
	}

	.score-box {
		flex: 1;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		.sc-b-score {
			padding-top: 13px;
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 40px;
			color: #3687ff;
			line-height: 47px;
			text-align: center;
			width: 302px;
			height: 146px;
			background: url('../images/score-1.png') no-repeat center / cover;
		}
	}
}
</style>
