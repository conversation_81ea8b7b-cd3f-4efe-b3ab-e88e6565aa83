<template>
	<div class="base-info-box">
		<div class="left-box">
			<div class="base_info h-260" :class="getClassName1(baseInfo.inspection_index)">
				<CodeAvatar class="avatar" :head_url="baseInfo.head_url" />
				<div class="info">
					<div class="name">{{ baseInfo.username }}</div>
					<div class="position m-top-10">{{ baseInfo.current_job }}</div>
					<div class="xuncha-index-box">
						<div class="xc-index">
							<div class="xuncha-title">巡察指数</div>
							<div class="same-xu" @click="onSequenceModal({ user_id: baseInfo.user_id })">
								{{ baseInfo.in_org_flag === 0 ? '同序列' : '班子内' }}&nbsp;{{ baseInfo.inspection_index_rank }}
							</div>
							<div class="dash-line"></div>
							<div class="xuncha-index">{{ baseInfo.inspection_index }}</div>
						</div>
					</div>
					<div class="bottom m-top-10">
						<div class="progress" v-if="baseInfo.inspection_index" :style="`width:${100 + baseInfo.inspection_index}%`"></div>
					</div>
					<ColorIndexDesc class="m-top-14" />
				</div>
			</div>
			<Card title="班子巡察指数" class="right-box h-508 m-top-16" header size-type="2">
				<div class="content-inner">
					<Pyramid :active-info="activeInfo" />
				</div>
			</Card>
		</div>
		<div class="right-box">
			<Card title="特征标签" class="h-527 shadow" header size-type="2">
				<div class="tag-content" @click="onTagModalOpen" v-if="tagListLength !== 0">
					<!-- <div>
						<template v-for="(item, index) in " :key="index">
							<PopTag :tag="item" :color="'rgba(0, 142, 255, 1)'" />
						</template>
					</div> -->
					<TagBox :data="baseInfo.positive_feature?.slice?.(0, 10)" color="rgba(0, 142, 255, 1)" />
					<TagBox :data="baseInfo.negative_feature?.slice?.(0, 10)" color="rgba(255, 106, 22, 1)" />

					<!-- <template v-for="(item, index) in baseInfo.integrity_tag" :key="index">
							<PopTag :tag="item" :color="'rgba(255, 163, 0, 1)'" />
						</template> -->
				</div>
				<template v-else>
					<a-empty />
				</template>
			</Card>
			<Card title="廉政风险" class="h-242 m-top-16 shadow" header size-type="2">
				<div class="intergrity-risk over-scroll-auto" v-if="baseInfo.integrity_tag?.length">
					<TagBox :data="baseInfo.integrity_tag" :color="'rgba(255, 163, 0, 1)'" />
				</div>
				<template v-else>
					<a-empty />
				</template>
			</Card>
		</div>

		<a-modal class="tag-ant-modal" :visible="tagVisible" @cancel="onTagModalClose" title="特征标签" :footer="null">
			<div class="modal-content">
				<div class="tag-content">
					<div class="inner">
						<!-- <template v-for="item in userInfo.feature_list">
							<TagBox color="#008EFF" :data="item.positive_feature_map" />
							<TagBox color="#FF6A16" :data="item.negative_feature_map" />
						</template> -->
						<TagBox color="#008EFF" :data="baseInfo.positive_feature || []" />
						<TagBox color="#FF6A16" :data="baseInfo.negative_feature || []" />
					</div>
					<div class="legend">
						<div class="tag-1">干部特点</div>
						<div class="tag-2">主要不足</div>
					</div>
				</div>
				<div class="other-content">
					<div class="title">其他标签</div>
					<div class="inner" style="border-bottom: none">
						<TagBox color="#60CA71" :data="baseInfo.other_feature || []" />
					</div>
				</div>
			</div>
		</a-modal>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { getInspectionIndexTag } from '@/apis/inspection-index'
import { useRoute } from 'vue-router'
import { convertMap } from '@/utils/utils'
import ColorIndexDesc from './ColorIndexDesc.vue'
import { getInspectionIndexPyramidList } from '@/apis/inspection-index'
import PopTag from '@/components/Tag.vue'
import TagBox from '@/components/TagBox.vue'
import ImgPyramid1 from '@/assets/images/pyramid-1.png'
import ImgPyramid2 from '@/assets/images/pyramid-2.png'
import ImgPyramid3 from '@/assets/images/pyramid-3.png'
import ImgPyramid4 from '@/assets/images/pyramid-4.png'
import Pyramid from '@/components/Pyramid.vue'
import { SequenceModal } from '@/views/DataScreen/Components/SequenceModal'

const route = useRoute()

const { user_id } = route.query
// 用户信息

const data = ref<any>([])
const baseInfo = ref({
	username: '',
	head_url: '',
	current_job: '',
	leader: 1,
	inspection_index: 0,
	inspection_index_rank: '0/0',
	positive_feature: [],
	negative_feature: [],
	other_feature: [],
	integrity_tag: [],
	inspectionIndexPercent: 0,
})

const tagListLength = computed(() => {
	return baseInfo.value.positive_feature?.length + baseInfo.value.negative_feature?.length + baseInfo.value.other_feature?.length
})

const coor_menu_selected = ref<any>(1)

const tagVisible = ref(false)
const activeRow = ref(1)

const activeInfo = ref<any>({
	username: '',
	inspection_index: 0,
	pyramid_index: 1,
})

const pyramidList = computed(() => {
	const list = [
		{
			img: ImgPyramid1,
			label: '10%',
			key: 1,
		},
		{
			img: ImgPyramid2,
			label: '20%',
			key: 2,
		},
		{
			img: ImgPyramid3,
			label: '30%',
			key: 3,
		},
		{
			img: ImgPyramid4,
			label: '40%',
			key: 4,
		},
	]
	return list
})

const onSequenceModal = ({ org_id, user_id }: { org_id?: string; user_id?: string }) => {
	SequenceModal({
		org_id: org_id,
		user_id: user_id,
		title: (baseInfo.value.in_org_flag === 0 ? '同序列' : '班子内') + '排名',
	})
}
const getClassName1 = (value: number) => {
	if (value >= -10) {
		return 'green'
	} else if (value >= -15) {
		return 'orange'
	} else {
		// 棕色
		return 'brown'
	}
}
const onTagModalClose = () => {
	tagVisible.value = false
}

const onTagModalOpen = () => {
	tagVisible.value = true
}

/**
 * @description: 加载数据
 * @return {*}
 */
const loadData = async (user_id: any) => {
	if (!user_id) return

	const flag = coor_menu_selected.value

	const res = await getInspectionIndexPyramidList({ user_id })

	if (res.code === 0 && res.data) {
		activeInfo.value = res.data

		data.value = res.data.user_list
	}
}

const initData = async (user_id) => {
	const res = await getInspectionIndexTag({ user_id })

	if (res.code === 0) {
		const { inspection_index, positive_feature, negative_feature, other_feature, integrity_tag } = res.data
		// 几个标签转换

		baseInfo.value = {
			...res.data,
			inspectionIndexPercent: inspection_index ? (inspection_index > 100 ? 100 : inspection_index) : 0,
			positive_feature: convertMap(positive_feature, true),
			negative_feature: convertMap(negative_feature, true),
			other_feature: convertMap(other_feature, true),
			integrity_tag: convertMap(integrity_tag, true),
		}
		console.log(baseInfo.value)
	}
}
loadData(user_id)
initData(user_id)
</script>

<style lang="less" scoped>
.flex {
	display: flex;
}
.shadow {
	box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
}
.over-scroll-auto {
	overflow: auto;
}
.base-info-box {
	display: flex;
	width: 100%;
	gap: 0px 16px;
	.left-box {
		flex: 1;
		height: 100%;
		.base_info {
			flex: 1;
			height: 100%;
			display: flex;
			padding: 20px 24px;
			background: linear-gradient(90deg, #ffffff 0%, #ecf5ff 100%);
			box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
			border-radius: 8px 8px 8px 8px;
			.avatar {
				width: 168px;
				height: 220px;
			}
			.info {
				flex: 1;
				margin-left: 24px;
				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 32px;
					color: #000000;
					line-height: 38px;
				}
				.position {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24px;
					line-height: 1;
					color: #000000;
					// 三行
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 3;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
			.xuncha-index-box {
				margin-top: 24px;
				.xc-index {
					display: flex;
					align-items: center;
					.xuncha-title {
						margin-right: 15px;
						font-family: PingFang SC, PingFang SC;
						font-weight: 800;
						font-size: 20px;
						color: #333333;
						line-height: 23px;
					}
					.same-xu {
						padding: 0px 10px;
						display: flex;
						align-items: center;
						justify-content: center;
						height: 36px;
						border-radius: 2px 2px 2px 2px;
						border: 1px solid #60ca71;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 18px;
						color: #60ca71;
						line-height: 20px;
					}
					.dash-line {
						border-top: 1px dashed rgba(96, 202, 113, 1);
						flex: 1;
						margin: 0px 20px;
					}
					.xuncha-index {
						font-family: ArTarumianBakhum, ArTarumianBakhum;
						font-weight: 400;
						font-size: 52px;
						line-height: 1;
						color: #60ca71;
					}
				}
			}

			.bottom {
				width: 100%;
				height: 12px;
				background: #f5f5f5;
				border-radius: 30px 30px 30px 30px;
				opacity: 1;
				overflow: hidden;
				.progress {
					height: 12px;
					width: 50%;
					background: #60ca71;
					border-radius: 30px 30px 30px 30px;
					opacity: 1;
				}
			}
		}
		.green {
			background: linear-gradient(180deg, #daf4dd 0%, #ffffff 33%);
		}
		.orange {
			--theme-color: rgba(255, 153, 0, 1);
			background: linear-gradient(180deg, rgba(254, 240, 218, 1) 0%, rgba(255, 255, 255, 1) 33%);
			.same-xu {
				border-color: var(--theme-color) !important;
				color: var(--theme-color) !important;
			}
			.dash-line {
				border-top-color: var(--theme-color) !important;
			}
			.xuncha-index {
				color: var(--theme-color) !important;
			}
			.bottom {
				.progress {
					background-color: var(--theme-color) !important;
				}
			}
		}
		.brown {
			--theme-color: rgba(180, 105, 0, 1);
			background: linear-gradient(180deg, rgba(240, 225, 204, 1) 0%, rgba(255, 255, 255, 1) 33%);
			.same-xu {
				color: var(--theme-color) !important;
				border-color: var(--theme-color) !important;
			}
			.dash-line {
				border-top-color: var(--theme-color) !important;
			}
			.xuncha-index {
				color: var(--theme-color) !important;
			}
			.bottom {
				.progress {
					background-color: var(--theme-color) !important;
				}
			}
		}
	}
	.right-box {
		flex: 1;
		height: 100%;
		box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
		.content-inner {
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.tag-content {
			margin-top: 24px;
			display: flex;
			flex-wrap: wrap;
			gap: 24px;
		}
	}
}
</style>
<style lang="less">
.tag-ant-modal {
	width: 1300px !important;
	.ant-modal-title {
		font-size: 24px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		text-align: center;
	}
	.tag-content,
	.other-content {
		background: #f7fbff;
		.inner {
			padding: 24px 24px 50px;
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
		}
		.legend {
			padding: 12px 0px;
			display: flex;
			justify-content: center;
			gap: 30px;
			.tag-1,
			.tag-2 {
				display: flex;
				align-items: center;
				font-size: 16px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				&::before {
					content: '';
					display: inline-block;
					width: 8px;
					height: 8px;
					margin-right: 4px;
				}
			}
			.tag-1 {
				&::before {
					background: #008eff;
				}
			}
			.tag-2 {
				&::before {
					background: #ff6a16;
				}
			}
		}
	}
	.other-content {
		padding: 24px;
		margin-top: 20px;
		height: 220px;
		background: #f6fff7;
		border-radius: 8px 8px 8px 8px;
		.title {
			display: flex;
			align-items: center;
			font-size: 22px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			line-height: 22px;
			&::before {
				content: '';
				margin-right: 7px;
				display: inline-block;
				width: 8px;
				height: 24px;
				background: url('@/assets/images/card-icon.png') no-repeat center / cover;
			}
		}
	}
}
</style>
