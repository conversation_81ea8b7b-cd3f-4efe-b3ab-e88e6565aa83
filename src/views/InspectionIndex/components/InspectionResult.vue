<template>
	<Card title="巡察结果运用" header size-type="3" style="height: auto">
		<div class="inspection-result">
			<div class="inspection-item" v-for="(ins, index) in data" :key="index">
				<div class="icon" :class="[`icon-${index + 2}`]"></div>
				<div class="right-content">
					<div class="i-index-box">
						<span class="inspection">{{ ins.socre }}</span>
						<span class="go-icon" @click="openModal(ins)"></span>
					</div>
					<div class="label-text">{{ ins.name }}</div>
				</div>
			</div>
		</div>
	</Card>
	<InspectionResult
		title="巡察扣分详情"
		:visible="visible"
		:onCancel="onCancel"
		:name="modalData.name"
		:socre="modalData.socre"
		:list="modalData.list"
	/>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getInspectionIndexOrgInspectionUse } from '@/apis/inspection-index'
import InspectionResult from '@/views/TeamInspectionIndex/components/InspectionModal.vue'
const route = useRoute()
const { user_id } = route.query
const data = ref<any>([])
const visible = ref<boolean>(false)
const modalData = ref<any>({})
const load = async (user_id: any) => {
	const res = await getInspectionIndexOrgInspectionUse({ user_id })
	if (res.code == 0 && res.data) {
		data.value = res.data
	}
}
load(user_id)
const openModal = (ins: any) => {
	modalData.value = ins
	if (ins.list && ins.list.length > 0) visible.value = true
}
const onCancel = () => {
	visible.value = false
}
</script>

<style lang="scss" scoped>
.inspection-result {
	padding: 30px 0px;
	display: flex;
	gap: 0px 41px;
	width: 100%;

	.inspection-item {
		display: flex;
		align-items: center;
		flex: 1;
		.icon {
			margin-right: 16px;
			width: 100px;
			height: 100px;
		}
		.icon-2 {
			background: url('../images/inspection-2.png') no-repeat center center / 100% 100%;
		}
		.icon-3 {
			background: url('../images/inspection-3.png') no-repeat center center / 100% 100%;
		}
		.icon-4 {
			background: url('../images/inspection-4.png') no-repeat center center / 100% 100%;
		}
		.icon-5 {
			background: url('../images/inspection-5.png') no-repeat center center / 100% 100%;
		}
		.right-content {
			.i-index-box {
				display: flex;
				align-items: center;
				.inspection {
					font-family: ArTarumianBakhum, ArTarumianBakhum;
					font-weight: 400;
					font-size: 36px;
					line-height: 1;
					color: #d33625;
					margin-right: 20px;
				}
				.go-icon {
					cursor: pointer;
					width: 32px;
					height: 32px;
					background: url('../images/inspection-1.png') no-repeat center center / 100% 100%;
				}
			}
			.label-text {
				margin-top: 8px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 22px;
				color: #3d3d3d;
				line-height: 26px;
			}
		}
	}
}
</style>
