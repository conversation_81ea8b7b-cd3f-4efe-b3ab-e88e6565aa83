<template>
	<Card title="谈话等次" header style="height: auto">
		<div class="intergrity-risk">
			<TagBox :data="data" :color="'rgba(255, 163, 0, 1)'" />
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Card from '@/components/Card.vue'
import TagBox from '@/components/TagBox.vue'

defineProps({
	data: {
		type: Array,
		default: () => [],
	},
})
</script>

<style lang="scss" scoped>
.intergrity-risk {
	padding-bottom: 130px;
}
</style>
