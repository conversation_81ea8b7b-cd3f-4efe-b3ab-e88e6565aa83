<template>
	<Card title="巡察测评" header size-type="3" style="height: auto">
		<div class="box-content__line">
			<v-chart :option="option" autoresize></v-chart>
		</div>
		<div class="box-content__table">
			<DataTable :data-source="dataSource" :columns="columns" />
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import DataTable from '@/components/Table.vue'
import { getInspectionIndexEvalItem } from '@/apis/inspection-index'
import { decreaseOpacity, convertPxToRem } from '@/utils/utils'
import { historyPush } from '@/utils/history'
import { useRoute } from 'vue-router'
import { data1 } from './data'

const { user_id } = useRoute().query

const data: any = ref({
	xlabel: [],
	other_result: [],
	avg_list: {},
})

const apiData = ref<any>({
	line: [],
	table: [],
	data: [],
	xlabel: [],
	source: [],
})

const createData = (origin_data: [], title: string) => {
	const data: any = {}
	data.name = title
	for (let i = 0; i < origin_data.length; i++) {
		data[`name${i + 1}`] = typeof origin_data[i] === 'number' ? origin_data[i]?.toFixed?.(2) : origin_data[i]
	}

	return data
}

const option = ref({})

const dataSource = ref<any>([])

const initOption = ({ XName, dataMap }: any) => {
	const datas: any[] = []

	dataMap.map((item: any) => {
		const { data, title } = item

		let symbolSize = 8
		let lineWidth = 2

		const colorMap: any = {
			巡察组: 'rgb(106, 174, 251,1)',
			班子成员: 'rgb(71, 211, 255, 1)',
			其他干部: 'rgb(255, 163, 0, 1)',
			综合平均: 'rgb(96, 202, 113, 1)',
		}

		const color = colorMap[item.title]

		const [, ..._data] = data

		datas.push({
			symbolSize: convertPxToRem(symbolSize),
			symbol: 'circle',
			name: title,
			type: 'line',
			yAxisIndex: 1,
			data: _data,
			color: color,
			smooth: false,
			itemStyle: {
				borderWidth: convertPxToRem(4),
				borderColor: color,
				color: '#fff',
				shadowColor: decreaseOpacity(color, 0.5),
				shadowBlur: 13,
			},
			lineStyle: {
				color: color,
				type: 'solid',
				width: convertPxToRem(lineWidth),
			},
		})
	})

	option.value = {
		// backgroundColor: '#0e2147',

		grid: {
			left: '4%',
			top: datas.length > 11 ? '23%' : '13%',
			bottom: '9%',
			right: '3%',
			// containLabel: true,
		},
		legend: {
			show: true,
			top: 0,
			left: 'center',
			icon: 'circle',
			itemWidth: convertPxToRem(12),
			itemHeight: convertPxToRem(12),
			textStyle: {
				color: '#333333',
				fontSize: convertPxToRem(18),
			},
			itemStyle: {
				borderWidth: convertPxToRem(4),
			},
			itemGap: convertPxToRem(28),
		},
		yAxis: [
			{
				type: 'value',
				position: 'right',
				splitLine: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
			},
			{
				type: 'value',
				position: 'left',
				min: (value: any) => value.min - 0.5,
				max: (value: any) => 0,
				nameTextStyle: {
					color: '#00FFFF',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',
						color: '#EEEEEE',
					},
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: ['none', 'arrow'],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				axisTick: {
					show: false,
				},
				// splitNumber: 4,
				// interval: 1,
				axisLabel: {
					color: '#666666',
					fontSize: convertPxToRem(16),
					// formatter: (value: any) => {
					// 	value = Number(value)
					// 	return value > 100 ? '' : value.toFixed(2)
					// },
				},
				onZero: false,
			},
		],
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					inside: false,
					textStyle: {
						color: '#666666', // x轴颜色
						fontWeight: 'normal',
						fontSize: convertPxToRem(16),
						lineHeight: convertPxToRem(22),
					},
					interval: 0,
				},
				data: XName,
			},
		],
		series: datas,
	}
}
const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
	const columns: Array<any> = []

	list.forEach((item: any, index: number) => {
		const key = `name`
		if (index === 0) {
			columns[index] = {
				key,
				title,
				width: '10%',
				colClick: (value: any) => {
					const user = apiData.value.source.find((item: any) => item.name === value.name)

					historyPush(`/inspection-index?user_id=${user.user_id}`)
				},
			}
		}
		columns[index + 1] = {
			key: `${key}${index + 1}`,
			title: item,
			width: 100 / (list.length + 1) + '%',
			align: 'center',
			showMax: true,
			showMin: true,
		}
		if (item === '政治三力') {
			columns[index + 1].customization = 'CustomBlock'
		}
	})
	return columns
}
const columns = ref<any>([])

const loadData = async () => {
	if (!user_id) return

	// formmaterData({
	// 	title: ['政治三力', '学习能力', '决策能力', '执行能力', '沟通协调能力', '群众工作能力', '奉献度', '勤奋度', '正直度', '廉洁度', '测评分数'],
	// 	down: [36, 41, 84, 84, 84, 84, 84, 84, 84, 84, 84],
	// 	same: [15, 45, 100, 100, 100, 100, 100, 100, 100, 100, 100],
	// 	upp: [46, 46, 64, 64, 64, 64, 64, 64, 64, 64, 64],
	// 	avg: [20, 21, 37, 37, 37, 37, 37, 37, 37, 37, 37],
	// })
	const res: any = await getInspectionIndexEvalItem({ user_id })
	if (res.code === 0 && Object.keys(res.data || {}).length > 0) {
		data.value = res.data

		formmaterData(res.data)
	}
}
const dataMapSource = ref<any>([])
// 格式化数据
const formmaterData = (data: any) => {
	const { avg, upp, same, down, title = [] } = data

	apiData.value.xlabel = title

	const _data = [
		{
			title: '巡察组',
			data: down,
		},
		{ title: '班子成员', data: same },
		{
			title: '其他干部',
			data: upp,
		},
		{
			title: '综合平均',
			data: avg,
		},
	]

	dataMapSource.value = _data

	const xlabel: any[] = [].concat(title)

	columns.value = dynamicTableHeader(xlabel)

	const datasource: any = []

	_data.map((item: any) => {
		if (item.data) {
			const _data: any[] = [].concat(item.data)

			datasource.push(createData(_data as any, item.title))
		}
	})

	dataSource.value = datasource
	const [, ..._xlabel] = xlabel
	initOption({
		XName: _xlabel,
		dataMap: _data,
	})
}

loadData()
</script>

<style scoped lang="less">
.box-content__line {
	// height: 440px;
	height: 295px;
}
.box-content__table {
	margin-top: 39px;

	flex: 1;
}
</style>
