<template>
	<router-view v-slot="{ Component }">
		<keep-alive :exclude="['org-details']" :include="keepalivePage">
			<component :is="Component" />
		</keep-alive>
	</router-view>
</template>
<script lang="ts">
export default {
	name: 'DataScreen',
}
</script>
<script lang="ts" setup>
import { ref, provide } from 'vue'
import useKeepAlive from '@/store/keepalive'

const keepalivePage = ref<Array<string>>([])
// 页面缓存
const keepalive = useKeepAlive()

const page = {
	updatePage: (pageName: string) => {
		keepalivePage.value.push(pageName)
	},
	clearPage: () => {
		keepalivePage.value = []
	},
}
keepalive.push('DataScreen')

provide('page', page)
const html = document.querySelector('html')
html?.addEventListener('touchend ', (e: Event) => {
	e.preventDefault()
})
if (html) {
	html.style.height = document.documentElement.clientHeight + 'px'
}
</script>

<style lang="scss" scoped></style>
