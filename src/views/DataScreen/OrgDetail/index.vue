<template>
	<div class="data-screen-orgdetail">
		<PHeader :title="data.org?.org_name" />
		<div class="dso-content">
			<div class="dso-left">
				<div class="dso-l-top" :class="getClassName1(data.org?.patrol_index)">
					<div class="header-box">
						<span
							class="header-detail"
							:style="{
								cursor: teamStyle.cursor ? 'not-allowed' : '',
							}"
							@click="
								() => {
									teamStyle.disabled && onInspectPage(`/team-inspection-index?org_id=${data.org.org_id}`)
								}
							"
							>详情 ></span
						>
					</div>
					<div class="title">{{ data.org?.org_name }}</div>
					<div class="dso-l-main">
						<div class="dso-lm-left">
							<div class="dso-lml-title">
								班子巡察指数
								<span class="ds-icon"></span>
							</div>
							<div class="main-content">
								<div class="top-icon"></div>
								<div class="text">{{ data.org?.patrol_index }}</div>
								<div class="platform-icon"></div>
							</div>
						</div>
						<div
							class="dso-lm-right"
							@click="onSequenceModal({ org_id: data.org.org_id })"
							:style="{ cursor: teamStyle.cursor ? 'not-allowed' : '' }"
						>
							<div class="dso-lml-title">同序列</div>
							<div class="main-content">
								<div class="top-icon"></div>
								<div class="text">{{ data.org?.patrol_index_rank }}</div>
							</div>
						</div>
					</div>
				</div>
				<div class="dso-l-bottom" :class="getClassName(data?.leader?.inspection_index)">
					<div class="header-box">
						<span
							class="header-detail"
							:style="{
								cursor: leaderStyle.cursor ? 'not-allowed' : '',
							}"
							@click="
								() => {
									leaderStyle.disabled && onInspectPage(`/inspection-index?user_id=${data.leader.user_id}`)
								}
							"
							>详情 ></span
						>
					</div>
					<div class="info-card">
						<div class="left-avatar">
							<CodeAvatar :head_url="data.leader?.head_url" />
						</div>
						<div class="right-info">
							<div class="user-name">{{ data.leader?.name }}</div>
							<div class="position">{{ data.leader?.current_job }}</div>
						</div>
					</div>
					<div class="dso-l-main">
						<div class="dso-lm-left">
							<div class="dso-lml-title">
								一把手巡察指数
								<span class="ds-icon"></span>
							</div>
							<div class="main-content">
								<div class="top-icon"></div>
								<div class="text">{{ data.leader?.inspection_index || 0 }}</div>
								<div class="platform-icon"></div>
							</div>
						</div>
						<div
							class="dso-lm-right"
							@click="onSequenceModal({ user_id: data.leader.user_id })"
							:style="{ cursor: leaderStyle.cursor ? 'not-allowed' : '' }"
						>
							<div class="dso-lml-title">同序列</div>
							<div class="main-content">
								<div class="top-icon"></div>
								<div class="text">{{ data.leader?.rank }}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="dso-right">
				<div
					class="user-card-item"
					:style="`background-image: ${determineColor(item.inspection_index).backgroundColor}; cursor: ${
						item.inspection_index == '-' || item.inspection_index == null ? 'not-allowed' : ''
					} `"
					v-for="item in data.team"
					:key="item"
					@click="
						() => {
							item.inspection_index !== '-' && item.inspection_index !== null && onInspectPage(`/inspection-index?user_id=${item.user_id}`)
						}
					"
				>
					<CodeAvatar :head_url="item.head_url" />
					<div class="user-info">
						<div class="username">{{ item.name }}</div>
						<div class="position">{{ item.current_job }}</div>
						<div class="insep-index">
							<span class="inspe-text">巡察指数</span>
							<span
								class="index-num"
								:style="{
									color: determineColor(item.inspection_index).color,
								}"
								>{{ item.inspection_index || 0 }}</span
							>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'org-details',
}
</script>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue'
import PHeader from '../Components/PHeader.vue'
import CodeAvatar from '@/components/CodeAvatar.vue'
import { getOrgDetails } from '@/apis/data-screen'
import { getQueryVariable } from '@/utils/utils'
import { useRouter } from 'vue-router'
import { SequenceModal } from '../Components/SequenceModal'
const router = useRouter()
const { org_id } = getQueryVariable()

const data = ref<any>({
	org: {
		pms_org_screen_id: 0,
		org_id: 210,
		sequence: '',
		org_name: '',
		org_type: 0,
		operating_index: 0,
		ecology: 0,
		performance: 0,
		reputation: 0,
		org_structure: 0,
		cadre_index_avg: 0,
		result: 0,
		patrol_index: -0,
		patrol_index_rank: '-',
	},
	team: [],
	leader: {},
})
// data?.leader?.inspection_index == '-' || data?.leader?.inspection_index == null
// data?.leader?.inspection_index !== '-' && data?.leader?.inspection_index !== null
const leaderStyle = computed(() => {
	const _data = unref(data).leader
	return {
		cursor: _data?.inspection_index == '-' || _data?.inspection_index == null,
		disabled: _data?.inspection_index !== '-' && _data?.inspection_index !== null,
	}
})
// data?.leader?.inspection_index == '-' || data?.leader?.inspection_index == null
// data?.leader?.inspection_index !== '-' && data?.leader?.inspection_index !== null
const teamStyle = computed(() => {
	const _data = unref(data).org
	return {
		cursor: _data?.patrol_index == '-' || _data?.patrol_index == null,
		disabled: _data?.patrol_index !== '-' && _data?.patrol_index !== null,
	}
})

const onInspectPage = (path: string) => {
	router.push(path)
}
const onSequenceModal = ({ org_id, user_id }: { org_id?: string; user_id?: string }) => {
	console.log(user_id, !leaderStyle.value.disabled)
	console.log(org_id, !teamStyle.value.disabled)
	if (user_id && !leaderStyle.value.disabled) return

	if (org_id && !teamStyle.value.disabled) return

	SequenceModal({
		org_id: org_id,
		org_name: data.value.org.org_name,
		user_id: user_id,
	})
}
function determineColor(value: number) {
	const colorMap = {
		color: '',
		backgroundColor: '',
	}

	if (value >= -10) {
		colorMap.color = 'rgba(96, 202, 113, 1)'
		colorMap.backgroundColor = 'linear-gradient(270deg, #EAFFEB 0%, #FFFFFF 100%);'
	} else if (value >= -15) {
		colorMap.color = 'rgba(255, 128, 0, 1)'
		colorMap.backgroundColor = 'linear-gradient( 270deg,#FFF6ED 0%, #FFFFFF 100%);'
	} else {
		colorMap.color = 'rgba(180, 105, 0, 1)'
		colorMap.backgroundColor = 'linear-gradient( 270deg, #F6EDE0 0%, rgba(255,255,255,0) 100%);'
	}
	return colorMap
}

const getClassName = (value) => {
	if (value >= -10) {
		return 'green'
	} else if (value >= -15) {
		return 'orange'
	} else {
		// 棕色
		return 'brown'
	}
}
const getClassName1 = (value) => {
	if (value >= -10) {
		return 'green'
	} else if (value >= -20) {
		return 'orange'
	} else {
		// 棕色
		return 'brown'
	}
}
const initData = async () => {
	const res = await getOrgDetails({
		org_id: org_id,
	})
	if (res.code === 0) {
		data.value = res.data
		// 找出 leader == 1的数据
		const index = res.data.team.findIndex((item: any) => item.leader === 1)

		if (index !== -1) {
			data.value.leader = data.value.team.splice(index, 1)?.[0]
		}

		data.value.team = data.value.team.filter((item: any) => item.name)
	}
}

initData()
</script>

<style lang="less" scoped>
.data-screen-orgdetail {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
	.dso-content {
		display: flex;
		padding: 16px 24px;
		flex: 1;
		width: 100%;
		overflow: hidden;
		.dso-left {
			padding: 40px 32px;
			margin-right: 16px;
			width: 516px;
			height: 100%;
			display: flex;
			flex-direction: column;
			background: #ffffff;

			.header-box {
				width: 100%;
				height: 31px;
				background: no-repeat center / 100% 100%;
				background-image: url('./image/detail-6.png');
				display: flex;
				justify-content: flex-end;
				.header-detail {
					padding: 7px 20px 0px 0px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 16px;
					line-height: 16px;
					color: #ffffff;
					cursor: pointer;
				}
			}

			.dso-l-top {
				display: flex;
				flex-direction: column;
				height: 365px;
				background: linear-gradient(180deg, #e6ffe7 0%, #f6fff6 15%, #ffffff 100%);
				border-bottom: 1px solid #60ca71;
				.title {
					padding: 0px 24px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 22px;
					color: #000000;
				}
			}
			.dso-l-bottom {
				padding: 0xp 24px;
				margin-top: 65px;
				flex: 1;
				background: linear-gradient(180deg, #e6ffe7 0%, #f6fff6 15%, #ffffff 100%);
				border-bottom: 1px solid #60ca71;
				.info-card {
					display: flex;
					padding: 14px 24px 0px;
					.left-avatar {
						.avatar {
							width: 88px;
							height: 118px;
						}
					}
					.right-info {
						margin-left: 31px;
						.user-name {
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: bold;
							font-size: 20px;
							color: #000000;
							line-height: 23px;
						}
						.position {
							margin-top: 11px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 500;
							font-size: 16px;
							color: #000000;
							line-height: 19px;
							text-align: left;
						}
					}
				}
				.dso-l-main {
					margin-top: 41px;
				}
			}

			.dso-l-main {
				flex: 1;
				padding: 0px 24px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.top-icon {
					margin-top: 16px;
					width: 10px;
					height: 6px;
					background: url('./image/detail-5.png') no-repeat center / 100% 100%;
				}
				.text {
					margin-top: 15px;
				}
				.dso-lml-title {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 100%;
					height: 40px;
					background: url('./image/detail-2.png') no-repeat center / 100% 100%;

					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 16px;
					color: rgba(0, 0, 0, 0.9);
					line-height: 19px;
					.ds-icon {
						width: 24px;
						height: 24px;
						background: url('./image/detail-1.png') no-repeat center / 100% 100%;
					}
				}
				.dso-lm-left {
					width: 228px;
					height: 228px;
					margin-right: 22px;
					display: flex;
					flex-direction: column;
					align-items: center;
					.dso-lml-title {
						width: calc(100% - 30px);
						height: 40px;
						flex-shrink: 0;
					}
					.main-content {
						flex: 1;
						font-family: ArTarumianBakhum, ArTarumianBakhum;
						font-weight: 400;
						font-size: 40px;
						color: #60ca71;
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 100%;
						position: relative;
						.platform-icon {
							position: absolute;
							bottom: 0px;
							height: 116px;
							width: 100%;
							background: url('./image/detail-4.png') no-repeat center / contain;
						}
					}
				}
				.dso-lm-right {
					flex: 1;
					height: 219px;
					display: flex;
					flex-direction: column;
					cursor: pointer;
					.main-content {
						flex: 1;
						display: flex;
						flex-direction: column;
						align-items: center;
						background: url('./image/detail-3.png') no-repeat center / 100% 100%;
						font-family: ArTarumianBakhum, ArTarumianBakhum;
						font-weight: 400;
						font-size: 40px;
						color: #60ca71;
					}
				}
			}
		}
		.orange {
			background: linear-gradient(180deg, rgba(255, 248, 241, 1) 0%, rgba(255, 248, 241, 1) 15%, #ffffff 100%) !important;
			border-bottom-color: #ff8000 !important;
			.header-box {
				background-image: url('./image/orange-2.png') !important;
			}
			.dso-lml-title {
				background-image: url('./image/orange-4.png') !important;
			}
			.top-icon {
				background-image: url('./image/orange-5.png') !important;
			}
			.text {
				color: #ff8000;
			}
			.platform-icon {
				background-image: url('./image/orange-3.png') !important;
			}
			.dso-lm-right {
				.main-content {
					background-image: url('./image/orange-1.png') !important;
				}
			}
		}
		.brown {
			background: linear-gradient(180deg, #f9f2e8 0%, #f9f2e8 15%, #ffffff 100%) !important;
			border-bottom-color: #b46900 !important;
			.header-box {
				background-image: url('./image/brown-2.png') !important;
			}
			.dso-lml-title {
				background-image: url('./image/brown-4.png') !important;
			}
			.top-icon {
				background-image: url('./image/brown-5.png') !important;
			}
			.text {
				color: #b46900;
			}
			.platform-icon {
				background-image: url('./image/brown-3.png') !important;
			}
			.dso-lm-right {
				.main-content {
					background-image: url('./image/brown-1.png') !important;
				}
			}
		}
		.dso-right {
			padding: 50px 36px;
			flex: 1;
			height: 100%;
			background: #ffffff;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			gap: 67px 24px;
			overflow: auto;
			&::-webkit-scrollbar {
				display: none;
			}
			.user-card-item {
				cursor: pointer;
				padding: 16px;
				width: 32%;
				height: 164px;
				border-radius: 8px 8px 8px 8px;

				display: flex;
				.avatar {
					width: 100px;
					height: 132px;
				}
				.user-info {
					margin-left: 20px;

					.username {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: bold;
						font-size: 18px;
						color: #000000;
						line-height: 21px;
					}
					.position {
						min-height: 18px;
						margin-top: 8px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 16px;
						color: #000000;
						line-height: 19px;
						// 三行
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 3;
						-webkit-box-orient: vertical;
					}
					.insep-index {
						margin-top: 17px;
						display: flex;
						align-items: center;
						.inspe-text {
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 16px;
							color: #000000;
							line-height: 19px;
						}
						.index-num {
							margin-left: 5px;
							font-family: ArTarumianBakhum, ArTarumianBakhum;
							font-weight: 400;
							font-size: 36px;
							color: #60ca71;
							line-height: 42px;
							text-align: left;
						}
					}
				}
			}
		}
	}
}
</style>
