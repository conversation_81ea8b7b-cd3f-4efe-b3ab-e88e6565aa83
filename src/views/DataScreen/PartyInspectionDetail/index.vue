<template>
	<div class="party-inspection">
		<Title />
		<div class="content">
			<div class="left-box"></div>
			<div class="right-box">
				<div class="focus-communicate-box">
					<button class="four-focus">四个聚焦</button>
					<button
						class="communicate-level"
						@click="
							() => {
								onInspectPage(`/data-screen/party-inspection-detail/CommunicateLevel.vue`)
							}
						"
					>
						谈话等次
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import Title from './component/Title.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const onInspectPage = (path: string) => {
	router.push(path)
}
</script>

<style lang="scss" scoped>
.party-inspection {
	.focus-communicate-box {
		width: 372px;
		line-height: 30px;
		text-align: center;
		color: white;
		font-family: Source <PERSON>, Source <PERSON> CN;
		font-weight: 400;
		font-size: 24px;
		color: #ffff;
		line-height: 28px;
		text-align: center;
		font-style: normal;
		text-transform: none;

		.four-focus {
			width: 170px;
			height: 64px;
			background-color: #008cff;
		}
		.communicate-level {
			width: 170px;
			height: 64px;
			background-color: #f67d14;
			margin-left: 32px;
		}
	}
}
</style>
