<template>
	<div class="editable-cell">
		<div v-if="isEditing" class="editable-cell-input-wrapper">
			<a-input v-model:value="currentValue" @pressEnter="save" />
			<check-outlined class="editable-cell-icon-check" @click="save" />
		</div>
		<div v-else class="editable-cell-text-wrapper">
			{{ text || ' ' }}
			<edit-outlined class="editable-cell-icon" @click="edit" />
		</div>
	</div>
</template>

<script setup>
import { ref } from 'vue'
import { CheckOutlined, EditOutlined } from '@ant-design/icons-vue'

const props = defineProps({
	value: String,
	dataIndex: String,
	record: Object,
	text: String,
})
const emit = defineEmits(['update:value'])

const isEditing = ref(false)
const currentValue = ref(props.value)

const edit = () => {
	isEditing.value = true
}

const save = () => {
	emit('update:value', currentValue.value)
	isEditing.value = false
}
</script>

<style scoped>
/* 你的 CSS 样式 */
</style>
