<template>
	<div class="communicate-content">
		<Title />
		<div class="head-detail"><img :src="back3" alt="左箭头" class="back3" />巡查班子：都督乡</div>
		<div class="body-detail">
			<div class="operation-description">
				<div class="operation-title" @click="loadData()">操作说明：</div>
				<!-- <div class="operation-title">操作说明：</div> -->

				<ol type="1" class="operation-list">
					<li>在表格中首页输入各类评定等次的票数，注意只能输入0-100的数字；</li>
					<li>最后的得分、综合计分由系统自动计算，无需输入。（赋分规则：A等95分、B等85分、C等75分、D等60分。）</li>
					<li>班子成员不涉及评定的无需输入；</li>
				</ol>
			</div>
			<a-table :dataSource="dataSource" :columns="columns" bordered>
				<template #bodyCell="{ column, text, record }">
					<template v-if="editableColumns.includes(column.dataIndex)">
						<div class="editable-cell">
							<div v-if="isEditable(record.key, column.dataIndex)">
								<a-input
									:value="editableData.get(record.key)[column.dataIndex]"
									v-if="editableData.get(record.key) !== null"
									@input="handleInput($event, record.key, column.dataIndex)"
									@blur="(event: any) => handleBlur(record.key, column.dataIndex, event)"
									@pressEnter="(event: any) => handleEnter(record.key, column.dataIndex, event)"
								/>
								<!-- 提示信息 -->
								<div v-if="!inputValid(record.key, column.dataIndex)" class="error-message">请输入0到100之间的数字</div>
							</div>
							<!-- <div v-else class="editable-cell-text-wrapper"> -->
							<div v-else class="editable-cell-text-wrapper" @click="handleClick(record.key, column.dataIndex)">
								{{ text || '-' }}
							</div>
						</div>
					</template>
					<template v-else>
						{{ text || '-' }}
					</template>
				</template>
			</a-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import back3 from '@/assets/images/back-3.png'
import Title from './Title.vue'
import { defineProps, reactive, onMounted } from 'vue'
// import { CheckOutlined, EditOutlined } from '@ant-design/icons-vue'
// import { cloneDeep } from 'lodash-es'
import { getSpeechRatingList, getSpeechRatingAdd, getSpeechRatingCompute } from '../../../../../src/apis/communicate'
// 定义数据类型
interface DataItem {
	[index: string]: any
	key: number
	name: string
	// user_id: number
	partyA?: number
	partyB?: number
	partyC?: number
	partyD?: number
	otherA?: number
	otherB?: number
	otherC?: number
	otherD?: number
	inspectA?: number
	inspectB?: number
	inspectC?: number
	inspectD?: number
	score?: number
	totalScore?: number
}

interface Column {
	title: string
	dataIndex?: string
	tablekey: string
	children?: object
}

interface ParamData {
	type: number
	user_id: number
	// user_name: string
	a?: number
	b?: number
	c?: number
	d?: number
}

interface UserData {
	user_id: number
	user_name: string
	score: number
	comprehensive_score: number
	speech_list: ParamData[]
}

const editableColumns = [
	'partyA',
	'partyB',
	'partyC',
	'partyD',
	'otherA',
	'otherB',
	'otherC',
	'otherD',
	'inspectA',
	'inspectB',
	'inspectC',
	'inspectD',
]

const editableData = reactive(new Map<number, DataItem>())

const currentEditableCell = reactive<{ key: number | null; dataIndex: string | null }>({ key: null, dataIndex: null })

const isEditable = (key: number, dataIndex: string) => {
	return currentEditableCell.key === key && currentEditableCell.dataIndex === dataIndex
}
const handleInput = (event: any, key: number, dataIndex: string) => {
	const value = event.target.value
	// console.log(editableData.get(key))
	if (value === '') {
		editableData.get(key)[dataIndex] = null
	} else if (!isNaN(value)) {
		const numValue = Number(value)
		editableData.get(key)[dataIndex] = numValue
	} else {
		editableData.get(key)[dataIndex] = value
	}
}

const handleClick = (key: number, dataIndex: string) => {
	if (!isEditable(key, dataIndex)) {
		const item = dataSource.find((item) => item.key === key)
		if (item) {
			const itemCopy = { ...item }
			editableData.set(key, itemCopy)
			currentEditableCell.key = key
			currentEditableCell.dataIndex = dataIndex
		}
	}
}

const handleEnter = (key: number, dataIndex: string, event?: any) => {
	const isValid = inputValid(key, dataIndex)
	if (isValid) {
		saveEdit(key, dataIndex)
	}
}

const handleBlur = (key: number, dataIndex: string, event?: any) => {
	const isValid = inputValid(key, dataIndex)
	if (isValid) {
		saveEdit(key, dataIndex)
	}
}

const inputValid = (key: number, dataIndex: string) => {
	const value = editableData.get(key)[dataIndex]
	return value >= 0 && value <= 100
}

const saveEdit = async (key: number, dataIndex: string) => {
	// 从editableData中获取临时数据，并更新到dataSource中
	// console.log(editableData)
	const tempData = editableData.get(key)
	// console.log(tempData)
	if (tempData) {
		let item = dataSource.find((item) => item.key === key)
		if (item) {
			// console.log(item)
			// console.log(dataIndex, key)
			item[dataIndex] = tempData[dataIndex]

			const result2 = collectComputeParam(item)
			// console.log(result2)
			await _getSpeechRatingCompute(result2, key)
			// console.log(item)
			const result = collectAddParam(item)
			// console.log(result)
			_getSpeechRatingAdd(result)
		}
		editableData.delete(key)
		currentEditableCell.key = null
		currentEditableCell.dataIndex = null
	}
	console.log(dataSource)
}
// 定义响应式数据源，指定类型
let dataSource = reactive<DataItem[]>([])

const _getSpeechRatingList = () => {
	const org_id = 197
	getSpeechRatingList({ org_id })
		.then((res: any) => {
			if (res.code == 0) {
				// console.log(transformListData(res.data))
				Object.assign(dataSource, transformListData(res.data))
			}
			// console.log(dataSource)
		})
		.catch(() => {
			console.log('error')
		})
}

onMounted(_getSpeechRatingList)

const _getSpeechRatingAdd = (param: UserData) => {
	getSpeechRatingAdd(param)
		.then((res: any) => {
			if (res.code == 0) {
				console.log(res)
			} else {
				console.log(res.message)
			}
		})
		.catch(() => {
			console.log('error')
		})
}

const _getSpeechRatingCompute = async (param: Array<object>, key: number) => {
	try {
		const res: any = await getSpeechRatingCompute(param)
		if (res.code === 0) {
			console.log(dataSource, key)
			const item = dataSource.find((item) => item.key === key)
			console.log(item)
			if (item) {
				item.score = res.data.score
				item.totalScore = res.data.comprehensive_score
			}
			console.log(dataSource)
		} else {
			console.log(res.message)
		}
	} catch (error) {
		console.log('error', error)
	}
}

// 使用 reactive 创建响应式数据源 接口有问题时备用数据
// const dataSource = reactive<DataItem[]>([
// 	{
// 		name: '张三',
// 		partyA: 1,
// 		partyB: 5,
// 		partyC: 2,
// 		partyD: 1,
// 		otherA: 4,
// 		otherB: 3,
// 		otherC: 2,
// 		otherD: 1,
// 		inspectA: 2,
// 		inspectB: 3,
// 		inspectC: 4,
// 		inspectD: 1,
// 		score: 85,
// 		totalScore: 75,
// 		key: 233,
// 	},
// 	{
// 		name: '李四',
// 		partyA: 2,
// 		partyB: 4,
// 		partyC: 3,
// 		partyD: 1,
// 		otherA: 3,
// 		otherB: 5,
// 		otherC: 1,
// 		otherD: 1,
// 		inspectA: 3,
// 		inspectB: 2,
// 		inspectC: 4,
// 		inspectD: 1,
// 		score: 80,
// 		totalScore: 70,
// 		key: 555,
// 	},
// 	{
// 		name: '王五',
// 		partyA: 1,
// 		partyB: 2,
// 		partyC: 5,
// 		partyD: 2,
// 		otherA: 2,
// 		otherB: 3,
// 		otherC: 4,
// 		otherD: 1,
// 		inspectA: 1,
// 		inspectB: 3,
// 		inspectC: 5,
// 		inspectD: 1,
// 		score: 75,
// 		totalScore: 65,
// 		key: 664,
// 	},
// 	{
// 		name: '赵六',
// 		partyA: 4,
// 		partyB: 3,
// 		partyC: 2,
// 		partyD: 1,
// 		otherA: 5,
// 		otherB: 1,
// 		otherC: 3,
// 		otherD: 1,
// 		// inspectA: 1,
// 		inspectB: 2,
// 		inspectC: 3,
// 		inspectD: 1,
// 		score: 70,
// 		totalScore: 60,
// 		key: 332,
// 	},
// ])

//整理getLists返回数据
const transformListData = (data: UserData[]): DataItem[] => {
	return data.map((item) => {
		const speech1 = item.speech_list.find((s) => s.type === 1)!
		const speech2 = item.speech_list.find((s) => s.type === 2)!
		const speech3 = item.speech_list.find((s) => s.type === 3)!

		return {
			name: item.user_name,
			partyA: speech2.a || 0,
			partyB: speech2.b || 0,
			partyC: speech2.c || 0,
			partyD: speech2.d || 0,
			otherA: speech3.a || 0,
			otherB: speech3.b || 0,
			otherC: speech3.c || 0,
			otherD: speech3.d || 0,
			inspectA: speech1.a || 0,
			inspectB: speech1.b || 0,
			inspectC: speech1.c || 0,
			inspectD: speech1.d || 0,
			score: item.score,
			totalScore: item.comprehensive_score,
			key: item.user_id,
		}
	})
}
//整理add接口请求数据
const collectParam = (source: DataItem, dataindex: string, key: number) => {
	let result: ParamData = { type: 0, user_id: key }

	if (dataindex.startsWith('party')) {
		result.type = 2
		result.a = source['partyA']
		result.b = source['partyB']
		result.c = source['partyC']
		result.d = source['partyD']
	} else if (dataindex.startsWith('other')) {
		result.type = 3
		result.a = source['otherA']
		result.b = source['otherB']
		result.c = source['otherC']
		result.d = source['otherD']
	} else if (dataindex.startsWith('inspect')) {
		result.type = 1
		result.a = source['inspectA']
		result.b = source['inspectB']
		result.c = source['inspectC']
		result.d = source['inspectD']
	}

	return result
}
const collectAddParam = (source: DataItem): UserData => {
	// console.log(source)
	// console.log(source['score'])
	const transformed: UserData = {
		user_id: source['key'],
		score: source['score'],
		comprehensive_score: source['totalScore'],
		speech_list: [
			collectParam(source, 'inspect', source['key']),
			collectParam(source, 'party', source['key']),
			collectParam(source, 'other', source['key']),
		],
	}

	return transformed
}

const collectComputeParam = (source: DataItem) => {
	const transformed = [
		collectParam(source, 'inspect', source['key']),
		collectParam(source, 'party', source['key']),
		collectParam(source, 'other', source['key']),
	]

	return transformed
}

// 定义列数据
const columns = reactive<Column[]>([
	{
		title: '班子成员姓名',
		dataIndex: 'name',
		tablekey: 'name',
	},
	{
		title: '班子成员评定情况（占40%）',
		tablekey: 'party-judge',
		children: [
			{
				title: 'A',
				dataIndex: 'partyA',
				tablekey: 'partyA',
			},
			{
				title: 'B',
				dataIndex: 'partyB',
				tablekey: 'partyB',
			},
			{
				title: 'C',
				dataIndex: 'partyC',
				tablekey: 'partyC',
			},
			{
				title: 'D',
				dataIndex: 'partyD',
				tablekey: 'partyD',
			},
		],
	},
	{
		title: '其他干部评定情况（占40%）',
		tablekey: 'other-judge',
		children: [
			{
				title: 'A',
				dataIndex: 'otherA',
				key: 'otherA',
			},
			{
				title: 'B',
				dataIndex: 'otherB',
				tablekey: 'otherB',
			},
			{
				title: 'C',
				dataIndex: 'otherC',
				tablekey: 'otherC',
			},
			{
				title: 'D',
				dataIndex: 'otherD',
				tablekey: 'otherD',
			},
		],
	},
	{
		title: '巡察组评定情况（占20%）',
		tablekey: 'inspect-judge',
		children: [
			{
				title: 'A',
				dataIndex: 'inspectA',
				tablekey: 'inspectA',
			},
			{
				title: 'B',
				dataIndex: 'inspectB',
				tablekey: 'inspectB',
			},
			{
				title: 'C',
				dataIndex: 'inspectC',
				tablekey: 'inspectC',
			},
			{
				title: 'D',
				dataIndex: 'inspectD',
				tablekey: 'inspectD',
			},
		],
	},
	{
		title: '得分',
		dataIndex: 'score',
		tablekey: 'score',
	},
	{
		title: '综合计分',
		dataIndex: 'totalScore',
		tablekey: 'totalScore',
	},
])

// 导出响应式数据和列数据，以便在模板中使用
// defineProps({})
</script>

<style lang="scss" scoped>
.communicate-content {
	width: 100%;
	.head-detail {
		width: 100%;
		height: 80px;
		display: flex;
		align-items: center;
		text-align: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 26px;
		color: #000000;
		line-height: 30px;
		text-align: left;
		font-style: normal;
		text-transform: none;
		.back3 {
			width: 24px;
			height: 24px;
			margin-right: 12px;
			margin-left: 38px;
		}
	}
	.body-detail {
		width: 100%;
		.operation-description {
			margin-left: 36px;
			margin-top: 42px;
			width: 100%;
			height: 100px;
			display: flex;
			font-family: Source Han Sans CN, Source Han Sans CN;
			color: #000000;
			text-align: left;
			font-style: normal;
			text-transform: none;
			.operation-title {
				// width: 110px;
				height: 33px;
				font-weight: bold;
				font-size: 22px;
				line-height: 26px;
			}
			.operation-list {
				padding-left: 15px;
				li {
					list-style: decimal;
				}
				font-weight: 400;
				font-size: 22px;
				line-height: 26px;
			}
		}
		:deep(.ant-table-cell) {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 22px;
			background-color: #ffff;
			line-height: 26px;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}
		:deep(.ant-table-thead tr:first-child th) {
			background-color: #f3f3f3;
		}
		:deep(.ant-table-tbody tr td:nth-child(1)) {
			text-align: left;
		}
	}
	.ant-input {
		width: 85%;
		font-size: 22px;
	}
}
</style>
