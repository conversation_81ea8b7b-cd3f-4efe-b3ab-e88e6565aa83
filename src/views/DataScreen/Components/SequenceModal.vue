t
<template>
	<Modal v-model:visible="visible" :footer="null" :closable="false" class="white-inspection-modal" :afterClose="onAfterClose" destroy-on-close>
		<div class="i-modal-header">
			<div class="i-modal-left">
				<span class="im-title"> {{ title }}</span>
			</div>
			<div class="i-modal-right">
				<span class="close-icon" @click="onClose">x</span>
			</div>
		</div>
		<div class="table-box">
			<slot>
				<LocaleProvider :locale="zh_CN">
					<!-- :pagination="{
							pageSize: pageSize,
							showSizeChanger: false,
						}" -->
					<Table
						:data-source="dataSource"
						:columns="columns"
						row-key="user_id"
						:row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : 'table-striped-1')"
						:loading="loading"
						:pagination="false"
						:scroll="{ y: '50vh' }"
						bordered
					>
						<template #bodyCell="{ column, text, record }">
							<template v-if="column.key === 'inspection_index'">
								<span class="index-num">{{ text }}</span>
							</template>
							<template v-if="column.key === 'username'">
								<div class="user-info-box">
									<CodeAvatar class="avatar" :head_url="record.head_url" />
									<div class="user-info">
										<div class="user-name">{{ record.username }}</div>
										<div class="user-department">{{ record.current_job }}</div>
									</div>
								</div>
							</template>
							<template v-if="column.key === 'rank'">
								{{ text || '-' }}
							</template>
						</template>
					</Table>
				</LocaleProvider>
			</slot>
		</div>
	</Modal>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { Modal, Table, LocaleProvider } from 'ant-design-vue'

import CodeAvatar from '@/components/CodeAvatar.vue'
import { getOrgIndexRank, getUserIndexRank } from '@/apis/data-screen'
import zh_CN from 'ant-design-vue/es/locale-provider/zh_CN'
const visible = ref(true)
const props = defineProps({
	title: {
		type: String,
		default: '同序列排名',
	},
	pageSize: {
		type: Number,
		default: 5,
	},
	org_id: {
		type: String,
		default: '',
	},
	user_id: {
		type: String,
		default: '',
	},
	onClose: {
		type: Function,
		required: true,
	},
	loading: {
		type: Boolean,
		default: false,
	},
})

const ORG_COLUMNS = [
	{
		dataIndex: 'org_name',
		key: 'org_name',
		title: '班子名称',
		align: 'left',
		width: '33%',
	},
	{
		dataIndex: 'patrol_index',
		key: 'index',
		title: '巡察指数',
		align: 'center',
		width: '33%',
	},
	{
		dataIndex: 'patrol_index_rank',
		key: 'patrol_index_rank',
		title: '序列排名',
		align: 'center',
		width: '33%',
	},
]

const USER_COLUMNS = [
	{
		dataIndex: 'username',
		key: 'username',
		align: 'left',
		title: '班子成员',
		width: '33%',
	},
	{
		dataIndex: 'inspection_index',
		key: 'inspection_index',
		align: 'center',
		title: '巡察指数',
		width: '33%',
	},
	{
		dataIndex: 'inspection_index_rank',
		key: 'inspection_index_rank',
		align: 'center',
		title: '序列排名',
		width: '33%',
	},
]
const emits = defineEmits(['close'])
const dataSource = ref([])
console.log('props.org_id', props.org_id, props.user_id)
const columns = ref<any>(props.org_id ? ORG_COLUMNS : USER_COLUMNS)

const onAfterClose = () => {
	emits('close')
}
const getOrgRankData = async () => {
	const res = await getOrgIndexRank({ org_id: props.org_id })
	console.log(res)
	if (res.code === 0) {
		columns.value = ORG_COLUMNS

		dataSource.value = res.data
	}
}
const getUserRankData = async () => {
	const res = await getUserIndexRank({ user_id: props.user_id })
	if (res.code === 0) {
		columns.value = USER_COLUMNS

		dataSource.value = res.data
	}
}
const getRankData = async () => {
	if (props.org_id) {
		getOrgRankData()
	} else if (props.user_id) {
		getUserRankData()
	}
}

onMounted(() => {
	getRankData()
})
</script>
<style lang="scss">
.white-inspection-modal {
	width: 1462px !important;
	// .ant-spin-container::after {
	// 	background: rgba(0, 0, 0, 0.3) !important;
	// }
	.ant-modal-content {
		width: 100%;

		// background: url('./image/modal-2.png') no-repeat center center / 100% 100%;
	}

	.ant-modal-body {
		padding: 0px !important;
		width: 100%;
		min-height: 634px;
		.i-modal-header {
			position: relative;
			width: 100%;
			height: 50px;
			// background: url('./image/modal-1.png') no-repeat center center / 100% 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0px 24px !important;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
			.i-modal-left {
				width: 100%;
				text-align: center;
				.im-title {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 26px;
					color: #000000;
					line-height: 30px;
					text-align: center;
				}
			}
			.i-modal-right {
				font-size: 0px;
				position: absolute;
				right: 24px;
				top: 5px;
				.close-icon {
					display: inline-block;
					width: 24px;
					height: 24px;
					color: #000000;
					font-size: 24px;
					// background: url('./image/modal-3.png') no-repeat center center / 100% 100%;
					cursor: pointer;
				}
			}
		}
	}
	.table-box {
		padding: 22px 24px;

		.ant-table-empty {
			background-color: transparent;
		}
		.ant-empty-description {
			color: #3d3d3d;
			font-size: 22px;
		}
		.ant-table-thead {
			.ant-table-cell {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 22px;
				text-align: left;
				font-weight: 500;
				color: #3d3d3d;
				line-height: 20px;
			}
		}
		.table-striped-1 {
		}
		.table-striped {
		}
		.ant-table-placeholder {
			background-color: transparent;
			&:hover > td {
				background-color: transparent;
			}
		}
		.ant-table-tbody {
			.ant-table-cell {
				background-color: transparent;
				font-size: 22px;
			}
		}
		.index-num {
		}

		.user-info-box {
			display: flex;
			align-items: center;
			.avatar {
				width: 40px;
				height: 50px;
			}
			.user-info {
				margin-left: 8px;
				.user-name {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 22px;
					color: #000000;
					line-height: 22px;
				}
				.user-department {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 22px;
					color: #000000;
					line-height: 22px;
				}
			}
		}
	}
	.ant-pagination-item-link {
	}
	.ant-pagination-item {
		a {
			font-size: 17px;
		}
	}
	.ant-pagination-item-active {
		a {
			font-size: 17px;
		}
	}
	.ant-pagination-item-container {
		width: 50px;
	}
	.ant-pagination-item-ellipsis {
		white-space: nowrap;
		width: 50px;
	}
	.ant-select-selector {
		.ant-select {
		}
		.ant-select-selection-item {
		}
	}
	.ant-select-dropdown {
		.ant-select-item-option-selected {
		}
	}
}
</style>
