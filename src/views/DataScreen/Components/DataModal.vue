t
<template>
	<Modal :visible="visible" :footer="null" :closable="false" class="inspection-modal" @cancel="onClose" destroy-on-close>
		<div class="i-modal-header">
			<div class="i-modal-left">
				<span class="im-title"> {{ title }}</span>
			</div>
			<div class="i-modal-right">
				<span class="close-icon" @click="onClose"></span>
			</div>
		</div>
		<div class="table-box">
			<slot>
				<Table
					:data-source="dataSource"
					:columns="columns"
					row-key="user_id"
					:row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : 'table-striped-1')"
					:pagination="{
						pageSize: pageSize,
						showSizeChanger: false,
					}"
					:loading="loading"
				>
					<template #bodyCell="{ column, text, record }">
						<template v-if="column.key === 'index'">
							<span class="index-num">{{ text }}</span>
						</template>
						<template v-if="column.key === 'avatar_party_user'">
							<div class="user-info-box">
								<CodeAvatar class="avatar" :head_url="record.head_url" />
								<div class="user-info">
									<div class="user-name">{{ record.name }}</div>
									<div class="user-department">{{ record.current_job }}</div>
								</div>
							</div>
						</template>
						<template v-if="column.key === 'rank'">
							{{ text || '-' }}
						</template>
					</template>
				</Table>
			</slot>
		</div>
	</Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Modal, Table } from 'ant-design-vue'
import CodeAvatar from '@/components/CodeAvatar.vue'
defineProps({
	title: {
		type: String,
		default: '',
	},
	pageSize: {
		type: Number,
		default: 5,
	},
	visible: {
		type: Boolean,
		default: false,
	},
	dataSource: {
		type: Array,
		required: true,
	},
	columns: {
		type: Array<any>,
		required: true,
	},
	onClose: {
		type: Function,
		required: true,
	},
	loading: {
		type: Boolean,
		default: false,
	},
})
</script>
<style lang="scss">
.inspection-modal {
	width: 1066px !important;
	.ant-spin-container::after {
		background: rgba(0, 0, 0, 0.3) !important;
	}
	.ant-modal-content {
		width: 100%;

		background: url('./image/modal-2.png') no-repeat center center / 100% 100%;
	}

	.ant-modal-body {
		padding: 0px !important;
		width: 100%;
		min-height: 634px;
		background-color: transparent !important;
		.i-modal-header {
			width: 100%;
			height: 50px;
			background: url('./image/modal-1.png') no-repeat center center / 100% 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0px 24px !important;
			.i-modal-left {
				.im-title {
					font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
					font-weight: bold;
					font-size: 24px;
					color: #ffffff;
					line-height: 32px;
					text-shadow: 0px 2px 20px #0086ff;
				}
			}
			.i-modal-right {
				font-size: 0px;
				.close-icon {
					display: inline-block;
					width: 24px;
					height: 24px;
					background: url('./image/modal-3.png') no-repeat center center / 100% 100%;
					cursor: pointer;
				}
			}
		}
	}
	.table-box {
		padding: 33px 33px 32px;
		& * {
			border: none !important;
		}
		.ant-table-empty {
			background-color: transparent;
		}
		.ant-empty-description {
			color: #ffffff;
			font-size: 14px;
		}
		.ant-table-thead {
			.ant-table-cell {
				background: rgb(45, 85, 227);
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 14px;
				color: #ffffff;
				text-align: left;
				font-weight: 500;
				color: #ffffff;
				line-height: 20px;
			}
		}
		.table-striped-1 {
			background: #06193a;
		}
		.table-striped {
			background: rgba(14, 37, 92, 1);
		}
		.ant-table-placeholder {
			background-color: transparent;
			&:hover > td {
				background-color: transparent;
			}
		}
		.ant-table-tbody {
			.ant-table-cell {
				background-color: transparent;
				color: #ffffff;
			}
		}
		.index-num {
			color: #cf7901 !important;
		}

		.user-info-box {
			display: flex;
			align-items: center;
			.avatar {
				width: 40px;
				height: 50px;
			}
			.user-info {
				margin-left: 8px;
				.user-name {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 14px;
					color: #ffffff;
					line-height: 16px;
				}
				.user-department {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 12px;
					color: rgba(255, 255, 255, 0.75);
					line-height: 14px;
				}
			}
		}
	}
	.ant-pagination-item-link {
		background: rgba(28, 98, 254, 0.2);
		color: rgba(255, 255, 255, 0.3);
	}
	.ant-pagination-item {
		background: rgba(28, 97, 254, 0.5);
		a {
			color: rgba(255, 255, 255, 0.65);
			font-size: 14px;
		}
	}
	.ant-pagination-item-active {
		background: #1c60fe;
		a {
			color: #ffffff;
			font-size: 14px;
		}
	}
	.ant-pagination-item-ellipsis {
		color: #ffffff !important;
	}
	.ant-pagination-item-container {
		width: 50px;
	}
	.ant-pagination-item-ellipsis {
		white-space: nowrap;
		width: 50px;
	}

	.ant-select-selector {
		background: rgba(28, 97, 254, 0.5) !important;
		.ant-select {
		}
		.ant-select-selection-item {
			color: rgba(255, 255, 255, 0.65) !important;
		}
	}
	.ant-select-dropdown {
		background: rgba(28, 97, 254, 0.5) !important;
		.ant-select-item-option-selected {
			background: rgba(28, 97, 254, 0.8) !important;
			&:hover {
				background: rgba(28, 97, 254, 0.8) !important;
			}
		}
	}
}
</style>
