import { createApp, defineComponent } from 'vue'
import SequenceModalVue from './SequenceModal.vue'

/**
 * 创建一个modal弹出框
 * @param {any} component - 弹出框的组件
 * @param {any} props - 弹出框组件的参数
 * @returns {object} - 返回一个instance和一个关闭函数
 */
export const createModalInstance = (component: any, props = {}) => {
	const onClose = () => {
		instance.unmount()
	}

	const instance = createApp(component, {
		...props,
		onClose,
	})

	const dismissEle: any = document.createElement('div')

	instance.mount(dismissEle)

	return { instance, onClose }
}

const SequenceModal = (props = {}) => {
	return createModalInstance(SequenceModalVue, props)
}

export { SequenceModal }
