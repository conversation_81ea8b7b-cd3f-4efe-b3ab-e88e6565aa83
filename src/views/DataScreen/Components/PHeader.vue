<template>
	<div class="header">
		<div class="back">
			<div class="common" @click="onBack">
				<span class="icon"></span>
				<span class="text">返回</span>
				<span class="line"></span>
				<div class="org-name">{{ title }}</div>
			</div>
		</div>
		<div class="bg-line"></div>
	</div>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router'

defineProps({
	title: String,
})

const router = useRouter()

const onBack = () => {
	router.back()
}
</script>

<style lang="less" scoped>
.header {
	display: flex;
	align-items: center;
	padding: 0 32px;
	width: 100%;
	height: 68px;
	background: #008cff;

	.title {
		text-align: center;
		font-size: 27px;
		line-height: 32px;
		font-weight: 600;
		color: #ffffff;
	}
	.bg-line {
		flex: 1;
		height: 2px;
		background: url(./image/header.png) center / 100% 2px no-repeat;
	}
	.common {
		display: flex;
		align-items: center;
		cursor: pointer;
		.icon {
			margin-right: 10px;
			display: inline-block;
			width: 24px;
			height: 24px;
		}
		.line {
			margin: 0px 12px;
			width: 1px;
			height: 12px;
			background: #ffffff;
		}
		.text {
			font-size: 16px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
		}
		.org-name {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 22px;
			color: #ffffff;
			line-height: 26px;
		}
	}
	.back {
		.icon {
			background: url(@/assets/images/back-header.png) center / cover no-repeat;
		}
	}
	.flex-box {
		display: flex;
		flex: 1;
	}
	.flex-justify-right {
		justify-content: flex-end;
	}
	.user-box {
		margin-right: 41px;
		display: flex;
		align-items: center;
		.user-icon {
			margin-right: 9px;
			display: inline-block;
			width: 36px;
			height: 36px;
			background: url('@/assets/images/user.png') center / cover no-repeat;
		}
		.user-name {
			margin-right: 8px;
			font-weight: 400;
			font-size: 24px;
			color: #ffffff;
			line-height: 24px;
			text-align: left;
		}
		.drop-icon {
			display: inline-block;
			width: 24px;
			height: 24px;
			background: url('@/assets/images/drop.png') center / cover no-repeat;
		}
	}
	.login-out {
		justify-content: flex-end;
		.icon {
			width: 24px;
			height: 24px;
			background: url(@/assets/images/login-out.png) center / contain no-repeat;
		}
	}
}

.reset-password {
	padding: 5px 10px;
	.reset-password-icon {
		vertical-align: middle;
		display: inline-block;
		width: 24px;
		height: 24px;
		background: url(@/assets/images/reset-password.png) center / contain no-repeat;
	}
	.reset-text {
		font-weight: 400;
		font-size: 24px;
		color: #222222;
		line-height: 24px;
		vertical-align: sub;
	}
}
</style>
