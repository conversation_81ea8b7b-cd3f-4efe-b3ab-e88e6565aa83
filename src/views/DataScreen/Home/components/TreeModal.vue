t
<template>
	<Modal :visible="visible" :footer="null" :closable="false" class="inspection-modal" @cancel="onClose" destroy-on-close>
		<div class="i-modal-header">
			<div class="i-modal-left">
				<span class="im-title"> {{ title }}</span>
			</div>
			<div class="i-modal-right">
				<span class="close-icon" @click="onClose"></span>
			</div>
		</div>
		<div class="rule-box">
			<div class="rule-icon"></div>
		</div>
	</Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Modal, Table } from 'ant-design-vue'
import CodeAvatar from '@/components/CodeAvatar.vue'
defineProps({
	title: {
		type: String,
		default: '',
	},
	visible: {
		type: Boolean,
		default: false,
	},
	onClose: {
		type: Function,
		required: true,
	},
})
</script>
<style lang="scss">
.inspection-modal {
	width: 1500px !important;
	padding-bottom: 0px;
	box-shadow: 0px 0px 30px 20px #06193a;
	.ant-spin-container::after {
		background: rgba(0, 0, 0, 0.3) !important;
	}
	.ant-modal-content {
		width: 100%;

		background: url('../images/modal-2.png') no-repeat center center / 100% 100%;
	}

	.ant-modal-body {
		padding: 0px !important;
		width: 100%;
		min-height: 634px;
		background-color: transparent !important;
		.i-modal-header {
			width: 100%;
			height: 50px;
			background: url('../images/modal-1.png') no-repeat center center / 100% 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0px 24px !important;
			.i-modal-left {
				.im-title {
					font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
					font-weight: bold;
					font-size: 24px;
					color: #ffffff;
					line-height: 32px;
					text-shadow: 0px 2px 20px #0086ff;
				}
			}
			.i-modal-right {
				font-size: 0px;
				.close-icon {
					display: inline-block;
					width: 24px;
					height: 24px;
					background: url('../images/modal-3.png') no-repeat center center / 100% 100%;
					cursor: pointer;
				}
			}
		}
	}
	.rule-box {
		padding: 43px 33px 32px;
		display: flex;
		justify-content: center;
		.rule-icon {
			width: 1118px;
			height: 806px;
			background: url('../images/rule-icon-1.png') no-repeat center center / 100% 100%;
		}
		& * {
			border: none !important;
		}
	}
}
</style>
