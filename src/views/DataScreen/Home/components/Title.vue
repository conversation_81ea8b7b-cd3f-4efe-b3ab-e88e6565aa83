<template>
	<div class="inner-card__header p-i-header">
		<div class="sub-title">
			{{ title }}
			<slot name="sub-title"></slot>
		</div>
		<div class="left">
			<slot></slot>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
defineProps({
	title: {
		type: String,
		required: false,
	},
	subTitle: {
		type: String,
		required: false,
	},
})
</script>

<style lang="scss" scoped>
.inner-card__header {
	background: url('../images/xuncha2.png') no-repeat center/ 100% 100%;
	display: flex;
	padding-left: 12px;
	padding-right: 12px;
	height: 36px;
	.sub-title {
		flex: 1;
		display: flex;
		align-items: center;

		font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>;
		font-weight: bold;
		font-size: 20px;
		color: #ffffff;
		line-height: 26px;
		text-shadow: 0px 0px 8px #088bff;
		text-align: left;
		&::before {
			content: '';
			display: inline-block;
			width: 14px;
			height: 16px;
			background: url('../images/xuncha4.png') no-repeat center/ 100%;
			margin-right: 4px;
		}
	}
	.sub-title-1 {
		margin-left: 21px;
		font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
		font-weight: bold;
		font-size: 16px;
		color: rgba(255, 255, 255, 0.6);
		line-height: 26px;
		text-shadow: 0px 0px 8px #088bff;
		text-align: left;
	}
	.left {
		margin-top: 3px;
	}
}
</style>
