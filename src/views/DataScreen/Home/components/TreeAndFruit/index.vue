<template>
	<div class="container" ref="tree_box" :key="rerender">
		<div class="bg-tree"></div>
		<div class="fruit-box" :class="{ 'level-hidden': status === 2 }" @click="onHandleClick">
			<template v-if="menuKey !== '1'">
				<div
					v-for="(item, index) in showTreeData"
					@click="onDetail(item)"
					:key="item.org_id"
					:style="formatPosition(item)"
					:data-x="item.x"
					:data-y="item.y"
					class="circle"
					:class="getClass(item)"
				>
					<span>{{ item.org_name }}</span>
					<span v-if="item.patrol_index">{{ item.patrol_index }}</span>
				</div>
			</template>
			<template v-else>
				<div
					v-for="(item, index) in showTreeData"
					@click="onDetail(item)"
					:key="item.org_id"
					:style="formatPosition(item)"
					:data-x="item.x"
					:data-y="item.y"
					class="circle all-circle"
					:class="getClass(item)"
				>
					<span class="patrol_index" v-if="item.patrol_index">{{ item.patrol_index }}</span>
					<div class="hover-box" :class="{ showOrg: searchValue.org_id === item.org_id }">
						<div class="org-info">
							<span>{{ item.org_name }}</span>
							<span v-if="item.patrol_index" class="m-left-5">{{ item.patrol_index }}</span>
						</div>
					</div>
				</div>
			</template>
		</div>
		<div class="sub-fruit-box" v-if="status === 2" @click="onCloseLevel2">
			<div
				v-for="(item, index) in subFruitData"
				@click.stop="onDetail(item)"
				:key="item.org_id"
				:style="formatPosition(item)"
				class="circle"
				:class="getClass(item)"
			>
				<span>{{ item.name || item.org_name }}</span>
				<span v-if="item.patrol_index">{{ item.patrol_index }}</span>
			</div>
		</div>

		<div class="search-box" v-if="menuKey === '1'">
			<span class="search-icon"></span>
			<a-auto-complete
				ref="autoCompleteRef"
				:options="searchList"
				dropdownClassName="org-drop-select"
				v-model:value="searchValue.org_name"
				style="width: 200px"
				placeholder="请输入"
				@search="onSearch"
			>
				<template v-slot:option="{ patrol_index, org_name, org_id }">
					<div class="ant-select-item" @click="onSelect({ org_name, org_id })">{{ org_name }}</div>
				</template>
			</a-auto-complete>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, defineProps, computed, onActivated, onUnmounted, defineComponent, h, unref } from 'vue'
import { positionList, subPositionList, allPartyPosition } from './position.js'
import { debounce } from '@/utils/utils.js'

const props = defineProps({
	treeData: {
		type: Array,
		default: () => [],
	},
	menuKey: {
		type: String,
	},
})

const subFruitData = ref<any>([])
// 当前级别
const status = ref(1)
// 选中结果
const searchValue = ref<any>({})
// 搜索结果
const searchList = ref<any>([])

const getClass = (item: any) => {
	if (item.patrol_index === undefined || item.patrol_index === null) {
		return { white: true }
	}

	return { greed: item.patrol_index > -10, yellow: item.patrol_index < -10 && item.patrol_index > -20, orange: item.patrol_index < -20 }
}
const onSearch = (value) => {
	// 找出value 相同项
	searchList.value = props.treeData.filter((item: any) => item.org_name.includes(value))
}
const shuffleArray = (array: Array<any>, seed: number) => {
	let rng = mulberry32(seed)
	let shuffledArray = array.slice() // 复制数组以避免修改原数组

	for (let i = shuffledArray.length - 1; i > 0; i--) {
		let j = Math.floor(rng() * (i + 1))
		;[shuffledArray[i], shuffledArray[j]] = [shuffledArray[j], shuffledArray[i]]
	}

	return shuffledArray
}

const mulberry32 = (seed: number) => {
	return function () {
		seed |= 0
		seed = (seed + 0x6d2b79f5) | 0
		let t = Math.imul(seed ^ (seed >>> 15), 1 | seed)
		t = (t + Math.imul(t ^ (t >>> 7), 61 | t)) ^ t
		return ((t ^ (t >>> 14)) >>> 0) / 4294967296
	}
}

const showTreeData = computed(() => {
	let len = props.treeData.length
	let _treeData = unref(props.treeData)
	// 打乱顺序, 但是打乱逻辑要保持一致
	_treeData = shuffleArray(_treeData, 12345)

	if (len > 30 && len < 90) {
		return _treeData.map((item: any, index: number) => {
			item.x = subPositionList[index]['x']
			item.y = subPositionList[index]['y']
			return item
		})
	} else if (len > 90) {
		return _treeData.map((item: any, index: number) => {
			console.log('🚀 ~ returnprops.treeData.map ~ index:', index)
			item.x = allPartyPosition[index]?.['x']
			item.y = allPartyPosition[index]?.['y']
			return item
		})
	} else {
		return _treeData.map((item: any, index: number) => {
			item.x = positionList[index]['x']
			item.y = positionList[index]['y']
			return item
		})
	}
})
const dataFormat = (data: Array<any>): Array<any> => {
	return data.map((item: any, index: number) => {
		if (!item.x) {
			let _index = item.x ? index + 1 : index

			item.x = positionList[_index]['x']
			item.y = positionList[_index]['y']
		}
		return item
	})
}

const emits = defineEmits(['select'])

const tree_box = ref()
interface Position {
	x: number
	y: number
}
const dataList = ref<any[]>()

const boxW = ref<number>(0)
const boxH = ref<number>(0)
onMounted(() => {
	dataList.value = positionList
	boxW.value = tree_box.value.offsetWidth
	boxH.value = tree_box.value.offsetHeight
})
// 转换行内样式
const formatPosition = ({ x, y }: Position) => {
	return { left: (boxW.value / 912) * x + 'px', top: (boxH.value / 866) * y + 'px' }
}
const onSelect = ({ org_id, org_name }: any) => {
	searchValue.value = {
		org_id,
		org_name,
	}
}

const autoCompleteRef = ref()

const onSearchClick = () => {
	// setInterval(() => {
	// 	console.log('运行。。。')
	// 	autoCompleteRef.value.focus()
	// }, 700)
}
onMounted(() => {
	console.log('dsadsa', autoCompleteRef.value)
	// autoCompleteRef.value.addEventListener('touchend', (e) => {
	// 	e.stopPropagation()
	// 	e.preventDefault()
	// })
	document.querySelector('#rc_select_0')?.addEventListener('touchend', (e) => {
		e.stopPropagation()
		e.preventDefault()

		autoCompleteRef.value.focus()
	})
})
const onHandleClick = () => {
	if (!searchValue.value.org_name) {
		searchValue.value = {}
	}
}
const onDetail = (item: any) => {
	if (item.has_subordinate === 1 && item.subordinate_list) {
		const { subordinate_list, ...current } = item
		subFruitData.value = dataFormat([...item.subordinate_list, current])
		status.value = 2
	} else {
		// 虚拟组织
		if (item.org_id < 0) return

		emits('select', item)
	}
}
const onCloseLevel2 = () => {
	status.value = 1

	subFruitData.value = []
}

const RefreshComponent = defineComponent({
	render(props: { $slots: { default: () => any } }) {
		console.log('rerender')
		return props.$slots.default()
	},
})
// hover状态控制
const hover_id = ref([])

const rerender = ref<any>('')
// 窗口变化重新渲染dom
const reRenderDom = debounce(() => {
	if (typeof window.orientation !== 'undefined') {
		// 当前设备是移动设备
		return
	}
	rerender.value = Date.now()

	boxW.value = tree_box.value.offsetWidth
	boxH.value = tree_box.value.offsetHeight
}, 200)

onActivated(() => {
	rerender.value = Date.now()
})

onMounted(() => {
	window.addEventListener('resize', reRenderDom)
})

onUnmounted(() => {
	window.removeEventListener('resize', reRenderDom)
})
</script>

<style lang="less" scoped>
.container {
	box-sizing: border-box;
	position: relative;
	width: 100%;
	height: 100%;
	.bg-tree {
		position: absolute;
		inset: 0;
		// 兼容
		left: 0px;
		right: 0px;
		top: 0px;
		bottom: 0px;
		background-image: url('@/assets/images/data-screen/tree_bgk.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		background-position: center;
		z-index: 2;
	}
	.fruit-box {
		position: absolute;
		inset: 0;
		z-index: 3;
	}
	.level-hidden {
		z-index: 1;
		opacity: 0.4;
	}
	.sub-fruit-box {
		position: absolute;
		inset: 0;
		z-index: 4;
	}
	.search-box {
		position: absolute;
		top: 6px;
		right: 0px;
		z-index: 1000;
		width: 325px;
		height: 40px;
		display: flex;
		padding-left: 14px;
		align-items: center;
		background: url('@/assets/images/data-screen/search-bg.png') no-repeat center center / 100% 100%;
		.search-icon {
			margin-right: 10px;
			display: inline-block;
			width: 20px;
			height: 20px;
			background: url('@/assets/images/data-screen/search.png') no-repeat center center / 100% 100%;
		}
		.ant-select-item {
			width: 100%;
			height: 100%;
		}

		:deep(.ant-select) {
			& * {
				border: none !important;
				box-shadow: none;
			}
			.ant-select-selector {
				background-color: transparent;
				border: none !important;
				width: 100%;
				height: 100%;
				outline: none;
			}
			input {
				border: none !important;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 16px;
				color: rgba(201, 226, 250, 0.65);
				line-height: 19px;
				pointer-events: auto;
				&:focus {
					outline: none;
					border: none;
				}
			}
		}
	}
	.circle {
		opacity: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: absolute;
		width: 90px;
		height: 90px;
		padding: 15px;
		text-align: center;
		font-weight: 500;
		font-size: 14px;
		color: #ffffff;
		line-height: 19px;
		text-shadow: 0px 0px 4px rgba(255, 255, 255, 0.22);
		border-radius: 50%;
		background-image: url('@/assets/images/data-screen/green_round_bgk.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		background-position: center;
		cursor: pointer;
	}
	.white {
		background: url('@/assets/images/data-screen/white_round_bgk.png') no-repeat center center / 100% 100%;
	}
	.all-circle {
		height: 64px;
		width: 64px;
		font-size: 16px;
		color: #ffffff;
		line-height: 1;
		.patrol_index {
			margin-top: -2px;
			font-size: 16px;
			line-height: 13px;
			vertical-align: middle;
		}
		.showOrg {
			display: block !important;
		}
		&:hover {
			.hover-box {
				display: block;
			}
		}
		.hover-box {
			display: none;
			position: absolute;
			z-index: 999;
			top: -25px;
			left: 50%;
			transform: translateX(-50%);
			.org-info {
				padding: 6px 9px;
				width: fit-content;
				white-space: nowrap;
				background: linear-gradient(180deg, #043c95 0%, rgba(4, 33, 68, 0.74) 14%, rgba(4, 23, 62, 0.8) 51%, rgba(7, 45, 94, 0.77) 88%, #043e97 100%);
				border: 1px solid #00d5fc;
				position: relative;
				z-index: 3;
			}

			&::after {
				content: '';
				position: absolute;
				z-index: 2;
				left: 50%;
				bottom: 0px;
				transform: translate(-50%, 90%);
				width: 22.37px;
				height: 27.28px;
				background: url('@/assets/images/data-screen/hover-icon.png') no-repeat center center / 100% 100%;
			}
		}
	}
	.green {
		background-image: url('@/assets/images/data-screen/green_round_bgk.png');
	}

	.yellow {
		background-image: url('@/assets/images/data-screen/yellow_round_bgk.png');
	}
	.orange {
		width: 90px;
		height: 90px;
		background-image: url('@/assets/images/data-screen/orange_round_bgk.png');
	}
}
.circle-enter,
.circle-leave-to {
	opacity: 0;
}

.circle-enter-active,
.circle-leave-active {
	transition: opacity 0.2s ease;
}
</style>
<style lang="less">
.org-drop-select {
	background-color: #051c50;
	.ant-select-item-option-active {
		background-color: rgb(4, 55, 150);
	}
	.ant-select-item {
		color: #ffffff;
	}
	.ant-select-item-option-active:hover {
		background-color: rgba(8, 20, 52, 0.8);
	}
	.rc-virtual-list-scrollbar-thumb {
		background-color: rgb(27, 57, 131) !important;
	}
}
</style>
