<template>
	<div class="container" ref="tree_box">
		<div
			v-for="(item, index) in showTreeData"
			@click="onDetail(item)"
			:key="index"
			:style="formatPosition(item)"
			class="circle"
			:class="getClass(item)"
		>
			<span>{{ item.org_name }}</span>
			<span v-if="item.patrol_index">{{ item.patrol_index }}</span>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, defineProps, computed } from 'vue'
import { positionList } from './position.js'

const props = defineProps({
	treeData: {
		type: Array,
		default: () => [],
	},
})

const getClass = (item) => {
	return { greed: item.patrol_index > -10, yellow: item.patrol_index < -10 && item.patrol_index > -20, orange: item.patrol_index < -20 }
}

const showTreeData = computed(() => {
	let len = props.treeData.length
	if (len >= 30) {
		let data = props.treeData.slice(0, 30)
		console.log(data, 'data')
		return data.map((item: any, index: number) => {
			item.x = positionList[index]['x']
			item.y = positionList[index]['y']
			return item
		})
	} else {
		return props.treeData.map((item: any, index: number) => {
			item.x = positionList[index]['x']
			item.y = positionList[index]['y']
			return item
		})
	}
})

const emits = defineEmits(['select'])

const tree_box = ref()
interface Position {
	x: number
	y: number
}
const dataList = ref<any[]>()

const boxW = ref<number>(0)
const boxH = ref<number>(0)
onMounted(() => {
	dataList.value = positionList
	boxW.value = tree_box.value.offsetWidth
	boxH.value = tree_box.value.offsetHeight
})
// 转换行内样式
const formatPosition = ({ x, y }: Position) => {
	return { left: (boxW.value / 912) * x + 'px', top: (boxH.value / 866) * y + 'px' }
}

const onDetail = (item: any) => {
	emits('select', item)
}
</script>

<style lang="less" scoped>
.container {
	box-sizing: border-box;
	position: relative;
	width: 100%;
	height: 100%;
	background-image: url('@/assets/images/data-screen/tree_bgk.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	background-position: center;

	.circle {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: absolute;
		width: 90px;
		height: 90px;
		padding: 10px;
		font-weight: 500;
		font-size: 14px;
		color: #ffffff;
		line-height: 19px;
		text-shadow: 0px 0px 4px rgba(255, 255, 255, 0.22);
		border-radius: 50%;
		background-image: url('@/assets/images/data-screen/green_round_bgk.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		background-position: center;
		cursor: pointer;
	}

	.green {
		background-image: url('@/assets/images/data-screen/green_round_bgk.png');
	}

	.yellow {
		background-image: url('@/assets/images/data-screen/yellow_round_bgk.png');
	}
	.orange {
		width: 90px;
		height: 90px;
		background-image: url('@/assets/images/data-screen/orange_round_bgk.png');
	}
}
</style>
