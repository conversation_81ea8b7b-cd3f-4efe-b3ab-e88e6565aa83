<template>
	<div class="party-inspection">
		<Title title="班子巡察">
			<div class="p-i-detail">
				<div class="p-id-text" @click="onDetail">详情</div>
			</div>
		</Title>
		<div class="pi-content">
			<div class="progress-proportion">
				<div class="pro-left">已开展 {{ data.totalInspection }}个班子</div>
				<div class="pro-right" style="width: 80%">总巡察{{ data.total }}个班子</div>
			</div>
			<div class="pi-c-chart">
				<div class="chart-box">
					<PieEcharts :data="chartsData" />
				</div>
			</div>
		</div>
	</div>
	<div class="party-inspection-problem">
		<Title title="巡察问题" />
		<div class="pip-content">
			<div class="score-item" v-for="item in summaryList" :key="item.inspection_item_id">
				<div class="score-title">{{ item.content }}</div>
				<div class="score-detail">
					<span class="number">{{ item.size }}</span>
					<span class="text">个班子，累计扣分</span>
					<div class="number">{{ item.org_reduce_score_sum }}</div>
				</div>
			</div>
		</div>
	</div>
	<Modal :visible="visible" :footer="null" :closable="false" class="inspection-modal" @cancel="onClose">
		<div class="i-modal-header">
			<div class="i-modal-left">
				<span class="im-title">巡察指数60-70的班子</span>
			</div>
			<div class="i-modal-right">
				<span class="close-icon" @click="onClose"></span>
			</div>
		</div>
		<div class="table-box">
			<Table
				:data-source="dataSource"
				:columns="columns"
				:row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : 'table-striped-1')"
				:pagination="{
					pageSize: 10,
				}"
			>
				<template #bodyCell="{ column, text }">
					<template v-if="column.key === 'index'">
						<span class="index-num">{{ text }}</span>
					</template>
				</template>
			</Table>
		</div>
	</Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Title from './Title.vue'
import PieEcharts from './PieCharts.vue'
import { getTeamInspectionList, getTeamSummary } from '@/apis/data-screen'
import { Modal, Table } from 'ant-design-vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
// Import Swiper styles
import 'swiper/css'

const data = ref({ three_size: 0, total: 0, totalInspection: 0, one_size: 0, two_size: 0 })

const summaryList = ref<any>([])

const visible = ref(false)

const chartsData = ref<any>([])

const columns = [
	{
		dataIndex: 'name',
		key: 'name',
		title: '班子名称',
		align: 'left',
	},
	{
		dataIndex: 'index',
		key: 'index',
		title: '巡察指数',
		align: 'center',
	},
	{
		dataIndex: 'rank',
		key: 'rank',
		title: '序列排名',
		align: 'center',
	},
]

const dataSource = ref([])

const onClose = () => {
	visible.value = false
}

const onSwiper = (swiper: any) => {
	console.log(swiper)
}

const onSlideChange = () => {
	console.log('slide change')
}
const onDetail = () => {
	visible.value = true
}

const loadData = async () => {
	const res = await getTeamInspectionList()

	if (res.code === 0) {
		data.value = res.data

		chartsData.value = [
			{ name: '-10(含)以上', value: data.value['one_size'] },
			{ name: '-20(含)~-10', value: data.value['two_size'] },
			{ name: '-20以下', value: data.value['three_size'] },
		]
	}
}

loadData()

const loadSummaryList = async () => {
	const res = await getTeamSummary()
	if (res.code === 0) {
		summaryList.value = res.data
	}
}
loadSummaryList()
</script>

<style lang="scss" scoped>
.party-inspection {
	height: 358px;
	width: 100%;
	display: flex;
	flex-direction: column;
	:deep(.inner-card__header) {
		flex-shrink: 0;
	}
	.p-i-detail {
		width: 103px;
		height: 27px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: url('../images/xuncha1.png') no-repeat center / cover;
		.p-id-text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 14px;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			&::after {
				content: '';
				display: inline-block;
				width: 16px;
				height: 16px;
				background: url('../images/xuncha5.png') no-repeat center / contain;
			}
		}
	}
	.pi-content {
		padding: 46px 27px 0px;
		flex: 1;
		background: url('../images/xuncha3.png') no-repeat center / 100% 100%;
		display: flex;
		flex-direction: column;
		.progress-proportion {
			display: flex;
			font-size: 14px;
			color: #ffffff;
			.pro-left {
				padding: 7px 24px;
				min-width: fit-content;
				background-color: #de8201;
				border-radius: 20px 0px 0px 20px;
				clip-path: polygon(0% 0%, 100% 0%, calc(100% - 10px) 100%, 0% 100%);
			}
			.pro-right {
				text-align: right;
				padding: 7px 24px;
				min-width: fit-content;
				background-color: #1c60fe;
				border-radius: 0px 20px 20px 0px;
				clip-path: polygon(10px 0%, 100% 0%, 100% 100%, 0% 100%);
			}
		}
		.pi-c-chart {
			flex: 1;
			width: 100%;
			display: flex;
			align-items: center;
			.chart-box {
				width: 100%;
				height: 178px;
			}
		}
	}
}
.party-inspection-problem {
	margin-top: 27px;
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;
	.pip-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 14px 0px;
		overflow: auto;
		padding: 29px 23px;
		&::-webkit-scrollbar {
			display: none;
		}
		.score-item {
			padding: 13px 14px;
			background: #051646;
			.score-title {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				line-height: 16px;
			}
			.score-detail {
				display: flex;
				align-items: flex-end;
				.number {
					font-size: 24px;
					line-height: 24px;
					color: #ff6c00;
					font-family: Rany-Medium;
				}
				.text {
					margin-top: 10px;
					font-size: 14px;
					color: rgba(255, 255, 255, 0.65);
				}
			}
		}
	}
}
</style>
<style lang="scss">
.inspection-modal {
	width: 1066px !important;
	height: 634px;
	.ant-modal-content {
		width: 100%;
		height: 100%;
		background: url('../images/modal-2.png') no-repeat center center / 100% 100%;
	}

	.ant-modal-body {
		padding: 0px !important;
		width: 100%;
		height: 100%;
		background-color: transparent !important;
		.i-modal-header {
			width: 100%;
			height: 50px;
			background: url('../images/modal-1.png') no-repeat center center / 100% 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0px 24px !important;
			.i-modal-left {
				.im-title {
					font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
					font-weight: bold;
					font-size: 24px;
					color: #ffffff;
					line-height: 32px;
					text-shadow: 0px 2px 20px #0086ff;
				}
			}
			.i-modal-right {
				font-size: 0px;
				.close-icon {
					display: inline-block;
					width: 24px;
					height: 24px;
					background: url('../images/modal-3.png') no-repeat center center / 100% 100%;
					cursor: pointer;
				}
			}
		}
	}
	.table-box {
		padding: 33px 33px 32px;
		& * {
			border: none !important;
		}
		.ant-table-empty {
			background-color: transparent;
		}
		.ant-empty-description {
			color: #ffffff;
			font-size: 14px;
		}
		.ant-table-thead {
			.ant-table-cell {
				background: rgb(45, 85, 227);
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 14px;
				color: #ffffff;
				text-align: left;
				font-weight: 500;
				color: #ffffff;
				line-height: 20px;
			}
		}
		.table-striped-1 {
			background: #06193a;
		}
		.table-striped {
			background: rgba(14, 37, 92, 1);
		}
		.ant-table-placeholder {
			background-color: transparent;
			&:hover > td {
				background-color: transparent;
			}
		}
		.ant-table-tbody {
			.ant-table-cell {
				background-color: transparent;
				color: #ffffff;
			}
		}
		.index-num {
			color: #cf7901;
		}
	}
	.ant-empty-description .ant-pagination-item-link {
		background: rgba(28, 98, 254, 0.2);
		color: rgba(255, 255, 255, 0.3);
	}
	.ant-pagination-item {
		background: rgba(28, 97, 254, 0.5);
		a {
			color: rgba(255, 255, 255, 0.65);
			font-size: 14px;
		}
	}
	.ant-pagination-item-active {
		background: #1c60fe;
		a {
			color: #ffffff;
			font-size: 14px;
		}
	}
}
</style>
