<template>
	<v-chart
		:option="option"
		ref="echarts"
		@touch="
			() => {
				console.log('dsadsa')
			}
		"
		style="height: 100%; width: 100%"
		autoresize
	/>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { convertPxToRem, debounce } from '@/utils/utils'
const option = ref({})
const echarts = ref<any>()
const emits = defineEmits(['select'])

const props = defineProps({
	data: { type: Array, required: true },
	type: { type: Number, required: false },
})

const initOption = (data: any) => {
	option.value = {
		legend: {
			show: true,
			orient: 'vertical',
			icon: 'circle',
			right: '10%',
			top: '30%',
			itemWidth: 6,
			itemHeight: 6,
			textStyle: {
				rich: {
					a: {
						fontSize: convertPxToRem(14),
						color: '#FFFFFF',
						width: convertPxToRem(80),
					},
					b: {
						fontSize: convertPxToRem(14),
						padding: [0, 0, 0, 20],
						color: 'rgba(0, 238, 99, 1)',
					},
					c: {
						fontSize: convertPxToRem(14),
						padding: [0, 0, 0, 20],
						color: 'rgba(255, 128, 0, 1)',
					},
					d: {
						fontSize: convertPxToRem(14),
						padding: [0, 0, 0, 20],
						color: 'rgba(180, 105, 0, 1)',
					},
				},
				color: 'auto',
			},
			formatter: (value: any) => {
				// 根据name 找出value
				const styleMap: any = {
					'-10(含)以上': 'b',
					'-20(含)~-10': 'c',
					'-20以下': 'd',
					'-15(含)~-10': 'c',
					'-15以下': 'd',
				}
				const item: any = data.find((item: any) => item.name === value)

				return `{a|${value}}{${styleMap[value]}|${item.value}个}`
			},
		},
		series: [
			{
				label: {
					show: false,
				},
				color: ['#00EE63', '#FF8000', '#B46900'],
				type: 'pie',
				radius: ['50%', '90%'],
				center: ['23%', '50%'],
				data: data,
				// width: convertPxToRem(148),
				// height: convertPxToRem(148),
				itemStyle: {
					emphasis: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 0.5)',
					},
				},
				labelLine: {
					show: true,
				},
				z: 2,
			},
			{
				label: {
					show: false,
				},
				emptyCircleStyle: {
					color: 'rgba(0, 19, 66, 0.75)',
				},
				type: 'pie',
				center: ['23%', '50%'],
				radius: ['0%', '60%'],
				z: 3,
			},
		],
	}
}

onMounted(() => {
	const instance = echarts.value.chart

	console.log(instance)

	instance.on('legendselectchanged', (e: any) => {
		instance.dispatchAction({
			type: 'legendAllSelect',
		})
		const data = props.data
		// 找出e.name

		const item: any = data.find((item: any) => item.name === e.name)
		console.log('dsdas')
		emits('select', {
			...item,
			type: props.type,
		})
	})
})

watch(
	() => props.data,
	(newVal: any) => {
		initOption(newVal)
	},
	{
		deep: true,
	}
)
// 窗口变化重新渲染dom
const reRenderDom = debounce(() => {
	props.data.length && initOption([...props.data])
}, 200)

onMounted(() => {
	window.addEventListener('resize', reRenderDom)
})

onUnmounted(() => {
	window.removeEventListener('resize', reRenderDom)
})
</script>

<style lang="scss" scoped></style>
