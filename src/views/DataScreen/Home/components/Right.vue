<template>
	<div class="leader-inspection">
		<Title>
			<template #sub-title>
				<div :class="leader === 1 ? 'title' : 'sub-title-1'" class="cursor-pointer" @click="leader = 1">一把手巡察指数</div>
				<div :class="leader === 2 ? 'title' : 'sub-title-1'" class="m-left-5 cursor-pointer" @click="leader = 2">其他班子成员巡察指数</div>
			</template>
		</Title>
		<div class="pi-content" @touchend.prevent="() => {}">
			<div style="height: 33.078%" class="level-1">
				<PieCharts class="charts" :data="data.inspectionIndex" @select="onPieSelect" :type="1" />
			</div>
			<div style="height: 36.79%" class="level-2">
				<div class="l-i-title">
					<div class="l-i-title-text">
						<span class="icon"></span>
						<span>巡察测评概况</span>
					</div>
					<div class="bottom-img"></div>
				</div>
				<div class="charts-box">
					<PieCharts class="charts" :data="data.speechRatingIndex" @select="onPieSelect" :type="2" />
				</div>
			</div>
			<div style="height: 36.79%" class="level-3">
				<div class="l-i-title">
					<div class="l-i-title-text">
						<span class="icon"></span>
						<span>谈话等次概况</span>
					</div>
					<div class="bottom-img"></div>
				</div>
				<div class="charts-box">
					<PieCharts class="charts" :data="data.orgInspectionIndex" @select="onPieSelect" :type="3" />
				</div>
			</div>
		</div>
	</div>
	<DataModal :visible="modalVisible" :title="title" :dataSource="dataSource" :columns="columns" :on-close="onModalClose" :loading="modalloading" />
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import Title from './Title.vue'
import PieCharts from './PieCharts.vue'
import { getAHand, getAHandList } from '@/apis/data-screen'
import DataModal from '../../Components/DataModal.vue'

const data = ref<any>({
	speechRatingIndex: [],
	inspectionIndex: [],
	orgInspectionIndex: [],
})

const title = ref('')
const leader = ref(1)
const modalVisible = ref(false)
const modalloading = ref(false)

const dataSource = ref([])

const columns = [
	{
		dataIndex: 'avatar_party_user',
		key: 'avatar_party_user',
		align: 'left',
		title: '班子成员',
		width: '40%',
	},
	{
		dataIndex: 'inspection_index',
		key: 'index',
		align: 'center',
		title: '巡察指数',
		width: '30%',
	},
	{
		dataIndex: 'rank',
		key: 'rank',
		align: 'center',
		title: '序列排名',
		width: '30%',
	},
]
function formatData(data: any) {
	return [
		{ name: '-10(含)以上', value: data['one_size'], index_type: 1 },
		{ name: '-15(含)~-10', value: data['two_size'], index_type: 2 },
		{ name: '-15以下', value: data['three_size'], index_type: 3 },
	]
}

const loadData = async () => {
	const res = await getAHand({ leader: leader.value })

	if (res.code === 0) {
		const { speechRatingIndex, inspectionIndex, orgInspectionIndex } = res.data
		data.value = {
			speechRatingIndex: formatData(speechRatingIndex),
			inspectionIndex: formatData(inspectionIndex),
			orgInspectionIndex: formatData(orgInspectionIndex),
		}
	}
}

const onModalClose = () => {
	modalVisible.value = false
	dataSource.value = []
}

const onPieSelect = async ({ type, index_type, name }: any) => {
	title.value = `巡察指数${name}的班子成员`
	modalVisible.value = true
	modalloading.value = true
	const res = await getAHandList({ leader: leader.value, type: type, index_type })
	if (res.code === 0) {
		dataSource.value = res.data
		modalloading.value = false
	}
}

watch(
	leader,
	() => {
		loadData()
	},
	{
		immediate: true,
		deep: true,
	}
)
</script>

<style lang="less" scoped>
.flex-align-center {
	display: flex;
	align-items: center;
}
.flex-columns {
	display: flex;
	flex-direction: column;
}
.leader-inspection {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	.p-i-header {
		height: 36px;
		width: 100%;
	}
	.p-i-detail {
		width: 103px;
		height: 27px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: url('../images/xuncha1.png') no-repeat center / cover;
		.p-id-text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 14px;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			&::after {
				content: '';
				display: inline-block;
				width: 16px;
				height: 16px;
				background: url('../images/xuncha5.png') no-repeat center / contain;
			}
		}
	}
	.pi-content {
		padding: 0px 34px;
		flex: 1;
		background: url('../images/xuncha3.png') no-repeat center / 100% 100%;
		display: flex;
		flex-direction: column;
		height: 100%;
		.charts {
			height: 178px !important;
		}
		.l-i-title {
			display: flex;
			flex-direction: column;
			align-items: center;
			.l-i-title-text {
				display: flex;
				align-items: center;
				.icon {
					position: relative;
					display: inline-block;
					width: 8px;
					height: 12px;
					background: url('../images/leader-1.png') no-repeat center / cover;
					margin-right: 4px;
					filter: brightness(130%);
					&::before {
						position: absolute;
						left: 50%;
						transform: translateX(-50%);
						content: '';
						width: 10px;
						height: 12px;
						background-color: rgba(0, 145, 255, 0.6);
						filter: blur(4px);
					}
				}
				font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
				font-weight: bold;
				font-size: 16px;
				color: rgba(255, 255, 255, 0.85);
				line-height: 22px;
				text-shadow: 0px 0px 10px #0091ff, 0px 0px 4px #0091ff;
			}
			.bottom-img {
				width: 262.66px;
				height: 8px;
				background: url('../images/leader-2.png') no-repeat center / 100% 100%;
			}
		}

		.level-1 {
			.flex-columns;
			justify-content: center;
			height: 33.078%;
		}
		.level-2 {
			.flex-columns;
			.charts-box {
				.flex-align-center;
				flex: 1;
			}
			height: 36.79%;
		}
		.level-3 {
			flex: 1;
			.flex-columns;
			.charts-box {
				.flex-align-center;
				flex: 1;
			}
		}
	}
}
.party-inspection-problem {
	margin-top: 27px;
	display: flex;
	flex-direction: column;
	flex: 1;
}

.title {
	display: flex;
	align-items: center;

	font-family: 'Alimama ShuHeiTi', AlimamaShuHeiTi, Alimama ShuHeiTi;
	font-weight: bold;
	font-size: 20px;
	color: #ffffff;
	line-height: 26px;
	text-shadow: 0px 0px 8px #088bff;
	text-align: left;
}
.sub-title-1 {
	margin-left: 21px;
	font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
	font-weight: bold;
	font-size: 16px;
	color: rgba(255, 255, 255, 0.6);
	line-height: 26px;
	text-shadow: 0px 0px 8px #088bff;
	text-align: left;
}
.cursor-pointer {
	cursor: pointer;
}
</style>
