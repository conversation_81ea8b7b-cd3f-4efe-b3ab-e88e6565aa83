<template>
	<div class="party-inspection">
		<Title title="班子巡察">
			<div class="p-i-detail">
				<div
					class="p-id-text"
					@click="
						() => {
							// data?.leader?.inspection_index !== '-' &&
							// 	data?.leader?.inspection_index !== null &&
							onInspectPage(`/data-screen/party-inspection-detail`)
						}
					"
				>
					详情
				</div>
			</div>
		</Title>
		<div class="pi-content">
			<div class="progress-proportion">
				<div class="pro-left">已开展 {{ data.totalInspection }}个班子</div>
				<div class="pro-right" style="width: 80%">总巡察{{ data.total }}个班子</div>
			</div>
			<div class="pi-c-chart">
				<div class="chart-box">
					<PieEcharts :data="chartsData" @select="onDetail" />
				</div>
			</div>
		</div>
	</div>
	<div class="party-inspection-problem">
		<Title title="巡察问题" />
		<div class="pip-content">
			<swiper
				slides-per-view="auto"
				direction="vertical"
				loop
				:autoplay="{
					delay: 2000,
					pauseOnMouseEnter: true,
					disableOnInteraction: false,
				}"
				:modules="[Autoplay]"
				:space-between="10"
			>
				<swiper-slide v-for="item in summaryList" :key="item.inspection_item_id" style="height: auto">
					<div class="score-item">
						<div class="score-title">{{ item.content }}</div>
						<div class="score-detail">
							<span class="number">{{ item.size }}</span>
							<span class="text">个班子，累计扣分</span>
							<div class="number">{{ item.org_reduce_score_sum }}</div>
						</div>
					</div>
				</swiper-slide>
			</swiper>
		</div>
	</div>
	<DataModal :visible="visible" :page-size="10" :title="title" :dataSource="dataSource" :columns="columns" :on-close="onClose" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Title from './Title.vue'
import PieEcharts from './PieCharts.vue'
import { getTeamInspectionList, getTeamSummary, getTeamInspectionListForScore } from '@/apis/data-screen'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay } from 'swiper/modules'
import DataModal from '../../Components/DataModal.vue'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/autoplay'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const data = ref({ three_size: 0, total: 0, totalInspection: 0, one_size: 0, two_size: 0 })

const summaryList = ref<any>([])
const title = ref<any>()
const visible = ref(false)
const modalloading = ref(false)

const chartsData = ref<any>([])

const dataSource = ref([])

const columns = [
	{
		dataIndex: 'org_name',
		key: 'org_name',
		title: '班子名称',
		align: 'left',
		width: '40%',
	},
	{
		dataIndex: 'patrol_index',
		key: 'index',
		title: '巡察指数',
		align: 'center',
		width: '30%',
	},
	{
		dataIndex: 'patrol_index_rank',
		key: 'patrol_index_rank',
		title: '序列排名',
		align: 'center',
		width: '30%',
	},
]

const onClose = () => {
	console.log('close')
	visible.value = false
	dataSource.value = []
}

const onDetail = async ({ index_type, name }: any) => {
	console.log('sdad')
	title.value = `巡察指数${name}的班子`
	visible.value = true
	modalloading.value = true
	const res = await getTeamInspectionListForScore({ index_type })
	if (res.code === 0) {
		dataSource.value = res.data
		modalloading.value = false
	}
}

const loadData = async () => {
	const res = await getTeamInspectionList()

	if (res.code === 0) {
		data.value = res.data

		chartsData.value = [
			{ name: '-10(含)以上', value: data.value['one_size'], index_type: 1 },
			{ name: '-20(含)~-10', value: data.value['two_size'], index_type: 2 },
			{ name: '-20以下', value: data.value['three_size'], index_type: 3 },
		]
	}
}

loadData()

const loadSummaryList = async () => {
	const res = await getTeamSummary()
	if (res.code === 0) {
		summaryList.value = res.data
	} else {
		message.warn(res.message)
	}
}
loadSummaryList()

const onInspectPage = (path: string) => {
	router.push(path)
}
</script>

<style lang="scss" scoped>
.party-inspection {
	height: 358px;
	width: 100%;
	display: flex;
	flex-direction: column;
	:deep(.inner-card__header) {
		flex-shrink: 0;
	}
	.p-i-detail {
		width: 103px;
		height: 27px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: url('../images/xuncha1.png') no-repeat center / cover;
		.p-id-text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 14px;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			&::after {
				content: '';
				display: inline-block;
				width: 16px;
				height: 16px;
				background: url('../images/xuncha5.png') no-repeat center / contain;
			}
		}
	}
	.pi-content {
		padding: 46px 27px 0px;
		flex: 1;
		background: url('../images/xuncha3.png') no-repeat center / 100% 100%;
		display: flex;
		flex-direction: column;
		.progress-proportion {
			display: flex;
			font-size: 14px;
			color: #ffffff;
			.pro-left {
				padding: 7px 24px;
				min-width: fit-content;
				background-color: #de8201;
				border-radius: 20px 0px 0px 20px;
				clip-path: polygon(0% 0%, 100% 0%, calc(100% - 10px) 100%, 0% 100%);
			}
			.pro-right {
				text-align: right;
				padding: 7px 24px;
				min-width: fit-content;
				background-color: #1c60fe;
				border-radius: 0px 20px 20px 0px;
				clip-path: polygon(10px 0%, 100% 0%, 100% 100%, 0% 100%);
			}
		}
		.pi-c-chart {
			flex: 1;
			width: 100%;
			display: flex;
			align-items: center;
			.chart-box {
				width: 100%;
				height: 178px;
			}
		}
	}
}
.party-inspection-problem {
	margin-top: 27px;
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;
	.pip-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 14px 0px;
		overflow: auto;
		padding: 29px 23px;
		cursor: grab;
		&:active {
			cursor: grabbing;
		}
		&::-webkit-scrollbar {
			display: none;
		}
		.score-item {
			height: 100%;
			padding: 13px 14px;
			background: #051646;
			user-select: none;

			.score-title {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				line-height: 16px;
			}
			.score-detail {
				display: flex;
				align-items: flex-end;
				margin-top: 10px;
				.number {
					font-size: 24px;
					line-height: 24px;
					color: #ff6c00;
					font-family: Rany-Medium;
				}
				.text {
					font-size: 14px;
					color: rgba(255, 255, 255, 0.65);
				}
			}
		}
	}
}
</style>
