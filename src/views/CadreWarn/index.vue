<template>
	<div class="cadre-warn">
		<p-header :title="$route.meta.title" :on-back="onBack" />
		<div class="content-box">
			<div class="menu-box">
				<div
					class="check-item"
					:class="{
						active: selectIndex == item.key,
					}"
					v-for="(item, index) in menuList"
					:key="item.key"
					@click="onChange(item.key, index)"
				>
					{{ item.label }}
				</div>
			</div>
			<div class="table-box">
				<a-table :columns="columns" :data-source="dataSource" :pagination="false" :scroll="{ y: tableScroll }" bordered :loading="loading">
					<template #bodyCell="{ column, text, record }">
						<template v-if="column.dataIndex === 'name'">
							<a @click="onLocation(record)">{{ text }}</a>
						</template>
						<template v-if="column.dataIndex === 'type'">
							<div style="text-align: left" class="type-box">
								<template v-for="(item, index) in lzString(text)" :key="index">
									<span>{{ item }}</span>
									<template v-if="record.num > 1 && item === '廉政风险'">
										<span class="number-icon">x</span>
										<span class="number">{{ record.num }}</span>
									</template>
									<span v-if="index < lzString(text).length - 1">;</span>
								</template>
							</div>
						</template>
						<template v-if="column.dataIndex === 'detail'">
							<div v-html="splitString(text)"></div>
						</template>
					</template>
				</a-table>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { alertUserList } from '@/apis/cadre-warn'
import { historyPush } from '@/utils/history'

const route = useRoute()
const router = useRouter()
const { org_id } = route.query

const activeIndex = ref<any>([])

const selectIndex = ref(0)

const loading = ref(false)

const menuList = ref<any>([
	{
		label: '全部',
		key: '0',
		value: 0,
	},
	{
		label: '处分影响期内',
		key: '1',
		value: 0,
	},
	{
		label: '试用期即将届满',
		key: '2',
		value: 0,
	},
	{
		label: '任现职时间较长',
		key: '3',
		value: 0,
	},
	{
		label: '任职回避',
		key: '5',
		value: 0,
	},
	{
		label: '廉政风险',
		key: '6',
		value: 0,
	},
	{
		label: '江湖习气重',
		key: '7',
		value: 0,
	},
])

const columns = [
	{
		dataIndex: 'name',
		key: 'name',
		name: '预警干部',
		title: '预警干部',
		align: 'center',
	},
	{
		dataIndex: 'current_job',
		key: 'current_job',
		name: '现任职务',
		title: '现任职务',
		align: 'left',
	},
	{
		dataIndex: 'current_job_time',
		key: 'current_job_time',
		name: '任现职务时间',
		title: '任现职务时间',
		align: 'center',
		width: '10%',
	},
	{
		dataIndex: 'type',
		key: 'type',
		name: '预警类别',
		title: '预警类别',
		align: 'center',
	},
	{
		dataIndex: 'detail',
		key: 'detail',
		name: '详情',
		title: '详情',
		align: 'left',
	},
]

const data = ref<any>([])

const dataSource = computed(() => {
	if (selectIndex.value == 0) {
		return data.value
	} else {
		return data.value.filter((item: any) => item.type_flag.split(';').includes(selectIndex.value))
	}
})

const splitString = (str: string) => {
	return str.split(';').join('<br />')
}
const lzString = (str: string) => {
	return str.split(';')
}
const onBack = () => {
	router.back()
}

const onChange = (index: any, type: any) => {
	selectIndex.value = index
}
const onDetail = () => {
	console.log('详情')
}
const onLocation = (data: any) => {
	if (!data.user_id) return

	historyPush(`/cadre-portrait/home?user_id=${data.user_id}`)
}
const loadData = () => {
	loading.value = true
	alertUserList({ org_id })
		.then((res: any) => {
			if (res.code == 0) {
				data.value = res.data
			} else {
				// console.log(res.message)
			}
			loading.value = false
		})
		.catch(() => {
			loading.value = false
		})
}

loadData()

const tableScroll = ref('')

onMounted(() => {
	// ant-table-thead
	const thead = document.querySelector('.ant-table-thead')
	// 获取thead的距离
	const theadTop: any = thead?.getBoundingClientRect().height
	// content-box
	const tableBox = document.querySelector('.table-box')
	// 获取contentBox的距离
	const tableBoxTop: any = tableBox?.getBoundingClientRect().height

	tableScroll.value = `${tableBoxTop - theadTop}px`
})
</script>

<style lang="less" scoped>
.cadre-warn {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	.content-box {
		padding: 24px;
		margin-top: 12px;
		width: 100%;
		flex: 1;
		overflow: hidden;
		background: #ffffff;
		display: flex;
		flex-direction: column;
		.menu-box {
			display: flex;
			padding: 24px 0px 24px 16px;
			gap: 0px 15px;
			border-top: 2px solid rgba(0, 0, 0, 0.06);
			.check-item {
				padding: 11px 25px;
				min-width: 173px;
				font-weight: 500;
				font-size: 26px;
				color: #000000;
				line-height: 30px;
				border-radius: 3px;
				text-align: center;
				background: #f5f5f5;
			}
			.active {
				background: #008eff;
				color: #ffffff;
			}
		}
		.table-box {
			overflow: hidden;
			flex: 1;
		}
		.type-box {
			.number-icon {
				margin: 0px 2px 0px 4px;
				font-family: 'Source Han Sans CN-Regular';
				font-size: 22px;
				color: rgba(240, 42, 42, 1);
			}
			.number {
				font-family: Rany, Rany;
				font-weight: normal;
				font-size: 25px;
				color: #f02a2a;
				line-height: 40px;
			}
		}
	}
}
:deep(.ant-table-cell) {
	font-size: 20px !important;
}
</style>
