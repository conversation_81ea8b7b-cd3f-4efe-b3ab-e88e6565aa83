<template>
	<div class="comparison">
		<div class="header"></div>
		<div class="contrast-box">
			<Card title="人员设置" header class="chosse-box">
				<div class="choose">
					<div class="contrast-seek">
						<div class="seek-title">
							<div class="input-box"><span>姓名：</span><a-input type="text" v-model:value="search.name" /></div>
							<div class="input-box"><span>职务：</span><a-input type="text" v-model:value="search.current_position" /></div>
							<button @click="onLookUp" class="look-up" border>查询</button>
							<button @click="onReset" class="reset">重置</button>
						</div>
						<div class="seek-table">
							<a-table
								:row-selection="{ selectedRowKeys: state.selectedRowKeys, onSelect: onSelect, hideSelectAll: true }"
								:columns="columns"
								:data-source="dataSource"
								:pagination="false"
								rowKey="user_id"
								bordered
								:style="{
									maxHeight: '551px',
									overflow: 'auto',
								}"
							/>
						</div>
					</div>
					<div class="already-have">
						<div class="have-nubmer">
							<span class="label">已选: </span>
							<span class="select-number">{{ quotaList.length }}/3</span>
						</div>
						<div class="have-name">
							<div v-for="(item, index) in quotaList" :key="index" class="select-box">
								<span>{{ item.name }}</span
								><span class="have-delete" @click.stop="deleteName(index)"></span>
							</div>
						</div>
					</div>
				</div>
			</Card>
			<Card title="基础信息" header class="veidoo-box">
				<div class="veidoo">
					<div class="veidoo-item">
						<div class="title">基础信息</div>
						<div class="data">
							<div class="check-item" v-for="(item, index) in basicMessage" :key="index">
								<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
							</div>
						</div>
					</div>
					<div class="veidoo-item">
						<div class="title">干部画像</div>
						<div class="data">
							<div class="check-item" v-for="(item, index) in cadreMessage" :key="index">
								<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
							</div>
						</div>
					</div>
				</div>
			</Card>
			<div class="start-analyse">
				<a-button class="analyse" @click.stop="onLocation">开始分析</a-button>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref } from 'vue'
import { getDimensionConfig } from '@/apis/cadre-portrait/home'
import { message } from 'ant-design-vue'
const columns = [
	{
		title: '姓名',
		dataIndex: 'name',
	},
	{
		title: '职务',
		dataIndex: 'current_position',
	},
]
export default defineComponent({
	setup() {
		const comparisonUser = JSON.parse(sessionStorage.getItem('comparisonUser') || '{}')
		if (!Object.keys(comparisonUser).length) {
			window.location.href = '/cadre-portrait/home'
		}
		const data = ref(comparisonUser)
		const quotaList = ref<any>([])
		const dataSource = ref<any>(comparisonUser)
		const basicMessage = ref<any>([])
		const cadreMessage = ref<any>([])
		const search = reactive<any>({
			name: '',
			current_position: '',
		}) //搜索对象
		const state = reactive<any>({
			selectedRowKeys: [],
		}) // 选中的key
		let _selectedRowKeys: any = []
		/**
		 * @description: 查询
		 * @return {*}
		 */
		const onLookUp = () => {
			if (!search.name && !search.current_position) {
				return (dataSource.value = data.value)
			}
			const res = data.value.filter((item: any) => search.name == item.name || search.current_position == item.current_position)

			dataSource.value = res
		}
		/**
		 * @description: 重置
		 * @return {*}
		 */
		const onReset = () => {
			dataSource.value = data.value
			search.name = ''
			search.current_position = ''
		}
		/**
		 * @description: 选中项
		 * @param {*} record
		 * @return {*}
		 */
		const onSelect = (record: any) => {
			const index = _selectedRowKeys.findIndex((item: any) => {
				return item === record.user_id
			})
			if (index == -1 && _selectedRowKeys.length < 3) {
				_selectedRowKeys.push(record.user_id)
				quotaList.value.push(record)
			} else {
				if (index == -1) {
					return void 0
				}
				_selectedRowKeys.splice(index, 1)
				quotaList.value.splice(index, 1)
			}
			state.selectedRowKeys = [..._selectedRowKeys]
		}
		// 删除选中项中的数据
		const deleteName = (index: number) => {
			quotaList.value.splice(index, 1)
			state.selectedRowKeys.splice(index, 1)
			_selectedRowKeys.splice(index, 1)
		}
		const getRadio = async () => {
			const { data, code } = await getDimensionConfig({})
			if (code == 0) {
				data.map((item: any) => {
					if (item.name == '基础信息') {
						basicMessage.value = item.children
					} else {
						cadreMessage.value = item.children
					}
				})
			}
		}
		/**
		 * @description: 跳转去对比
		 * @return {*}
		 */
		const onLocation = () => {
			if (quotaList.value.length < 2 || quotaList.value.length > 3) {
				message.error('请选择2个人或者3个人对比')
				return
			}
			let user_id: any[] = []
			let config_ids: any[] = []
			quotaList.value.forEach((element: any) => {
				user_id.push(element.user_id)
			})
			basicMessage.value.forEach((element: any) => {
				if (element.is_select) {
					config_ids.push(element.config_id)
				}
			})
			cadreMessage.value.forEach((element: any) => {
				if (element.is_select) {
					config_ids.push(element.config_id)
				}
			})
			window.open(`/comparison-results?user_id=${encodeURIComponent(user_id.join(','))}&config_ids= ${encodeURIComponent(config_ids.join(','))}`)
			// router.push({
			// 	path: '/comparison-results',
			// 	query: {
			// 		user_id: user_id.join(','),
			// 		config_ids: config_ids.join(','),
			// 	},
			// })
		}
		getRadio()
		return { columns, dataSource, search, quotaList, state, basicMessage, cadreMessage, onSelect, onLookUp, onReset, onLocation, deleteName }
	},
})
</script>

<style scoped lang="less">
.comparison {
	position: relative;
	width: 100%;
	height: 100%;
	overflow-y: auto;
	padding-top: 172px;
	z-index: 1;
	background-color: #f6f8fc;
	.header {
		position: absolute;
		top: 0;
		width: 100%;
		height: 488px;
		border-radius: 0px 0px 0px 0px;
		background: url('@/assets/images/comparison-header.png') no-repeat center / cover;
		z-index: -1;
	}
	.contrast-box {
		margin: 0 auto;
		width: 1200px;
		min-height: 100%;
		// overflow-y: auto;
		// height: 1215px;
		// border: 1px solid #23dbfc;
		::v-deep(.card-box) {
			padding: 0;
			.title {
				font-size: 20px;
			}
		}
		.chosse-box {
			padding: 24px;
			background-color: #ffffff;
			.choose {
				margin-top: 24px;
				display: flex;
				justify-content: space-between;
			}
			.contrast-seek {
				width: 706px;
				display: flex;
				flex-direction: column;
				margin-right: 12px;

				.seek-table {
					height: 551px;
					::v-deep(.ant-table) {
						&::-webkit-scrollbar {
							display: none !important;
						}
					}
					::v-deep(.ant-table-thead) {
						.ant-table-cell {
							background-color: #f3f3f3;
						}
					}
				}

				.seek-title {
					padding: 21px 20px;
					display: flex;
					align-items: center;
					margin-bottom: 17px;
					width: 100%;
					height: 76px;
					background: #f7f8fa;
					border-radius: 4px 4px 4px 4px;
					.input-box {
						display: flex;
						align-items: center;
						height: 40px;
						span {
							font-size: 16px;
							font-family: Source Han Sans CN;
							font-weight: 400;
							color: #222222;
							white-space: nowrap;
						}
						input {
							width: 160px;
						}
					}
					.look-up {
						width: 80px;
						height: 36px;
						background: #e5231a;
						border-radius: 4px 4px 4px 4px;
						opacity: 1;
						font-size: 16px;
						line-height: 16px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #ffffff;
						outline: none;
					}
					.reset {
						width: 80px;
						height: 36px;
						background: #ededed;
						border-radius: 4px 4px 4px 4px;
						font-size: 16px;
						line-height: 16px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #666666;
						opacity: 1;
						outline: none;
					}
					button {
						margin-right: 10px;
						width: 80px;
						height: 36px;
						font-size: 16px;
						font-weight: 400;
					}
					span {
						margin-right: 11px;
					}

					div {
						margin-right: 24px;
					}
					// button {
					// 	outline: none;
					// }
				}

				// input {
				// 	background: #fff;
				// 	border: 1px solid #00d2ff;
				// 	border-radius: 2px;
				// 	width: 120px;
				// 	height: 24px;
				// 	color: #fff;
				// }

				// button {
				// 	width: 62px;
				// 	height: 24px;
				// 	border: 1px solid #00d2ff;
				// 	border-radius: 4px;
				// 	padding: 0px;
				// 	font-size: 14px;
				// 	font-family: Source Han Sans CN;
				// 	font-weight: 400;
				// 	color: #00d2ff;
				// }
			}

			.already-have {
				width: 408px;
				display: flex;
				flex-direction: column;

				.have-nubmer {
					display: flex;
					align-items: center;
					padding: 28px 20px;
					width: 408px;
					height: 76px;
					background: #f7f8fa;
					border-radius: 4px 4px 4px 4px;
					opacity: 1;
					font-size: 14px;
					font-weight: 400;
					color: #00d2ff;
					.label {
						font-size: 16px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #222222;
						line-height: 24px;
					}
					.select-number {
						margin-left: 10px;
						font-size: 16px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #2462ff;
					}
				}

				.have-name {
					margin-top: 20px;
					padding: 20px;
					height: 551px;
					background: #f7f8fa;
					border-radius: 4px 4px 4px 4px;
					opacity: 1;
					.select-box {
						display: flex;
						justify-content: space-between;
					}
					div {
						margin-bottom: 33px;
						cursor: pointer;
					}

					span {
						font-size: 16px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #222222;
					}

					.have-delete {
						width: 24px;
						height: 24px;
						display: inline-block;
						background-image: url('@/assets/images/delete.png');
						background-size: 100% 100%;
						margin-left: 15px;
						vertical-align: middle;
					}
				}
			}
		}

		.veidoo-box {
			margin-top: 24px;
			padding: 24px;
			width: 100%;
			background: #ffffff;
			.veidoo {
				margin-top: 10px;
				width: 100%;
				.top-content {
					display: flex;
					margin-top: 49px;
					margin-bottom: 31px;

					.label {
						display: flex;
						align-items: center;
						font-size: 18px;
						font-weight: bold;
						color: #00fff6;
						line-height: 18px;
						text-shadow: 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2),
							0 0 4px rgba(35, 219, 252, 0.2);
						background: linear-gradient(0deg, #3bdeff 6.1279296875%, #d1fbff 55.4443359375%, #ddf9ff 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;

						&::after {
							margin-left: 7px;
							content: '';
							display: inline-block;
							width: 24px;
							height: 16px;
							background: url('@/assets/images/label-icon.png') no-repeat center / cover;
						}
					}
				}
				.top-content-two {
					margin-bottom: 22px;
				}
				.veidoo-item {
					display: flex;
					border: 1px solid #ebebeb;
					.title {
						display: flex;
						align-items: center;
						justify-content: center;

						width: 100px;
						height: 192px;
						background: #f3f3f3;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;

						font-size: 16px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #222222;
						line-height: 24px;
					}

					.data {
						flex: 1;
						padding: 28px 84px;

						display: flex;
						flex-wrap: wrap;
						.check-item {
							width: 25%;
						}
					}

					.two-title {
						display: flex;
						width: 169px;
						display: flex;
						align-items: center;
						justify-content: center;
						padding: 68px 0;
					}

					div {
						font-size: 14px;
						font-family: Source Han Sans CN;
						font-weight: 500;
						color: #00d2ff;
					}
				}
			}
		}
		.start-analyse {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 16px;
			width: 1200px;
			height: 88px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			button {
				width: 160px;
				height: 48px;
				background: #e5231a;
				border-radius: 4px 4px 4px 4px;
				opacity: 1;
				font-size: 16px;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #ffffff;
				outline: none;
				border: 0;
				&:active {
					opacity: 0.7;
				}
			}
		}
	}
}
</style>
