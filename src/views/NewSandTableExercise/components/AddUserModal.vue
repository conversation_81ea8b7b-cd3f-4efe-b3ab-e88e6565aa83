<template>
	<a-modal class="add-user-modal" title="添加调整名单" width="40%" :visible="visible" @cancel="oncancel" :afterClose="onAfterClose" :footer="null">
		<div class="user-modal-content">
			<div class="tag">注：仅支持添加中层干部兼（挂）职情况</div>
			<div class="form-box">
				<a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }" :model="formState">
					<a-form-item name="name" label="干部姓名">
						<div class="input-box">
							<PAutoComplete
								ref="dropRef"
								class="poc-input"
								v-model:value="formState.name"
								placeholder=""
								@search="fetchInfo($event)"
								@change="updateLabel"
								:options="fetchState"
								allow-clear
								:focu="false"
							>
								<template #default="{ data }">
									<div class="search-result">
										<div class="name">{{ data.name }}</div>
										<div class="job-name">{{ data.current_job }}</div>
									</div>
								</template>
							</PAutoComplete>
							<span style="white-space: nowrap" class="label-text"> &nbsp; 输入姓名后匹配选择</span>
						</div>
					</a-form-item>
					<a-form-item name="" label="调整类别">
						<PocCheck type="radio" class="m-left-10" :options="otherOptions" v-model:value="formState.adjust_type" />
					</a-form-item>
					<a-form-item name="current_job" label="拟任职务">
						<a-input class="position-input" placeholder="请输入" v-model:value="formState.current_job" />
					</a-form-item>
				</a-form>
				<div class="btn-box">
					<a-button type="primary" @click="onsubmit">确定</a-button>
				</div>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref, shallowRef, nextTick } from 'vue'
import PocCheck from './check.vue'
import { getMiddleLayer, addCandidateMiddle } from '@/apis/new-sand-table-exercise'
import { message } from 'ant-design-vue'
import PAutoComplete from '@/components/AutoComplete.vue'
import Data from './data.ts'
const props = defineProps({
	mock_id: String,
	onSuccess: Function,
})

const emits = defineEmits(['close'])

const dropRef = ref<any>()

const visible = ref(true)

const formState = ref({
	name: '',
	adjust_type: '',
	current_job: '',
})

const fetchState = shallowRef([])

const otherOptions = [
	{ label: '任职兼挂职', value: '1001205' },
	{ label: '党组成员', value: '1001208' },
	{ label: '其他', value: '1001206' },
]

const onAfterClose = () => {
	emits('close')
}

const fetchInfo = (data: any) => {
	searchByText(data)

	const same = data === formState.value.name

	formState.value = {
		name: data,
		adjust_type: '',
		current_job: same ? formState.value.current_job : '',
	}
}

/**
 * 更新表单中的职务
 * @param {string} data 新的职务
 * @returns {void}
 */
const updateLabel = (_data: string, record: any): void => {
	if (!record) return
	console.log('🚀 ~ updateLabel ~ record:', record)

	formState.value = { ...formState.value, ...record }
	nextTick(() => {
		record && searchByText(record.name)
	})
}

const searchByText = async (value: any) => {
	if (value === '') {
		fetchState.value = []
		return
	}
	const res = await getMiddleLayer({
		name: value,
	})

	if (res.code === 0) {
		fetchState.value = res.data.map((item: any) => {
			return {
				...item,
				label: item.name,
				value: item.user_id,
			}
		})
	}
}

/**
 * 提交表单，将表单中的信息提交到接口
 * @returns {Promise<void>}
 */
const onsubmit = async () => {
	// 将表单中的数据拷贝到一个新的对象中
	const params: any = { ...formState.value }
	if (!params.name) {
		return message.warn('请输入干部姓名')
	}
	// 将模拟考试的id赋值给params.mock_id
	params.mock_id = props.mock_id

	// 将当前职务赋值给params.job_name
	params.job_name = params.current_job

	// 删除params.current_job，避免提交到接口
	delete params.current_job

	// 调用添加考生的接口
	const res = await addCandidateMiddle(params)

	// 如果接口调用成功，则关闭弹窗
	if (res.code === 0) {
		visible.value = false

		message.success('添加成功')

		props.onSuccess?.()
	} else {
		message.error(res.message)
	}
}

const oncancel = () => {
	dropRef.value.close()

	setTimeout(() => {
		visible.value = false
	}, 200)
}
</script>

<style lang="less" scoped>
.input-box {
	display: flex;
	align-items: center;
}
.search-result {
	padding: 18px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	.name {
		font-size: 23px;
		color: #008eff;
	}
	.job-name {
		width: 400px;
		margin-top: 6px;
		font-size: 21px;
		color: rgba(0, 0, 0, 0.65);
		line-height: 25px;
		white-space: pre-wrap;
		overflow: hidden;
	}
}
.user-modal-content {
	.tag {
		padding: 12px 45px;
		font-size: 21px;
		color: #eb8e04;
		text-align: left;

		width: 100%;
		background: #fcf6ec;
	}
	.form-box {
		padding: 30px;
	}

	.input-box {
		.label-text {
			font-weight: 400;
			font-size: 21px;
			color: rgba(0, 0, 0, 0.25);
			line-height: 25px;
		}
	}
	:deep(.poc-input) {
		height: 54px;
		.ant-input {
			font-size: 23px !important;
			color: rgba(0, 0, 0, 0.5);
		}
	}
	.position-input {
		font-size: 23px !important;
		color: rgba(0, 0, 0, 0.5);
	}
	.btn-box {
		width: 100%;
		display: flex;
		justify-content: center;
		button {
			margin-top: 138px;
			font-size: 21px;
			color: #ffffff;
			width: 255px;
			height: 60px;
			background: #008eff;
		}
	}
}
</style>
<style lang="less">
.add-user-modal {
	.ant-form-item-label {
		label {
			font-weight: 500;
			font-size: 23px !important;
			color: #000000;
			line-height: 26px;
			text-align: left;
		}
	}
	.ant-modal-body {
		padding: 0px;
	}
}

.ant-dropdown {
	max-height: 368px;
	overflow: auto;
}
.ant-modal-title {
	font-weight: bold;
	font-size: 27px;
	color: #222222;
}
</style>
