<template>
	<a-drawer
		class="new-position-type-drawer"
		title="调整类别"
		:visible="visible"
		@close="onClose"
		@afterVisibleChange="afterVisibleChange"
		:closable="false"
		:footer-style="{ textAlign: 'center' }"
		size="default"
	>
		<div class="select-list">
			<div
				class="select-item"
				:class="{ active: item.op_key === currentKey }"
				v-for="item in dropList"
				:key="item.key"
				@click="onSelect(item.op_key)"
			>
				{{ item.op_value }}
			</div>
			<div class="btn-box">
				<a-button style="margin-right: 8px" @click="onClose">取消</a-button>
				<a-button type="primary" @click="onConfirm">确认</a-button>
			</div>
		</div>
		<!-- <template #footer>
		
		</template> -->
	</a-drawer>
</template>

<script lang="ts" setup>
import { ref, watchEffect, computed } from 'vue'
import useMock from '@/store/newSandbox'

const props = defineProps({
	type: String || Number,
})

const emits = defineEmits(['close', 'confirm'])

const visible = ref(true)

const mock = useMock()

const currentKey: any = ref(undefined)
// 关闭
const onClose = () => {
	visible.value = false
}

const onSelect = (key: any) => {
	currentKey.value = key
}

// 过度结束后触发
const afterVisibleChange = (visible: boolean) => {
	visible || emits('close')
}

const onConfirm = () => {
	emits('confirm', currentKey.value)

	onClose()
}

const dropList = computed(() => {
	// return mock.typeOption.filter((item: any) => {
	// 	return item.op_key != '1001206'
	// })
	return mock.typeOption
})

watchEffect(() => {
	currentKey.value = props.type
})
</script>

<style lang="less">
.new-position-type-drawer {
	.select-list {
		.select-item {
			margin-top: 15px;
			font-size: 23px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.85);

			display: flex;
			align-items: center;
			justify-content: center;
			height: 61px;
			background: #f9f9f9;
			cursor: pointer;
			&:not(1) {
				margin-top: 15px;
			}
		}
		.active {
			color: #008eff;
			background: #edf7ff;
		}
	}
	.ant-drawer-title {
		font-size: 26px;
	}
	// .ant-drawer-footer {
	// 	padding: 40px;
	// }
	.btn-box {
		margin-top: 60px;
		display: flex;
		justify-content: center;
		button {
			padding: 0px;
			width: 180px;
			height: 57px;
			font-size: 25px;
			line-height: 25px;
		}
	}
}
</style>
