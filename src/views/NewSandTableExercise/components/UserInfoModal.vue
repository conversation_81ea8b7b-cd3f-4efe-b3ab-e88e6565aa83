<template>
	<a-modal class="user-info-modal" width="70%" title="某某镇党委副书记" v-model:visible="visible" :afterClose="onAfterClose" :footer="null">
		<UmiContent :user_id="user_id" />
	</a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

import UmiContent from './UimContent.vue'

defineProps({
	type: String,
	user_id: String,
})

const emits = defineEmits(['close'])

const visible = ref(true)

const onAfterClose = () => {
	emits('close')
}
</script>

<style lang="less">
.user-info-modal {
	.flex {
		display: flex;
	}
	.flex-1 {
		flex: 1;
	}
	.justify-between {
		justify-content: space-between;
	}
	.align-center {
		align-items: center;
	}
	.ant-modal-title {
		font-weight: bold;
		font-size: 27px;
		color: rgba(0, 0, 0, 0.9);
		line-height: 32px;
	}

	.card-common-style {
		padding: 24px;
		background: linear-gradient(180deg, rgba(0, 142, 255, 0.08) 0%, rgba(0, 142, 255, 0.03) 100%);
		border-radius: 6px 6px 6px 6px;
	}
}
</style>
