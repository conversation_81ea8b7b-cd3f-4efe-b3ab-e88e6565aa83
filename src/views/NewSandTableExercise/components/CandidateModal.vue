<template>
	<a-modal class="user-info-modal" width="70%" :title="job" v-model:visible="visible" :afterClose="onAfterClose" :footer="null">
		<UmiContent :user_id="user_id" :on-router="routerPush || onRouter" />
		<div class="button-box">
			<template v-if="type === '候选'">
				<a-button @click="onRemoveCandidate" v-if="!candidateStatus">移出候选</a-button>
				<a-button type="primary" @click="onAddCollection">收藏</a-button>
			</template>

			<template v-else-if="['岗找人', '人找岗'].includes(type as string)">
				<a-button type="primary" @click="onFindByJob">岗找人</a-button>
				<a-button type="primary" @click="onFindByPerson">人找岗</a-button>
			</template>

			<a-button v-else type="primary" @click="onAddCandidate">加入候选</a-button>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { folderModal } from '@/components/components'
import { openPersonFindJobModal } from './component'
import { addCandidate, cancelMock } from '@/apis/new-sand-table-exercise'
import { message } from 'ant-design-vue'
import UmiContent from './UimContent.vue'
import router from '@/router'

const emits = defineEmits(['close'])

const visible = ref(true)

const candidateStatus = ref(false)

const props = defineProps({
	mock_id: String,
	type: String,
	user_id: String,
	pms_job_id: Number,
	job: String,
	from_tag: Number,
	org_name: String,
	from_job_id: Number,
	from_user_id: Number,
	pms_mock_job_detail_id: Number,
	onRouter: {
		type: Function,
	},
	routerPush: {
		type: Function,
	},
	onSuccess: {
		type: Function,
	},
})
const onAfterClose = () => {
	emits('close')
}
// 加入候选参数配备
const createParams = () => {
	const { user_id, mock_id, from_tag, pms_job_id, from_job_id, from_user_id } = props

	const params = {
		mock_id,
		pms_job_id: Number(pms_job_id),
		user_id,
		flag: 1,
		from_tag: Number(from_tag),
		from_job_id,
		from_user_id,
	}
	return params
}
const onAddCandidate = async () => {
	// router.push({
	// 	path: '/new-sand-table-exercise/look-for-by-job',
	// 	query: {
	// 		from: 'candidate',
	// 	},
	// })
	const params = createParams({})

	const res = await addCandidate([params])

	if (res.code === 0) {
		message.success('添加成功')

		visible.value = false

		props.onRouter?.({
			path: '/new-sand-table-exercise/org-team',
		})
	} else {
		message.error(res.message)
	}
}
const onRemoveCandidate = async () => {
	const res = await cancelMock({
		pms_mock_job_detail_id: props.pms_mock_job_detail_id,
	})
	if (res.code === 0) {
		message.success('撤销成功')

		candidateStatus.value = true

		props.onSuccess?.()
	}
}
// 岗找人
const onFindByJob = () => {
	const onRouter: any = props.onRouter

	visible.value = false

	onRouter({
		path: '/new-sand-table-exercise/look-for-by-job',
		query: {
			from_tag: props.from_tag,
			pms_job_id: props.pms_job_id,
			from_job_id: props.pms_job_id,
			from: 'home',
			from_user_id: props.user_id,
			org_name: props.org_name,
		},
	})
}
// 人找岗
const onFindByPerson = () => {
	openPersonFindJobModal({
		user_id: props.user_id,
		from_job_id: props.pms_job_id,
		from_tag: props.from_tag,
		mock_id: props.mock_id,
		onSuccess: props.onSuccess,
		job_name: props.job,
	})
}

const onAddCollection = () => {
	folderModal({
		selectedRowKeys: [props.user_id],
	})
}

// router.beforeEach(() => {
// 	visible.value = false
// 	console.log('🚀 ~ router.beforeEach ~ visible:', visible)
// })
</script>

<style lang="less">
.user-info-modal {
	.flex {
		display: flex;
	}
	.flex-1 {
		flex: 1;
	}
	.justify-between {
		justify-content: space-between;
	}
	.align-center {
		align-items: center;
	}
	.ant-modal-title {
		font-weight: bold;
		font-size: 27px;
		color: rgba(0, 0, 0, 0.9);
		line-height: 32px;
	}

	.card-common-style {
		padding: 24px;
		background: linear-gradient(180deg, rgba(0, 142, 255, 0.08) 0%, rgba(0, 142, 255, 0.03) 100%);
		border-radius: 6px 6px 6px 6px;
	}
	.button-box {
		padding: 30px 0px 0px;
		display: flex;
		justify-content: center;
		gap: 0px 27px;
		button {
			width: 253px;
			height: 60px;
			border-radius: 3px 3px 3px 3px;
			font-size: 21px;
		}
	}
}
</style>
