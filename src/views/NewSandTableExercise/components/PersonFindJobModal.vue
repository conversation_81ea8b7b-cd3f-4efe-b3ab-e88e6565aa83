<template>
	<a-modal class="person-find-job-modal" width="80%" :title="job_name" v-model:visible="visible" :afterClose="onAfterClose" :footer="null">
		<div class="inner-content-1">
			<UmiContent :user_id="user_id" :on-router="onRouter" />
			<div class="right-job">
				<div class="type">
					<div class="common-title">
						<span>调整类别</span>
					</div>
					<div class="person-content">
						<div
							class="check-item"
							:class="{ 'check-item-active': item.op_key === typeKey }"
							@click="onTypeChange(item.op_key)"
							v-for="(item, index) in mock.typeOption"
							:key="index"
						>
							{{ item.op_value }}
						</div>
					</div>
				</div>
				<div class="position">
					<div class="common-title">
						<span
							>拟任职务 <span class="subtitle"> （{{ treeSelectData.length }}/3） </span>
						</span>
					</div>
					<div class="position-content">
						<div class="search-box m-top-17">
							<a-input placeholder="请输入关键字" v-model:value="searchText">
								<template #prefix>
									<span class="search-icon"></span>
								</template>
								<template #suffix>
									<span class="search-text" @click="onSearch">搜索</span>
								</template>
							</a-input>
						</div>
						<div class="check-box m-top-12">
							<div
								class="check-item"
								:class="{ 'check-item-active': item.key === sequence }"
								@click="onSequence(item.key)"
								v-for="item in sequenceOption"
								:key="item.key"
							>
								{{ item.label }}
							</div>
						</div>
						<div class="select-box m-top-18">
							<div class="tree-list">
								<div class="tree-item" v-for="(item, index) in treeData" :key="index">
									<div class="tree-header" @click="onExpand(item)">
										<span
											class="icon"
											:class="{
												exp: !isExpand(item.org_id),
											}"
										></span>
										<img class="sub-org-icon" src="../images/tree-icon.png" />
										<span class="org-name">{{ item.org_name }}</span>
									</div>
									<div class="sub-org-list" v-if="isExpand(item.org_id)">
										<div class="sub-item" v-for="(item1, index) in item.jobs" :key="index">
											<div
												class="position-box"
												:class="{
													position_box_active: isTreeSelect(item1),
												}"
												@click="onPositionActive(item1)"
											>
												<span class="pos">{{ item1.jog_name }}</span>
												<span class="line"></span>
												<span class="name">{{ item1.user_name }}</span>
											</div>
											<div class="check-b">
												<a-checkbox :checked="isTreeSelect(item1)" @click="onPositionActive(item1)" />
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="btn-box">
					<a-button @click="onCancel">取消</a-button>
					<a-button type="primary" @click="onAdd">加入候选</a-button>
				</div>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref, shallowRef, PropType } from 'vue'
import UmiContent from './UimContent.vue'
import { getJobList, addCandidate, getCode } from '@/apis/new-sand-table-exercise'
import { message } from 'ant-design-vue'
import useMock from '@/store/newSandbox'
import router from '@/router'
const emits = defineEmits(['close'])

const mock = useMock()

const visible = ref(true)

const searchText = ref('')

const props = defineProps({
	type: String,
	user_id: String,
	mock_id: String,
	from_job_id: Number,
	from_tag: String,
	job_name: String,
	onRouter: Function as PropType<any>,
	onSuccess: {
		default: () => void 0,
		type: Function,
	},
})

const sequenceOption = [
	{ key: '103002', label: '乡镇' },
	{ key: '10300101', label: '经济部门' },
	{ key: '10300102', label: '保障部门' },
	{ key: '10300103', label: '执纪执法部门' },
	{ key: '10300105', label: '国有企业' },
	{ key: '10300106', label: '学校' },
	{ key: '10300107', label: '医院' },
	{ key: '121212', label: '缺配职务' },
]
const typeOption = ref<any>([])

const typeKey = ref<any>()
const expandedKeys = ref<string[]>([])

const treeData = ref<any>([])
const sequence = ref()

const treeSelectData = ref<any>([])

const onAfterClose = () => {
	emits('close')
}

const onSequence = (key: any) => {
	if (key === sequence.value) {
		sequence.value = undefined
	} else {
		sequence.value = key
	}

	loadJobList()
}

const onTypeChange = (key: string) => {
	typeKey.value = key
}

const onExpand = (org: any) => {
	const { org_id } = org

	const index = expandedKeys.value.findIndex((item: any) => {
		return item === org_id
	})

	if (index === -1) {
		expandedKeys.value.push(org_id)
	} else {
		expandedKeys.value.splice(index, 1)
	}
}

const isExpand = (key: string) => {
	return expandedKeys.value.includes(key)
}

const onPositionActive = (org: any) => {
	const { pms_job_id, user_id } = org
	const index = treeSelectData.value.findIndex((item: any) => {
		return item.pms_job_id === pms_job_id && user_id === item.user_id
	})

	if (index === -1) {
		if (treeSelectData.value.length === 3) return
		// 多选
		treeSelectData.value.push(org)
		// 单选
		// treeSelectData.value = [org]
	} else {
		treeSelectData.value.splice(index, 1)
	}
}
// 组织树选中
const isTreeSelect = (org: any) => {
	const { pms_job_id, user_id } = org
	return treeSelectData.value.findIndex((item: any) => item.pms_job_id === pms_job_id && user_id === item.user_id) !== -1
}

const onCancel = () => {
	visible.value = false
}

const onSearch = () => {
	loadJobList()
}
// 加入候选参数配备
const createParams = ({ pms_job_id, to_tag, from_user_id }: any) => {
	const { user_id, mock_id, from_tag } = props

	const params = {
		mock_id,
		pms_job_id,
		user_id,
		flag: 2,
		from_job_id: props.from_job_id,
		from_user_id,
		adjust_type: typeKey.value,
		to_tag,
		from_tag,
	}
	return params
}
const onAdd = async () => {
	let params: any = undefined
	const limitKey = ['1001201', '1001202', '1001203']
	if (!typeKey.value) {
		return message.error('请选择调整类型')
	}
	if (limitKey.includes(typeKey.value) && treeSelectData.value.length === 0) {
		return message.error('请选择拟任职务')
	} else {
		if (treeSelectData.value.length > 0) {
			params = treeSelectData.value.map((item: any) => {
				const { pms_job_id, to_tag, user_id } = item
				return createParams({
					pms_job_id,
					adjust_type: typeKey.value,
					to_tag,
					from_user_id: user_id,
				})
			})
		} else {
			params = [createParams({})]
		}
	}

	const res = await addCandidate(params)

	if (res.code === 0) {
		visible.value = false
		props.onSuccess?.()
	} else {
		message.error(res.message)
	}
}

const loadCode = async () => {
	const res = await getCode({
		code: '10012',
	})
	if (res.code === 0) {
		typeOption.value = res.data
	}
}
loadCode()

const loadJobList = async () => {
	const res = await getJobList({
		sequence: sequence.value,
		job_name: searchText.value,
	})
	if (res.code === 0) {
		treeData.value = res.data
	}
}

loadJobList()

router.beforeEach(() => {
	visible.value = false
})
</script>

<style lang="less">
.person-find-job-modal {
	.flex {
		display: flex;
	}
	.flex-1 {
		flex: 1;
	}
	.justify-between {
		justify-content: space-between;
	}
	.align-center {
		align-items: center;
	}
	.ant-modal-title {
		font-weight: bold;
		font-size: 27px;
		color: rgba(0, 0, 0, 0.9);
		line-height: 32px;
	}
	.inner-content-1 {
		display: flex;
		height: 924px;
		.right-job {
			display: flex;
			flex-direction: column;
			width: 527px;
			height: 100%;
			margin-left: 23px;
			.common-title {
				display: flex;
				align-items: center;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 24px;
				color: #222222;
				&::before {
					margin-right: 12px;
					content: '';
					display: inline-block;
					width: 6px;
					height: 24px;
					background: url('../images/common-title-icon.png') center / cover no-repeat;
					margin-right: 8px;
				}
			}
			.subtitle {
				font-weight: 500;
				font-size: 24px;
				color: rgba(0, 0, 0, 0.45);
				line-height: 28px;
				text-align: left;
			}
			.check-item {
				min-width: 98px;
				padding: 9px 11px;
				background: #f9f9f9;
				border-radius: 3px;
				border: 2px solid rgba(0, 0, 0, 0.05);

				font-weight: 500;
				font-size: 23px;
				line-height: 23px;
				text-align: center;
				color: #000000;
			}
			.check-item-active {
				color: #008eff;
				background: #edf7ff;
				border-radius: 3px 3px 3px 3px;
				border: 2px solid rgba(0, 142, 255, 0.1);
			}
			.select-item {
				color: #008eff;
				border: 2px solid rgba(0, 142, 255, 0.1);
			}
			.person-content {
				display: flex;
				flex-wrap: wrap;
				padding: 18px 0px;
				gap: 12px 11px;
			}

			.search-box {
				margin-top: 30px;
				padding: 0px;
				.ant-input-affix-wrapper {
					height: 48px;
				}
				.search-icon {
					width: 24px;
					height: 24px;
					background: url(../images/search.png) center / cover no-repeat;
				}
				input {
					font-size: 21px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.25);
				}
				.search-text {
					height: 20px;
					padding-left: 20px;
					font-size: 21px;
					line-height: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #008eff;
					cursor: pointer;
					border-left: 1px solid #d9d9d9;
				}
			}
			.position {
				flex: 1;
				overflow: hidden;
				display: flex;
				flex-direction: column;
				.position-content {
					display: flex;
					flex-direction: column;
					flex: 1;
					overflow: hidden;
					.select-box {
						flex: 1;
						overflow: auto;
					}
					.check-box {
						display: flex;
						padding: 0px 0px;
						gap: 12px 11px;
						display: flex;
						flex-wrap: wrap;
					}
				}
			}
		}
	}

	.type {
	}
	.sub-org-icon {
		margin-left: 5px;
		width: 25px;
		height: 23px;
	}
	.short-name {
		display: inline-block;
		width: 90%;
		font-size: 23px;

		span.name {
			display: inline-block;
			width: 80%;
		}
	}

	.btn-box {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0px 27px;
		button {
			width: 195px;
			height: 60px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 21px;
			line-height: 21px;
		}
	}
	.tree-list {
		width: 100%;
		.tree-header {
			width: 100%;
			display: flex;
			align-items: center;
			.icon {
				display: inline-block;
				width: 24px;
				height: 24px;
				background: url('../images/tree-select.png') center / cover no-repeat;
			}
			.exp {
				transform: rotateZ(-90deg);
			}
			.org-name {
				margin-left: 9px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 23px;
				color: #000000;
			}
		}
		.sub-org-list {
			width: 100%;
			.sub-item {
				padding: 10px 18px 10px 44px;
				width: 100%;
				display: flex;
				.position-box {
					flex: 1;
					display: flex;

					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 23px;
					color: #000000;
					.pos {
						width: 250px;
					}
					.name {
						white-space: nowrap;
					}
				}
				.position_box_active {
					.pos {
						color: #008eff !important;
					}
					.name {
						color: #008eff !important;
					}
				}
				.check-b {
					margin-left: 10px;
					padding-top: 10px;
				}
			}
		}
	}
}
</style>
