/**
 * 当前文件是NewSandTableExercise视图的一些通用组件
 * 提供的功能包括：
 * 1. 弹出框组件
 * 2. 用户信息弹出框组件
 * 3. 候选人弹出框组件
 * 4. 找岗/找人弹出框组件
 * 5. 岗位类型抽屉组件
 * 6. 添加用户弹出框组件
 */
import { createApp, defineComponent } from 'vue'

import CompareModalVue from './CompareModal.vue'
import UserInfoModal from './UserInfoModal.vue'
import CandidateModal from './CandidateModal.vue'
import PersonFindJobModal from './PersonFindJobModal.vue'
import PositionTypeDrawerVue from './PositionTypeDrawer.vue'
import AddUserModal from './AddUserModal.vue'
import AdjustmentDetailsModal from './AdjustmentDetailsModal.vue'
import AdjustListModal from './AdjustListModal.vue'
import Operation from './Operation.vue'
import FindjobModal from './FindjobModal.vue'
/**
 * 创建一个modal弹出框
 * @param {any} component - 弹出框的组件
 * @param {any} props - 弹出框组件的参数
 * @returns {object} - 返回一个instance和一个关闭函数
 */
export const createModalInstance = (component: any, props = {}) => {
	const onClose = () => {
		instance.unmount()
	}

	const instance = createApp(component, {
		...props,
		onClose,
	})

	const dismissEle: any = document.createElement('div')

	instance.mount(dismissEle)

	return { instance, onClose }
}

/**
 * 比较弹出框
 */
export const compareModal = {
	/**
	 * 打开一个比较弹出框
	 * @param {any} props - 弹出框组件的参数
	 * @returns {object} - 返回一个instance和一个关闭函数
	 */
	openModal(props?: any) {
		const { instance, onClose } = createModalInstance(CompareModalVue, props)
		return { instance, onClose }
	},
}

/**
 * 用户信息弹出框
 * @param {any} props - 弹出框组件的参数
 * @returns {object} - 返回一个instance
 */
export const userInfoModal = (props: any) => {
	const { instance } = createModalInstance(UserInfoModal, props)
	return { instance }
}

/**
 * 候选人弹出框
 * @param {any} props - 弹出框组件的参数
 * @returns {object} - 返回一个instance
 */
export const openCandidate = (props: any) => {
	const { instance } = createModalInstance(CandidateModal, props)
	return { instance }
}

/**
 * 找岗/找人弹出框
 * @param {any} props - 弹出框组件的参数
 * @returns {object} - 返回一个instance
 */
export const openPersonFindJobModal = (props: any) => {
	const { instance } = createModalInstance(PersonFindJobModal, props)
	return { instance }
}

/**
 * 岗位类型抽屉
 */
export const positionTypeDrawer = {
	/**
	 * 打开一个岗位类型抽屉
	 * @param {any} props - 弹出框组件的参数
	 * @returns {object} - 返回一个instance
	 */
	openDrawer(props?: any) {
		const { instance } = createModalInstance(PositionTypeDrawerVue, props)
		return { instance }
	},
}

/**
 * 添加用户弹出框
 * @param {any} props - 弹出框组件的参数
 * @returns {object} - 返回一个instance
 */
export const openAddUserModal = (props?: any) => {
	const { instance } = createModalInstance(AddUserModal, props)
	return { instance }
}

/**
 * 调整详情弹出框
 * @param {any} props - 弹出框组件的参数
 * @returns {object} - 返回一个instance
 * @returns {object} - 返回一个关闭函数
 */
export const openAdjustmentDetailsModal = (props?: any) => {
	const { instance, onClose } = createModalInstance(AdjustmentDetailsModal, props)
	return { instance, onClose }
}
/**
 * 缺配
 * @param {any} props - 弹出框组件的参数
 * @returns {object} - 返回一个instance
 * @returns {object} - 返回一个关闭函数
 */
export const openAdjustList = (props?: any) => {
	const { instance, onClose } = createModalInstance(AdjustListModal, props)
	return { instance, onClose }
}
/**
 * 缺配
 * @param {any} props - 弹出框 动议名单操作
 * @returns {object} - 返回一个instance
 * @returns {object} - 返回一个关闭函数
 */
export const onOperation = (props?: any) => {
	const { instance, onClose } = createModalInstance(Operation, props)
	return { instance, onClose }
}
// 岗找人弹窗
export const openFindJobModal = (props?: any) => {
	const { instance, onClose } = createModalInstance(FindjobModal, props)
	return { instance, onClose }
}
