<template>
	<div class="header-back">
		<span class="back-icon" @click="onBack"></span>
		<span class="title">{{ title }}</span>
		<span class="sub-title" v-if="subTitle">
			<div class="line"></div>
			{{ subTitle }}</span
		>
	</div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

defineProps({
	title: String,
	subTitle: String,
})

const router = useRouter()

const onBack = () => {
	router.back()
}
</script>

<style lang="less" scoped>
.header-back {
	display: flex;
	align-items: center;
	padding: 32px 33px;
	background-color: #fff;
	border-bottom: 2px solid #f5f5f5;
	.back-icon {
		display: inline-block;
		width: 23px;
		height: 23px;
		background: #252525;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		background: url('@/assets/images/arrow-left.png') center / 100% no-repeat;
		cursor: pointer;
	}
	.title {
		margin-left: 19px;
		font-size: 27px;
		font-family: <PERSON> <PERSON>, Source <PERSON>;
		font-weight: bold;
		color: #222222;
		line-height: 32px;
	}
	.sub-title {
		display: flex;
		align-items: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 27px;
		line-height: 27px;
		color: rgba(0, 0, 0, 0.9);
		.line {
			margin: 0 18px;
			width: 1px;
			height: 23px;
			background-color: #000000;
		}
	}
}
</style>
