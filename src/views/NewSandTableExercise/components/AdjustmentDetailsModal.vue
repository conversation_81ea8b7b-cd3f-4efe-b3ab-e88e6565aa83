<template>
	<a-modal title="干部调整详情" v-model:visible="visible" width="98%" class="adjustment-details-modal" :afterClose="onAfterClose" :footer="null">
		<div class="ad-modal-content">
			<UimContent :user_id="user_id" />
			<div class="right-box">
				<div class="common-title">
					<span>拟任职务</span>
				</div>
				<div class="job-list">
					<div class="job-item" v-for="(item, index) in positionList.candidates" :key="index">
						<div class="job-name">{{ item.job_name }}</div>
						<div class="control-box">
							<span class="text revoke" @click="onRevoke(item)">
								<span>撤销调整</span>
							</span>
							<span class="text edit" @click="onEdit(item)">
								<span>修改职务描述</span>
							</span>
							<span class="text add" @click="onAdd(item)" v-if="item.pms_job_id">
								<span>添加候选人</span>
							</span>
						</div>
					</div>
					<div class="btn-box">
						<a-button :disabled="positionList.candidates?.length == 3" type="primary" @click="onJob">+拟任职务</a-button>
					</div>
				</div>
			</div>
		</div>
	</a-modal>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import UimContent from './UimContent.vue'
import { getUserDesiredJob, cancalAdjust, updateJobName } from '@/apis/new-sand-table-exercise'
import { openAdjustList, openPersonFindJobModal } from './component.ts'

import router from '@/router'
import { message } from 'ant-design-vue'

const visible = ref(true)

const props = defineProps({
	user_id: String,
	mock_id: String,
	onSuccess: Function,
})

const emits = defineEmits(['close'])

const positionList = ref<any>({ candidates: [] })

const close = () => (visible.value = false)

const success = () => props.onSuccess?.(close)
const onAfterClose = () => {
	emits('close')
}

// 生成对应三个方法
const onRevoke = async ({ pms_mock_job_detail_id }: any) => {
	const res = await cancalAdjust({ pms_mock_job_detail_id })
	if (res.code === 0) {
		loadPosition()

		success()
	} else {
		message.error(res.message)
	}
}
const onEdit = ({ job_name, pms_mock_job_detail_id }: any) => {
	const { instance } = openAdjustList({
		currentSelectUser: {
			draft_sit_job: job_name,
		},
		pms_mock_job_detail_id,
		onConfirm: async (positionInput: any, cb: () => void) => {
			const res = await updateJobName({
				pms_mock_job_detail_id,
				name: positionInput,
			})
			if (res.code === 0) {
				message.success('修改成功')

				loadPosition()

				cb?.()

				success()
			} else {
				message.error(res.message)
			}
		},
	})
}
const onAdd = (record: any) => {
	const { from_tag, pms_job_id } = record
	const { mock_id, user_id } = props

	router.push({
		path: '/new-sand-table-exercise/look-for-by-job',
		query: {
			from_user_id: user_id,
			from_tag: from_tag,
			pms_job_id: pms_job_id,
			from_job_id: pms_job_id,
			mock_id,
			from: 'candidate',
		},
	})
}

const onJob = (record: any) => {
	const { job_name } = record
	const { from_tag, from_job_id } = positionList.value
	const { user_id, mock_id } = props
	openPersonFindJobModal({
		user_id: user_id,
		from_job_id: from_job_id,
		from_tag: from_tag,
		job: job_name,
		mock_id,
		onSuccess() {
			loadPosition()

			success()
		},
	})
}

const loadPosition = async () => {
	const { mock_id, user_id } = props
	const res = await getUserDesiredJob({
		mock_id,
		user_id,
	})
	if (res.code === 0) {
		positionList.value = res.data
	}
}

loadPosition()

router.beforeEach(() => {
	visible.value = false
	console.log('🚀 ~ router.beforeEach ~ visible:', visible)
})
</script>

<style lang="less" scoped>
.ad-modal-content {
	display: flex;
	height: 924px;
	.right-box {
		padding: 18px;
		flex-shrink: 0;
		width: 600px;
		height: 100%;
		background: #ebf3f5;
		.btn-box {
			width: 100%;
			display: flex;
			justify-content: center;
			button {
				margin-top: 54px;
				width: 195px;
				height: 54px;
				font-weight: 500;
				font-size: 21px;
				line-height: 25px;
			}
		}
		.common-title {
			display: flex;
			align-items: center;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 24px;
			color: #222222;
			&::before {
				margin-right: 12px;
				content: '';
				display: inline-block;
				width: 6px;
				height: 24px;
				background: url('../images/common-title-icon.png') center / cover no-repeat;
				margin-right: 8px;
			}
		}
		.job-list {
			display: flex;
			flex-direction: column;
			.job-item {
				margin-top: 18px;
				background: #ffffff;
				.job-name {
					padding: 18px;
					font-weight: 500;
					font-size: 23px;
					color: rgba(0, 0, 0, 0.85);
					line-height: 26px;
					border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
				}
				.control-box {
					padding: 18px;
					width: 100%;
					display: flex;
					justify-content: space-between;
					.text {
						display: inline-block;
						font-weight: 400;
						font-size: 21px;
						color: #008eff;
						line-height: 24px;
						&::before {
							content: '';
							margin-right: 3px;
							display: inline-block;
							width: 24px;
							height: 24px;
							background: no-repeat center / 100% 100%;
							vertical-align: middle;
						}
					}
					.revoke {
						&::before {
							background-image: url('../images/revoke.png');
						}
					}
					.add {
						&::before {
							background-image: url('../images/add.png');
						}
					}
					.edit {
						&::before {
							background-image: url('../images/edit.png');
						}
					}
				}
			}
		}
	}
}
</style>
