<template>
	<a-modal class="adjust-list-modal" v-model:visible="visible" :afterClose="onAfterClose" title="拟任职务" :footer="false">
		<div class="adj-content-box">
			<div class="tag">注：仅支持编辑任党组成员或者兼（挂）职信息，职务信息不支持手动编辑，需通过职务调整进行操作。</div>
			<div class="position">
				<div class="p-name">{{ positionObj.job_name }}</div>
				<div class="input-p-name m-top-12">
					<a-input type="text" @change="onChange" :value="positionObj.text_job_name" />
				</div>
			</div>
			<div class="btn-box">
				<a-button type="primary" @click="onConfirm">确定</a-button>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getJobName } from '@/apis/new-sand-table-exercise'
import { message } from 'ant-design-vue'

/**
 * 当前选中的用户
 */
const props = defineProps({
	currentSelectUser: {
		type: Object,
		default: () => ({}),
	},
	/**
	 * 点击确定执行的回调
	 */
	onConfirm: {
		type: Function,
	},
	/**
	 * 当前用户的任职务详情id
	 */
	pms_mock_job_detail_id: String,
})

/**
 * 是否显示modal
 */
const visible = ref(true)

/**
 * 文本职务详情
 */
const positionObj = ref({
	job_name: props.currentSelectUser?.draft_sit_job, //标准职务
	text_job_name: '', //文本职务，可以修改
})

/**
 * 发射事件，关闭modal
 */
const emits = defineEmits(['close'])

/**
 * modal关闭后执行的回调
 */
const onAfterClose = () => {
	emits('close')
}

/**
 * 点击确定按钮后执行的回调
 */
const onConfirm = () => {
	props.onConfirm?.(positionObj.value.text_job_name, () => {
		visible.value = false
	})
}

/**
 * 输入框值改变时的回调
 */
const onChange = (e: any) => {
	positionObj.value.text_job_name = e.target.value
}

/**
 * 加载文本职务详情
 */
const loadText = async () => {
	console.log(props.pms_mock_job_detail_id)
	const res = await getJobName({
		pms_mock_job_detail_id: props.pms_mock_job_detail_id,
	})
	if (res.code === 0) {
		positionObj.value = res.data
	} else {
		message.error(res.message)
	}
}

loadText()

defineExpose({
	/**
	 * 关闭模态框，将可见性值设置为 false。
	 *
	 * @param {none} 无 - 此函数不接受任何参数。
	 * @return {none} 此函数不返回任何值。
	 */
	close: () => {
		visible.value = false
	},
})
</script>

<style lang="less" scoped></style>

<style lang="less">
.adjust-list-modal {
	.ant-modal-body {
		padding: 0px;
	}
	.ant-modal-title {
		font-weight: bold;
		font-size: 27px;
		color: #222222;
		line-height: 32px;
	}
	.tag {
		font-weight: 500;
		font-size: 21px;
		color: #eb8e04;
		line-height: 25px;
		padding: 14px 30px;
		background: #fcf6ec;
	}
	.position {
		padding: 30px 36px;
		.p-name {
			font-weight: 500;
			font-size: 23px;
			color: #000000;
		}
		input {
			height: 54px;
			font-weight: 400;
			font-size: 23px;
			color: rgba(0, 0, 0, 0.85);
		}
	}
	.btn-box {
		width: 100%;
		display: flex;
		justify-content: center;
		button {
			margin: 224px auto 30px;
			width: 255px;
			height: 60px;
			font-size: 21px;
			color: #ffffff;
		}
	}
}
.adj-content-box {
}
</style>
