<template>
	<a-drawer
		class="position-type-drawer"
		title="操作"
		:visible="visible"
		@close="onClose"
		@afterVisibleChange="afterVisibleChange"
		:closable="false"
		:footer-style="{ textAlign: 'center' }"
		size="default"
	>
		<div class="select-list">
			<div class="select-item" v-for="item in menu" :key="item.op_key" @click="onSelect(item.op_key)">
				{{ item.op_value }}
			</div>
		</div>
		<!-- <template #footer>
			<a-button style="margin-right: 8px" @click="onClose">取消</a-button>
			<a-button type="primary" @click="onConfirm">确认</a-button>
		</template> -->
	</a-drawer>
</template>

<script lang="ts" setup>
import { ref, watchEffect, createVNode, computed } from 'vue'
import { getUserDesiredJob, cancalAdjust, updateJobName } from '@/apis/new-sand-table-exercise'
import { openAdjustList, openPersonFindJobModal } from './component.ts'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'
import router from '@/router'
import { message } from 'ant-design-vue'
const props = defineProps({
	type: String || Number,
	user_id: String,
	job_name: String,
	mock_id: String,
	from_tag: String,
	pms_job_id: String,
	from_job_id: String,
	pms_mock_job_detail_id: String,
	onSuccess: Function,
})

const emits = defineEmits(['close', 'confirm'])

const visible = ref(true)

const currentKey: any = ref(undefined)

const success = () => props.onSuccess?.()
// 关闭
const onClose = () => {
	visible.value = false
}
const dropList = [
	{
		op_key: '1',
		op_value: '添加拟任职务',
	},
	{
		op_key: '2',
		op_value: '添加待配人员',
	},
	{
		op_key: '3',
		op_value: '撤销调整',
	},
]

const menu = computed(() => {
	if (!props.pms_job_id) {
		return dropList.filter((item: any) => {
			return item.op_key !== '2'
		})
	} else {
		return dropList
	}
})
const positionList = ref<any>({ candidates: [] })

const onSelect = (key: any) => {
	currentKey.value = key
	switch (key) {
		case '1':
			onJob()
			break
		case '2':
			onAdd()
			break
		case '3':
			onRevoke()
			break
	}
}

const onRevoke = async () => {
	Modal.confirm({
		title: '提示',
		icon: createVNode(ExclamationCircleOutlined),
		content: createVNode('div', {}, '确认撤销调整?'),
		async onOk() {
			const { pms_mock_job_detail_id } = props
			const res = await cancalAdjust({ pms_mock_job_detail_id })
			if (res.code === 0) {
				success()

				visible.value = false
			} else {
				message.error(res.message)
			}
		},
		class: 'operation-modal',
		maskClosable: true,
	})
}
const onEdit = () => {
	const { job_name, pms_mock_job_detail_id } = props
	const { instance } = openAdjustList({
		currentSelectUser: {
			draft_sit_job: job_name,
		},
		pms_mock_job_detail_id,
		onConfirm: async (positionInput: any, cb: () => void) => {
			const res = await updateJobName({
				pms_mock_job_detail_id,
				name: positionInput,
			})
			if (res.code === 0) {
				message.success('修改成功')

				cb?.()
			} else {
				message.error(res.message)
			}
		},
	})
}
const onAdd = () => {
	const { mock_id, user_id, from_tag, pms_job_id } = props

	router.push({
		path: '/new-sand-table-exercise/look-for-by-job',
		query: {
			from_user_id: user_id,
			from_tag: from_tag,
			pms_job_id: pms_job_id,
			from_job_id: pms_job_id,
			mock_id,
			from: 'candidate',
		},
	})
}

const onJob = () => {
	const { user_id, mock_id, job_name } = props
	const { from_tag, from_job_id } = positionList.value
	openPersonFindJobModal({
		user_id: user_id,
		from_job_id: from_job_id,
		from_tag: from_tag,
		job: job_name,
		mock_id,
		onSuccess() {
			success()
		},
	})
}

// 过度结束后触发
const afterVisibleChange = (visible: boolean) => {
	visible || emits('close')
}

const onConfirm = () => {
	emits('confirm', currentKey.value)

	onClose()
}

const loadPosition = async () => {
	const { mock_id, user_id } = props
	const res = await getUserDesiredJob({
		mock_id,
		user_id,
	})
	if (res.code === 0) {
		positionList.value = res.data
	}
}

loadPosition()

watchEffect(() => {
	currentKey.value = props.type
})

router.beforeEach(() => {
	visible.value = false
})
</script>

<style lang="less">
.position-type-drawer {
	.ant-drawer-title {
		font-size: 28px;
	}
	.select-list {
		.select-item {
			margin-top: 15px;
			font-size: 23px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.85);

			display: flex;
			align-items: center;
			justify-content: center;
			height: 61px;
			background: #f9f9f9;
			cursor: pointer;
			&:not(1) {
				margin-top: 15px;
			}
		}
		.active {
			color: #008eff;
			background: #edf7ff;
		}
	}
	button {
		width: 150px;
		height: 47px;
	}
}
.operation-modal {
	.ant-modal-confirm-title {
		font-size: 25px;
	}
	svg {
		width: 25px;
		height: 25px;
	}
	.ant-modal-confirm-content {
		font-size: 22px;
	}
	button {
		width: 90px;
		height: 40px;
		font-size: 20px;
	}
}
</style>
