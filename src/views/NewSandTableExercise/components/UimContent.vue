<template>
	<div class="uim-content">
		<div class="level-1 flex card-common-style">
			<CodeAvatar :head_url="userInfo.avatar">
				<template #avatar="{ avatar }">
					<img :src="avatar" class="avatar" />
				</template>
			</CodeAvatar>
			<div class="base-info">
				<div class="flex justify-between">
					<div class="user-name">{{ userInfo.name }}</div>
					<div class="user-name"><a-button class="cadre-button" type="primary" @click="onCadre">干部指数页面</a-button></div>
				</div>
				<div class="flex justify-between m-top-22">
					<div class="index-item flex align-center">
						<span class="index">干部指数</span>
						<span class="line"></span>
						<span class="number">{{ userInfo.leader_index || '-' }}</span>
					</div>
					<div class="index-item flex align-center warn-item">
						<span class="index">风险指数</span>
						<span class="line"></span>
						<span class="number">{{ userInfo.risk_index || '-' }}</span>
					</div>
					<div class="index-item flex align-center">
						<span class="index">测评得分</span>
						<span class="line"></span>
						<span class="number">{{ userInfo.eval || '-' }}</span>
					</div>
				</div>
				<div class="flex justify-between">
					<div class="index-item no-icon flex align-center m-top-22">
						<span class="index">序列排名</span>
						<span class="line"></span>
						<span class="number">{{ userInfo.leader_index_rank || '-' }}</span>
					</div>
					<div class="flex-1"></div>
					<div class="index-item no-icon flex align-center">
						<span class="index">序列排名</span>
						<span class="line"></span>
						<span class="number">{{ userInfo.eval_rank || '-' }}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="level-2 m-top-12 card-common-style">
			<div class="flex justify-between">
				<div class="data-label flex justify-between">
					<div class="flex-1">
						<div class="label flex align-center">现任职务</div>
						<div class="data">{{ userInfo.current_position }}</div>
					</div>
					<div class="flex-1">
						<div class="label flex align-center">任现职务时间</div>
						<div class="data">{{ userInfo.position_time }}</div>
					</div>
					<div class="flex-1">
						<div class="label flex align-center">出生年月</div>
						<div class="data">{{ userInfo.birthday_age }}</div>
					</div>
				</div>
			</div>
			<div class="line"></div>
			<div class="flex justify-between">
				<div class="data-label flex justify-between">
					<div class="flex-1">
						<div class="label flex align-center">民族</div>
						<div class="data">{{ userInfo.ethnic }}</div>
					</div>
					<div class="flex-1">
						<div class="label flex align-center">学历</div>
						<div class="data">{{ userInfo.education }}</div>
					</div>
					<div class="flex-1">
						<div class="label flex align-center">毕业院校及专业</div>
						<div class="data">{{ userInfo.school_major }}</div>
					</div>
				</div>
			</div>
		</div>
		<div class="level-3 m-top-12 card-common-style">
			<div class="card-title flex align-center">特征标签</div>
			<div class="tag-box m-top-27">
				<div class="text"><TagBox :data="userInfo.positive_feature_map" color="#008EFF" /></div>
				<div class="text m-top-24" style="padding-top: 0px">
					<TagBox :data="userInfo.negative_feature_map" color="#FF6A16" />
				</div>
			</div>
		</div>
		<div class="level-4 m-top-12 card-common-style">
			<div class="card-title flex align-center">一言素描</div>
			<div class="desc-box m-top-18">
				{{ userInfo.sketch }}
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, PropType } from 'vue'
import defaultAvatar from '@/assets/images/avatar.png'
import CodeAvatar from '@/components/CodeAvatar.vue'
import TagBox from '@/components/TagBox.vue'
import { getLeaderBaseInfo } from '@/apis/cadre-portrait/home'
import { CDN_URL } from '@/config/env'
const props = defineProps<{
	user_id: any
	onRouter: (params: any) => void
}>()

import type { UserInfo } from '@/types/user'
import router from '@/router'

const userInfo = ref<UserInfo & any>({
	user_id: 0,
	name: '',
	birthday_age: '',
	education: '',
	current_position: '',
	position_time: '',
	leader_index: 0,
	leader_index_rank: '',
	risk_index: 0,
	risk_index_avg: 0,
	feature: [],
	goods_range: '',
	like: '',
	sketch: '',
	areas_responsibility: '',
	feature_list: [],
	sketch_list: [],
	promotion_type: -1,
})

const convertMap = (data: Array<any>) => {
	const positive_feature_map: any = []
	data.map((item: any) => {
		const _index = positive_feature_map.find((item1: any) => item1.label === item)
		if (!_index) {
			positive_feature_map.push({
				label: item,
				count: 1,
			})
		} else {
			_index.count++
		}
	})

	return positive_feature_map
}

const onCadre = () => {
	;(props.onRouter || router.push)({
		path: '/cadre-portrait/home',
		query: {
			user_id: props.user_id,
			top_page: 1,
		},
	})
}

const initLeaderBaseInfoData = async () => {
	const user_id = props.user_id

	const res: any = await getLeaderBaseInfo({ user_id })
	if (res.data.positive_feature) {
		res.data.positive_feature_map = convertMap(res.data.positive_feature)
			.sort((a: any, b: any) => b.count - a.count)
			.slice(0, 3)
	}
	if (res.data.negative_feature) {
		res.data.negative_feature_map = convertMap(res.data.negative_feature)
			.sort((a: any, b: any) => b.count - a.count)
			.slice(0, 2)
	}
	const feature_list = res.data.feature_list

	const positive_feature: any = []
	const negative_feature: any = []
	const other_feature: any = []
	// 和该字段和res.data.feature_list 数据一样
	feature_list?.map((item: any) => {
		if (item.positive_feature) {
			item.positive_feature_map = convertMap(item.positive_feature)

			positive_feature.push(...item.positive_feature)
		}
		if (item.negative_feature) {
			item.negative_feature_map = convertMap(item.negative_feature)

			negative_feature.push(...item.negative_feature)
		}
		if (item.other_feature) {
			item.other_feature_map = convertMap(item.other_feature)

			other_feature.push(...item.other_feature)
		}
	})
	// 正面标签
	res.data.positive_feature_detail = convertMap(positive_feature).sort((a: any, b: any) => b.count - a.count)
	// 负面标签
	res.data.negative_feature_detail = convertMap(negative_feature).sort((a: any, b: any) => b.count - a.count)
	// 其他标签
	if (res.data.other_feature) {
		res.data.other_feature_detail = convertMap(res.data.other_feature).sort((a: any, b: any) => b.count - a.count)
	}

	userInfo.value = res.data
}

initLeaderBaseInfoData()
</script>

<style lang="less" scoped>
.uim-content {
	width: 100%;
	.card-common-style {
		padding: 24px;
		background: linear-gradient(180deg, rgba(0, 142, 255, 0.01) 0%, rgba(0, 142, 255, 0.03) 100%);
		border-radius: 6px 6px 6px 6px;
	}
	.flex {
		display: flex;
	}
	.justify-between {
		justify-content: space-between;
	}
	.align-center {
		align-items: center;
	}
	.level-1 {
		.avatar {
			width: 132px;
			height: 149px;
		}
		.cadre-button {
			height: unset;
			padding: 15px 18px;
			background: #e5251b !important;
			font-size: 26px;
			line-height: 26px;
			color: #ffffff;
			border: none;
		}
		.base-info {
			margin-left: 24px;
			flex: 1;
			.user-name {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 30px;
				color: rgba(0, 0, 0, 0.9);
			}

			.warn-item {
				&::before {
					background: #ff8c4b !important;
				}
				.number {
					color: #ff8c4b !important;
				}
			}
			.index-item {
				flex: 1;
				&::before {
					margin-right: 8px;
					content: '';
					display: inline-block;
					width: 8px;
					height: 8px;
					background: #008eff;
					border-radius: 50%;
				}
				.index {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					font-size: 23px;
					color: rgba(0, 0, 0, 0.75);
				}
				.line {
					margin: 0px 15px;
					display: inline-block;
					height: 1px;
					width: 46px;
					border-bottom: 1px dashed rgba(0, 0, 0, 0.4);
				}
				.number {
					font-family: Rany, Rany;
					font-weight: 500;
					font-size: 26px;
					color: #008eff;
					line-height: 30px;
				}
			}
			.warn-icon {
				&::before {
					background: transparent !important;
				}
			}
			.no-icon {
				&::before {
					background: transparent !important;
				}
			}
		}
	}
	.level-2 {
		.line {
			width: 100%;
			height: 1px;
			margin: 18px 0px;
			background: rgba(0, 0, 0, 0.1);
		}
		.data-label {
			width: 100%;
			.label {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 23px;
				color: rgba(0, 0, 0, 0.65);
				line-height: 26px;
				&::before {
					margin-right: 8px;
					content: '';
					display: inline-block;
					width: 8px;
					height: 8px;
					background: #008eff;
					border-radius: 50%;
				}
			}
			.data {
				margin-top: 11px;
				margin-left: 16px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 23px;
				color: rgba(0, 0, 0, 0.85);
				line-height: 26px;
			}
		}
	}
	.tag-box {
		gap: 24px 24px;
	}
	.level-3 {
		min-height: 219px;
	}
	.level-4 {
		min-height: 178px;
		.desc-box {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 23px;
			color: rgba(0, 0, 0, 0.75);
		}
	}
	.card-title {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 24px;
		color: rgba(0, 0, 0, 0.95);
		line-height: 28px;
		&::before {
			margin-right: 8px;
			content: '';
			display: inline-block;
			width: 12px;
			height: 12px;
			background: #008eff;
			border-radius: 50%;
		}
	}
}
.flex-1 {
	flex: 1;
}
</style>
