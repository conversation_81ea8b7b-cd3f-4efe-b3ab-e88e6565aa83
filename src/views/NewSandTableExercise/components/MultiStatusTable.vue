<template>
	<div class="multi-status-table">
		<a-table
			v-bind="$attrs"
			:columns="columns"
			:data-source="dataSource"
			:customRow="
				(record) => {
					return {
						onClick: (event) => {
							onRowClick(record)
						}, // 点击行
					}
				}
			"
			bordered
		>
			<template #bodyCell="{ column, record }">
				<template v-if="column.key === 'user_name'">
					<div class="user-avatar">
						<CodeAvatar :user_id="record.user_id" :head_url="record.head_url">
							<template #avatar="{ avatar }">
								<img v-lazy="avatar" alt="" class="avatar" />
							</template>
						</CodeAvatar>

						<div class="username">{{ record.user_name || record.name }}</div>
					</div>
				</template>
			</template>
		</a-table>
	</div>
</template>

<script lang="ts" setup>
import defaultAvatar from '@/assets/images/avatar.png'
import CodeAvatar from '@/components/CodeAvatar.vue'
import { CDN_URL } from '@/config/env'
const emits = defineEmits(['rowClick'])
const columns = [
	// },
	{
		key: 'user_name',
		dataIndex: 'user_name',
		align: 'center',
		width: '7%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},

	// {
	// 	key: 'portrait',
	// 	align: 'center',
	// 	width: '9%',
	// 	title: '干部画像',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'current_job',
		dataIndex: 'current_job',
		align: 'left',
		width: '10%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'current_job_time',
		dataIndex: 'current_job_time',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'full_time_diploma',
		dataIndex: 'full_time_diploma',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'school_department',
		dataIndex: 'school_department',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'cadre_index',
		dataIndex: 'cadre_index',
		align: 'center',
		width: '6%',
		title: '干部指数',
	},
	{
		key: 'cadre_index_rank',
		dataIndex: 'cadre_index_rank',
		align: 'center',
		width: '10%',
		title: '指数同序列排名',
	},
	{
		key: 'eval',
		dataIndex: 'eval',
		align: 'center',
		width: '10%',
		title: '民主测评',
	},
	{
		key: 'eval_org_rank',
		dataIndex: 'eval_org_rank',
		align: 'center',
		width: '10%',
		title: '测评班子内排名',
	},
]

defineProps({
	dataSource: {
		default: () => [] as any,
	},
})

const onRowClick = (item: any) => {
	emits('rowClick', item)
}
</script>

<style lang="less" scoped>
.user-avatar {
	img {
		width: 120px;
		height: 150px;
	}
	.username {
		text-align: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 27px;
		color: #008eff;
	}
}
:deep(.ant-table-cell) {
	font-size: 24px;
}
.username {
	margin-top: 7px;
	font-size: 27px;
	color: #008eff;
}
</style>
