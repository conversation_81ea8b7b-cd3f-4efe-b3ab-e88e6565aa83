<template>
	<a-modal class="compare-modal" v-model:visible="visible" :afterClose="onAfterClose" :footer="null">
		<compare-table :table-data="tableData" :columns="columns" />
	</a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import CompareTable from './CompareTable.vue'
// import RowTable from '@/components/RowTable.vue'
import { getTeamStructureCompare } from '@/apis/sand-table-exercise'

const props = defineProps({
	mockId: {
		type: String,
	},
	orgId: {
		type: String,
	},
	userIds: {
		type: String,
	},
	type: {
		type: String,
	},
})

interface OrgStructureDetail {
	jobNum: number
	jobVacancy: number
	age: Standards[]
	gender: Standards[]
	educational: Standards[]
	specialty: Standards[]
	experience: Standards[]
	cadreIndex: Standards[]
	withdraw: Standards[]
}

interface OrgStructureVo {
	orgId: number
	orgName: string
	beforeLight: number
	afterLight: number
	before: OrgStructureDetail
	after: OrgStructureDetail
}

interface Standards {
	standard: any
	info: string
}

interface OrgStatistics {
	count: number
	list: OrgStructureVo[]
}
// 生成假数据的辅助函数
function generateStandards(): Standards[] {
	return [
		{ standard: undefined, info: '' },
		// 可以根据需要添加更多的数据
	]
}

function generateOrgStructureDetail(): OrgStructureDetail {
	return {
		jobNum: Math.floor(Math.random() * 100),
		jobVacancy: Math.floor(Math.random() * 20),
		age: generateStandards(),
		gender: generateStandards(),
		educational: generateStandards(),
		specialty: generateStandards(),
		experience: generateStandards(),
		cadreIndex: generateStandards(),
		withdraw: generateStandards(),
	}
}

function generateOrgStructureVo(): OrgStructureVo {
	return {
		orgId: Math.floor(Math.random() * 1000),
		orgName: `机构${Math.floor(Math.random() * 10)}`,
		beforeLight: Math.floor(Math.random() * 4),
		afterLight: Math.floor(Math.random() * 4),
		before: generateOrgStructureDetail(),
		after: generateOrgStructureDetail(),
	}
}

// 生成十条假数据
const fakeData: OrgStatistics = {
	count: 10,
	list: Array.from({ length: 10 }, () => generateOrgStructureVo()),
}

// 打印生成的假数据

const columns = [
	{
		title: '方案分析',
		dataIndex: 'title',
		key: 'title',
		align: 'center',
		rowClass: 'table-title-gray',
	},
	{
		title: '职数配置情况',
		dataIndex: 'position_should',
		key: 'position_should',
		align: 'center',
	},
	{
		title: '年龄结构',
		dataIndex: 'age',
		key: 'age',
		align: 'center',
	},
	{
		title: '性别结构',
		dataIndex: 'gender',
		key: 'gender',
		align: 'center',
	},
	{
		title: '学历结构',
		dataIndex: 'educational',
		key: 'educational',
		align: 'center',
	},
	{
		title: '专业结构',
		dataIndex: 'specialty',
		key: 'specialty',
		align: 'center',
	},
	{
		title: '经历结构',
		dataIndex: 'experience',
		key: 'experience',
		align: 'center',
	},
	{
		title: '干部指数',
		dataIndex: 'cadreIndex',
		key: 'cadreIndex',
		align: 'center',
	},
	{
		title: '任职回避',
		dataIndex: 'withdraw',
		key: 'withdraw',
		align: 'center',
	},
]
const emits = defineEmits(['close'])

const visible = ref(true)

const tableData = ref<any>([fakeData.list[0].before, fakeData.list[0].after] as any)

const loadData = async () => {
	const res: any = await getTeamStructureCompare(props.orgId, props.mockId, props.userIds, props.type)

	if (res.code === 0) {
		const { before, after } = res.data

		tableData.value = [
			{ title: '人员调配前', ...before },
			{
				title: '人员调配后',
				...after,
			},
		] as any
	}
}

loadData()

const onAfterClose = () => {
	emits('close')
}
</script>

<style lang="less">
.compare-modal {
	width: 1689px !important;
}
</style>
