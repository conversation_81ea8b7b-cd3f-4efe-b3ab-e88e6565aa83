<template>
	<a-modal class="look-for-modal" v-model:visible="visible" :afterClose="onAfterClose" destoryOnClose :title="job_name" :footer="false">
		<div class="content">
			<img class="m-top-65" src="../images/gangzhaoren.png" />
			<div class="text-box m-top-30">
				<div>当前职务缺配，</div>
				<div>
					可通过
					<span class="highlight">“岗找人”</span>
					前往选择候选人。
				</div>
			</div>
			<div class="button-box m-top-74">
				<a-button @click="visible = false">取消</a-button>
				<a-button type="primary" @click="_onLookFor">岗找人</a-button>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const props = defineProps({
	job_name: String,
	onLookFor: Function,
})

const emits = defineEmits(['close'])

const visible = ref<any>(true)

const onAfterClose = () => {
	emits('close')
}

const _onLookFor = () => {
	visible.value = false

	props.onLookFor?.()
}
</script>

<style lang="less">
.look-for-modal {
	width: 733px !important;
	.ant-modal-title {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 27px;
		color: rgba(0, 0, 0, 0.9);
	}
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		img {
			width: 150px;
			height: 150px;
		}
		.text-box {
			font-size: 27px;
			text-align: center;
			.highlight {
				color: #008eff;
			}
		}
		.button-box {
			display: flex;
			gap: 0px 27px;
			margin-bottom: 21px;
			button {
				width: 255px;
				height: 60px;
				font-weight: 500;
				font-size: 21px;
			}
		}
	}
}
</style>
