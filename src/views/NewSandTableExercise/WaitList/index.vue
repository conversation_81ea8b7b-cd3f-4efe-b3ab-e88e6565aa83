<template>
	<div class="wait-list">
		<HeaderBack title="待配名单" />
		<div class="scroll">
			<MultiStatusTable :data-source="dataSource" @row-click="onRowClick" />
		</div>
	</div>
</template>

<script lang="ts">
export default {
	name: 'newSandTableExerciseWaitList',
}
</script>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import MultiStatusTable from '../components/MultiStatusTable.vue'
import HeaderBack from '../components/HeaderBack.vue'
import { openPersonFindJobModal } from '../components/component.ts'
import { getSubmitList } from '@/apis/new-sand-table-exercise'

const route = useRoute()
const router = useRouter()
const { mock_id } = route.query

const dataSource = ref([])

const loadData = async () => {
	const res = await getSubmitList({ mockId: mock_id })

	if (res.code === 0) {
		dataSource.value = res.data
	}
}

const onRowClick = (record: any) => {
	onOpenCandidate(record)
}
const onOpenCandidate = (record: any) => {
	const { user_id, pms_job_id, from_tag, current_job } = record

	openPersonFindJobModal({
		user_id: user_id,
		from_job_id: pms_job_id,
		from_tag: from_tag,
		job_name: current_job,
		mock_id,
		onSuccess() {
			loadData()
		},
	})
}
loadData()
</script>

<style lang="less" scoped>
.wait-list {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	.scroll {
		padding: 24px 24px;
		flex: 1;
		overflow: auto;
	}
}
</style>
../components/component.ts
