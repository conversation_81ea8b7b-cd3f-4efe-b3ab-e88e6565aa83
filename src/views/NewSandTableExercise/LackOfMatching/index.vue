<template>
	<div class="lack-of-matching">
		<HeaderBack title="缺配职务" />
		<div class="top-head">
			<div class="menu-box">
				<div
					class="check-item"
					:class="{
						active: selectIndex == index,
					}"
					v-for="(item, index) in data.head"
					:key="item.key"
					@click="onChange(item.key, index)"
				>
					{{ item.category }}: {{ item.lack_num }}
				</div>
			</div>
			<div class="search-box">
				<a-input-search v-model:value="inputSearchValue" placeholder="请输入" enter-button="查询" size="large" @search="onSearch" />
			</div>
		</div>
		<div class="content-box" @scroll="onScroll">
			<div class="org-item" v-for="(item, index) in data.lack_detail_list" :key="index">
				<div class="org-name">{{ item.organization }}</div>
				<div class="position-list-box">
					<div class="position-box" v-for="(items, indexs) in item.job" :key="indexs">
						<div class="position-item">
							<span class="icon"></span>
							<div class="top-box">{{ items.job_name }}</div>
							<div class="btn-box" @click="onSelect(item, items)">前往配置</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
export default {
	name: 'newSandTableExerciseLackOfMatching',
}
</script>

<script lang="ts" setup>
import { ref, inject, onActivated, nextTick } from 'vue'
import HeaderBack from '../components/HeaderBack.vue'
import { getlackJob } from '@/apis/new-sand-table-exercise'
import { useRouter, useRoute } from 'vue-router'
import { openFindJobModal } from '../components/component'
// 10290405：乡镇街道，10290402：事业，10290403：参公，10290404：国企，10290401：行政
const menuConfig = [
	{
		label: '全部',
		value: 0,
	},
	{
		label: '参公',
		key: 10290403,
		value: 0,
	},
	{
		label: '行政',
		key: 10290401,
		value: 0,
	},
	{
		label: '事业',
		key: 10290402,
		value: 0,
	},
	{
		label: '国企',
		key: 10290404,
		value: 0,
	},
	{
		label: '乡镇（街道）',
		key: 10290405,
		value: 0,
	},
]
const route = useRoute()
const router = useRouter()
const visible = ref<any>(false)
const activeIndex = ref<any>([])
const currentUser = ref<any>({})
const currentOrg = ref<any>({})
const scrollTop = ref<number>(0)
const inputSearchValue = ref<string>('')

const pageApi: any = inject('pageApi')

const { mock_id, org_id } = route.query

const data = ref<any>({
	head: [],

	lack_detail_list: [{}],
})

const selectIndex = ref(0)
const onLookFor = () => {
	visible.value = false

	const { org_id, organization } = currentOrg.value
	const { job_id, from_tag } = currentUser.value

	pageApi.routerPushCache('newSandTableExerciseLackOfMatching')

	router.push({
		path: '/new-sand-table-exercise/look-for-by-job',
		query: {
			mock_id,
			from_tag,
			pms_job_id: job_id,
			org_id,
			org_name: organization,
			from: 'home',
		},
	})
}

const onSelect = (org: any, users: any) => {
	currentOrg.value = org
	currentUser.value = users

	visible.value = !visible.value

	openFindJobModal({
		onLookFor: onLookFor,
		job_name: users.job_name,
	})
}

const onSearch = () => {
	loadData()
}

const onScroll = (e: any) => {
	scrollTop.value = e.target.scrollTop
}

const onChange = (index: any, type: any) => {
	selectIndex.value = type
	const indexs = activeIndex.value
	// 是否已选中
	const hasKey = index === indexs.join(',')

	if (hasKey) return

	if (index && index.indexOf(',') != -1) {
		activeIndex.value = index.split(',')
	} else {
		activeIndex.value = index ? [index] : []
	}

	document.querySelector('.content-box')?.scrollTo({
		top: 0,
	})

	loadData()
}

const loadData = async () => {
	const res = await getlackJob({
		institutionNatures: activeIndex.value,
		name: inputSearchValue.value,
	})

	if (res.code === 0) {
		res.data.head = res.data.head.reverse()

		data.value = res.data
	}
}

loadData()

onActivated(() => {
	pageApi?.routerRemoveCache('newSandTableExerciseLackOfMatching')

	loadData()

	nextTick(() => {
		const contentBox = document.querySelector('.content-box')

		contentBox?.scrollTo({
			top: scrollTop.value,
		})
	})
})

const isActive = (indexs: any) => {
	// index.value = indexs
	// return activeIndex.value.includes(index)
}
</script>

<style lang="less" scoped>
.lack-of-matching {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background: #f5f5f5;
	.top-head {
		display: flex;
		justify-content: space-between;
		background-color: #ffffff;

		.menu-box {
			display: flex;
			padding: 24px 0px 24px 16px;
			gap: 0px 15px;
			border-top: 2px solid rgba(0, 0, 0, 0.06);
			.check-item {
				padding: 11px 25px;
				min-width: 173px;
				font-weight: 500;
				font-size: 26px;
				color: #000000;
				line-height: 30px;
				border-radius: 3px;
				text-align: center;
				background: #f5f5f5;
			}
			.active {
				background: #008eff;
				color: #ffffff;
			}
		}
		.search-box {
			padding-right: 20px;
			display: flex;
			align-items: center;

			.ant-input-search {
				width: 351px !important;
				height: 54px;
				font-size: 21px;
				border: none;
			}
			:deep(input) {
				height: 54px;
				font-size: 24px;
			}
			:deep(.ant-input-search-button) {
				height: 54px;
				width: 84px;
				.anticon-search {
					font-size: 24px;
				}
			}
		}
	}

	.content-box {
		padding: 24px;
		margin-top: 12px;
		width: 100%;
		flex: 1;
		overflow: auto;
		background: #ffffff;
		.org-item {
			border-radius: 3px;
			overflow: hidden;
			.org-name {
				padding: 14px 26px;
				font-weight: bold;
				font-size: 26px;
				color: rgba(0, 0, 0, 0.85);
				line-height: 26px;
				background: #edf7ff;
			}
			.position-list-box {
				display: flex;
				flex-wrap: wrap;
			}
			.position-box {
				display: flex;
				flex-wrap: wrap;
				padding: 15px 24px;
				gap: 0px 9px;

				.position-item {
					width: 359px;
					height: 221px;
					background: #f9f9f9;
					border-radius: 3px;
					display: flex;
					flex-direction: column;
					position: relative;

					.icon {
						position: absolute;
						left: 0px;
						top: 0px;
						transform: translateX(-20%);
						width: 63px;
						height: 39px;
						display: inline-block;
						background: url('../images/org-team-vancy.png') center / cover no-repeat;
					}
					.top-box {
						flex: 1;
						padding: 42px 24px 11px;
						// 超出3行省略
						display: -webkit-box;
						-webkit-box-orient: vertical;
						line-clamp: 3;
						overflow: hidden;
						text-overflow: ellipsis;
						-webkit-line-clamp: 3;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						font-size: 24px;
						color: rgba(0, 0, 0, 0.85);
						line-height: 28px;
					}
					.btn-box {
						border-top: 1px solid rgba(0, 0, 0, 0.1);
						display: flex;
						align-items: center;
						justify-content: center;
						height: 60px;
						font-weight: 500;
						font-size: 24px;
						color: #008eff;
					}
				}
			}
		}
	}
}
</style>
