<template>
	<div class="page">
		<HeaderBack title="调整名单" v-if="FULL_VERSION" />
		<div class="page_title">
			<span>{{ mockName || mock_name || '调整方案' }}</span>
			<a-button type="primary" @click="onCompare" :disabled="!tab1.tableData.length">调整对比分析</a-button>
		</div>
		<div class="page_content">
			<a-tabs v-model:activeKey="activeTabKey">
				<template #rightExtra>
					<a-button class="ant-button" @click="onAddUser" v-if="FULL_VERSION">+添加</a-button>
				</template>
				<a-tab-pane key="user" tab="按人员查看">
					<div class="page_content_status_list m-top-10">
						<span class="label"> 调整类别: </span>
						<span
							:class="{ page_content_status_list_item_active: item.op_key === tab1.adjustType }"
							class="page_content_status_list_item"
							v-for="item in userStatusList"
							:key="item.op_key"
							@click="tab1.adjustType = item.op_key"
							>{{ item.op_value }}</span
						>
					</div>
					<a-table
						class="page_content_table m-top-30"
						:dataSource="tab1.tableData"
						:columns="tab1Columns"
						bordered
						:scroll="{ x: '100%', y: '60vh' }"
						:loading="tab1.loading"
						:pagination="false"
					>
						<template #bodyCell="{ column, record }">
							<template v-if="column.key === 'index'">
								<div>
									<div v-if="editKey && editKey === record.pms_mock_job_detail_id" class="editable-cell-input-wrapper">
										<a-input v-model:value="editValue" @blur="onSave(record)" ref="inputRef" />
									</div>
									<span @click="onColEdit(record)" v-else>{{ record.seq }}</span>
								</div>
							</template>
							<template v-if="column.key === 'draft_sit_job'">
								<a @click="onOpenPosition(record)">{{ record.draft_sit_job || '未设置' }}</a>
							</template>
							<template v-if="column.key === 'user_name'">
								<div class="user-box" @click="onOpenAdjustment(record)">
									<CodeAvatar :head_url="record.head_url" />
									<a>{{ record.user_name }}</a>
								</div>
							</template>
							<template v-if="column.key === 'handler'">
								<!-- <a-popconfirm title="确认撤销调整？" ok-text="确定" cancel-text="取消" @confirm="cancalAdjustHandler(record.pms_mock_job_detail_id)">
									<a-button type="link" :loading="curCancalId === record.pms_mock_job_detail_id">撤销调整</a-button></a-popconfirm
								> -->
								<svg
									t="1713961871264"
									class="icon w-64 h-64 m-top-10"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="12011"
									@click="onOpenOperation(record)"
								>
									<path
										d="M140.8 64a76.8 76.8 0 0 0-76.8 76.8v742.4a76.8 76.8 0 0 0 76.8 76.8h742.4a76.8 76.8 0 0 0 76.8-76.8V140.8a76.8 76.8 0 0 0-76.8-76.8H140.8z m167.6672 599.232h404.544l2.2528 0.064a38.4 38.4 0 0 1 0 76.672l-2.2528 0.064H308.4672l-2.2656-0.064a38.4 38.4 0 0 1 0-76.672l2.2656-0.064z m0-379.264h404.544l2.2528 0.064a38.4 38.4 0 0 1 0 76.672l-2.2528 0.064H308.4672l-2.2656-0.064a38.4 38.4 0 0 1 0-76.672l2.2656-0.064z"
										fill="#1296db"
										p-id="12012"
										data-spm-anchor-id="a313x.search_index.0.i1.73913a81A5Tw1V"
										class=""
									></path>
									<path
										d="M649.6768 423.4752a6.3232 6.3232 0 0 0-1.4592 4.0448v162.6368a6.3232 6.3232 0 0 0 10.368 4.864l91.7504-76.4672a12.6464 12.6464 0 0 0 0-19.4304l-91.7504-76.4544a6.3232 6.3232 0 0 0-8.9088 0.8064zM308.48 473.6a38.4 38.4 0 0 0-2.2656 76.736L308.48 550.4H512a38.4 38.4 0 0 0 2.2528-76.736L512 473.6H308.4672z"
										fill="#ffffff"
										p-id="12013"
										data-spm-anchor-id="a313x.search_index.0.i2.73913a81A5Tw1V"
										class="selected"
									></path>
								</svg>
							</template>
							<template v-if="column.key === 'adjust_reason'">
								<div>
									<div v-if="reasonEditKey && reasonEditKey === record.pms_mock_job_detail_id" class="editable-cell-input-wrapper">
										<a-input v-model:value="reasonEditValue" @blur="onSave1(record)" ref="inputRef" />
									</div>
									<div
										v-else
										:class="{
											'text-center': !record.adjust_reason?.trim(),
										}"
									>
										<a @click="onColEdit1(record)">{{ record.adjust_reason?.trim() || '-' }}</a>
									</div>
								</div>
							</template>
							<template v-if="column.key === 'adjust_type'">
								<a @click="onAdjustType(record)"> {{ record.adjust_type || '请选择' }}</a>
							</template>
						</template>
					</a-table>
				</a-tab-pane>
				<a-tab-pane key="job" tab="按岗位查看" force-render>
					<div class="page_content_status_list m-top-10">
						<span class="label"> 调整类别: </span>
						<span
							:class="{ page_content_status_list_item_active: item.value === tab2.sequences }"
							class="page_content_status_list_item"
							v-for="item in roleStatusList"
							:key="item.value"
							@click="tab2.sequences = item.value"
							>{{ item.label }}</span
						>
					</div>
					<a-table
						class="page_content_table"
						:dataSource="tab2.tableData"
						:columns="tab2Columns"
						bordered
						:scroll="{ x: '100%' }"
						:loading="tab2.loading"
						:pagination="false"
					>
						<template #bodyCell="{ column, index, record }">
							<!-- <template v-if="column.key === 'index'">{{ index + 1 }}</template> -->
							<template v-if="column.key === 'adjust_type'">
								<a @click="onAdjustType(record)"> {{ record.adjust_type || '请选择' }}</a>
							</template>
							<template v-if="column.key === 'user_name'">
								<div class="user-box">
									<CodeAvatar :head_url="record.head_url" />
									<span>{{ record.user_name }}</span>
								</div>
							</template>
							<template v-if="column.key === 'handler'">
								<a-popconfirm title="确认撤销调整？" ok-text="确定" cancel-text="取消" @confirm="cancalAdjustHandler(record.pms_mock_job_detail_id)">
									<a-button type="link" :loading="curCancalId === record.pms_mock_job_detail_id">撤销调整</a-button></a-popconfirm
								>
							</template>
						</template>
					</a-table>
				</a-tab-pane>
			</a-tabs>
		</div>
	</div>
	<a-modal class="adjust-list-modal" v-model:visible="visible" @cancel="oncancel" title="拟任职务" :footer="false">
		<div class="adj-content-box">
			<div class="tag">注：仅支持编辑任党组成员或者兼（挂）职信息，职务信息不支持手动编辑，需通过职务调整进行操作。</div>
			<div class="position">
				<div class="p-name">{{ currentSelectUser.draft_sit_job }}</div>
				<div class="input-p-name m-top-12">
					<a-input type="text" @change="onChange" :value="positionInput" />
				</div>
			</div>
			<div class="btn-box">
				<a-button type="primary" @click="onConfirm">确定</a-button>
			</div>
		</div>
	</a-modal>
</template>
<script lang="ts">
export default {
	name: 'newSandTableExerciseAdjustList',
}
</script>
<script setup lang="ts">
import { message } from 'ant-design-vue'
import HeaderBack from '../../components/HeaderBack.vue'
import { ref, reactive, watchEffect, watch, nextTick, computed, inject, onActivated } from 'vue'
import {
	getAdjustList,
	cancalAdjust,
	updateJobName,
	updateSerialNumber,
	changeAdjustType,
	getMock,
	adjustReason,
} from '@/apis/new-sand-table-exercise'
import { positionTypeDrawer } from '../../components/component'
import { openAddUserModal, openAdjustmentDetailsModal, openAdjustList, onOperation } from '../../components/component'
import { cloneDeep } from 'lodash'
import { useRoute } from 'vue-router'
import { convertPixelsToVw as vw, convertPxToRem } from '@/utils/utils'
import { CDN_URL, FULL_VERSION, LIMIT_VERSION } from '@/config/env'
import useNewSandbox from '@/store/newSandbox'
import router from '@/router'
import defaultAvatar from '@/assets/images/avatar.png'
import CodeAvatar from '@/components/CodeAvatar.vue'
const route = useRoute()
const mock = useNewSandbox()

const pageApi: any = inject('pageApi')

const { mock_name, mock_id, activeKey = 'user', status } = route.query
const activeTabKey = ref(activeKey)
// 输入框
const inputRef = ref()

const visible = ref(false)
// 编辑的id
const editKey = ref<any>()
const editValue = ref<any>()
//调整理由
const reasonEditKey = ref<any>()
const reasonEditValue = ref<any>()

const mockId = ref<any>(mock_id)
const mockName = ref<any>(mock_name)

const positionInput = ref('')

const userStatusList = computed(() => {
	const list = [
		{
			op_value: '全部',
			op_key: '',
		},
		...mock.typeOption,
	]
	return list
})
const fullNumber = (value: number) => {
	return Math.round(convertPxToRem(value))
}
const tab1Columns = ref([
	{
		title: '序号',
		dataIndex: 'index',
		key: 'index',
		align: 'center',
		width: fullNumber(127),
		fixed: 'left',
		// customCell: (record: any, index: number) => {
		// 	return { rowSpan: record.rowSpan }
		// },
	},
	{
		title: '拟任职务',
		dataIndex: 'draft_sit_job',
		key: 'draft_sit_job',
		align: 'left',
		width: fullNumber(397),
	},
	{
		title: '姓名',
		dataIndex: 'user_name',
		key: 'user_name',
		align: 'center',
		width: fullNumber(184),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '现任职务',
		dataIndex: 'current_job',
		key: 'current_job',
		align: 'left',
		width: fullNumber(432),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '性别',
		dataIndex: 'gender',
		key: 'gender',
		align: 'center',
		width: fullNumber(116),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '出生年月',
		dataIndex: 'birthday',
		key: 'birthday',
		align: 'center',
		width: fullNumber(200),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '任现职务时间',
		dataIndex: 'current_job_time',
		key: 'current_job_time',
		align: 'center',
		width: fullNumber(201),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '全日志学历',
		dataIndex: 'full_time_diploma',
		key: 'full_time_diploma',
		align: 'center',
		width: fullNumber(176),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '全日志学历毕业学校及专业',
		dataIndex: 'school_department',
		key: 'school_department',
		align: 'left',
		width: fullNumber(343),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '2023年干部指数同序列排名',
		dataIndex: 'cadre_index_rank2023',
		key: 'cadre_index_rank2023',
		align: 'center',
		width: fullNumber(236),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '2024年干部指数同序列排名',
		dataIndex: 'cadre_index_rank2024',
		key: 'cadre_index_rank2024',
		align: 'center',
		width: fullNumber(258),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '班子内指数排名',
		dataIndex: 'org_cadre_rank',
		key: 'org_cadre_rank',
		align: 'center',
		width: fullNumber(205),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '班子内测评排名',
		dataIndex: 'eval_org_rank',
		key: 'eval_org_rank',
		align: 'center',
		width: fullNumber(190),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '调整理由',
		dataIndex: 'adjust_reason',
		key: 'adjust_reason',
		align: 'left',
		width: fullNumber(350),
		// customCell: (record: any, index: number) => {
		// 	return { rowSpan: record.rowSpan }
		// },
	},
	{
		title: '调整类别',
		dataIndex: 'adjust_type',
		key: 'adjust_type',
		align: 'center',
		width: fullNumber(195),
		fixed: 'right',
	},
	{
		title: '操作',
		dataIndex: 'handler',
		key: 'handler',
		align: 'center',
		width: fullNumber(235),
		fixed: 'right',
	},
])
if (LIMIT_VERSION) {
	tab1Columns.value = tab1Columns.value.filter((item: any) => {
		return item.key !== 'handler'
	})
}
const roleStatusList = ref([
	{
		label: '全部',
		value: '',
	},
	{
		label: '乡镇（街道）',
		value: '103002',
	},
	{
		label: '经济部门',
		value: '10300101',
	},
	{
		label: '保障部门',
		value: '10300102',
	},
	{
		label: '执纪执法部门',
		value: '10300103',
	},
	{
		label: '国有企业',
		value: '10300105',
	},
	{
		label: '学校',
		value: '10300106',
	},
	{
		label: '医院',
		value: '10300107',
	},
])
const tab2Columns = ref([
	{
		title: '序号',
		dataIndex: 'seq_job',
		key: 'seq_job',
		align: 'center',
		width: vw(127),
		fixed: 'left',
	},
	{
		title: '拟任职务',
		dataIndex: 'draft_sit_job',
		key: 'draft_sit_job',
		align: 'left',
		width: vw(397),
		customCell: (record: any, index: number) => {
			return { rowSpan: record.rowSpan }
		},
	},
	{
		title: '姓名',
		dataIndex: 'user_name',
		key: 'user_name',
		align: 'center',
		width: vw(154),
	},
	{
		title: '现任职务',
		dataIndex: 'current_job',
		key: 'current_job',
		align: 'left',
		width: vw(432),
	},
	{
		title: '性别',
		dataIndex: 'gender',
		key: 'gender',
		align: 'center',
		width: vw(116),
	},
	{
		title: '出生年月',
		dataIndex: 'birthday',
		key: 'birthday',
		align: 'center',
		width: vw(150),
	},
	{
		title: '任现职务时间',
		dataIndex: 'current_job_time',
		key: 'current_job_time',
		align: 'center',
		width: vw(201),
	},
	{
		title: '全日志学历',
		dataIndex: 'full_time_diploma',
		key: 'full_time_diploma',
		align: 'center',
		width: vw(176),
	},
	{
		title: '全日志学历毕业学校及专业',
		dataIndex: 'school_department',
		key: 'school_department',
		align: 'left',
		width: vw(343),
	},
	{
		title: '2023年干部指数同序列排名',
		dataIndex: 'cadre_index_rank2023',
		key: 'cadre_index_rank2023',
		align: 'center',
		width: vw(236),
	},
	{
		title: '2024年干部指数同序列排名',
		dataIndex: 'cadre_index_rank2024',
		key: 'cadre_index_rank2024',
		align: 'center',
		width: vw(258),
	},
	{
		title: '班子内指数排名',
		dataIndex: 'org_cadre_rank',
		key: 'org_cadre_rank',
		align: 'center',
		width: vw(205),
	},
	{
		title: '班子内测评排名',
		dataIndex: 'eval_org_rank',
		key: 'eval_org_rank',
		align: 'center',
		width: vw(190),
	},
	{
		title: '调整类别',
		dataIndex: 'adjust_type',
		key: 'adjust_type',
		align: 'center',
		width: vw(165),
	},
	{
		title: '操作',
		dataIndex: 'handler',
		key: 'handler',
		align: 'center',
		fixed: 'right',
		width: vw(235),
	},
])

if (LIMIT_VERSION) {
	tab2Columns.value = tab2Columns.value.filter((item: any) => {
		return item.key !== 'handler'
	})
}

const onOpenOperation = (record: any) => {
	const { draft_sit_job_id, draft_sit_job } = record
	const { instance, onClose } = onOperation({
		mock_id: mockId.value,
		pms_job_id: draft_sit_job_id,
		job_name: draft_sit_job,
		...record,
		onSuccess(close: any) {
			close?.()

			tab1.getList()
		},
	})
}

const onCompare = () => {
	pageApi.routerPushCache('newSandTableExerciseAdjustList')

	router.push({
		path: '/new-sand-table-exercise/adjust-analysis',
		query: {
			mock_id: mockId.value,
		},
	})
}

const loadMock = async () => {
	const res = await getMock()
	if (res.code === 0) {
		mockId.value = res.data.mock_id
		mockName.value = res.data.mock_name
	}
}

LIMIT_VERSION && loadMock()

onActivated(() => {
	pageApi?.routerRemoveCache('newSandTableExerciseAdjustList')
})

watch(
	() => status,
	(newV: any) => {
		console.log(status, 'status')
		if (newV === '2') {
			tab1Columns.value.pop()
			tab2Columns.value.pop()
		}
	},
	{
		immediate: true,
	}
)

class AdjustList {
	public lookType: string
	public sequences?: string
	public adjustType?: string
	public loading: boolean
	public tableData: any[]

	constructor(lookType: any) {
		this.lookType = lookType
		this.sequences = ''
		this.adjustType = ''
		this.loading = false
		this.tableData = []
	}
	getList() {
		try {
			this.loading = true
			let params = {
				lookType: this.lookType,
				sequences: this.sequences,
				adjustType: this.adjustType,
				mockId: mockId.value,
			}
			this.lookType === 'user' ? delete params.sequences : delete params.adjustType
			getAdjustList(params)
				.then((res: any) => {
					const { code, data, message: msg } = res
					this.loading = false
					if (code === 0) {
						this.tableData = formateList(data, this.lookType)
					} else {
						message.error(msg)
					}
				})
				.finally(() => {
					this.loading = false
				})
		} catch (error: any) {
			message.error(error.message)
			this.loading = false
		}
	}
}

const tab1: AdjustList = reactive<AdjustList>(new AdjustList('user'))
const tab2: AdjustList = reactive<AdjustList>(new AdjustList('job'))

const curCancalId = ref()
const cancalAdjustHandler = async (pms_mock_job_detail_id: number) => {
	try {
		curCancalId.value = pms_mock_job_detail_id
		const { code, data, message: msg } = await cancalAdjust({ pms_mock_job_detail_id })
		curCancalId.value = null
		if (code === 0) {
			message.success(data)
			if (activeTabKey.value === 'user') {
				tab1.getList()
			} else {
				tab2.getList()
			}
		} else {
			message.error(msg)
		}
	} catch (error: any) {
		message.error(error.message)
	}
}

const formateList = (list: any[], type: string): any[] => {
	const tableData: any[] = cloneDeep(list)
	let prop = type === 'user' ? 'user_id' : 'draft_sit_job_id'
	let curIndex = 0
	let indexCount = 1
	while (indexCount < tableData.length) {
		var item = tableData.slice(curIndex, curIndex + 1)[0] //获取没有比较的第一个对象
		if (!item.rowSpan) {
			item.rowSpan = 1 //初始化为1
		}
		if (item[prop] === tableData[indexCount][prop]) {
			//第一个对象与后面的对象相比，有相同项就累加，并且后面相同项设置为0
			item.rowSpan++
			tableData[indexCount].rowSpan = 0
		} else {
			curIndex = indexCount
		}
		indexCount++
	}
	return tableData
}

const onAddUser = () => {
	openAddUserModal({
		mock_id: mockId.value,
		onSuccess() {
			tab1.getList()
		},
	})
}

const onChange = (e: any) => {
	positionInput.value = e.target.value
}

const onAdjustType = (record: any) => {
	if (LIMIT_VERSION) return

	positionTypeDrawer.openDrawer({
		type: record.adjust_type,
		onConfirm: async (key: any) => {
			const res = await changeAdjustType({
				adjust_type: key,
				pms_mock_job_detail_id: record.pms_mock_job_detail_id,
			})

			if (res.code === 0) {
				message.success('修改成功！')

				tab1.getList()
			} else {
				message.error(res.message)
			}
		},
	})
}

const currentSelectUser = ref<any>({})
const onOpenPosition = (record: any) => {
	if (LIMIT_VERSION) return

	currentSelectUser.value = record

	const { instance } = openAdjustList({
		pms_mock_job_detail_id: record.pms_mock_job_detail_id,
		onConfirm: async (positionInput: any, cb: () => void) => {
			const res = await updateJobName({
				pms_mock_job_detail_id: currentSelectUser.value.pms_mock_job_detail_id,
				name: positionInput,
			})
			if (res.code === 0) {
				// .exposed.close()
				message.success('修改成功')

				cb?.()

				tab1.getList()
			} else {
				message.error(res.message)
			}
		},
	})
}

const onOpenAdjustment = (record: any) => {
	// 打开调整详情弹窗，该弹窗中可以看到该岗位的所有调整记录
	// openAdjustmentDetailsModal({
	// 	mock_id: mockId.value,
	// 	user_id: record.user_id,
	// 	onSuccess() {
	// 		tab1.getList()
	// 	},
	// })
	pageApi.routerPushCache('newSandTableExerciseAdjustList')

	router.push('/cadre-portrait/home?user_id=' + record.user_id)
}

const onConfirm = async () => {
	if (activeTabKey.value === 'user') {
	}
}

const oncancel = () => {
	positionInput.value = ''
}

const onColEdit = (record: any) => {
	editValue.value = record.seq
	editKey.value = record.pms_mock_job_detail_id

	nextTick(() => {
		inputRef.value?.focus()
	})
}
const onColEdit1 = (record: any) => {
	if (LIMIT_VERSION) return

	reasonEditKey.value = record.pms_mock_job_detail_id
	reasonEditValue.value = record.adjust_reason

	nextTick(() => {
		inputRef.value?.focus()
	})
}
const onSave = async (record: any) => {
	const { pms_mock_job_detail_id } = record

	nextTick(() => {
		editValue.value = undefined
		editKey.value = undefined
	})

	if (editValue.value == record.seq) {
		return
	}

	const res = await updateSerialNumber([
		{
			pms_mock_job_detail_id,
			seq: String(editValue.value),
		},
	])
	if (res.code === 0) {
		message.success('修改成功')

		tab1.getList()

		editValue.value = undefined
		editKey.value = undefined
	} else {
		message.error(res.message)
	}
}
const onSave1 = async (record: any) => {
	const { pms_mock_job_detail_id } = record

	nextTick(() => {
		reasonEditKey.value = undefined
		reasonEditValue.value = undefined
	})

	if (reasonEditValue.value == record.adjust_reason) {
		return
	}

	const res = await adjustReason({
		pms_mock_job_detail_id,
		adjust_reason: reasonEditValue.value?.trim(),
	})
	if (res.code === 0) {
		message.success('修改成功')

		tab1.getList()

		reasonEditValue.value = undefined
		reasonEditKey.value = undefined
	} else {
		message.error(res.message)
	}
}
const returnType = (adjust_key: any) => {
	return mock.typeOption.find((item: any) => {
		return item.op_key === adjust_key
	})?.op_value
}

watchEffect(() => {
	if (!mockId.value) return

	if (activeTabKey.value === 'user') {
		tab1.getList()
	} else {
		tab2.getList()
	}
})
</script>

<style lang="less" scoped>
.page {
	display: flex;
	flex-direction: column;
	height: 100%;
	&_title {
		padding: 16px 16px 16px 30px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		span {
			font-weight: bold;
			font-size: 27px;
			color: rgba(0, 0, 0, 0.85);
		}
		button {
			width: 180px;
			height: 50px;
			font-size: 23px;
			line-height: 23px;
		}
	}
	&_content {
		flex: 1;
		overflow: auto;
		padding: 16px;
		background: #fff;
		.label {
			font-size: 24px;
			color: rgba(0, 0, 0, 0.9);
		}
		.editable-cell-input-wrapper {
			input {
				font-size: 26px;
			}
		}
		.ant-button {
			padding: 0px;
			width: 111px;
			height: 48px;
			background: #008eff;
			border-radius: 3px 3px 3px 3px;

			font-weight: 400;
			font-size: 23px;
			color: #ffffff;
			line-height: 23px;
		}
		&_status_list_item {
			margin-right: 10px;
			padding: 8px 15px;
			background: #f5f5f5;
			cursor: pointer;
			font-size: 24px;
			&_active {
				color: #fff;
				background: #008eff;
			}
		}
		&_table {
			margin-top: 24px;
			&_name {
				display: flex;
				justify-content: space-between;
				img {
					width: 18px;
					height: 18px;
				}
			}
			:deep(.ant-table-cell) {
				font-size: 26px;
			}
			:deep(.ant-btn-link) {
				font-size: 26px;
			}
		}
	}
	.user-box {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		img {
			width: 93px;
			height: 116px;
		}
	}
	:deep(.ant-tabs-tab-btn) {
		font-weight: bold;
		font-size: 27px;
		line-height: 32px;
	}
}
:deep(.ant-table-thead) {
	.ant-table-cell {
		text-align: center !important;
	}
}
.text-center {
	text-align: center;
}
</style>
