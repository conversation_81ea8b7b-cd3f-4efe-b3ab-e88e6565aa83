<template>
	<div class="wait-list">
		<MultiStatusTable :data-source="dataSource" @row-click="onRowClick" :pagination="false" />
	</div>
</template>

<script lang="ts" setup>
import { ref, PropType } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MultiStatusTable from '../components/MultiStatusTable.vue'
import { getOrgTeamDetail2 } from '@/apis/new-sand-table-exercise'
const route = useRoute()
const router = useRouter()
const dataSource = ref([])

defineProps({
	onRouter: {
		default: () => void 0,
		type: Function,
	},
	onRowClick: {
		type: Function as PropType<() => void>,
	},
})

const loadData = async () => {
	const res: any = await getOrgTeamDetail2({ mockId: route.query.mock_id || '' })
	if (res.code == 0) {
		dataSource.value = res.data
	}
}

loadData()
</script>

<style lang="less" scoped>
.wait-list {
	width: 100%;
	height: 100%;
	overflow: auto;
	padding-top: 20px;
}
</style>
../components/component.ts
