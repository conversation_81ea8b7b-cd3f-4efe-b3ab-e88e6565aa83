<template>
	<div class="look-for-by-job">
		<header-back title="岗位找人" :sub-title="org_name ? org_name + '队伍' : undefined" />
		<div class="content-box">
			<div class="left">
				<div
					class="menu-item"
					:class="{ 'menu-item-active': activeIndex === index }"
					@click="onActive(item, index)"
					v-for="(item, index) in menuConfig"
					:key="index"
				>
					<img class="icon" :src="activeIndex === index ? item.activeIcon : item.icon" />
					<div class="label">{{ item.label }}</div>
				</div>
			</div>
			<div class="right">
				<keep-alive>
					<component :is="componentsName" :on-router="onRouter" :on-row-click="onRowClick" />
				</keep-alive>
			</div>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'newSandTableExerciseLookForByJob',
}
</script>
<script lang="ts" setup>
import { ref, reactive, computed, inject, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import HeaderBack from '../components/HeaderBack.vue'
import searchPng from '../images/search.png'
import searchActivePng from '../images/search-active.png'
import starPng from '../images/star.png'
import starActivePng from '../images/star-active.png'
import waitListPng from '../images/wait-list.png'
import waitListActivePng from '../images/wait-list-active.png'
import { openCandidate } from '../components/component.ts'
import Search from './Search.vue'
import Collection from './Collection.vue'
import WaitList from './WaitList.vue'

const menuConfig = [
	{
		label: '干部查询',
		icon: searchPng,
		activeIcon: searchActivePng,
	},
	{
		label: '我的收藏',
		icon: starPng,
		activeIcon: starActivePng,
	},
	{
		label: '待配名单',
		icon: waitListPng,
		activeIcon: waitListActivePng,
	},
]

const pageApi: any = inject('pageApi')

const route = useRoute()
const router = useRouter()

const activeIndex = ref(0)

const { org_name } = useRoute().query

const formState = reactive({})

const onActive = (_item: any, index: any) => {
	activeIndex.value = index
}

const componentsName = computed(() => {
	const componentMap: any = {
		0: Search,
		1: Collection,
		2: WaitList,
	}
	return componentMap[activeIndex.value]
})

// 路由跳转
const onRouter = (params: any) => {
	if (route.query.from === 'candidate') {
		router.back()

		pageApi.routerPushCache('newSandTableExerciseLookForByJob')
	} else {
		params.query = Object.assign(params.query || {}, route.query)

		routerPush(params)
	}
}

const routerPush = (params: any) => {
	console.log('运行')
	pageApi.routerPushCache('newSandTableExerciseLookForByJob')

	router.push(params)
}

const onRowClick = (record: any) => {
	const { from_tag, mock_id, org_id, org_name, pms_job_id, from_job_id, from_user_id } = route.query
	openCandidate({ user_id: record.user_id, from_tag, job: record.current_job, pms_job_id, mock_id, from_job_id, from_user_id, onRouter, routerPush })
}

onActivated(() => {
	pageApi?.routerRemoveCache('newSandTableExerciseLookForByJob')
})
</script>

<style lang="less" scoped>
.look-for-by-job {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	.content-box {
		display: flex;
		flex: 1;
		overflow: hidden;
		.left {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 165px;
			height: 100%;
			background: #ffffff;
			border-right: 1px solid rgba(0, 0, 0, 0.1);
			.menu-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				.icon {
					height: 35px;
					width: 35px;
					margin-bottom: 11px;
				}
				.label {
					font-size: 24px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: #666666;
					line-height: 28px;
				}
				&:nth-child(1) {
					margin-top: 105px;
				}
				&:nth-child(2) {
					margin-top: 123px;
				}
				&:nth-child(3) {
					margin-top: 123px;
				}
			}

			.menu-item-active {
				.label {
					color: #028fff;
				}
			}
		}
		.right {
			position: relative;
			height: 100%;
			flex: 1;
			overflow: hidden;
			background-color: #fff;
		}
	}
}
</style>
