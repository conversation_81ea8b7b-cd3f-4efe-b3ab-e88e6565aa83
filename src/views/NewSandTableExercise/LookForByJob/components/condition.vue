<template>
	<div class="condition">
		<div class="label">{{ label }}：</div>
		<div class="con-box">
			<div class="con-item" v-for="(item, index) in data" :key="index">
				<span>{{ item.label }}</span>
				<svg class="delete" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" @click="onDelete(item, label)">
					<g id="Frame">
						<g id="Group 427323481">
							<path
								id="Vector"
								d="M27.21 8.78997C22.1567 3.73668 13.8433 3.73668 8.78997 8.78997C3.73668 13.8433 3.73668 22.1567 8.78997 27.21C13.8433 32.2633 22.1567 32.2633 27.21 27.21C32.2633 22.1567 32.2633 14.0063 27.21 8.78997Z"
								fill="black"
								fill-opacity="0.2"
							/>
							<path
								id="Vector_2"
								d="M22.7458 21.5593C23.0847 21.8983 23.0847 22.4068 22.7458 22.7458C22.4068 23.0847 21.8983 23.0847 21.5593 22.7458L18 19.1864L14.4407 22.7458C14.1017 23.0847 13.5932 23.0847 13.2542 22.7458C12.9153 22.4068 12.9153 21.8983 13.2542 21.5593L16.8136 18L13.2542 14.4407C12.9153 14.1017 12.9153 13.5932 13.2542 13.2542C13.5932 12.9153 14.1017 12.9153 14.4407 13.2542L18 16.8136L21.5593 13.2542C21.8983 12.9153 22.4068 12.9153 22.7458 13.2542C23.0847 13.5932 23.0847 14.1017 22.7458 14.4407L19.1864 18L22.7458 21.5593Z"
								fill="white"
							/>
						</g>
					</g>
				</svg>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	label: {
		default: String,
	},
	data: {
		default: () => [] as any,
	},
})

const emits = defineEmits(['delete'])

const onDelete = (item: any, label: any) => {
	emits('delete', item, label)
}
</script>

<style lang="less" scoped>
.condition {
	padding: 12px 18px;
	display: flex;
	align-items: center;
	background: rgba(0, 142, 255, 0.07);
	.label {
		font-weight: 400;
		font-size: 24px;
		color: #000000;
		line-height: 28px;
	}
	.con-box {
		display: flex;
		align-items: center;
		.con-item {
			display: flex;
			align-items: center;
			margin-right: 10px;
			font-weight: 400;
			font-size: 24px;
			color: #008eff;
			&:not(:last-child) {
				margin-right: 30px;
			}
			.delete {
				margin-left: 6px;
				width: 27px;
				height: 27px;
				cursor: pointer;
			}
		}
	}
}
</style>
