<template>
	<div class="result">
		<div class="left">
			<div class="inner">
				<span @click="onSearchChange"> 查 询 条 件 </span>
			</div>
			<span class="icon" @click="onSearchChange"></span>
		</div>
		<div class="right">
			<div class="top-box">
				<div class="level-1">
					<Condition label="年龄" :data="ageCondition" @delete="onDeleteCondition" />
					<Condition label="干部来源" :data="sourceCondition" @delete="onDeleteCondition" />
					<!-- <Condition label="民族" :data="nationCondition" /> -->
					<Condition label="其他" :data="otherOptions" @delete="onDeleteCondition" />
				</div>
				<div class="level-2 m-top-18">
					<div class="left-input">
						<div class="input-item">
							干部指数本单位排名前
							<a-input
								class="res-input"
								placeholder="请输入"
								type="number"
								@change="onChangeInput($event, 'cadre_index_rank')"
								v-model:value="dataList.cadre_index_rank"
							/>
						</div>
						<div class="input-item">
							干部指数序列排名前
							<a-input
								class="res-input"
								placeholder="请输入"
								type="number"
								@change="onChangeInput($event, 'cadre_index_rank_percent')"
								v-model:value="dataList.cadre_index_rank_percent"
							/>%
						</div>
						<div class="input-item">
							民主测评本单位排名前
							<a-input
								class="res-input"
								placeholder="请输入"
								type="number"
								@change="onChangeInput($event, 'eval_index_rank')"
								v-model:value="dataList.eval_index_rank"
							/>
						</div>
						<div class="input-item">
							民主测评序列排名前
							<a-input
								class="res-input"
								placeholder="请输入"
								type="number"
								@change="onChangeInput($event, 'eval_index_rank_percent')"
								v-model:value="dataList.eval_index_rank_percent"
							/>%
						</div>
					</div>
					<div class="right-button">
						<a-button @click="onReset">重置</a-button>
						<a-button type="primary" @click="getUserListByOrgId">查询</a-button>
					</div>
				</div>
				<div class="filter-menu">
					<div class="filter">
						<div class="label">年龄：</div>
						<div class="filter-btn">
							<div
								:class="`item item-up ${filterState.sort_type === 'ASC' ? 'active-up' : ''}`"
								@click="handlePaginationChange('birthday', 'ascend', 1)"
							>
								<div :class="`icon`"></div>
								升序
							</div>
							<div
								:class="`item item-down ${filterState.sort_type === 'DESC' ? 'active-down' : ''}`"
								@click="handlePaginationChange('birthday', 'descend', 2)"
							>
								<div :class="`icon`"></div>
								降序
							</div>
						</div>
					</div>
					<div class="filter m-left-60">
						<div class="label">干部指数：</div>
						<div class="filter-btn">
							<div
								:class="`item item-up ${filterState.cadre_sort_type === 'ASC' ? 'active-up' : ''}`"
								@click="handlePaginationChange('cadre_index', 'ascend', 3)"
							>
								<div :class="`icon`"></div>
								升序
							</div>
							<div
								:class="`item item-down  ${filterState.cadre_sort_type === 'DESC' ? 'active-down' : ''}`"
								@click="handlePaginationChange('cadre_index', 'descend', 4)"
							>
								<div :class="`icon`"></div>
								降序
							</div>
						</div>
					</div>
					<div class="filter m-left-60">
						<div class="label">民主测评：</div>
						<div class="filter-btn">
							<div
								:class="`item item-up ${filterState.min_zu_sort_type === 'ASC' ? 'active-up' : ''}`"
								@click="handlePaginationChange('minzu_index', 'ascend', 5)"
							>
								<div :class="`icon`"></div>
								升序
							</div>
							<div
								:class="`item item-down  ${filterState.min_zu_sort_type === 'DESC' ? 'active-down' : ''}`"
								@click="handlePaginationChange('minzu_index', 'descend', 6)"
							>
								<div :class="`icon`"></div>
								降序
							</div>
						</div>
					</div>
					<!-- <div class="filter">
						<div class="label"></div>
						<div class="filter-btn">
							<div :class="`item ${pageType === 1 ? 'active' : ''}`" @click="pageType = 1">
								<div v-html="svgIconPing(pageType === 1 ? '#008EFF' : undefined)" :class="`icon`"></div>
								平铺
							</div>
							<div :class="`item ${pageType === 2 ? 'active' : ''}`" @click="pageType = 2">
								<div v-html="svgIconTable(pageType === 2 ? '#008EFF' : undefined)" :class="`icon`"></div>
								列表
							</div>
						</div>
					</div> -->
				</div>
			</div>
			<div class="result-box">
				<MultiStatusTable :data-source="dataSource" @row-click="onRowClick" :pagination="true" />
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Condition from './condition.vue'
import MultiStatusTable from '../../components/MultiStatusTable.vue'
import MeScroll from 'mescroll.js'
import { openCandidate } from '../../components/component.ts'
import { getOrgTeamDetail } from '@/apis/new-sand-table-exercise'
const props = defineProps({
	formState: {
		type: Object,
		default: () => ({}),
	},
	onRouter: {
		default: () => void 0,
		type: Function,
	},
	onRowClick: {
		default: (args) => void 0,
		type: Function,
	},
})

const route = useRoute()
const router = useRouter()

const dataList = ref({
	cadre_index_rank: props.formState._cadre_index_rank,
	cadre_index_rank_percent: props.formState.cadre_index_rank_percent,
	eval_index_rank: props.formState._eval_index_rank,
	eval_index_rank_percent: props.formState.eval_index_rank_percent,
})
const ageCondition = ref([
	{ label: '80后', value: 1 },
	{ label: '85后', value: 2 },
	{ label: '90后', value: 3 },
	{ label: '95后', value: 4 },
])
// source
const sourceCondition = ref([
	{ label: '选调生', value: '1' },
	{ label: '村官', value: '2' },
	{ label: '五方面人才', value: '3' },
])
const otherOptions = ref([
	{ label: '女干部', value: '1' },
	{ label: '少数民族', value: '2' },
	{ label: '中共党员', value: '3' },
	{ label: '非中共党员', value: '4' },
])
const onChangeInput = (e: any, type: string) => {
	switch (type) {
		case 'cadre_index_rank':
			dataList.value.cadre_index_rank = e.target.value
			props.formState.cadre_index_rank = [e.target.value, props.formState.cadre_index_rank && props.formState.cadre_index_rank[1]]

			props.formState._cadre_index_rank = e.target.value
			break
		case 'cadre_index_rank_percent':
			dataList.value.cadre_index_rank_percent = e.target.value
			props.formState.cadre_index_rank = [(props.formState.cadre_index_rank && props.formState.cadre_index_rank[0]) || '', e.target.value]

			props.formState.cadre_index_rank_percent = e.target.value
			break
		case 'eval_index_rank':
			dataList.value.eval_index_rank = e.target.value
			props.formState.eval_index_rank = [e.target.value, (props.formState.eval_index_rank && props.formState.eval_index_rank[1]) || '']

			props.formState._eval_index_rank = e.target.value
			break
		case 'eval_index_rank_percent':
			dataList.value.eval_index_rank_percent = e.target.value
			props.formState.eval_index_rank = [(props.formState.eval_index_rank && props.formState.eval_index_rank[0]) || '', e.target.value]

			props.formState.eval_index_rank_percent = e.target.value
			break
	}
}
const onReset = () => {
	dataList.value.cadre_index_rank = null
	dataList.value.cadre_index_rank_percent = null
	dataList.value.eval_index_rank = null
	dataList.value.eval_index_rank_percent = null
	props.formState.cadre_index_rank = null
	props.formState.cadre_index_rank_percent = null
	props.formState.eval_index_rank = null
	props.formState.eval_index_rank_percent = null

	props.formState._cadre_index_rank = null
	props.formState._eval_index_rank = null

	console.log(dataList.value, 'dataList.value')

	getUserListByOrgId()
}
const getUserListByOrgId = async () => {
	const _p = { ...props.formState }

	delete _p._cadre_index_rank
	delete _p._eval_index_rank

	const params = {
		..._p,
		cadre_index_rank: props.formState._cadre_index_rank,
		cadre_index_rank_percent: props.formState.cadre_index_rank_percent,
		eval_index_rank: props.formState._eval_index_rank,
		eval_index_rank_percent: props.formState.eval_index_rank_percent,
	}
	const res: any = await getOrgTeamDetail(params)
	if (res.code === 0) {
		dataSource.value = res.data
	}
}
const headOption = () => {
	let ageConditions = props.formState.age?.map((aValue: any) => {
		return ageCondition.value.find((condition: any) => condition.value === aValue)
	})
	let sourceConditions = props.formState.user_source?.map((aValue: any) => {
		return sourceCondition.value.find((condition: any) => condition.value === aValue)
	})
	let otherOptionsList = props.formState.other?.map((aValue: any) => {
		return otherOptions.value.find((condition: any) => condition.value === aValue)
	})
	ageCondition.value = ageConditions
	sourceCondition.value = sourceConditions
	otherOptions.value = otherOptionsList
}
headOption()
getUserListByOrgId()
const onDeleteCondition = (item: any, label: any) => {
	if (label === '年龄') {
		ageCondition.value = ageCondition.value.filter((condition: any) => condition.value !== item.value)
		console.log(ageCondition.value, '年龄')
		props.formState.age = ageCondition.value.map((item: any) => item.value)
	} else if (label === '干部来源') {
		sourceCondition.value = sourceCondition.value.filter((condition: any) => condition.value !== item.value)
		props.formState.user_source = sourceCondition.value.map((item: any) => item.value)
	} else if (label === '其他') {
		otherOptions.value = otherOptions.value.filter((condition: any) => condition.value !== item.value)
		props.formState.other = otherOptions.value.map((item: any) => item.value)
	}
	getUserListByOrgId()
	// headOption()
}
const emits = defineEmits(['searchChange'])

const scrollContainer1 = ref<any>(undefined)

const onSearchChange = () => {
	emits('searchChange')
}
// 路由跳转
// const onRouter = (params: any) => {
// 	if (route.query.from === 'candidate') {
// 		router.back()
// 	} else {
// 		params.query = Object.assign(params.query || {}, route.query)

// 		router.push(params)
// 	}
// }

// const onRowClick = (record: any) => {
// 	const { from_tag, mock_id, org_id, org_name, pms_job_id, from_job_id, from_user_id } = route.query
// 	openCandidate({ user_id: record.user_id, from_tag, job: record.current_job, pms_job_id, mock_id, from_job_id, from_user_id, onRouter })
// }
const dataSource = ref([])

// 民族
const nationCondition = [
	{
		label: '汉族',
		value: 1,
	},
	{
		label: '少数民族',
		value: 2,
	},
]

const filterState = reactive({
	sort_type: undefined,
	cadre_sort_type: undefined,
	min_zu_sort_type: undefined,
})
const handlePaginationChange = async (field: any, order: any, date: number) => {
	props.formState.order_by = date
	const sortMap: any = {
		ascend: 'ASC',
		descend: 'DESC',
	}

	switch (field) {
		case 'cadre_index':
			filterState.sort_type = undefined
			filterState.min_zu_sort_type = undefined
			filterState.cadre_sort_type = sortMap[order]
			break
		case 'birthday':
			filterState.sort_type = sortMap[order]
			filterState.cadre_sort_type = undefined
			filterState.min_zu_sort_type = undefined
			break
		case 'minzu_index':
			filterState.min_zu_sort_type = sortMap[order]
			filterState.sort_type = undefined
			filterState.cadre_sort_type = undefined
			break
	}
	getUserListByOrgId()
}

// var mescroll1 = new MeScroll(scrollContainer1.value, {
// 	down: {
// 		use: false,
// 	},
// 	up: {
// 		htmlNodata: ' ',
// 		htmlLoading: ' ',
// 		onScroll: (_mescroll: any, y: number) => {
// 			scrollContainer1.value = y
// 		},
// 		callback: async () => {
// 			try {
// 				const params = {
// 					...props.formState,
// 					cadre_index_rank: (props.formState.cadre_index_rank && props.formState.cadre_index_rank[0]) || '',
// 					cadre_index_rank_percent: (props.formState.cadre_index_rank && props.formState.cadre_index_rank[1]) || '',
// 					eval_index_rank: (props.formState.eval_index_rank && props.formState.eval_index_rank[0]) || '',
// 					eval_index_rank_percent: (props.formState.eval_index_rank && props.formState.eval_index_rank[1]) || '',
// 				}
// 				const response: any = await getUserListByOrgId(params)

// 				if (response.code === 0) {
// 					const newData = response.data

// 					mescroll1.endByPage(newData.length, response.data)
// 				} else {
// 					console.error('获取数据失败')
// 				}
// 			} catch (error) {
// 				console.error('发生错误', error)
// 			}
// 		},
// 	},
// })
</script>

<style lang="less" scoped>
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.result {
	position: absolute;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	overflow: auto;
	background: #fff;
	display: flex;
	.left {
		position: relative;
		background: #f7f7f7;
		width: 112px;
		height: 100%;
		flex-shrink: 0;
		.inner {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: #008eff;
			font-size: 24px;
			writing-mode: vertical-lr;
			cursor: pointer;
		}
		.icon {
			position: absolute;
			top: 50%;
			right: 0px;
			transform: translateY(-50%) rotateZ(180deg);
			display: inline-block;
			width: 30px;
			height: 105px;
			background: url('../../images/icon-11.png') center / cover no-repeat;
		}
	}
	.right {
		padding: 24px 20px;
		height: 100%;
		overflow: auto;
		.level-1 {
			display: flex;
			flex-wrap: wrap;
			gap: 18px 18px;
		}
		.level-2 {
			padding: 24px;
			display: flex;
			width: 100%;
			background: #f7f7f7;

			.left-input {
				width: 70%;
				display: flex;
				flex-wrap: wrap;
				gap: 21px 41px;
				.input-item {
					display: flex;
					align-items: center;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 24px;
					color: #000000;
					.res-input {
						margin-left: 12px;
						width: 168px;
						height: 66px;
						background: #ffffff;
						font-size: 24px;
						color: rgba(0, 0, 0, 0.5);
					}
				}
			}
			.right-button {
				flex: 1;
				display: flex;
				justify-content: flex-end;
				align-items: flex-end;
				gap: 0px 30px;
				button:nth-child(1) {
					background: rgba(0, 142, 255, 0.1);
				}
				button {
					border-radius: 6px;
					width: 90px;
					height: 48px;
					font-size: 24px;
				}
			}
		}
		.filter-menu {
			padding: 22px 27px;
			display: flex;
			.filter {
				display: flex;
				align-items: center;
				.label {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 24px;
					color: #000000;
				}
				.active {
					color: #008eff;
				}
				.filter-btn {
					.flex-center;

					.item {
						.flex-center;
						width: 128px;
						height: 48px;
						background: #f7f8fa;
						border-radius: 38px;
						font-size: 24px;
						.icon {
							width: 27px;
							height: 27px;
						}
						&:not(:last-child) {
							margin-right: 15px;
						}
					}

					.item-up {
						.icon {
							background: url('../../images/up.png') center / cover no-repeat;
						}
					}
					.active-up {
						.active;
						.icon {
							background: url('../../images/up-active.png') center / cover no-repeat;
						}
					}
					.item-down {
						.icon {
							background: url('../../images/down.png') center / cover no-repeat;
						}
					}
					.active-down {
						.active;
						.icon {
							background: url('../../images/down-active.png') center / cover no-repeat;
						}
					}
				}
			}
		}
	}
}
</style>
../../components/component.ts
