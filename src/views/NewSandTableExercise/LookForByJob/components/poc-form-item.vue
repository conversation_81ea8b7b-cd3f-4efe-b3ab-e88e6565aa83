<template>
	<div class="poc-form-item">
		<div class="title">{{ label }}:</div>
		<a-form-item v-bind="$attrs" v-if="formItem">
			<slot></slot>
		</a-form-item>
		<slot v-else></slot>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	label: String,
	formItem: {
		type: Boolean,
		default: true,
	}, // 是否需要内部a-form-item ，有时候存在需要自定义item的存在
})
</script>

<style lang="less" scoped>
.poc-form-item {
	width: 100%;
	margin: 27px 0px;
	.title {
		margin-bottom: 12px;
		display: flex;
		align-items: center;
		font-size: 21px;
		font-family: Source Han <PERSON>, Source Han Sans CN;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.9);
		line-height: 29px;
		&::before {
			margin-right: 9px;
			content: '';
			display: inline-block;
			width: 9px;
			height: 9px;
			background: #008eff;
		}
	}
}
:deep(.ant-row) {
	margin-bottom: 0px;
}
</style>
