<template>
	<div class="collection">
		<a-tabs v-model:value="size" v-model:activeKey="activeKey" style="margin-bottom: 16px">
			<a-tab-pane key="1" tab="全部收藏"></a-tab-pane>
			<a-tab-pane :tab="tab.name" v-for="tab in tmpData" :key="tab.id"></a-tab-pane>
		</a-tabs>

		<div class="filter-menu">
			<div class="filter">
				<div class="label">年龄：</div>
				<div class="filter-btn">
					<div :class="`item item-up ${filterState.sort_type === 'ASC' ? 'active-up' : ''}`" @click="handlePaginationChange('age', 'ascend')">
						<div :class="`icon`"></div>
						升序
					</div>
					<div :class="`item item-down ${filterState.sort_type === 'DESC' ? 'active-down' : ''}`" @click="handlePaginationChange('age', 'descend')">
						<div :class="`icon`"></div>
						降序
					</div>
				</div>
			</div>
			<div class="filter m-left-60">
				<div class="label">干部指数：</div>
				<div class="filter-btn">
					<div
						:class="`item item-up ${filterState.cadre_sort_type === 'ASC' ? 'active-up' : ''}`"
						@click="handlePaginationChange('cadre_index', 'ascend')"
					>
						<div :class="`icon`"></div>
						升序
					</div>
					<div
						:class="`item item-down  ${filterState.cadre_sort_type === 'DESC' ? 'active-down' : ''}`"
						@click="handlePaginationChange('cadre_index', 'descend')"
					>
						<div :class="`icon`"></div>
						降序
					</div>
				</div>
			</div>
			<div class="filter m-left-60">
				<div class="label">民主测评：</div>
				<div class="filter-btn">
					<div :class="`item item-up ${filterState.min_zu_sort_type === 'ASC' ? 'active-up' : ''}`" @click="handlePaginationChange('eval', 'ascend')">
						<div :class="`icon`"></div>
						升序
					</div>
					<div
						:class="`item item-down  ${filterState.min_zu_sort_type === 'DESC' ? 'active-down' : ''}`"
						@click="handlePaginationChange('eval', 'descend')"
					>
						<div :class="`icon`"></div>
						降序
					</div>
				</div>
			</div>
			<!-- <div class="filter">
						<div class="label"></div>
						<div class="filter-btn">
							<div :class="`item ${pageType === 1 ? 'active' : ''}`" @click="pageType = 1">
								<div v-html="svgIconPing(pageType === 1 ? '#008EFF' : undefined)" :class="`icon`"></div>
								平铺
							</div>
							<div :class="`item ${pageType === 2 ? 'active' : ''}`" @click="pageType = 2">
								<div v-html="svgIconTable(pageType === 2 ? '#008EFF' : undefined)" :class="`icon`"></div>
								列表
							</div>
						</div>
					</div> -->
		</div>
		<div class="table-box">
			<MultiStatusTable class="m-top-24" :pagination="false" @rowClick="onRowClick" :data-source="oriData.users" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, PropType } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MultiStatusTable from '../components/MultiStatusTable.vue'
import { openCandidate } from '../components/component'
import { favorite, getFavoriteList } from '@/apis/new-sand-table-exercise'
import { getUserInfoItem } from '@/utils/utils'

const props = defineProps({
	onRouter: {
		default: () => void 0,
		type: Function,
	},
	onRowClick: {
		type: Function as PropType<() => void>,
		required: true,
	},
})

const size = ref<string>('large')

const route = useRoute()
const router = useRouter()

const user_id = getUserInfoItem('_uid')

const filterState = reactive({
	sort_type: undefined,
	cadre_sort_type: undefined,
	min_zu_sort_type: undefined,
	field: undefined,
	order: undefined,
})

const oriData = ref<{
	favorites: any[]
	users: any[]
}>({
	favorites: [],
	users: [],
})
const sortMap: any = {
	ascend: 'ASC',
	descend: 'DESC',
}
const handlePaginationChange = async (field: any, order: any) => {
	switch (field) {
		case 'cadre_index':
			filterState.sort_type = undefined
			filterState.min_zu_sort_type = undefined
			filterState.cadre_sort_type = sortMap[order]
			break
		case 'age':
			filterState.sort_type = sortMap[order]
			filterState.cadre_sort_type = undefined
			filterState.min_zu_sort_type = undefined
			break
		case 'eval':
			filterState.min_zu_sort_type = sortMap[order]
			filterState.sort_type = undefined
			filterState.cadre_sort_type = undefined
			break
	}
	filterState.order = order
	filterState.field = field

	sortData()
}

const sortData = () => {
	const { order, field } = filterState as any
	oriData.value.users.sort((a: any, b: any) => {
		if (sortMap[order] === sortMap.ascend) {
			return a[field] - b[field]
		} else {
			return b[field] - a[field]
		}
	})
}

const tmpData = ref<any>([])

const activeKey = ref('1')

// 根据传入的id获取对应的user_ids, 再使用favorite接口 根据user_ids获取到对应的用户数据列表
const loadUserList = async (id: any) => {
	const item = tmpData.value.find((item: any) => item.id === id)

	if (item) {
		const res = await favorite({ user_ids: item.user_ids })
		if (res.code === 0) {
			oriData.value.users = res.data
		}
	}
}
/**
 * @return {*}
 */
watch(
	activeKey,
	() => {
		loadUserList(activeKey.value)
	},
	{
		immediate: true,
	}
)
const loadCollectUser = async (adminId: number) => {
	const res = await getFavoriteList(adminId)

	const data = res.data

	if (data?.length) {
		tmpData.value = data.map((item: any, index: any) => {
			return {
				name: item.name,
				id: index,
				user_ids: item.user_ids,
			}
		})

		activeKey.value = tmpData.value[0].id
	}
}
loadCollectUser(user_id)
</script>

<style lang="less" scoped>
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.collection {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	padding: 30px 30px;
	:deep(.ant-tabs-tab-btn) {
		padding: 0px 7px;
		font-weight: bold;
		font-size: 27px;
	}

	.filter-menu {
		padding: 0px 0px;
		display: flex;
		.filter {
			display: flex;
			align-items: center;
			.label {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 24px;
				color: #000000;
			}
			.active {
				color: #008eff;
			}
			.filter-btn {
				.flex-center;

				.item {
					.flex-center;
					width: 128px;
					height: 48px;
					background: #f7f8fa;
					border-radius: 38px;
					font-size: 24px;
					.icon {
						width: 27px;
						height: 27px;
					}
					&:not(:last-child) {
						margin-right: 15px;
					}
				}

				.item-up {
					.icon {
						background: url('../images/up.png') center / cover no-repeat;
					}
				}
				.active-up {
					.active;
					.icon {
						background: url('../images/up-active.png') center / cover no-repeat;
					}
				}
				.item-down {
					.icon {
						background: url('../images/down.png') center / cover no-repeat;
					}
				}
				.active-down {
					.active;
					.icon {
						background: url('../images/down-active.png') center / cover no-repeat;
					}
				}
			}
		}
	}
	.table-box {
		margin-top: 20px;
		flex: 1;
		overflow: auto;
	}
}
</style>
../components/component
