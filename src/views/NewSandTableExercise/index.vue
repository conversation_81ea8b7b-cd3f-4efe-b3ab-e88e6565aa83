<template>
	<div class="sand-table">
		<p-header title="沙盘推演" class="p-header" />
		<div class="content">
			<router-view v-slot="{ Component }">
				<keep-alive :include="routerCacheList" :exclude="['newSandTableExercisePlanList']">
					<component :is="Component" :key="refreshPage" />
				</keep-alive>
			</router-view>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'NewSandTableExercise',
}
</script>
<script lang="ts" setup>
import { ref, provide, onUnmounted, nextTick, onActivated } from 'vue'
import { useComparative } from '@/store/comparative'
import useKeepalive from '@/store/keepalive'
import useMock from '@/store/newSandbox'

const mockId = ref<number>()

const mock = useMock()

const comparative = useComparative()

comparative.initConfigIds()

const keepalive = useKeepalive()

keepalive.push('NewSandTableExercise')

const refreshPage = ref<number>()
// 控制是否需要清除pinia数据
const isClearPinia = ref<boolean>(true)

const updateMockId = (id: number) => {
	mockId.value = id
}

const onRefreshPage = () => {
	refreshPage.value = Math.random()
}

const notClearPinia = () => {
	isClearPinia.value = false
}

const clearMock = () => {
	mock.setMockId(undefined)
	mock.setMockName(undefined)
}

const routerCacheList = ref<any>([])
// 注册子组建router
const routerPushCache = (name: string) => {
	routerCacheList.value.push(name)
}

const routerRemoveCache = (name?: string) => {
	if (name) {
		routerCacheList.value = routerCacheList.value.filter((item: string) => item !== name)
	} else {
		routerCacheList.value.pop()
	}
}
const routerClearCache = () => {
	routerCacheList.value = []
}
provide('mockId', mockId)
provide('clearMock', clearMock)
provide('updateMockId', updateMockId)
provide('refreshPage', onRefreshPage)
provide('notClearPinia', notClearPinia)

provide('pageApi', {
	routerPushCache,
	routerRemoveCache,
	routerClearCache,
	onRefreshPage,
})

mock.initCode()
/**
 * @description: 页面卸载时清除pinia数据
 * @return {*}
 */
onUnmounted(() => {
	if (isClearPinia.value) {
		clearMock()
	}

	isClearPinia.value = true
})

onActivated(() => {
	const allModal = document.querySelectorAll('.ant-modal-root')

	allModal.forEach((item: any) => {
		item.style.display = 'block'
	})
})
</script>

<style lang="less" scoped>
.sand-table {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	background-color: #f5f5f5;

	.content {
		flex: 1;
		width: 100%;
		overflow: hidden;
	}
	.p-header {
		:deep(.title) {
			font-size: 36px !important;
		}

		:deep(.text) {
			font-size: 27px !important;
		}
		:deep(.common) {
			.icon {
				width: 31px;
				height: 21px;
			}
		}
		:deep(.login-out) {
			.icon {
				width: 23px;
				height: 25px;
			}
		}
	}
}
</style>
