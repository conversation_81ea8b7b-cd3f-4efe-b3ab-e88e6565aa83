<template>
	<div class="early-warning">
		<header-back title="预警名单" />
		<div class="table-box">
			<a-table :columns="columns" :data-source="dataSource" :pagination="false" :loading="loading">
				<template #bodyCell="{ column, record }">
					<template v-if="column.key === 'user_name'">
						<div class="user-avatar" @click="onRowClick(record)">
							<CodeAvatar :head_url="record.head_url">
								<template #avatar="{ avatar }">
									<img :src="avatar" class="avatar" />
								</template>
							</CodeAvatar>
							<div class="username">{{ record.user_name }}</div>
						</div>
					</template>
				</template>
			</a-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { shallowRef, ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import defaultAvatar from '@/assets/images/avatar.png'
import { getJobWarning } from '@/apis/new-sand-table-exercise'
import { CDN_URL } from '@/config/env'
import { openPersonFindJobModal } from '../components/component'
import { useRoute, useRouter } from 'vue-router'
import HeaderBack from '../components/HeaderBack.vue'
import CodeAvatar from '@/components/CodeAvatar.vue'
const route = useRoute()
const router = useRouter()

const loading = ref(false)

const { mock_id } = route.query
type UserInfo = {
	userId: number
	userName: string
	currentJob?: string
	currentJobTime?: string
	currentRankTime?: string
	warnInfo?: string
}
const dataSource = ref([])
const columns = [
	{
		key: 'user_name',
		dataIndex: 'user_name',
		align: 'center',
		width: '7%',
		// title: '预警干部',
		title: '',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},

	// {
	// 	key: 'portrait',
	// 	align: 'center',
	// 	width: '9%',
	// 	title: '干部画像',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'current_job',
		dataIndex: 'current_job',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'birthday',
	// 	dataIndex: 'birthday',
	// 	align: 'center',
	// 	width: '10%',
	// 	title: '出生年月（年龄）',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'current_job_time',
		dataIndex: 'current_job_time',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'current_rank_time',
		dataIndex: 'current_rank_time',
		align: 'center',
		width: '10%',
		title: '现职级任职时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'alert_reason',
		dataIndex: 'alert_reason',
		align: 'center',
		width: '10%',
		title: '预警原因',
		// colClass: blur.value ? 'filter-style' : '',
	},
]

const onRowClick = (record: any) => {
	onOpenCandidate(record)
}

const getDataList = async () => {
	loading.value = true

	const res: any = await getJobWarning({ mockId: route.query })
	if (res.code == 0) {
		dataSource.value = res.data
	}

	loading.value = false
}
getDataList()

const onOpenCandidate = (record: any) => {
	const { user_id, pms_job_id, from_tag, current_job } = record

	openPersonFindJobModal({
		user_id: user_id,
		from_job_id: pms_job_id,
		from_tag: from_tag,
		job_name: current_job,
		mock_id,
		onSuccess() {
			getDataList()
		},
	})
}
</script>

<style lang="less" scoped>
.early-warning {
	height: 100%;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	.table-box {
		flex: 1;
		overflow: auto;
		padding: 24px 24px;
	}
}
.user-avatar {
	img {
		width: 120px;
		height: 150px;
	}
	.username {
		text-align: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 27px;
		color: #008eff;
	}
}

:deep(.ant-table-cell) {
	font-size: 24px;
}
</style>
../components/component
