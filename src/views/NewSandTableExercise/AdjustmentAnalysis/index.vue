<template>
	<div class="adjustment-analysis">
		<HeaderBack title="调整对比分析" />
		<div class="sub-title">本次干部调整共涉及班子{{ userInfo.org_list?.length || 0 }}个</div>
		<div class="content">
			<div class="left-box">
				<div
					class="org-item"
					:class="{ active: item.org_id === selectOrg.org_id }"
					v-for="(item, index) in userInfo.org_list"
					:key="index"
					@click="onSelectOrg(item)"
				>
					<img src="../images/tree-icon.png" class="org-icon" />
					<span class="org-name">{{ item.org_name }}</span>
				</div>
			</div>
			<div class="right-box">
				<div class="org-container" v-if="selectOrg.org_id">
					<div class="bottom-box">
						<div class="flex">
							<div class="group">
								班子结构:
								<svg
									t="1697450179918"
									class="w-39 m-left-18 icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="4025"
								>
									<path
										d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
										:fill="cardColor[userInfo.users.org_screen || 0]"
										p-id="4026"
									></path>
								</svg>
							</div>
							<div class="group">班子运行指数: {{ userInfo.users.operating_index }} | 序列排名：{{ userInfo.users.operating_index_rank }}</div>
							<div class="group">班子配备指数: {{ userInfo.users.org_cadre_index }} | 序列排名：{{ userInfo.users.org_cadre_index_rank }}</div>
						</div>
						<div class="btn-box">
							<a-button type="primary" @click="onUserSelect">调整前后对比</a-button>
						</div>
					</div>
					<div class="lf-user-box" ref="scrollContainer">
						<div
							class="user-item"
							:class="{
								'active-select': item.mark,
							}"
							v-for="(item, index) in userInfo.users.user_list"
							:key="item.user_id"
						>
							<span
								:class="`card-icon card-icon-${item.alert == 1 ? 'warn' : !item.user_id ? 'vancy' : ''}`"
								v-if="!item.user_id || item.alert == 1"
							></span>
							<CodeAvatar :head_url="item.head_url" class="avatar" />

							<div class="user-name">{{ item.user_name || '---' }}</div>

							<div class="user-position">{{ item.position }}</div>
						</div>
						<!-- <div :class="`user-item ${pageStatus.newAdd ? 'active-select' : ''}`" @click="onAdd" v-if="userInfoList.vacancy">
							<div class="img-box">
								<img src="../images/vacancy.png" class="vacancy" />
							</div>
							<div class="user-name">---</div>
							<div class="user-position">---</div>
						</div> -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'newSandTableExerciseAdjustAnalysis',
}
</script>
<script lang="ts" setup>
import { ref, reactive, inject, onActivated } from 'vue'
import HeaderBack from '../components/HeaderBack.vue'
import { CDN_URL } from '@/config/env'
import { useRouter, useRoute } from 'vue-router'
import defaultAvatar from '@/assets/images/avatar.png'
import { adjustCompareOrg, getTeamUserList } from '@/apis/new-sand-table-exercise'
import { debounce } from '@/utils/utils'
import CodeAvatar from '@/components/CodeAvatar.vue'

const pageApi: any = inject('pageApi')

const userInfo = reactive<any>({
	org_list: [],
	users: {
		org_cadre_index_rank: 0,
		org_cadre_index: 0,
		operating_index_rank: 0,
		operating_index: 0,
		org_lack_num: 0,
		org_screen: 0,
	},
})

const cardColor = ['#60ca71', '#f6dd00', '#ffa300', '#ff473e']

const route = useRoute()

const router = useRouter()

const { mock_id } = route.query
// 选中后的
const selectOrg = ref<any>({})

const onUserSelect = () => {
	pageApi.routerPushCache('newSandTableExerciseAdjustAnalysis')

	router.push({
		path: '/new-sand-table-exercise/structural-analysis',
		query: {
			mock_id,
			org_id: selectOrg.value.org_id,
		},
	})
}

const onSelectOrg = (org: any) => {
	selectOrg.value = org

	loadOrgUser()
}

const loadOrgUser = debounce(async () => {
	const res = await getTeamUserList({
		mock_id,
		flag: 1,
		org_id: selectOrg.value.org_id,
	})
	if (res.code === 0) {
		userInfo.users = res.data
	}
}, 400)

const loadCompareOrg = async () => {
	const res = await adjustCompareOrg({
		mock_id: mock_id as string,
	})
	if (res.code === 0) {
		userInfo.org_list = res.data

		selectOrg.value = userInfo.org_list[0]

		loadOrgUser()
	}
}

loadCompareOrg()

onActivated(() => {
	pageApi?.routerRemoveCache('newSandTableExerciseAdjustAnalysis')
})
</script>

<style lang="less" scoped>
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.white-bg {
	background: #ffffff;
}
.flex {
	display: flex;
}
.adjustment-analysis {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	.sub-title {
		padding: 20px 16px;
		font-size: 20px;
		color: #333;
	}
	.content {
		display: flex;
		justify-content: space-between;
		flex: 1;
		display: flex;
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
		width: 100%;
		overflow: hidden;
		.left-box {
			display: flex;
			flex-direction: column;
			width: 375px;
			height: 100%;
			overflow: auto;
			.org-item {
				display: flex;
				align-items: center;
				width: 100%;
				min-height: 83px;
				background: #ffffff;
				padding: 21px 0px 21px 22px;
				.org-icon {
					height: 32px;
					width: 32px;
					vertical-align: middle;
				}
				.org-name {
					margin-left: 15px;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.85);
					font-size: 26px;
					line-height: 30px;
				}
			}
			.active {
				background: #008eff;
				.org-name {
					color: #ffffff;
				}
			}
		}
		.right-box {
			flex: 1;
			margin-left: 10px;
			.org-container {
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 100%;
				padding: 0px 0px;
				background: #ffffff;
				overflow: hidden;
				.bottom-box {
					padding: 18px 24px;
					display: flex;
					justify-content: space-between;
					align-items: center;
					background: #fcf6ec;
					border-radius: 3px 3px 3px 3px;
					opacity: 1;
					.group {
						display: flex;
						align-items: center;
						font-size: 24px;
						line-height: 24px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #000000;
						&:not(:last-child) {
							margin-right: 39px;
						}
						&::before {
							content: '';
							margin-right: 9px;
							display: inline-block;
							width: 12px;
							height: 12px;
							background: #ff9900;
							border-radius: 50%;
							opacity: 1;
						}
						.red-text {
							margin-left: 16px;
							color: #ff0000;
						}
						.yellow-text {
							color: #ff9900;
						}
						.btn-box {
						}
					}
					.link {
						float: right;
						font-size: 18px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
						cursor: pointer;
					}
				}
				button {
					width: 180px;
					height: 50px;
					font-size: 23px;
					line-height: 23px;
				}
				.lf-user-box {
					.white-bg;
					display: flex;
					flex-wrap: wrap;
					align-content: flex-start;
					padding: 14px 20px;
					flex: 1;
					overflow-y: auto;
					gap: 27px 27px;
					.user-item {
						position: relative;
						.white-bg;
						padding: 12px 0px;
						display: flex;
						align-items: center;
						flex-direction: column;
						width: 183px;
						height: 299px;
						// overflow: hidden;
						border-radius: 3px 3px 3px 3px;
						opacity: 1;
						.avatar {
							width: 127px;
							height: 159px;
							object-fit: fill;
						}
						.img-box {
							.flex-center;
							width: 100%;
							height: 140px;
							background-color: #eeeeee;
							.vacancy {
								width: 41px;
								height: 41px;
								object-fit: contain;
							}
						}
						.user-name {
							margin-top: 10px;
							text-align: center;
							font-size: 24px;
							line-height: 28px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 500;
							color: #000000;
						}
						.user-position {
							margin-top: 10px;
							text-align: center;
							font-size: 21px;
							line-height: 24px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: rgba(0, 0, 0, 0.9);
							// 超过两行省略
							display: -webkit-box;
							text-overflow: -o-ellipsis-lastline;
							overflow: hidden;
							text-overflow: ellipsis;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 3;
						}
						.card-icon {
							position: absolute;
							top: -4px;
							left: -10px;
							width: 63px;
							height: 39px;
						}
						.card-icon-warn {
							background: url('../images/org-team-warn.png') no-repeat center / cover;
						}
						.card-icon-vancy {
							background: url('../images/org-team-vancy.png') no-repeat center / cover;
						}
					}
					.active-select {
						background: #00a3ff;
						.user-position {
							color: #ffffff;
						}
						.user-name {
							color: #ffffff;
						}
					}
				}
			}
		}
	}
}
</style>
