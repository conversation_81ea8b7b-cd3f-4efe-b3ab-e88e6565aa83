<template>
	<div class="info-line">
		<span :class="`icon-${standard}`"></span>
		<span class="info">{{ info }}</span>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	standard: String,
	info: String,
})
</script>

<style lang="less" scoped>
.info-line {
	display: flex;
	.standard {
		display: inline-block;
		width: 24px;
		height: 24px;
	}
	.icon-0 {
		.standard;
		background: url('../../images/success-icon.png') center / cover no-repeat;
	}
	.icon-1 {
		.standard;
		background: url('../../images/warn-icon.png') center / cover no-repeat;
	}
	.info {
		margin-left: 6px;
		font-weight: 400;
		font-size: 23px;
		color: #3d3d3d;
		line-height: 26px;
		font-style: normal;
		text-align: left;
	}
}
</style>
