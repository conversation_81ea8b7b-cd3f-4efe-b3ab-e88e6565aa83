<template>
	<div class="structural-analysis">
		<HeaderBack :title="`${org_name}班子队伍分析`" />
		<div class="main">
			<div class="user-card-box">
				<div class="item-box" v-for="(item, index) in candidateData" :key="index">
					<div class="title">{{ item.job_name }}</div>
					<div class="flex m-top-17 user-item-box">
						<div
							:class="`user-item ${isSelect(item, item1) ? 'active' : ''}`"
							v-for="(item1, index) in item.details"
							:key="index"
							@click="onSelectCandidate(item, item1)"
						>
							<CodeAvatar :head_url="item1.head_url">
								<template #avatar="{ avatar }">
									<img :src="avatar" class="avatar" />
								</template>
							</CodeAvatar>
							<div class="user-name">{{ item1.user_name || '---' }}</div>
						</div>
					</div>
				</div>
			</div>
			<row-table class="data-row-table m-top-32" :columns="columns" :datasource="dataSource">
				<template #title="{ value }">
					<span>{{ value }}</span>
				</template>
				<template #jobVacancy="{ data }">
					<div class="job-vacancy">
						<span>应配：{{ data.job_num }}人</span>
						&nbsp;
						<span>缺配：{{ data.job_vacancy }}人</span>
					</div>
				</template>
				<template #age="{ value }">
					<div class="cell-class">
						<InfoLine v-for="(age, index) in value" :info="age.info" :standard="age.standard" :key="index" />
					</div>
				</template>
				<template #gender="{ value }">
					<div class="cell-class">
						<InfoLine v-for="(age, index) in value" :info="age.info" :standard="age.standard" :key="index" />
					</div>
				</template>
				<template #educational="{ value }">
					<div class="cell-class">
						<InfoLine v-for="(age, index) in value" :info="age.info" :standard="age.standard" :key="index" />
					</div>
				</template>
				<template #specialty="{ value }">
					<div class="cell-class">
						<InfoLine v-for="(age, index) in value" :info="age.info" :standard="age.standard" :key="index" />
					</div>
				</template>
				<template #experience="{ value }">
					<div class="cell-class">
						<InfoLine v-for="(age, index) in value" :info="age.info" :standard="age.standard" :key="index" />
					</div>
				</template>
				<template #cadre_index="{ value }">
					<div class="cell-class">
						<InfoLine v-for="(age, index) in value" :info="age.info" :standard="age.standard" :key="index" />
					</div>
				</template>
				<template #withdraw="{ value }">
					<div class="cell-class">
						<InfoLine v-for="(age, index) in value" :info="age.info" :standard="age.standard" :key="index" />
					</div>
				</template>
				<template #org_cadre_index="{ data }">
					<div class="cell-class">班子配备指数：{{ data.org_cadre_index }} | 序列排名：{{ data.org_cadre_index_rank }}</div>
				</template>
			</row-table>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'newSandTableExerciseStructuralAnalysis',
}
</script>
<script lang="ts" setup>
import { ref, unref, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import RowTable from '@/components/RowTable.vue'
import HeaderBack from '../components/HeaderBack.vue'
import InfoLine from './components/InfoLine.vue'
import defaultAvatar from '@/assets/images/avatar-width.png'
import { CDN_URL } from '@/config/env'
import { teamFindCandidate, teamMockAnalyze } from '@/apis/new-sand-table-exercise'

const pageApi = inject('pageApi')

const columnsTemplate = [
	{
		title: '',
		dataIndex: 'title',
		key: 'title',
		align: 'left',
		rowClass: 'table-title-gray',
	},
	{
		title: '职数配置情况',
		dataIndex: 'jobVacancy',
		key: 'jobVacancy',
		align: 'left',
	},
	{
		title: '年龄结构',
		dataIndex: 'age',
		key: 'age',
		align: 'left',
	},
	{
		title: '性别结构',
		dataIndex: 'gender',
		key: 'gender',
		align: 'left',
	},
	{
		title: '学历结构',
		dataIndex: 'educational',
		key: 'educational',
		align: 'left',
	},
	{
		title: '专业结构',
		dataIndex: 'specialty',
		key: 'specialty',
		align: 'left',
	},
	{
		title: '经历结构',
		dataIndex: 'experience',
		key: 'experience',
		align: 'left',
	},
	{
		title: '干部指数',
		dataIndex: 'cadre_index',
		key: 'cadre_index',
		align: 'left',
	},
	{
		title: '任职回避',
		dataIndex: 'withdraw',
		key: 'withdraw',
		align: 'left',
	},
	{
		title: '班子配备指数',
		dataIndex: 'org_cadre_index',
		key: 'org_cadre_index',
		align: 'left',
	},
]

const route = useRoute()
const router = useRouter()

const { mock_id, org_id, org_name = '' } = route.query

const columns = ref<any>([])

const dataSource: any = ref([])

const candidateData = ref<any>([])

const selectCandidate = ref<any>([])

const onSelectCandidate = (job: any, user: any) => {
	const _data = unref(selectCandidate)

	const { pms_job_id } = job
	const { pms_mock_job_detail_id } = user

	const index = _data.findIndex((ids: any) => {
		return ids.pms_job_id === pms_job_id
	})

	if (index !== -1) {
		const org_data = _data[index]

		if (org_data.pms_mock_job_detail_id === pms_mock_job_detail_id) {
			selectCandidate.value.splice(index, 1)
		} else {
			selectCandidate.value.splice(index, 1, { pms_job_id, pms_mock_job_detail_id })
		}
	} else {
		selectCandidate.value.push({ pms_mock_job_detail_id, pms_job_id })
	}

	loadData()
}

const isSelect = (job: any, user: any) => {
	const { pms_job_id } = job
	const { pms_mock_job_detail_id } = user

	const _data = unref(selectCandidate)

	const index = _data.findIndex((ids: any) => {
		return ids.pms_job_id === pms_job_id && ids.pms_mock_job_detail_id === pms_mock_job_detail_id
	})
	return index !== -1
}

const getCandidate = () => {
	return selectCandidate.value.map((item: any) => item.pms_mock_job_detail_id)
}
// 加载数据
const loadData = async () => {
	// dataSource.value = [classifyData(yourData), classifyData(yourData)]

	const res: any = await teamMockAnalyze({
		mock_id,
		org_id,
		pms_mock_job_detail_ids: getCandidate(),
	})

	if (res.code === 0) {
		const data: any = res.data as ResponseType
		const { before, after, town } = data
		// 乡镇
		if (town == 1) {
			columns.value = columnsTemplate.filter((col: any) => {
				return col.key !== 'specialty'
			})
		} else if (town == 2) {
			// 部门 不展示性别和专业
			columns.value = columnsTemplate.filter((col: any) => {
				return !(col.key == 'gender' || col.key == 'specialty')
			})
		}
		const list = [
			{
				title: '人员调配前',
				...before,
			},
		]
		if (after) {
			list.push({
				title: '模拟班子结构',
				...after,
			})
		}

		dataSource.value = list
	}
}

const loadCandidate = async () => {
	const res = await teamFindCandidate({
		mock_id,
		org_id,
	})
	if (res.code === 0) {
		candidateData.value = res.data

		loadData()
	}
}
loadCandidate()

function classifyData(data: any, title: any) {
	const result = {
		title: {
			type: title,
		},
		jobVacancy: {
			type: '职数配置情况',
			data: {
				jobVacancy: data.jobVacancy,
			},
		},
		ageStructure: {
			type: '年龄结构',
			data: {
				ageAvg: data.ageAvg,
				ageUnder35Percent: data.ageUnder35Percent,
				age35To39Percent: data.age35To39Percent,
				age40To44Percent: data.age40To44Percent,
				age45To55Percent: data.age45To55Percent,
				ageOver55Percent: data.ageOver55Percent,
				ageUnder35: data.ageUnder35,
				age35To39: data.age35To39,
				age40To44: data.age40To44,
				age45To55: data.age45To55,
				ageOver55: data.ageOver55,
			},
		},
		genderStructure: {
			type: '性别结构',
			data: {
				menPercent: data.menPercent,
				womenPercent: data.womenPercent,
				men: data.men,
				women: data.women,
			},
		},
		educationStructure: {
			type: '学历结构',
			data: {
				degreePercent: data.degreePercent,
				degree: data.degree,
			},
		},
		politicalOutlook: {
			type: '政治面貌',
			data: {
				partyMemberPercent: data.partyMemberPercent,
			},
		},
		ethnicStructure: {
			type: '民族结构',
			data: {
				minorityPercent: data.minorityPercent,
			},
		},
		// 可根据需要添加更多分类
	}
	return result
}
</script>

<style lang="less" scoped>
.flex {
	display: flex;
}
.flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}
.structural-analysis {
	width: 100%;
	height: 100%;
	overflow: auto;
	display: flex;
	flex-direction: column;
	.user-card-box {
		display: flex;
		gap: 0px 35px;
		width: 100%;
		overflow: auto;
		.title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 26px;
			color: #222222;
		}
	}
	.item-box {
		padding: 24px 16px;
		background: #f8f9fa;
	}
	.user-item-box {
		gap: 0px 29px;
	}
	.main {
		background-color: #fff;
		flex: 1;
		padding: 24px;
	}

	.user-item {
		position: relative;
		background-color: #fff;
		padding: 12px 8px;
		display: flex;
		align-items: center;
		flex-direction: column;
		width: 183px;
		height: 236px;
		// overflow: hidden;
		border-radius: 3px 3px 3px 3px;
		opacity: 1;
		.avatar {
			width: 127px;
			height: 159px;
			object-fit: fill;
		}
		.img-box {
			width: 100%;
			height: 140px;
			background-color: #eeeeee;
			.vacancy {
				width: 41px;
				height: 41px;
				object-fit: contain;
			}
		}
		.user-name {
			margin-top: 10px;
			text-align: center;
			font-size: 24px;
			line-height: 28px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
		}
		.user-position {
			margin-top: 10px;
			text-align: center;
			font-size: 21px;
			line-height: 25px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.9);
			// 超过两行省略
			display: -webkit-box;
			text-overflow: -o-ellipsis-lastline;
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;
		}
		.card-icon {
			position: absolute;
			top: 0px;
			left: 20px;
			width: 63px;
			height: 39px;
		}
		.card-icon-warn {
			background: url('../images/org-team-warn.png') no-repeat center / cover;
		}
		.card-icon-vancy {
			background: url('../images/org-team-vancy.png') no-repeat center / cover;
		}
	}

	.data-row-table {
		.cell-class {
			display: flex;
			flex-direction: column;
			gap: 12px 0px;
		}
		.job-vacancy {
			font-size: 23px;
		}
		:deep(.data-item) {
			font-size: 23px !important;
			color: rgba(0, 0, 0, 0.9);
		}
		:deep(.table-title) {
			padding: 29px 0px !important;
			font-size: 23px !important;
			width: 208px !important;
			justify-content: center !important;
		}
	}

	.active {
		background: #00a3ff;
		.user-name {
			color: #fff;
		}
	}
}
</style>
