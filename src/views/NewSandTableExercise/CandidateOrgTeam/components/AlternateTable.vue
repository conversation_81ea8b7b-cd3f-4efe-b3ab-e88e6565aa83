<template>
	<div class="alternate-table">
		<div class="header-box">
			<div class="left-title">{{ data.type == 1 ? '候选人员' : '拟任职务' }}</div>
			<div class="right-button">
				<a-button type="link" @click="onCandidate">候选人对比 ></a-button>
			</div>
		</div>
		<div class="user-table">
			<div class="user">
				<div :class="`user-item`">
					<img :src="`${data.head_url ? `${CDN_URL}/fr_img/${data.head_url}` : defaultAvatar}`" class="avatar" />
					<div class="user-name">{{ data.user_name }}</div>
					<div class="user-position">{{ data.job_name }}</div>
				</div>
			</div>
			<div class="table-box">
				<a-table :columns="columns" :data-source="data.job_detail_vos" :scroll="{ x: '100%' }" :pagination="false">
					<template #bodyCell="{ column, record, index }">
						<template v-if="column.key === 'rank'">{{ index + 1 }}</template>
						<template v-if="column.key === 'name'">
							<a @click="onUserDetails(record)"> {{ record.name }}</a>
						</template>
						<template v-if="column.key === 'adjust_type'">
							<a @click="onAdjustType(record)"> {{ record.adjust_type ? returnType(record.adjust_type) : '请选择' }}</a>
						</template>
						<template v-if="column.key === 'operate'">
							<a @click="onCancel(record)">撤销调整</a>
						</template>
					</template>
				</a-table>
				<a-button type="primary" @click="onHouXuan" v-if="data.type == 1 && data.job_detail_vos?.length < 3">+候选人</a-button>
				<a-button type="primary" @click="onOpenCandidate" v-if="data.type == 2 && data.job_detail_vos?.length < 3">+ 拟任职务</a-button>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, inject, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import defaultAvatar from '@/assets/images/avatar-width.png'
import { openCandidate, positionTypeDrawer, openPersonFindJobModal } from '../../components/component'
import { getRadio } from '@/apis/cadre-portrait/home'
import { historyPush } from '@/utils/history'
import { CDN_URL } from '@/config/env'
import { getCode, cancelMock, changeAdjustType } from '@/apis/new-sand-table-exercise'
import { message } from 'ant-design-vue'
import router from '@/router'
import useMock from '@/store/newSandbox'
import { convertPixelsToVw as vw } from '@/utils/utils'
const props = defineProps({
	data: {
		type: Object,
		default: () => ({}),
	},
	dataSource: {
		type: Array,
		default: () => [],
	},
})

const { mock_id, org_id } = useRoute().query

const loadData: any = inject('loadData')

const typeOption = ref<any>([])

const mock = useMock()

const columns = computed(() => {
	const columns = [
		{
			key: 'rank',
			dataIndex: 'rank',
			align: 'center',
			title: '序号',
			fixed: 'left',
			width: vw(100),
		},
		{
			key: 'draft_sit_job',
			dataIndex: 'draft_sit_job',
			align: 'center',
			title: '拟任职务',
			fixed: 'left',
			width: vw(330),
		},
		{
			key: 'user_name',
			dataIndex: 'user_name',
			align: 'center',
			title: '姓名',
			// colClass: blur.value ? 'filter-style' : '',
			colClass: 'cursor-pointer',
			colClick: (_data: any, event: any) => {
				event.stopPropagation()
			},
			fixed: 'left',
			width: vw(150),
		},
		{
			key: 'current_job',
			dataIndex: 'current_job',
			align: 'left',
			title: '现任职务',
			width: vw(208),
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'gender',
			dataIndex: 'gender',
			align: 'left',
			title: '性别',
			width: vw(107),
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'birthday',
			dataIndex: 'birthday',
			align: 'center',
			title: '出生年月（年龄）',
			width: vw(155),
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'current_job_time',
			dataIndex: 'current_job_time',
			align: 'center',
			title: '任现职务时间',
			width: vw(195),
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'full_time_diploma',
			dataIndex: 'full_time_diploma',
			align: 'center',
			title: '全日制学历',
			width: vw(150),
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'school_department',
			dataIndex: 'school_department',
			align: 'left',
			title: '全日制学历毕业学校及专业',
			width: vw(208),
			// colClass: blur.value ? 'filter-style' : '',
		},
		// {
		// 	key: 'specialty',
		// 	align: 'center',
		// 	width: '12%',
		// 	title: '专业',
		// 	// colClass: blur.value ? 'filter-style' : '',
		// },
		{
			key: 'cadre_index_rank2023',
			dataIndex: 'cadre_index_rank2023',
			align: 'center',
			title: '2023年干部指数同序列排名',
			width: vw(201),
		},
		{
			key: 'cadre_index_rank2024',
			dataIndex: 'cadre_index_rank2024',
			align: 'center',
			title: '2024年干部指数同序列排名',
			width: vw(204),
		},
		{
			key: 'org_cadre_rank',
			dataIndex: 'org_cadre_rank',
			align: 'center',
			title: '班子内指数排名',
			width: vw(147),
		},
		{
			key: 'eval_org_rank',
			dataIndex: 'eval_org_rank',
			align: 'center',
			title: '班子内测评排名',
			width: vw(149),
		},
		{
			key: 'adjust_type',
			dataIndex: 'adjust_type',
			align: 'center',
			title: '调整类别',
			fixed: 'right',
			width: vw(182),
		},
	]

	if (props.data.type == 2) {
		columns.push({
			key: 'operate',
			dataIndex: 'operate',
			align: 'center',
			title: '操作',
			fixed: 'right',
			width: vw(155),
		})
	}

	return columns
})

const onUserDetails = (data: any) => {
	const { user_id, current_job } = data
	openCandidate({
		type: '候选',
		user_id,
		job: current_job,
	})
}

const onOpenCandidate = () => {
	const { user_id, pms_job_id, from_tag, job_name } = props.data

	openPersonFindJobModal({
		user_id: user_id,
		from_job_id: pms_job_id,
		from_tag: from_tag,
		job: job_name,
		mock_id,
	})
}

const onHouXuan = () => {
	const { from_tag, pms_job_id, user_id } = props.data

	router.push({
		path: '/new-sand-table-exercise/look-for-by-job',
		query: {
			org_id,
			from_user_id: user_id,
			from_tag: from_tag,
			pms_job_id: pms_job_id,
			from_job_id: pms_job_id,
			mock_id,
			from: 'candidate',
		},
	})
}

const onCancel = async ({ pms_mock_job_detail_id }: any) => {
	const res = await cancelMock({ pms_mock_job_detail_id: pms_mock_job_detail_id })

	if (res.code === 0) {
		message.success('撤销成功!!!')

		loadData()
	} else {
		message.error(res.message)
	}
}

const onCandidate = async () => {
	if (props.data?.job_detail_vos?.length < 2) {
		return
	}
	const ids: any = await getRadio()

	const user_id = props.data?.job_detail_vos?.map((item: any) => item.user_id) || []

	const path = `/comparison-results?user_id=${encodeURIComponent(user_id.join(','))}&config_ids=${encodeURIComponent(ids.join(','))}`

	historyPush(path)
}

const loadCode = async () => {
	const res = await getCode({
		code: '10012',
	})
	if (res.code === 0) {
		typeOption.value = res.data
	}
}
loadCode()

const returnType = (adjust_key: any) => {
	return typeOption.value.find((item: any) => {
		return item.op_key === adjust_key
	})?.op_value
}

const onAdjustType = (record: any) => {
	positionTypeDrawer.openDrawer({
		type: record.adjust_type,
		onConfirm: async (key: any) => {
			const res = await changeAdjustType({
				adjust_type: key,
				pms_mock_job_detail_id: record.pms_mock_job_detail_id,
			})

			if (res.code === 0) {
				message.success('修改成功！')
				loadData()
			} else {
				message.error(res.message)
			}
		},
	})
}
</script>

<style lang="less" scoped>
.white-bg {
	background-color: #fff;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.alternate-table {
	width: 100%;
	overflow: hidden;
	.header-box {
		padding: 18px 24px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-weight: 500;
		font-size: 24px;
		color: #000000;
		background: #edf7ff;
		button {
			font-size: 24px;
		}
	}
	.user-table {
		padding: 24px 24px 24px;
		margin-top: 8px;
		background: #edf7ff;
		display: flex;
		width: 100%;
		overflow: hidden;
		.user-item {
			position: relative;
			padding: 12px 0px;
			display: flex;
			align-items: center;
			flex-direction: column;
			width: 183px;
			height: 299px;
			// overflow: hidden;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			.avatar {
				width: 127px;
				height: 159px;
				object-fit: fill;
			}
			.img-box {
				.flex-center;
				width: 100%;
				height: 140px;
				.vacancy {
					width: 41px;
					height: 41px;
					object-fit: contain;
				}
			}
			.user-name {
				margin-top: 10px;
				text-align: center;
				font-size: 24px;
				line-height: 28px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
			}
			.user-position {
				margin-top: 10px;
				text-align: center;
				font-size: 21px;
				line-height: 25px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.9);
				// 超过两行省略
				display: -webkit-box;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 3;
			}
			.card-icon {
				position: absolute;
				top: 0px;
				left: 20px;
				width: 63px;
				height: 39px;
			}
			.card-icon-warn {
				background: url('../images/org-team-warn.png') no-repeat center / cover;
			}
			.card-icon-vancy {
				background: url('../images/org-team-vancy.png') no-repeat center / cover;
			}
		}
		.table-box {
			flex: 1;
			overflow: hidden;
			:deep(.ant-table-cell) {
				font-weight: bold;
				font-size: 20px;
				color: #3d3d3d;
			}
			button {
				margin-top: 11px;
				width: 180px;
				height: 60px;
				font-weight: 500;
				font-size: 26px;
				color: #ffffff;
			}
		}
	}
}
</style>
../../components/component