<template>
	<div class="org-team">
		<div class="user-box">
			<header-back title="班子队伍" />
			<div class="controll">
				<div class="bottom-box">
					<div class="group">
						应配：{{ userInfoList.deserve }}人，缺配：<span class="yellow-text">{{ userInfoList.vacancy }}</span
						>人
					</div>
					<div class="group">班子结构预警项：<span class="yellow-text"> 年龄、学历、专业</span></div>

					<span class="link-button" @click="onStructure"> 班子结构对比 &gt; </span>
				</div>
			</div>
			<div class="lf-user-box">
				<div
					class="user-item"
					:class="{
						'active-select': item.mark,
					}"
					v-for="(item, index) in userInfoList.jobs"
					:key="item.user_id"
				>
					<span
						:class="`card-icon card-icon-${item.alert == 1 ? 'warn' : !item.user_id ? 'vancy' : ''}`"
						v-if="!item.user_id || item.alert == 1"
					></span>
					<img :src="`${item.head_url ? `${CDN_URL}/fr_img/${item.head_url}` : defaultAvatar}`" class="avatar" />
					<div class="user-name">{{ item.user_name || '---' }}</div>
					<div class="user-position">{{ item.job_name }}</div>
				</div>
				<!-- <div :class="`user-item ${pageStatus.newAdd ? 'active-select' : ''}`" @click="onAdd" v-if="userInfoList.vacancy">
							<div class="img-box">
								<img src="../images/vacancy.png" class="vacancy" />
							</div>
							<div class="user-name">---</div>
							<div class="user-position">---</div>
						</div> -->
				<div class="alternate-list">
					<AlternateTable :class="{ 'm-top-24': index !== 0 }" v-for="(item, index) in userInfoList.candidate_list" :key="index" :data="item" />
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, provide, unref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { openPersonFindJobModal, userInfoModal, openCandidate } from '../components/component'
import defaultAvatar from '@/assets/images/avatar-width.png'
import useMock from '@/store/newSandbox'
import HeaderBack from '../components/HeaderBack.vue'
import AlternateTable from './components/AlternateTable.vue'
import { CDN_URL } from '@/config/env'

import { getHomePage, getOrgTeam } from '@/apis/new-sand-table-exercise'

const route = useRoute()
const router = useRouter()
// type: 1为班子队伍， 2为候选
const { org_id, mock_id, type } = route.query as any

type UserData = {
	userId: number
	userName: string
	headUrl?: string
	currentJob?: string
	currentJobTime?: string
	birthday?: string
	age?: number
	initDegree?: string
	initSchool?: string
	specialty?: string
	cadreIndex?: string
	cadreIndexSort?: string
	userType?: number // 1 - 市管领导，2 - 区管领导，3 - 中层干部
	highestDegree?: string
}
const mock = useMock()
// 选中的用户
const selectUser = ref<any>({})
// 右侧选中用户
const waitSelectUser = ref<UserData>({} as UserData)
// 页面状态管理
const pageStatus = reactive({
	waitUserCheck: false, // 右侧用户池能否点击
	newAdd: false, // 新增用户
})

const userInfoList = ref<any>({
	orgId: undefined,
	orgName: `机构`,
	deserve: 0, //应配
	vacancy: 0, // 缺配
	users: [],
})

const visible = ref(false)

const onStructure = () => {
	router.push({
		path: '/new-sand-table-exercise/structural-analysis',
		query: {
			mock_id,
			org_id,
		},
	})
}
// 路由跳转
const onRouter = (params: any) => {
	params.query = Object.assign(params.query || {}, route.query)

	router.push(params)
}
/**
 * @description: 用户选择
 * @param {*} user
 * @return {*}
 */
const onUserSelect = (user: any, index: any, type: string) => {
	// pageStatus.waitUserCheck = true

	// selectUser.value = user

	// waitSelectUser.value = {} as UserData
	if (!user.user_id) {
		visible.value = true
	} else {
		openCandidate({
			job: user.job_name,
			user_id: user.user_id,
			pms_job_id: user.pms_job_id,
			from_tag: user.from_tag,
			type: '岗找人',
			mock_id,
			onRouter,
		})
	}
}

// const onSuccess = () => {
// 	router
// }
const onLookFor = () => {
	visible.value = false
	router.push({
		path: '/new-sand-table-exercise/look-for-by-job',
		query: {
			mock_id,
		},
	})
}

const loadData = async () => {
	const res = await getOrgTeam({
		org_id,
		mock_id,
		flag: 2,
	})
	if (res.code === 0) {
		userInfoList.value = res.data
	}
}
loadData()

provide('loadData', loadData)
</script>

<style lang="less" scoped>
.flex-direction-column {
	display: flex;
	flex-direction: column;
}
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.gray-bg {
	background: #f5f5f5;
}
.white-bg {
	background: #ffffff;
}
.org-team {
	width: 100%;
	height: 100%;
	.user-box {
		// flex: 1;
		// display: flex;
		// overflow: hidden;
		.flex-direction-column;
		margin-right: 20px;
		height: 100%;
		width: 100%;
		overflow: hidden;
		.controll {
			padding: 24px 24px 0px;
			.white-bg;
			.top-box {
				display: flex;
				justify-content: space-between;
				.group {
					display: flex;
					align-items: center;
					.sub-title {
						display: flex;
						align-items: center;
						font-size: 24px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: bold;
						color: #222222;
						line-height: 28px;
						&::before {
							margin-right: 12px;
							content: '';
							display: inline-block;
							width: 6px;
							height: 28px;
							background: url('../images/title-1.png') no-repeat center / cover;
						}
					}

					.reset {
						&::before {
							background-image: url('../images/reset.png');
						}
					}
					.refresh {
						&::before {
							background-image: url('../images/refresh.png');
						}
					}
				}
			}
			.bottom-box {
				padding: 18px 24px;
				display: flex;
				align-items: center;
				height: 72px;
				background: #fcf6ec;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				.group {
					display: flex;
					align-items: center;
					font-size: 24px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #000000;
					&:nth-child(2) {
						margin-left: 75px;
					}
					&::before {
						content: '';
						margin-right: 9px;
						display: inline-block;
						width: 12px;
						height: 12px;
						background: #e6a23c;
						border-radius: 50%;
						opacity: 1;
					}
					.yellow-text {
						color: #e6a23c;
					}
				}

				.link-button {
					flex: 1;
					text-align: right;
					font-size: 24px;
					line-height: 24px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #008eff;
					cursor: pointer;
				}
			}
		}
		.lf-user-box {
			.white-bg;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			padding: 14px 20px;
			flex: 1;
			overflow-y: auto;
			gap: 27px 27px;
			.user-item {
				position: relative;
				.white-bg;
				padding: 12px 0px;
				display: flex;
				align-items: center;
				flex-direction: column;
				width: 183px;
				height: 299px;
				// overflow: hidden;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				.avatar {
					width: 127px;
					height: 159px;
					object-fit: fill;
				}
				.img-box {
					.flex-center;
					width: 100%;
					height: 140px;
					background-color: #eeeeee;
					.vacancy {
						width: 41px;
						height: 41px;
						object-fit: contain;
					}
				}
				.user-name {
					margin-top: 10px;
					text-align: center;
					font-size: 24px;
					line-height: 28px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: #000000;
				}
				.user-position {
					margin-top: 10px;
					text-align: center;
					font-size: 21px;
					line-height: 25px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.9);
					// 超过两行省略
					display: -webkit-box;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 3;
				}
				.card-icon {
					position: absolute;
					top: 0px;
					left: 20px;
					width: 63px;
					height: 39px;
				}
				.card-icon-warn {
					background: url('../images/org-team-warn.png') no-repeat center / cover;
				}
				.card-icon-vancy {
					background: url('../images/org-team-vancy.png') no-repeat center / cover;
				}
			}
			.active-select {
				background: #00a3ff;
				.user-position {
					color: #ffffff;
				}
				.user-name {
					color: #ffffff;
				}
			}
		}
		.lf-button-box {
			.white-bg;
			margin-top: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 71px;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			gap: 30px;
			.ant-btn {
				padding: 0px;
				width: 141px;
				height: 42px;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				.ant-btn-primary {
				}
			}
		}
	}
	.alternate-list {
		margin: 0 -20px;
		border-top: 12px #f5f5f5 solid;
		padding: 27px 24px;
		background: #ffffff;
		width: 100%;
		overflow-x: hidden;
	}
}
</style>

../components/component
