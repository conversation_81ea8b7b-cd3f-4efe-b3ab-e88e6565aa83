<template>
	<Card title="其他班子成员" style="height: auto" header class="team-member">
		<DataTable :data-source="data" :columns="columns">
			<template v-slot:rank="{ value }">
				<span v-if="value > 3">{{ value }}</span>
				<span v-else :class="`rank-icon rank-${value}`"></span>
			</template>
			<template v-slot:portrait="{ data }">
				<span class="custom-portrait" @click="onLocation(data)"></span>
			</template>

			<template v-slot:same_rank="{ value }">{{ value }} </template>
		</DataTable>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { historyPush } from '@/utils/history'
import DataTable from '@/components/Table.vue'
import { getPatrolOtherUser } from '@/apis/statistics'
import { getQueryVariable } from '@/utils/utils'
const { org_id } = getQueryVariable()

const columns = [
	{
		title: '排名',
		dataIndex: 'rank',
		key: 'rank',
		width: '10%',
	},
	{
		title: '姓名',
		dataIndex: 'username',
		key: 'username',
		width: '10%',
	},
	{
		title: '职务',
		dataIndex: 'current_job',
		key: 'current_job',
		width: '25%',
	},
	{
		title: '巡察指数',
		dataIndex: 'inspection_index',
		key: 'inspection_index',
		width: '10%',
	},
	{
		title: '谈话等级评定（25）',
		dataIndex: 'speech_rating_index',
		key: 'speech_rating_index',
		width: '15%',
	},
	{
		title: '测评结果（25）',
		dataIndex: 'eval_index',
		key: 'eval_index',
		width: '10%',
	},
	{
		title: '巡察组评定（50）',
		dataIndex: 'org_inspection_index',
		key: 'org_inspection_index',
		width: '10%',
	},
	{
		title: '廉政风险（扣分）',
		dataIndex: 'integrity_index',
		key: 'integrity_index',
		width: '10%',
	},
]

const data = ref([])

const onLocation = (data: any) => {
	historyPush({
		path: `/team-inspection-index`,
		query: {
			user_id: data.org_id,
		},
	})
}
const loadData = async () => {
	const res = await getPatrolOtherUser({ org_id })
	if (res.code === 0) {
		data.value = res.data
	}
}

loadData()
</script>

<style lang="scss" scoped>
.team-member {
	.rank-icon {
		display: inline-block;
		width: 28px;
		height: 28px;
		vertical-align: middle;
	}
	.rank-1 {
		background: url('@/assets/images/rank-1.png') no-repeat center / contain;
	}
	.rank-2 {
		background: url('@/assets/images/rank-2.png') no-repeat center / contain;
	}
	.rank-3 {
		background: url('@/assets/images/rank-3.png') no-repeat center / contain;
	}
}
</style>
