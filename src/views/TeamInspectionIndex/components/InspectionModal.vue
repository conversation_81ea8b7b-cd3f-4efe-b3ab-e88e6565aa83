<template>
	<Modal :title="null" :footer="null" :visible="visible" destroy-on-close @cancel="onCancel" :closable="false" class="inspection-detail-modal">
		<div class="modal-content">
			<div class="modal-title">
				<div class="title">{{ title }}</div>
				<div class="close" @click="onCancel">x</div>
			</div>
			<div class="m-c-main">
				<div class="modal-sub-title">
					{{ name }} <span class="score">{{ socre }}</span>
				</div>
				<div class="mc-table-box">
					<div class="table-title">扣分详情：</div>
					<div class="table-box">
						<PopTable :columns="type === 1 ? columns : columns1" :data-source="list" bodyScroll>
							<template #two="{ value }">
								<div class="align-right">{{ value }}</div>
							</template>
							<template #three="{ value }">
								<div class="align-right">{{ value }}</div>
							</template>
							<template #question="{ value }">
								<div class="align-right">{{ value }}</div>
							</template>
							<template #org_score="{ value }">
								<div class="red">{{ value }}</div>
							</template>
							<template #type="{ value }">
								<div>{{ responsibilityMap.get(value) }}</div>
							</template>
							<template #my_score="{ value }">
								<div class="red">{{ value }}</div>
							</template>
						</PopTable>
					</div>
				</div>
			</div>
		</div>
	</Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Modal } from 'ant-design-vue'
import PopTable from '@/components/Table.vue'
defineProps({
	onCancel: Function as any,
	visible: { type: Boolean, default: false },
	socre: Number,
	list: Array as any,
	title: String,
	name: String,
	type: Number,
})
const responsibilityMap = new Map([
	[1, '直接责任'],
	[2, '主要领导责任'],
	[3, '重要领导责任'],
	[4, '一把手'],
	[5, '乡镇长'],
])
const columns = [
	{
		title: '二级指标',
		align: 'center',
		key: 'two',
		width: '10%',
	},
	{
		title: '三级指标',
		align: 'center',
		width: '10%',
		key: 'three',
	},
	{
		title: '具体问题',
		align: 'center',
		width: '10%',
		key: 'question',
	},
	{
		title: '班子扣分',
		align: 'center',
		width: '10%',
		key: 'org_score',
	},
]
const columns1 = [
	{
		title: '二级指标',
		align: 'center',
		key: 'two',
		width: '10%',
	},
	{
		title: '三级指标',
		align: 'center',
		width: '10%',
		key: 'three',
	},
	{
		title: '具体问题',
		align: 'center',
		width: '10%',
		key: 'question',
	},
	{
		title: '承担责任',
		align: 'center',
		width: '10%',
		key: 'type',
	},
	{
		title: '本人扣分',
		align: 'center',
		width: '10%',
		key: 'my_score',
	},
]
</script>

<style lang="less">
.inspection-detail-modal {
	width: 1462px !important;
	height: 602px;
	.ant-modal-body {
		padding: 0px;
	}
	.modal-content {
		.modal-title {
			padding: 12px 0px;
			display: flex;
			justify-content: center;
			position: relative;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
			.title {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 26px;
				color: #000000;
			}
			.close {
				position: absolute;
				top: 0px;
				right: 27px;
				font-size: 28px;
				cursor: pointer;
			}
		}
		.m-c-main {
			padding: 28px 24px;
			.modal-sub-title {
				display: flex;
				align-items: center;
				&::before {
					margin-right: 16px;
					content: '';
					display: inline-block;
					width: 16px;
					height: 16px;
					background: #ffa300;
					border-radius: 50%;
				}
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 24px;
				color: #3d3d3d;
				.score {
					margin-left: 36px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 32px;
					line-height: 1;
					color: #f02a2a;
				}
			}
			.mc-table-box {
				margin-top: 28px;
				.table-title {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 24px;
					color: #3d3d3d;
				}
				.table-box {
					margin-top: 10px;
					height: 40vh;
				}
			}
		}
	}
	.align-right {
		text-align: left;
	}
	.red {
		color: rgba(240, 42, 42, 1);
	}
}
</style>
