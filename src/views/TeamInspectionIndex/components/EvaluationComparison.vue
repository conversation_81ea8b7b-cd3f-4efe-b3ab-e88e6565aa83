<template>
	<Card title="班子成员测评对比" header size-type="3" style="height: auto">
		<div class="box-content__line">
			<v-chart :option="option" autoresize></v-chart>
		</div>
		<div class="box-content__table">
			<DataTable :data-source="dataSource" :columns="columns" expand />
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import DataTable from '@/components/Table.vue'
import { getPatrolOrgEvalCompare } from '@/apis/statistics'
import { decreaseOpacity, convertPxToRem } from '@/utils/utils'
import { historyPush } from '@/utils/history'
import { useRoute } from 'vue-router'

const { org_id } = useRoute().query

const data: any = ref({
	xlabel: [],
	other_result: [],
	avg_list: {},
})

const apiData = ref<any>({
	line: [],
	table: [],
	data: [],
	xlabel: [],
	source: [],
})
const colorList = [
	'#FF6C47',
	'#6AAEFB',
	'#6ADDE4',
	'#CBADFF',
	'#F06292',
	'#AED581',
	'#66BB6A',
	'#E6EE9C',
	'#BF360C',
	'#9FA8DA',
	'#80CBC4',
	'#9CCC65',
	'#03A9F4',
	'#00BCD4',
	'#A1887F',
	'#CDAD00',
	'#32CD32',
	'#CD5B45',
	'#A020F0',
	'#BDB76B',
]
const createData = (origin_data: [], title: string) => {
	const data: any = {}
	data.name = title
	for (let i = 0; i < origin_data.length; i++) {
		data[`name${i + 1}`] = typeof origin_data[i] === 'number' ? origin_data[i]?.toFixed?.(2) : origin_data[i]
	}

	return data
}

const option = ref({})

const dataSource = ref<any>([])

const initOption = ({ XName, dataMap }: any) => {
	const datas: any[] = []
	const lengendSelect: any = {}
	// 平均值在第一个
	let isAvgFirst = false
	dataMap.map((item: any, index) => {
		const { score, username } = item

		let symbolSize = 8
		let lineWidth = 2

		const color = colorList[index] || '#fff'
		const [, ..._score] = score
		datas.push({
			symbolSize: convertPxToRem(symbolSize),
			symbol: 'circle',
			name: username,
			type: 'line',
			yAxisIndex: 1,
			data: _score,
			color: color,
			smooth: false,
			itemStyle: {
				borderWidth: convertPxToRem(4),
				borderColor: color,
				color: '#fff',
				shadowColor: decreaseOpacity(color, 0.5),
				shadowBlur: 13,
			},
			lineStyle: {
				color: color,
				type: 'solid',
				width: convertPxToRem(lineWidth),
			},
		})
		if (username === '平均值') {
			isAvgFirst = index === 0

			lengendSelect[username] = true
		} else if (index === 0 && username !== '平均值') {
			lengendSelect[username] = true
		}
		// 第一个为平均值的情况，选中第二个
		else if (index === 1 && isAvgFirst) {
			lengendSelect[username] = true
			isAvgFirst = false
		} else {
			lengendSelect[username] = false
		}
	})

	option.value = {
		// backgroundColor: '#0e2147',

		grid: {
			left: '1%',
			top: datas.length > 11 ? '23%' : '13%',
			bottom: '0%',
			right: '3%',
			containLabel: true,
		},
		legend: {
			show: true,
			top: 0,
			left: 'center',
			// type: 'scroll',
			// data: Line,
			icon: 'circle',
			itemWidth: convertPxToRem(12),
			itemHeight: convertPxToRem(12),
			textStyle: {
				color: '#333333',
				fontSize: convertPxToRem(18),
			},
			itemStyle: {
				borderWidth: convertPxToRem(4),
			},
			itemGap: convertPxToRem(28),
			selected: lengendSelect,
		},
		yAxis: [
			{
				type: 'value',
				position: 'right',
				splitLine: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
			},
			{
				type: 'value',
				position: 'left',
				// scale: true,
				min: (value: any) => value.min - 0.5,
				max: (value: any) => 0,
				nameTextStyle: {
					color: '#00FFFF',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',
						color: '#EEEEEE',
					},
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: ['none', 'arrow'],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				axisTick: {
					show: false,
				},
				// splitNumber: 4,
				// interval: 1,
				axisLabel: {
					color: '#666666',
					fontSize: convertPxToRem(16),
					formatter: (value: any) => {
						value = Number(value)
						return value > 100 ? '' : value.toFixed(2)
					},
				},
			},
		],
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					inside: false,
					textStyle: {
						color: '#666666', // x轴颜色
						fontWeight: 'normal',
						fontSize: convertPxToRem(16),
						lineHeight: convertPxToRem(22),
					},
					interval: 0,
				},
				data: XName,
			},
		],
		series: datas,
	}
}
const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
	const columns: Array<any> = []

	list.forEach((item: any, index: number) => {
		const key = `name`
		if (index === 0) {
			columns[index] = {
				key,
				title,
				width: '10%',
				colClick: (value: any) => {
					historyPush(`/inspection-index?user_id=${value.user_id}`)
				},
			}
		}
		columns[index + 1] = {
			key: `${key}${index + 1}`,
			title: item,
			width: 100 / (list.length + 1) + '%',
			align: 'center',
			showMax: true,
			showMin: true,
		}
		if (item === '政治三力') {
			columns[index + 1].customization = 'CustomBlock'
		}
	})
	return columns
}
const columns = ref<any>([])

const loadData = async () => {
	if (!org_id) return

	// formmaterData({
	// 	title: [
	// 		'政治三力',
	// 		'学习能力',
	// 		'决策能力',
	// 		'执行能力',
	// 		'沟通协调能力',
	// 		'群众工作能力',
	// 		'奉献度',
	// 		'勤奋度',
	// 		'正直度',
	// 		'廉洁度',
	// 		'测评分数',
	// 		'巡察指数',
	// 	],
	// 	list: [
	// 		{ user_id: 1, username: '用户1', score: [47, 50, 71, 71, 71, 71, 71, 71, 71, 71, 71] },
	// 		{ user_id: 2, username: '用户2', score: [41, 50, 62, 62, 62, 62, 62, 62, 62, 62, 62] },
	// 		{ user_id: 3, username: '用户3', score: [15, 20, 74, 74, 74, 74, 74, 74, 74, 74, 74] },
	// 		{ user_id: 4, username: '用户4', score: [29, 40, 95, 95, 95, 95, 95, 95, 95, 95, 95] },
	// 		{ user_id: 5, username: '用户5', score: [42, 46, 85, 85, 85, 85, 85, 85, 85, 85, 85] },
	// 		{ user_id: 6, username: '用户6', score: [34, 34, 45, 45, 45, 45, 45, 45, 45, 45, 45] },
	// 		{ user_id: 7, username: '用户7', score: [24, 29, 60, 60, 60, 60, 60, 60, 60, 60, 60] },
	// 		{ user_id: 8, username: '用户8', score: [49, 50, 61, 61, 61, 61, 61, 61, 61, 61, 61] },
	// 		{ user_id: 9, username: '用户9', score: [27, 40, 98, 98, 98, 98, 98, 98, 98, 98, 98] },
	// 		{ user_id: 10, username: '用户10', score: [35, 41, 73, 73, 73, 73, 73, 73, 73, 73, 73] },
	// 	],
	// })

	const res: any = await getPatrolOrgEvalCompare({ org_id })
	if (res.code === 0 && Object.keys(res.data || {}).length > 0) {
		data.value = res.data

		formmaterData(res.data)
	}
}
const dataMapSource = ref<any>([])
// 格式化数据
const formmaterData = (data: any) => {
	const { list, title = [] } = data

	apiData.value.xlabel = title

	const _data = list

	dataMapSource.value = _data

	const xlabel: any[] = [].concat(title)

	columns.value = dynamicTableHeader(xlabel)

	const datasource: any = []

	_data.map((item: any) => {
		if (item.score) {
			const _data: any[] = [].concat(item.score)

			const data = createData(_data as any, item.username)

			data.user_id = item.user_id

			datasource.push(data)
		}
	})

	dataSource.value = datasource

	const [, ..._xlabel] = xlabel

	initOption({
		XName: _xlabel,
		dataMap: _data,
	})
}

loadData()
</script>

<style scoped lang="less">
.box-content__line {
	height: 295px;
}
.box-content__table {
	margin-top: 39px;

	flex: 1;
	:deep(.header-td) {
		white-space: nowrap;
	}
}
</style>
