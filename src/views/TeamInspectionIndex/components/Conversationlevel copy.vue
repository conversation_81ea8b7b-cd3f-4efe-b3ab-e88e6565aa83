<template>
	<Card title="谈话等次" header style="height: auto">
		<a-table :columns="columns" :data-source="data" bordered size="middle" :scroll="{ x: 'calc(700px + 50%)', y: 240 }" :pagnation="null" />
	</Card>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import type { TableColumnsType } from 'ant-design-vue'
import { getPatrolOrgSpeechRating } from '@/apis/statistics'
import { getQueryVariable } from '@/utils/utils'
const { org_id } = getQueryVariable()

const columns: TableColumnsType = [
	{
		title: '',
		dataIndex: 'username',
		key: 'username',
		fixed: 'left',
		align: 'center',
	},
	{
		title: '班子成员评定情况（占40%）',
		dataIndex: '',
		key: '',
		children: [
			{
				title: 'A',
				dataIndex: 'same_a',
				key: 'same_a',
			},
			{
				title: 'B',
				dataIndex: 'same_b',
				key: 'same_b',
			},
			{
				title: 'C',
				dataIndex: 'same_c',
				key: 'same_c',
			},
			{
				title: 'D',
				dataIndex: 'same_d',
				key: 'same_d',
			},
			{
				title: '小计',
				dataIndex: 'same_score',
				key: 'same_score',
			},
		],
	},
	{
		title: '其他干部评定情况（占40%）',
		children: [
			{
				title: 'A',
				dataIndex: 'upp_a',
				key: 'upp_a',
			},
			{
				title: 'B',
				dataIndex: 'upp_b',
				key: 'upp_b',
			},
			{
				title: 'C',
				dataIndex: 'upp_c',
				key: 'upp_c',
			},
			{
				title: 'D',
				dataIndex: 'upp_d',
				key: 'upp_d',
			},
			{
				title: '小计',
				dataIndex: 'upp_score',
				key: 'upp_score',
			},
		],
	},

	{
		title: '巡察组评定情况（占20%）',
		children: [
			{
				title: 'A',
				dataIndex: 'down_a',
				key: 'down_a',
			},
			{
				title: 'B',
				dataIndex: 'down_b',
				key: 'down_b',
			},
			{
				title: 'C',
				dataIndex: 'down_c',
				key: 'down_c',
			},
			{
				title: 'D',
				dataIndex: 'down_d',
				key: 'down_d',
			},
			{
				title: '小计',
				dataIndex: 'down_c',
				key: 'down_score',
			},
		],
	},
	{
		title: '总得分',
		dataIndex: 'total_score',
		key: 'total_score',
		fixed: 'right',
	},
]
const data = ref([])
// 数据处理
const flattenData = (data: any) => {
	return data.map((item: any) => {
		return {
			user_id: item.user_id,
			username: item.username,
			down_a: item.down.a,
			down_b: item.down.b,
			down_c: item.down.c,
			down_d: item.down.d,
			down_score: item.down.score,
			same_a: item.same.a,
			same_b: item.same.b,
			same_c: item.same.c,
			same_d: item.same.d,
			same_score: item.same.score,
			upp_a: item.upp.a,
			upp_b: item.upp.b,
			upp_c: item.upp.c,
			upp_d: item.upp.d,
			upp_score: item.upp.score,
			total_score: item.total_score,
		}
	})
}

const loadData = async () => {
	const res = await getPatrolOrgSpeechRating({ org_id })
	if (res.code === 0) {
		data.value = flattenData(res.data)
	}
}

loadData()
</script>
