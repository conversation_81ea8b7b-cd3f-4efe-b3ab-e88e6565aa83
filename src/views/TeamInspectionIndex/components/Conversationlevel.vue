<template>
	<Card title="谈话等次" header style="height: auto">
		<div class="charts-box" @touchend.prevent="() => {}">
			<Echarts$4 :option="option" ref="charts" />
		</div>
		<ConversationModal :visible="visible" title="谈话等次详情" :name="obj.username" :socre="obj.total_score" :list="someData" :on-cancel="onCancel" />
	</Card>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import ConversationModal from './ConversationModal.vue'
import { getPatrolOrgSpeechRating } from '@/apis/statistics'
import { getInspectionIndexOrgSpeechRating } from '@/apis/inspection-index'
import { getQueryVariable, convertPxToRem } from '@/utils/utils'
const { org_id } = getQueryVariable()

const visible = ref(false)
const list = ref([])
const obj = ref({})
const someData = ref<any>([])

const onCancel = () => {
	visible.value = false
}

const option = ref<any>()
const initOption = (datas: any[]) => {
	const data = {
		type: 'bar',
		barWidth: '64px',
		itemStyle: {
			color: (params: any) => {
				return 'rgba(58, 137, 255, 1)'
			},
		},
		datasetIndex: 0,
		data: [
			...datas.map((item: any, index: any) => {
				return [index, item.total_score, item.user_id]
			}),
		],
		label: {
			show: true,
			position: 'top',
			color: '#333333',
			fontSize: convertPxToRem(16),
		},
	}
	option.value = {
		grid: {
			left: '1%',
			top: datas.length > 11 ? '23%' : '13%',
			bottom: '0%',
			right: '3%',
			containLabel: true,
		},
		legend: {
			show: true,
			top: 0,
			left: 'center',
			// type: 'scroll',
			// data: Line,
			icon: 'circle',
			itemWidth: convertPxToRem(12),
			itemHeight: convertPxToRem(12),
			textStyle: {
				color: '#333333',
				fontSize: convertPxToRem(18),
			},
			itemStyle: {
				borderWidth: convertPxToRem(4),
			},
			itemGap: convertPxToRem(28),
			data: [
				{
					name: '-10（含）以上绿色',
					color: 'rgba(96, 202, 113, 1)',
				},
				{
					name: '-15（含）~-10',
					color: 'rgba(255, 128, 0, 1)',
				},
				{
					name: '-15以下',
					color: 'rgba(180, 105, 0, 1)',
				},
			],
		},
		yAxis: {
			type: 'value',
			position: 'left',
			// scale: true,
			min: (value: any) => value.min - 0.5,
			max: (value: any) => 0,
			nameTextStyle: {
				color: '#00FFFF',
			},
			splitLine: {
				lineStyle: {
					type: 'dashed',
					color: '#EEEEEE',
				},
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: '#333333',
				},
				symbol: ['none', 'arrow'],
				symbolOffset: 7,
				symbolSize: [7, 10],
			},
			axisTick: {
				show: false,
			},
			// splitNumber: 4,
			// interval: 1,
			axisLabel: {
				show: true,
				color: '#666666',
				fontSize: convertPxToRem(16),
				formatter: (value: any) => {
					value = Number(value)
					return value > 100 ? '' : value.toFixed(2)
				},
			},
		},
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
					onZero: false,
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					inside: false,
					textStyle: {
						color: '#666666', // x轴颜色
						fontWeight: 'normal',
						fontSize: convertPxToRem(16),
						lineHeight: convertPxToRem(22),
					},
					color: '#666666', // x轴颜色
					fontWeight: 'normal',
					fontSize: convertPxToRem(16),
					lineHeight: convertPxToRem(22),
					interval: 0,
				},
				data: datas.map((item: any) => item.username),
			},
		],
		series: [data],
	}
}
const getDataList = async (org_id: any) => {
	const res = await getInspectionIndexOrgSpeechRating({ org_id })
	if (res.code == 0 && res.data) {
		list.value = res.data
		initOption(res.data)
	}
}
getDataList(org_id)
const charts = ref()

onMounted(() => {
	charts.value.instance.on('click', (params: any) => {
		list.value.map((item: any) => {
			item.user_id == params.data[2] && (obj.value = item)
		})
		someData.value = [obj.value]
		visible.value = true
	})
})
</script>
<style lang="less">
.charts-box {
	height: 283px;
}
</style>
