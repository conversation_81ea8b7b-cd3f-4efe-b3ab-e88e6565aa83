<template>
	<Card style="height: auto" class="team-inspection" title="班子巡察" header>
		<div class="score-total">
			班子扣分：<span class="score">{{ data.org_score }}</span>
		</div>
		<div class="m-top-25">
			<TableData :data-source="data.org_score_list" :columns="columns" />
		</div>
		<div class="sub-title">班子巡察扣分汇总</div>
		<div class="m-top-25">
			<TableData :data-source="data.user_score_list" :columns="columns1" />
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import TableData from '@/components/Table.vue'
import { getPatrolOrgInspection } from '@/apis/statistics'
import { getQueryVariable } from '@/utils/utils'

const { org_id } = getQueryVariable()

const columns = [
	{
		title: '扣分指标',
		dataIndex: 'item',
		key: 'item',
		width: '50%',
		align: 'left',
	},
	{
		title: '具体问题',
		dataIndex: 'question',
		key: 'question',
		width: '30%',
		align: 'left',
	},
	{
		title: '班子扣分',
		dataIndex: 'org_score',
		key: 'org_score',
		width: '20%',
		align: 'center',
	},
]

const columns1 = [
	{
		title: '姓名',
		dataIndex: 'username',
		key: 'username',
		width: '20%',
		align: 'center',
	},
	{
		title: '主要领导责任扣分',
		dataIndex: 'main',
		key: 'main',
		width: '20%',
		align: 'center',
	},
	{
		title: '重要领导责任扣分',
		dataIndex: 'importance',
		key: 'importance',
		width: '20%',
		align: 'center',
	},
	{
		title: '直接责任扣分',
		dataIndex: 'direct',
		key: 'direct',
		width: '20%',
		align: 'center',
	},
	{
		title: '合计扣分',
		dataIndex: 'sum',
		key: 'sum',
		width: '20%',
		align: 'center',
	},
]

const total = ref(0)

const data = ref({
	org_score: 0,
	org_score_list: [],
	user_score_list: [],
})

const loadData = async () => {
	const res = await getPatrolOrgInspection({ org_id })

	if (res.code === 0) {
		data.value = res.data
	}
}

loadData()
</script>

<style lang="scss" scoped>
.team-inspection {
	.score-total {
		text-align: right;
		font-size: 16px;
		font-weight: 600;
		margin-bottom: 10px;
		.score {
			font-weight: bold;
			font-size: 20px;
			color: rgba(229, 37, 27, 1);
		}
	}
	.sub-title {
		margin-top: 35px;
		display: flex;
		align-items: center;
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 20px;
		color: #333333;
		line-height: 24px;
		&::before {
			margin-right: 10px;
			content: '';
			display: inline-block;
			width: 12px;
			height: 12px;
			background: #ec4224;
			border-radius: 50%;
		}
	}
}
</style>
