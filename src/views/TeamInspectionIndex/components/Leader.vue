<template>
	<Card title="一把手" header style="height: auto">
		<div class="leader">
			<div class="top-box">
				<div class="base_info">
					<div class="avatar-box">
						<CodeAvatar :head_url="data.head_url" />
					</div>
					<div class="info">
						<div class="name">{{ data.username }}</div>
						<div class="position">{{ data.current_job }}</div>
					</div>
				</div>
				<div class="data-box">
					<div class="data-item">
						<div class="label">巡察指数</div>
						<div class="num">{{ data.inspection_index }}</div>
					</div>
					<div class="data-item">
						<div class="label">谈话等次</div>
						<div class="num">{{ data.speech_rating_index }}</div>
					</div>
					<div class="data-item">
						<div class="label">测评结果</div>
						<div class="num">{{ data.eval_index }}</div>
					</div>
					<div class="data-item">
						<div class="label">巡察组评定</div>
						<div class="num">{{ data.org_inspection_index }}</div>
					</div>
					<div class="data-item">
						<div class="label">廉政风险</div>
						<div class="num">{{ data.integrity_index }}</div>
					</div>
				</div>
			</div>
			<div class="leader-tag">
				<div class="sub-title">
					<div class="sub-title_left">特征标签</div>
					<div class="detail-btn" @click="onOpenModal">
						更多
						<svg
							t="1725434893724"
							class="icon"
							viewBox="0 0 1024 1024"
							version="1.1"
							xmlns="http://www.w3.org/2000/svg"
							p-id="6250"
							width="16"
							height="16"
						>
							<path
								d="M761.055557 532.128047c0.512619-0.992555 1.343475-1.823411 1.792447-2.848649 8.800538-18.304636 5.919204-40.703346-9.664077-55.424808L399.935923 139.743798c-19.264507-18.208305-49.631179-17.344765-67.872168 1.888778-18.208305 19.264507-17.375729 49.631179 1.888778 67.872168l316.960409 299.839269L335.199677 813.631716c-19.071845 18.399247-19.648112 48.767639-1.247144 67.872168 9.407768 9.791372 21.984142 14.688778 34.560516 14.688778 12.000108 0 24.000215-4.479398 33.311652-13.439914l350.048434-337.375729c0.672598-0.672598 0.927187-1.599785 1.599785-2.303346 0.512619-0.479935 1.056202-0.832576 1.567101-1.343475C757.759656 538.879828 759.199462 535.391265 761.055557 532.128047z"
								fill="#575B66"
								p-id="6251"
							></path>
						</svg>
					</div>
				</div>
				<div class="leader-tag__box">
					<TagBox :data="data.positive_feature?.slice(0, 10)" color="rgba(0, 142, 255, 1)" />
					<Tag v-for="(tag, index) in data.negative_feature?.slice(0, 10)" color="#FF6A16" :tag="tag" :key="index" />
				</div>
			</div>
		</div>
		<TagModal
			:visible="tagVisible"
			@cancel="onTagModalClose"
			:other_feature="data.other_feature"
			:positive_feature="data.positive_feature"
			:negative_feature="data.negative_feature"
		/>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Tag from '@/components/Tag.vue'
import TagBox from '@/components/TagBox.vue'
import { convertMap, getQueryVariable } from '@/utils/utils'
import { getPatrolMainUser } from '@/apis/statistics'
import TagModal from '@/components/TagModal.vue'

const { org_id } = getQueryVariable()

const data = ref({
	username: '',
	current_job: '',
	user_id: undefined,
	head_url: '',
	inspection_index: 0,
	speech_rating_index: 0,
	eval_index: 0,
	org_inspection_index: 0,
	integrity_index: 0,
	positive_feature: [],
	negative_feature: [],
	other_feature: [],
	integrity_tag: [],
})

const tagVisible = ref(false)

const onTagModalClose = () => {
	tagVisible.value = false
}

const onOpenModal = () => {
	tagVisible.value = true
}

const loadData = async () => {
	const res = await getPatrolMainUser({ org_id })

	if (res.code === 0) {
		const { positive_feature = [], negative_feature = [], other_feature = [], integrity_tag = [] } = res.data
		res.data.positive_feature = convertMap(positive_feature, true)
		res.data.negative_feature = convertMap([...negative_feature, ...integrity_tag], true)
		res.data.other_feature = convertMap(other_feature, true)
		// res.data.integrity_tag = convertMap(integrity_tag)

		data.value = res.data
	}
}

loadData()
</script>

<style lang="less" scoped>
.leader {
	.top-box {
		display: flex;

		.base_info {
			flex: 1;
			display: flex;
			padding: 23px 15px;

			.avatar-box {
				width: 126px;
				height: 171px;

				.avatar {
					width: 100%;
					height: 100%;
				}
			}

			.info {
				margin-left: 20px;

				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 28px;
					color: #000000;
					line-height: 33px;
					text-align: left;
				}

				.position {
					margin-top: 6px;
					font-size: 20px;
					color: #333333;
					// 三行
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 3;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}

		.data-box {
			display: flex;
			gap: 0px 20px;

			.data-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 200px;
				height: 142px;
				background: #fdf3f3;

				.label {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					font-size: 20px;
					color: #3d3d3d;
				}

				.num {
					margin-top: 16px;
					font-family: ArTarumianBakhum, ArTarumianBakhum;
					font-weight: 400;
					font-size: 40px;
					color: #d23122;
					line-height: 47px;
				}
			}
		}
	}
	.sub-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		&_left {
			display: flex;
			align-items: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 20px;
			color: #333333;
			line-height: 24px;
			&::before {
				margin-right: 10px;
				content: '';
				display: inline-block;
				width: 12px;
				height: 12px;
				background: #ec4224;
				border-radius: 50%;
			}
		}
		.detail-btn {
			display: flex;
			align-items: center;
			font-size: 16px;
			cursor: pointer;
		}
	}
	.leader-tag {
		padding: 20px 22px;
		border: 1px solid #e9e9e9;
		&__box {
			padding: 33px 0px;
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
		}
	}
}
</style>
