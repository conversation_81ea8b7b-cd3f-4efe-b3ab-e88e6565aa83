<template>
	<div class="circle-title">
		{{ title }}
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
defineProps({
	title: {
		type: String,
		required: true,
	},
})
</script>

<style lang="scss" scoped>
.circle-title {
	display: flex;
	align-items: center;

	font-family: PingFang SC, PingFang SC;
	font-weight: 800;
	font-size: 24px;
	color: rgba(0, 0, 0, 0.95);
	line-height: 28px;

	&::before {
		margin-right: 4px;
		display: inline-block;
		content: '';
		width: 10px;
		height: 10px;
		border-radius: 50%;
		background: #f02a2a;
	}
}
</style>
