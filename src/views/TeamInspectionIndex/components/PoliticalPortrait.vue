<template>
	<Card title="政治画像" header style="height: auto">
		<div class="political-portrait">
			<div class="pp-zm">
				<div class="left-pp-zm">正面评价</div>
				<div class="right-pp-zm">
					{{ data.positive_comment }}
				</div>
			</div>
			<div class="dash-line"></div>
			<div class="pp-fm">
				<div class="left-pp-fm">主要问题和不足</div>
				<div class="right-pp-fm">{{ data.drawback }}</div>
			</div>
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import Card from '@/components/Card.vue'
import { getOrgPoliticalScreen } from '@/apis/data-screen'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
const route = useRoute()

const { org_id } = route.query

const data = ref({
	org_id: undefined,
	positive_comment: '',
	drawback: '',
})

onMounted(() => {
	getOrgPoliticalScreen({ org_id: org_id }).then((res) => {
		if (res.code === 0) {
			data.value = res.data
		} else {
			message.error(res.message)
		}
	})
})
</script>

<style lang="less" scoped>
.political-portrait {
	display: flex;
	flex-direction: column;
	.pp-zm,
	.pp-fm {
		display: flex;
		justify-content: space-between;
		.left-pp-zm,
		.left-pp-fm {
			min-height: 100px;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 122px;
			padding: 0px 16px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 22px;
			color: #333333;
			line-height: 26px;
			background: #def0ff;
		}
		.right-pp-zm,
		.right-pp-fm {
			flex: 1;
			padding: 5px 16px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 22px;
			color: #3d3d3d;
			line-height: 26px;
		}
	}
	.dash-line {
		margin: 20px 0px;
		width: 100%;
		border-bottom: 1px dashed #dfdfdf;
	}
}
</style>
