<template>
	<Card style="height: auto" title="班子巡察指数金字塔" header size-type="2">
		<div class="inspection-inner-box">
			<div class="pyramid">
				<div :class="`pyramid-item pyramid-${item.key}`" v-for="item in pyramidList" :key="item.key" @click="onActive(item.key)">
					<div class="pyramid-img">
						<div class="pyramid-label">{{ item.label }}</div>
						<img :src="item.img" alt="" />
						<div class="select" v-if="data.pyramid_index === item.key - 1">
							<div class="select-line"></div>
							<div class="text">
								<div>{{ data.org_name }} {{ data.inspection_index }}</div>
								<div @click="onSequenceModal({ org_id: data.org_id })">同序列： {{ data.inspection_index_rank }}</div>
							</div>
						</div>
						<div class="splitLine">
							<div class="line-box">
								<div class="left-line"></div>
								<div class="right-line"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref, computed, toRefs } from 'vue'

import { useHomeStore } from '@/store/home'

import ImgPyramid1 from '@/assets/images/pyramid-1.png'
import ImgPyramid2 from '@/assets/images/pyramid-2.png'
import ImgPyramid3 from '@/assets/images/pyramid-3.png'
import ImgPyramid4 from '@/assets/images/pyramid-4.png'
import { SequenceModal } from '@/views/DataScreen/Components/SequenceModal'
defineProps({
	data: Object as any,
})
const activeRow = ref(1)
const activeInfo = ref<any>({
	username: '',
	inspection_index: 0,
	pyramid_index: 1,
})
// 用户信息
const pyramidList = computed(() => {
	const list = [
		{
			img: ImgPyramid1,
			label: '10%',
			key: 1,
		},
		{
			img: ImgPyramid2,
			label: '20%',
			key: 2,
		},
		{
			img: ImgPyramid3,
			label: '30%',
			key: 3,
		},
		{
			img: ImgPyramid4,
			label: '40%',
			key: 4,
		},
	]
	return list
})

const onActive = (key: number) => {
	activeRow.value = key
}

const onSequenceModal = ({ org_id, user_id }: { org_id?: string; user_id?: string }) => {
	SequenceModal({
		org_id: org_id,
		user_id: user_id,
	})
}
</script>

<style scoped lang="less">
// ::v-deep(.pyramid) {
// 	padding: 0px 29px 27px 10px;
// }
.inspection-inner-box {
	display: flex;
	justify-content: center;
	.pyramid {
		transform: translateX(10%);
		width: 734px;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		user-select: none;
		.pyramid-item {
			position: relative;
			transform: translate(-60px);

			.pyramid-img {
				position: relative;
				margin: 0 auto;
				.pyramid-label {
					position: absolute;
					top: 40%;
					left: -20px;
					font-size: 22px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #666666;
					line-height: 26px;
				}
				img {
					width: 100%;
					height: 100%;
				}
				.select {
					position: absolute;
					top: 0;
					left: 100%;
					display: flex;
					align-items: center;
					width: 341px;
					height: 65.5px;
					-webkit-clip-path: polygon(0 0, 100% 0%, 100% 100%, 7% 100%);
					clip-path: polygon(0 0, 100% 0%, 100% 100%, 10% 100%);
					.select-line {
						height: 1px;
						background: #278236;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
					}
					.text {
						margin-left: 10px;
						font-size: 22px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #278236;
						line-height: 26px;
					}
				}
				.splitLine {
					position: absolute;
					inset: 0;
					// background-color: rgba(0, 0, 0, 0.9);
					.line-box {
						.left-line {
						}
						.right-line {
						}
					}
				}
			}
			&:nth-child(2) {
				margin-top: -10px;
			}
			&:nth-child(3) {
				margin-top: -20px;
			}
			&:nth-child(4) {
				margin-top: -20px;
			}
		}
		.pyramid-1 {
			.pyramid-img {
				width: 67px;
				height: 78px;

				.pyramid-label {
					transform: translateX(-15px);
				}
			}
			.select {
				width: 341px;
				background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
				transform: translate(-30px);
				.select-line {
					width: 191px;
					background-color: #278236;
				}
				.text {
					color: #278236;
				}
			}
		}
		.pyramid-2 {
			.pyramid-img {
				width: 146px;
				height: 85px;
				.pyramid-label {
					left: -30px !important;
				}
			}
			.select {
				width: 271px;
				background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
				transform: translate(-35px);
				.select-line {
					width: 146px;
					background-color: #278236;
				}
				.text {
					color: #278236;
				}
			}
		}
		.pyramid-3 {
			.pyramid-img {
				width: 239px;
				height: 101px;
			}
			.select {
				width: 271px;
				background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
				transform: translate(-40px, 10px);
				.select-line {
					width: 126px;
					background-color: #278236;
				}
				.text {
					color: #278236;
				}
			}
		}
		.pyramid-4 {
			.pyramid-img {
				width: 347px;
				height: 127px;
			}
			.select {
				width: 271px;
				background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
				transform: translate(-45px, 20px);
				.select-line {
					width: 66px;
					background-color: #278236;
				}
				.text {
					color: #278236;
				}
			}
		}
	}
}
</style>
