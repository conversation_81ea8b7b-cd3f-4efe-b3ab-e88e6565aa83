<template>
	<Card title="巡察结果运用" header size-type="3" style="height: auto">
		<div class="inspection-result">
			<div class="inspection-item" v-for="(ins, index) in dataSoure" :key="index">
				<div class="icon" :class="[`icon-${index + 2}`]"></div>
				<div class="right-content">
					<div class="i-index-box">
						<span class="inspection">{{ ins.socre }}</span>
						<span class="go-icon" @click="onModalShow(ins)"></span>
					</div>
					<div class="label-text">{{ ins.name }}</div>
				</div>
			</div>
		</div>
		<InspectionModal
			title="班子巡察扣分详情"
			:visible="visible"
			:on-cancel="onClose"
			:name="modalData.name"
			:socre="modalData.socre"
			:list="modalData.list"
			:type="1"
		></InspectionModal>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import InspectionModal from './InspectionModal.vue'
import { useRoute } from 'vue-router'
const route = useRoute()

import { getInspectionIndexOrgInspection } from '@/apis/inspection-index'
import { get } from 'node_modules/axios/index.d.cts'

const { org_id } = route.query

const visible = ref(false)
const dataSoure = ref([])
const modalData = ref<any>({})

const onModalShow = (ins: any) => {
	console.log(ins, '-')
	modalData.value = ins
	if (ins.list && ins.list.length > 0) visible.value = true
}
const onClose = () => {
	visible.value = false
}
const getDataList = async (org_id: any) => {
	const res = await getInspectionIndexOrgInspection({ org_id })
	if (res.code == 0 && res.data) {
		console.log(res.data, '数据')
		dataSoure.value = res.data
	}
}
getDataList(org_id)
</script>

<style lang="less" scoped>
.inspection-result {
	padding: 30px 0px;
	display: flex;
	gap: 0px 41px;
	width: 100%;

	.inspection-item {
		display: flex;
		align-items: center;
		flex: 1;
		.icon {
			margin-right: 16px;
			display: inline-block;
			width: 100px;
			height: 100px;
		}
		.icon-2 {
			background: url('../images/inspection-2.png') no-repeat center center / 100% 100%;
		}
		.icon-3 {
			background: url('../images/inspection-3.png') no-repeat center center / 100% 100%;
		}
		.icon-4 {
			background: url('../images/inspection-4.png') no-repeat center center / 100% 100%;
		}
		.icon-5 {
			background: url('../images/inspection-5.png') no-repeat center center / 100% 100%;
		}
		.right-content {
			.i-index-box {
				display: flex;
				align-items: center;
				.inspection {
					font-family: ArTarumianBakhum, ArTarumianBakhum;
					font-weight: 400;
					font-size: 36px;
					line-height: 1;
					color: #d33625;
					margin-right: 20px;
				}
				.go-icon {
					cursor: pointer;
					width: 32px;
					height: 32px;
					background: url('../images/inspection-1.png') no-repeat center center / 100% 100%;
				}
			}
			.label-text {
				margin-top: 8px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 22px;
				color: #3d3d3d;
				line-height: 26px;
			}
		}
	}
}
</style>
