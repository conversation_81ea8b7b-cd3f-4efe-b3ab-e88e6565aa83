<template>
	<Modal :title="null" :footer="null" :visible="visible" destroy-on-close @cancel="onCancel" :closable="false" class="conversation-modal">
		<div class="modal-content">
			<div class="modal-title">
				<div class="title">{{ title }}</div>
				<div class="close" @click="onCancel">x</div>
			</div>
			<div class="m-c-main">
				<div class="modal-sub-title">
					{{ name }} <span class="score">{{ socre }}</span>
				</div>
				<div class="mc-table-box">
					<PopTable :data-source="list" :columns="columns">
						<template v-slot:down="{ value }">
							<div class="table-tag-box">
								<PopTag v-if="value.a" :tag="{ label: 'A', count: value.a }" color="rgba(96, 202, 113, 1)" />
								<PopTag v-if="value.b" :tag="{ label: 'B', count: value.b }" color="rgba(255, 128, 0, 1)" />
								<PopTag v-if="value.c" :tag="{ label: 'C', count: value.c }" color="rgba(180, 105, 0, 1)" />
								<PopTag v-if="value.d" :tag="{ label: 'D', count: value.d }" color="rgba(230, 177, 39, 1)" />
							</div>
						</template>
						<template v-slot:same="{ value }">
							<div class="table-tag-box">
								<PopTag v-if="value.a" :tag="{ label: 'A', count: value.a }" color="rgba(96, 202, 113, 1)" />
								<PopTag v-if="value.b" :tag="{ label: 'B', count: value.b }" color="rgba(255, 128, 0, 1)" />
								<PopTag v-if="value.c" :tag="{ label: 'C', count: value.c }" color="rgba(180, 105, 0, 1)" />
								<PopTag v-if="value.d" :tag="{ label: 'D', count: value.d }" color="rgba(230, 177, 39, 1)" />
							</div>
						</template>
						<template v-slot:upp="{ value }">
							<div class="table-tag-box">
								<PopTag v-if="value.a" :tag="{ label: 'A', count: value.a }" color="rgba(96, 202, 113, 1)" />
								<PopTag v-if="value.b" :tag="{ label: 'B', count: value.b }" color="rgba(255, 128, 0, 1)" />
								<PopTag v-if="value.c" :tag="{ label: 'C', count: value.c }" color="rgba(180, 105, 0, 1)" />
								<PopTag v-if="value.d" :tag="{ label: 'D', count: value.d }" color="rgba(230, 177, 39, 1)" />
							</div>
						</template>
						<template v-slot:score="{ value }">
							<span class="score">{{ value }}</span>
						</template>
					</PopTable>
				</div>
			</div>
		</div>
	</Modal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { Modal } from 'ant-design-vue'
import PopTable from '@/components/Table.vue'
import { convertMap } from '@/utils/utils'
import PopTag from '@/components/Tag.vue'
defineProps({
	onCancel: Function as any,
	visible: { type: Boolean, default: false },
	socre: Number,
	list: {
		default: () => [],
	},
	title: String,
	name: String,
})

const columns = ref([
	{
		title: '巡察组',
		dataIndex: 'down',
		key: 'down',
		align: 'center',
		width: '30%',
	},
	{
		title: '班子成员',
		dataIndex: 'same',
		key: 'same',
		align: 'center',
		width: '20%',
	},
	{
		title: '其他干部',
		dataIndex: 'upp	',
		key: 'upp',
		align: 'center',
		width: '20%',
	},
])
</script>

<style lang="less">
.conversation-modal {
	width: 1462px !important;
	height: 602px;
	.ant-modal-body {
		padding: 0px;
	}
	.modal-content {
		.modal-title {
			padding: 12px 0px;
			display: flex;
			justify-content: center;
			position: relative;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
			.title {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 26px;
				color: #000000;
			}
			.close {
				position: absolute;
				top: 0px;
				right: 27px;
				font-size: 28px;
				cursor: pointer;
			}
		}
		.m-c-main {
			padding: 28px 24px;
			.modal-sub-title {
				display: flex;
				align-items: center;
				&::before {
					margin-right: 16px;
					content: '';
					display: inline-block;
					width: 16px;
					height: 16px;
					background: #ffa300;
					border-radius: 50%;
				}
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 24px;
				color: #3d3d3d;
				.score {
					margin-left: 36px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 32px;
					line-height: 1;
					color: #f02a2a;
				}
			}
			.mc-table-box {
				margin-top: 28px;
				.table-title {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 24px;
					color: #3d3d3d;
				}
				.table-box {
					margin-top: 10px;
					min-height: 400px;
					max-height: 657px;
				}
			}
		}
		.table-tag-box {
			display: flex;
			flex-wrap: wrap;
			gap: 22px 57px;
		}
	}
	.align-right {
		text-align: left;
	}
	.red {
		color: rgba(240, 42, 42, 1);
	}
}
</style>
