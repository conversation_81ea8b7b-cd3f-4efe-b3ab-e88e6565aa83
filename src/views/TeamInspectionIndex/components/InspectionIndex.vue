<template>
	<Card title="班子成员巡察指数" header size-type="3" style="height: auto">
		<div class="inspection-index">
			<div class="sub-box">
				<CircleTitle title="党政主要领导" />
				<div class="flex m-top-16">
					<div class="base_info h-260" v-if="data.one">
						<CodeAvatar class="avatar" :head_url="data.one?.head_url" @click="toPersonInspection(data.one?.user_id)" />
						<div :class="`info info-${getClassName1(data.one?.inspection_index)}`">
							<div class="name" @click="toPersonInspection(data.one?.user_id)">{{ data.one?.username }}</div>
							<div class="position m-top-14">{{ data.one?.current_job }}</div>
							<div class="xuncha-index-box m-top-45">
								<div class="xc-index">
									<div class="xuncha-title">巡察指数</div>
									<div class="same-xu" @click="onSequenceModal({ user_id: data.one?.user_id })">
										{{ data.one?.in_org_flag === 0 ? '同序列' : '班子内' }} &nbsp;{{ data.one?.inspection_index_rank }}
									</div>
									<div class="dash-line"></div>
									<div class="xuncha-index">{{ data.one?.inspection_index }}</div>
								</div>
							</div>
							<div class="bottom m-top-11">
								<div class="progress" v-if="data.one?.inspection_index" :style="`width:${100 + data.one?.inspection_index}%`"></div>
							</div>
						</div>
					</div>
					<div class="base_info h-260" v-if="data.two">
						<CodeAvatar class="avatar" :head_url="data.two?.head_url" @click="toPersonInspection(data.two?.user_id)" />
						<div :class="`info info-${getClassName1(data.two?.inspection_index)}`">
							<div class="name" @click="toPersonInspection(data.two?.user_id)">{{ data.two?.username }}</div>
							<div class="position m-top-14">{{ data.two?.current_job }}</div>
							<div class="xuncha-index-box m-top-45">
								<div class="xc-index">
									<div class="xuncha-title">巡察指数</div>
									<div
										class="same-xu"
										@click="onSequenceModal({ user_id: data.two?.user_id, title: (data.two?.in_org_flag === 0 ? '同序列' : '班子内') + '排名' })"
									>
										{{ data.two?.in_org_flag === 0 ? '同序列' : '班子内' }}&nbsp;{{ data.two?.inspection_index_rank }}
									</div>
									<div class="dash-line"></div>
									<div class="xuncha-index">{{ data.two?.inspection_index || '-' }}</div>
								</div>
							</div>
							<div class="bottom m-top-11">
								<div class="progress" v-if="data.two?.inspection_index" :style="`width:${100 + data.two?.inspection_index}%`"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="sub-box m-top-20">
				<CircleTitle title="其他班子成员巡察指数" />
				<div class="legend-box">
					<ColorIndexDesc :data="legendData" />
				</div>
				<div class="charts-box">
					<Echarts$4 :option="option" ref="charts" />
				</div>
			</div>
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import CircleTitle from './CircleTitle.vue'
import Echarts$4 from '@/components/Echarts$4.vue'
import ColorIndexDesc from '@/views/InspectionIndex/components/ColorIndexDesc.vue'
import { convertMap, getQueryVariable, convertPxToRem } from '@/utils/utils'
import { getPatrolMainUser } from '@/apis/statistics'
import { getOrgUserPatrolIndex } from '@/apis/inspection-index'
import { useRouter } from 'vue-router'
import { SequenceModal } from '@/views/DataScreen/Components/SequenceModal'
const router = useRouter()

const { org_id } = getQueryVariable()

const data = ref({
	one: {},
	two: undefined,
	other: [],
})

const legendData = [
	{
		color: 'rgba(96, 202, 113, 1)',
		label: '-10（含）以上',
	},
	{
		color: 'rgba(255, 128, 0, 1)',
		label: '-15（含）~-10',
	},
	{
		color: 'rgba(180, 105, 0, 1)',
		label: '-15以下',
	},
]
const getColor = (value: number) => {
	if (value >= -10) {
		return 'rgba(96, 202, 113, 1)'
	} else if (value >= -15) {
		return 'rgba(255, 128, 0, 1)'
	} else {
		// 棕色
		return 'rgba(180, 105, 0, 1)'
	}
}

const option = ref({})

const toPersonInspection = (user_id: any) => {
	router.push(`/inspection-index?user_id=${user_id}`)
}

const onSequenceModal = ({ org_id, user_id, title }: { org_id?: string; user_id?: string; title?: string }) => {
	SequenceModal({
		org_id: org_id,
		user_id: user_id,
		title: title,
	})
}
const getClassName1 = (value: number) => {
	if (value >= -10) {
		return 'green'
	} else if (value >= -20) {
		return 'orange'
	} else {
		// 棕色
		return 'brown'
	}
}
const initOption = (data: any) => {
	const baseData: any = []
	const xlabel: any = []
	data.sort((a, b) => b.inspection_index - a.inspection_index)
	console.log('🚀 ~ initOption ~ data:', data)

	data.map((item: any, index: number) => {
		baseData.push([index, item.inspection_index, item.user_id])
		xlabel.push(item.username)
	})

	const datas = [
		{
			type: 'bar',
			barWidth: baseData.length > 5 ? '64px' : '20%',
			itemStyle: {
				color: (params: any) => {
					return getColor(params.value[1])
				},
			},
			datasetIndex: 0,
			data: baseData,
			label: {
				show: true,
				position: 'top',
				color: '#333333',
				fontSize: convertPxToRem(16),
			},
		},
	]
	option.value = {
		grid: {
			left: '1%',
			top: datas.length > 11 ? '23%' : '13%',
			bottom: '0%',
			right: '3%',
			containLabel: true,
		},
		legend: {
			show: true,
			top: 0,
			left: 'center',
			// type: 'scroll',
			// data: Line,
			icon: 'circle',
			itemWidth: convertPxToRem(12),
			itemHeight: convertPxToRem(12),
			textStyle: {
				color: '#333333',
				fontSize: convertPxToRem(18),
			},
			itemStyle: {
				borderWidth: convertPxToRem(4),
			},
			itemGap: convertPxToRem(28),
			data: [
				{
					name: '-10（含）以上绿色',
					color: 'rgba(96, 202, 113, 1)',
				},
				{
					name: '-15（含）~-10',
					color: 'rgba(255, 128, 0, 1)',
				},
				{
					name: '-15以下',
					color: 'rgba(180, 105, 0, 1)',
				},
			],
		},
		yAxis: {
			type: 'value',
			position: 'left',
			// scale: true,
			min: (value: any) => value.min - 2,
			max: (value: any) => 0,
			nameTextStyle: {
				color: '#00FFFF',
			},
			splitLine: {
				lineStyle: {
					type: 'dashed',
					color: '#EEEEEE',
				},
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: '#333333',
				},
				symbol: ['none', 'arrow'],
				symbolOffset: 7,
				symbolSize: [7, 10],
			},
			axisTick: {
				show: false,
			},
			// splitNumber: 4,
			// interval: 1,
			axisLabel: {
				show: true,
				color: '#666666',
				fontSize: convertPxToRem(16),
				formatter: (value: any) => {
					value = Number(value)
					return value > 100 ? '' : value.toFixed(2)
				},
			},
		},
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
					onZero: false,
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					inside: false,
					textStyle: {
						color: '#666666', // x轴颜色
						fontWeight: 'normal',
						fontSize: convertPxToRem(16),
						lineHeight: convertPxToRem(22),
					},
					color: '#666666', // x轴颜色
					fontWeight: 'normal',
					fontSize: convertPxToRem(16),
					lineHeight: convertPxToRem(22),
					interval: 0,
				},
				data: xlabel,
			},
		],
		series: datas,
	}
}
const loadData = async () => {
	const res = await getOrgUserPatrolIndex({ org_id })

	if (res.code === 0) {
		data.value = res.data
		const { other } = res.data
		initOption(other)
	}
}

loadData()

const charts = ref()
onMounted(() => {
	charts.value.instance.on('click', (params: any) => {
		router.push(`/inspection-index?user_id=${params.value[2]}`)
	})
})
</script>

<style lang="less" scoped>
.flex {
	display: flex;
}
.inspection-index {
	.base_info {
		// flex: 1;
		width: 50%;
		height: 100%;
		display: flex;
		padding: 20px 24px;
		border-radius: 8px 8px 8px 8px;
		.avatar {
			width: 144px;
			height: 190px;
			cursor: pointer;
		}
		.info {
			flex: 1;
			margin-left: 24px;
			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 32px;
				color: #000000;
				line-height: 38px;
				cursor: pointer;
			}
			.position {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24px;
				line-height: 1;
				color: #000000;
				// 三行
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 3;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
		.info-green {
			.same-xu {
				border: 1px solid #60ca71;

				color: #60ca71;
			}
			.dash-line {
				border-top: 1px dashed rgba(96, 202, 113, 1);
			}
			.xuncha-index {
				color: #60ca71;
			}
			.bottom {
				background: #f5f5f5;

				.progress {
					background: #60ca71;
				}
			}
		}
		.info-orange {
			.same-xu {
				border: 1px solid rgba(255, 128, 0, 1);

				color: rgba(255, 128, 0, 1);
			}
			.dash-line {
				border-top: 1px dashed rgba(255, 128, 0, 1);
			}
			.xuncha-index {
				color: rgba(255, 128, 0, 1);
			}
			.bottom {
				background: #f5f5f5;

				.progress {
					background: rgba(255, 128, 0, 1);
				}
			}
		}
		.info-brown {
			.same-xu {
				border: 1px solid rgba(180, 105, 0, 1);
				color: rgba(180, 105, 0, 1);
			}
			.dash-line {
				border-top: 1px dashed rgba(180, 105, 0, 1);
			}
			.xuncha-index {
				color: rgba(180, 105, 0, 1);
			}
			.bottom {
				background: #f5f5f5;

				.progress {
					background: rgba(180, 105, 0, 1);
				}
			}
		}
		.xuncha-index-box {
			margin-top: 24px;
			.xc-index {
				display: flex;
				align-items: center;
				.xuncha-title {
					margin-right: 15px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 20px;
					color: #333333;
					line-height: 23px;
				}
				.same-xu {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 108px;
					height: 36px;
					border-radius: 2px 2px 2px 2px;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 18px;
					line-height: 20px;
				}
				.dash-line {
					flex: 1;
					margin: 0px 20px;
				}
				.xuncha-index {
					font-family: ArTarumianBakhum, ArTarumianBakhum;
					font-weight: 400;
					font-size: 52px;
					line-height: 1;
				}
			}
		}

		.bottom {
			width: 100%;
			height: 12px;
			background: #f5f5f5;
			border-radius: 30px 30px 30px 30px;
			opacity: 1;
			overflow: hidden;
			.progress {
				height: 12px;
				width: 50%;
				border-radius: 30px 30px 30px 30px;
				opacity: 1;
			}
		}
	}
	.sub-box {
		padding: 24px 20px;
		background: #f7fbff;
		.legend-box {
			display: flex;
			justify-content: center;
		}
	}
	.charts-box {
		height: 283px;
	}
}
</style>
