<template>
	<Card title="班子巡察指数" header class="h-455">
		<div class="org-base-info" :class="getClassName1(data?.inspection_index)">
			<div class="inner-box">
				<div class="dso-l-main">
					<div class="dso-lm-left">
						<div class="dso-lml-title">
							班子巡察指数
							<span class="ds-icon"></span>
						</div>
						<div class="main-content">
							<div class="top-icon"></div>
							<div class="text">{{ data?.inspection_index || '-' }}</div>
							<div class="platform-icon"></div>
						</div>
					</div>
					<!-- <div class="dso-lm-right">
					<div class="dso-lml-title">同序列</div>
					<div class="main-content">
						<div class="top-icon"></div>
						<div class="text">{{ data.inspection_index_rank }}</div>
					</div>
				</div> -->
				</div>
				<ColorIndexDesc :data="colorList" />
				<div :class="`level-icon-box level-icon-box-${getClassName1(data?.inspection_index)}`"></div>
			</div>
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getOrgDetails } from '@/apis/data-screen'
import { getQueryVariable } from '@/utils/utils'
import ColorIndexDesc from '@/views/InspectionIndex/components/ColorIndexDesc.vue'
const { org_id } = getQueryVariable()

const colorList = [
	{
		color: 'rgba(96, 202, 113, 1)',
		label: '-10（含）以上',
	},
	{
		color: 'rgba(255, 128, 0, 1)',
		label: '-20（含）~-10',
	},
	{
		color: 'rgba(180, 105, 0, 1)',
		label: '-20以下',
	},
]

defineProps({
	data: Object as any,
})
const getClassName1 = (value: number) => {
	if (value >= -10) {
		return 'green'
	} else if (value >= -20) {
		return 'orange'
	} else {
		// 棕色
		return 'brown'
	}
}

function determineColor(value: number) {
	const colorMap = {
		color: '',
		backgroundColor: '',
	}

	if (value >= -10) {
		colorMap.color = 'rgba(96, 202, 113, 1)'
		colorMap.backgroundColor = 'linear-gradient(270deg, #EAFFEB 0%, #FFFFFF 100%);'
	} else if (value >= -15) {
		colorMap.color = 'rgba(255, 128, 0, 1)'
		colorMap.backgroundColor = 'linear-gradient( 270deg,#FFF6ED 0%, #FFFFFF 100%);'
	} else {
		colorMap.color = 'rgba(180, 105, 0, 1)'
		colorMap.backgroundColor = 'linear-gradient( 270deg, #F6EDE0 0%, rgba(255,255,255,0) 100%);'
	}
	return colorMap
}
const getClassName = (value: number) => {
	if (value >= -10) {
		return 'green'
	} else if (value >= -15) {
		return 'orange'
	} else {
		// 棕色
		return 'brown'
	}
}
</script>

<style lang="less" scoped>
.org-base-info {
	padding: 24px 36px;
	width: 100%;
	display: flex;
	flex-direction: column;
	// background: linear-gradient(180deg, #e6ffe7 0%, #f6fff6 15%, #ffffff 100%);
	.org-name {
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 32px;
		color: #000000;
		line-height: 38px;
	}
	.inner-box {
		display: flex;
		align-items: center;
		flex: 1;
		.dso-l-main {
			margin-right: 52px;
			padding: 0px 24px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.top-icon {
				margin-top: 16px;
				width: 10px;
				height: 6px;
				background: url('../images/detail-5.png') no-repeat center / 100% 100%;
			}
			.text {
				margin-top: 15px;
			}
			.dso-lml-title {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 40px;
				background: url('../images/detail-2.png') no-repeat center / 100% 100%;

				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 16px;
				color: rgba(0, 0, 0, 0.9);
				line-height: 19px;
				.ds-icon {
					width: 24px;
					height: 24px;
					background: url('../images/detail-1.png') no-repeat center / 100% 100%;
				}
			}
			.dso-lm-left {
				width: 288px;
				height: 286px;
				margin-right: 22px;
				display: flex;
				flex-direction: column;
				align-items: center;
				.dso-lml-title {
					width: calc(100% - 30px);
					height: 40px;
					flex-shrink: 0;
				}
				.main-content {
					flex: 1;
					font-family: ArTarumianBakhum, ArTarumianBakhum;
					font-weight: 400;
					font-size: 40px;
					color: #60ca71;
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 100%;
					position: relative;
					.platform-icon {
						position: absolute;
						bottom: 0px;
						height: 146px;
						width: 100%;
						background: url('../images/detail-4.png') no-repeat center / contain;
					}
				}
			}
			.dso-lm-right {
				width: 210px;
				height: 280px;
				display: flex;
				flex-direction: column;
				.main-content {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					background: url('../images/detail-3.png') no-repeat center / 100% 100%;
					font-family: ArTarumianBakhum, ArTarumianBakhum;
					font-weight: 400;
					font-size: 40px;
					color: #60ca71;
				}
			}
		}
		.level-icon-box {
			margin-left: auto;
			width: 213px;
			height: 213px;
			background: no-repeat center center / 100% 100%;

			&-green {
				background-image: url('../images/level-icon-1.png');
			}
			&-orange {
				background-image: url('../images/level-icon-2.png');
			}
			&-brown {
				background-image: url('../images/level-icon-3.png');
			}
		}
		:deep(.color-index-desc) {
			gap: 30px 0px;
			display: flex;
			flex-direction: column;
		}
	}
}
.orange {
	// background: linear-gradient(180deg, rgba(255, 248, 241, 1) 0%, rgba(255, 248, 241, 1) 15%, #ffffff 100%) !important;
	border-bottom-color: #ff8000 !important;
	.header-box {
		background-image: url('../images/orange-2.png') !important;
	}
	.dso-lml-title {
		background-image: url('../images/orange-4.png') !important;
	}
	.top-icon {
		background-image: url('../images/orange-5.png') !important;
	}
	.text {
		color: #ff8000;
	}
	.platform-icon {
		background-image: url('../images/orange-3.png') !important;
	}
	.dso-lm-right {
		.main-content {
			background-image: url('../images/orange-1.png') !important;
		}
	}
}
.brown {
	// background: linear-gradient(180deg, #f9f2e8 0%, #f9f2e8 15%, #ffffff 100%) !important;
	border-bottom-color: #b46900 !important;
	.header-box {
		background-image: url('../images/brown-2.png') !important;
	}
	.dso-lml-title {
		background-image: url('../images/brown-4.png') !important;
	}
	.top-icon {
		background-image: url('../images/brown-5.png') !important;
	}
	.text {
		color: #b46900;
	}
	.platform-icon {
		background-image: url('../images/brown-3.png') !important;
	}
	.dso-lm-right {
		.main-content {
			background-image: url('../images/brown-1.png') !important;
		}
	}
}
</style>
