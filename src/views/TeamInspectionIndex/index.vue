<template>
	<Layout back title="班子巡察指数">
		<div class="team-inspection-index">
			<div class="org-name-box">{{ baseInfo.org_name }}</div>
			<div class="org-box">
				<OrgBaseInfo class="flex-1" :data="baseInfo" />
				<LeaderIndexCharts class="flex-1" :data="baseInfo" />
			</div>
			<PoliticalPortrait class="m-top-16" />
			<InspectionResult class="m-top-16" />
			<InspectionIndex class="m-top-16" />
			<EvaluationComparison class="m-top-16" />
			<ConversationLevel class="m-top-16" />
		</div>
	</Layout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Layout from '@/layout/index.vue'
import LeaderIndexCharts from './components/LeaderIndexCharts.vue'
import PoliticalPortrait from './components/PoliticalPortrait.vue'
import EvaluationComparison from './components/EvaluationComparison.vue'
import ConversationLevel from './components/Conversationlevel.vue'
import OrgBaseInfo from './components/OrgBaseInfo.vue'
import InspectionResult from './components/InspectionResult.vue'
import InspectionIndex from './components/InspectionIndex.vue'

import { getOrgInspectionList } from '@/apis/data-screen'

import { getQueryVariable } from '@/utils/utils'
const { org_id } = getQueryVariable()

const baseInfo = ref({
	org_id: undefined,
	org_name: '',
	inspection_index: undefined,
	pyramid_index: undefined,
	org_list: [],
})

const loadInspection = async () => {
	const res = await getOrgInspectionList({ org_id })
	if (res.code === 0) {
		baseInfo.value = res.data
	}
}

loadInspection()
</script>

<style lang="less" scoped>
.team-inspection-index {
	padding: 16px;
	overflow: auto;
	height: 100%;
	&::-webkit-scrollbar {
		display: none;
	}
	.org-name-box {
		width: 100%;
		height: 96px;
		background: linear-gradient(0deg, #ffffff 0%, #c8f1cd 100%);
		box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
		border-radius: 8px 8px 8px 8px;

		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 34px;
		color: #000000;
		line-height: 34px;

		display: flex;
		align-items: center;
		justify-content: center;
	}
	.org-box {
		margin-top: 16px;
		display: flex;
		gap: 0px 16px;
		.flex-1 {
			flex: 1;
		}
	}
}
</style>
