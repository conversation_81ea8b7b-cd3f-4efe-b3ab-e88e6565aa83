<template>
	<Layout back title="班子巡察指数">
		<div class="team-inspection-index">
			<LeaderIndexCharts />
			<TeamInspection />
			<Leader />
			<TeamMember />
			<EvaluationComparison />
			<ConversationLevel />
		</div>
	</Layout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Layout from '@/layout/index.vue'
import LeaderIndexCharts from './components/LeaderIndexCharts.vue'
import TeamInspection from './components/TeamInspection.vue'
import Leader from './components/Leader.vue'
import TeamMember from './components/TeamMember.vue'
import EvaluationComparison from './components/EvaluationComparison.vue'
import ConversationLevel from './components/Conversationlevel.vue'
</script>

<style lang="less" scoped>
.team-inspection-index {
	padding: 16px;
	overflow: auto;
	height: 100%;
	&::-webkit-scrollbar {
		display: none;
	}
}
</style>
