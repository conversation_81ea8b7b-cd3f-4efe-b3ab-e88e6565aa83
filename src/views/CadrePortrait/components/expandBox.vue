<template>
	<div class="content-item report-box">
		<div class="r-b-box flex">
			<div class="pre_fix">
				<span class="text-block">{{ title }}</span>
			</div>
			<div class="expand-icon" :class="{ is_expand: expandStatus }" v-if="expandIcon" @click="onExpand">
				<img :src="ExpandIcon" alt="" />
				<span>{{ expandStatus ? '收起' : '展开' }}</span>
			</div>
		</div>
		<div class="text" ref="textBox" :class="{ text_no_expand: expandIcon && !expandStatus }">
			<slot>{{ text }}</slot>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { nextTick, ref, watch } from 'vue'
import ExpandIcon from '../images/expand.png'
const props = defineProps({
	text: String,
	title: String,
})

const expandStatus = ref(false)
const textBox = ref()
const expandIcon = ref(false)
const onExpand = () => {
	expandStatus.value = !expandStatus.value
}

function getLineCount(element) {
	// 获取元素的总高度
	const totalHeight = element.clientHeight
	// 获取元素中一行文本的高度
	const lineHeight = parseFloat(getComputedStyle(element).lineHeight)
	// 计算总行数
	const lineCount = Math.round(totalHeight / lineHeight)
	return lineCount
}

watch(
	() => [props.text, textBox.value],
	() => {
		if (textBox.value) {
			nextTick(() => {
				console.log(getLineCount(textBox.value), getLineCount(textBox.value) > 2)
				expandIcon.value = getLineCount(textBox.value) > 2
			})
		}
	}
)
</script>

<style lang="scss" scoped>
.report-box {
	width: 100%;

	.r-b-box {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
}

.content-item {
	.pre_fix {
		margin-right: 15px;
		flex-shrink: 0;
		font-size: 22px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.95);
		line-height: 39px;
		.text-block {
			display: inline-block;
			min-width: 90px;
			width: fit-content;
			text-align-last: justify;
		}
		&::before {
			margin-right: 9px;
			content: '';
			display: inline-block;
			width: 12px;
			height: 12px;
			background: #ec4224;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border-radius: 50%;
		}
	}
	.expand-icon {
		display: flex;
		align-items: center;
		cursor: pointer;
		img {
			margin-right: 8px;
			width: 32px;
			height: 32px;
			vertical-align: middle;
			transform: rotateZ(180deg);
		}
		span {
			font-size: 22px;
			line-height: 22px;
			color: #ee391f;
		}
	}
	.is_expand {
		img {
			transform: rotateZ(0deg);
		}
	}
	.c-i-content {
		padding-left: 20px;
		.tag-box {
			display: flex;
			gap: 0px 10px;
			.tag-item {
				padding: 5px 18px;
				min-width: 80px;
				text-align: center;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 22px;
				color: #000000;
				line-height: 22px;
				border: 1px solid #d9d9d9;
			}
			.tag-item-active {
				background: #097afb;
				color: #ffffff;
				border-color: #097afb;
			}
		}
	}
	.cic-desc {
		padding-left: 0px !important;
	}
	.text {
		margin-top: 10px;
		padding-left: 20px;
		font-size: 20px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.95);
		line-height: 26px;
	}

	.text_no_expand {
		// 两行
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
}
</style>
