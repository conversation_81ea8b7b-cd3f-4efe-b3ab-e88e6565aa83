<template>
	<div class="personal">
		<div class="title">{{ user.name }}</div>
		<div class="radio-box">
			<div :class="`radio ${item.key === activeKey ? 'active' : ''}`" @click="onActive(item.key)" v-for="(item, index) in radio" :key="item.key">
				{{ item.label }}
			</div>
		</div>
		<div class="table-box">
			<div v-if="activeKey === '1'">
				<a-table :columns="columns1" bordered :data-source="state1" :pagination="false" />
			</div>
			<!-- <div v-if="activeKey === '2'">
				<a-table :columns="columns2" bordered :data-source="state2" :pagination="false" />
			</div> -->
			<div v-if="activeKey === '3'">
				<!--新内容还没写 
				<a-table :columns="columns2" bordered :data-source="state2" :pagination="false" /> -->
			</div>
			<div v-if="activeKey === '4'">
				<!--新内容还没写 
				<a-table :columns="columns2" bordered :data-source="state2" :pagination="false" /> -->
			</div>
			<div v-if="activeKey === '5'" class="matters">
				<div class="container">
					<div class="content">
						<div class="card">
							<div class="info-title">婚姻状况</div>
							<div class="table">
								<a-table :columns="columnsMatters1" bordered :data-source="state.marriages" :pagination="false" />
							</div>
						</div>
						<div class="card m-top-72">
							<div class="info-title">出国（境）证件记录</div>
							<div class="table">
								<a-table :columns="columnsMatters2" bordered :data-source="state.certs" :pagination="false" />
							</div>
						</div>
						<div class="card m-top-72">
							<div class="info-title">兼职情况</div>
							<div class="table">
								<!--新内容还没写
								 <a-table :columns="columnsMatters3" bordered :data-source="state.partTime" :pagination="false" /> -->
							</div>
						</div>
						<!-- <div class="card m-top-72">
							<div class="info-title">征求意见</div>
							<div class="table">
								<a-table :columns="columnsMatters3" bordered :data-source="state3" :pagination="false" />
							</div>
						</div> -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { complaintReport, getPeronelMatter, getSupervision, mattersCheck } from '@/apis/cadre-portrait/home'
import { ref } from 'vue'

const props = defineProps({
	user: {
		type: Object,
		default: () => ({}),
		required: true,
	},
})

const radio = [
	{
		label: '信访举报情况',
		key: '1',
	},
	// {
	// 	label: '个人事项核查报告',
	// 	key: '2',
	// },
	{
		label: '履职负面清单', //新增的需求。pc 端还没写，暂时先展示出来
		key: '3',
	},
	{
		label: '重大事项报告', //新增的需求。pc 端还没写，暂时先展示出来
		key: '4',
	},
	{
		label: '其他监督',
		key: '5',
	},
]

const columns1 = [
	{
		dataIndex: 'report_date',
		key: 'report_date',
		title: '信访日期',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'register_org',
		key: 'register_org',
		title: '登记机构',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'register_reason',
		key: 'register_reason',
		title: '信访目的',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'register_form',
		key: 'register_form',
		title: '信访形式',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'register_count',
		key: 'register_count',
		title: '信访人数',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'jointly',
		key: 'jointly',
		title: '是否联名',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'content',
		key: 'content',
		title: '概况信息',
		width: '30%',
		align: 'center',
	},
]

const columns2 = [
	{
		dataIndex: 'check_date',
		key: 'check_date',
		title: '查核时间',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'job',
		key: 'job',
		title: '时任职务或职级',
		width: '30%',
		align: 'center',
	},
	{
		dataIndex: 'type',
		key: 'type',
		title: '查核类型',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'result',
		key: 'result',
		title: '查核结果',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'explains',
		key: 'explains',
		title: '本人说明',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'opinion',
		key: 'opinion',
		title: '认定意见',
		width: '10%',
		align: 'center',
	},
]

const columnsMatters1 = [
	{
		dataIndex: 'native_place',
		key: 'native_place',
		title: '籍贯',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'position',
		key: 'position',
		title: '现任职务',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'birthday',
		key: 'birthday',
		title: '出生年月',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'content',
		key: 'content',
		title: '婚姻状况',
		align: 'center',
		width: '20%',
	},
]

const columnsMatters2 = [
	{
		dataIndex: 'cert_name',
		key: 'cert_name',
		title: '证件名称',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'cert_number',
		key: 'cert_number',
		title: '证件号码',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'cert_validity',
		key: 'cert_validity',
		title: '证件有效期',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'situation',
		key: 'situation',
		title: '使用情况',
		align: 'center',
		width: '20%',
	},
]
const columnsMatters3 = [
	{
		dataIndex: 'supervision_date',
		key: 'supervision_date',
		title: '征求意见时间',
		align: 'center',
		width: '10%',
	},
	{
		dataIndex: 'year',
		key: 'year',
		title: '年度',
		align: 'center',
		width: '10%',
	},
	{
		dataIndex: 'job',
		key: 'job',
		title: '时任职务',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'consultation_org',
		key: 'consultation_org',
		title: '征求意见单位',
		align: 'center',
		width: '10%',
	},
	{
		dataIndex: 'type',
		key: 'type',
		title: '类型',
		align: 'center',
		width: '10%',
	},
	{
		dataIndex: 'condition',
		key: 'condition',
		title: '反馈意见情况',
		align: 'center',
		width: '20%',
	},
]
const state = ref({
	marriages: [],
	certs: [],
})
const state1 = ref([])

const state2 = ref([])

const state3 = ref([])

// 组件实例
const componentInstance = ref()

const activeKey = ref(radio[0].key)

const onActive = (key: any) => {
	activeKey.value = key
}

const initData = async () => {
	const { user_id } = props.user
	const res = await getPeronelMatter(user_id)
	if (res.code === 0) {
		state.value = res.data
	}
}

const initData1 = async () => {
	const { user_id } = props.user
	const res = await complaintReport(user_id)
	if (res.code === 0) {
		state1.value = res.data
	}
}

const initData2 = async () => {
	const { user_id } = props.user
	const res = await mattersCheck(user_id)
	if (res.code === 0) {
		state2.value = res.data
	}
}
const initData3 = async () => {
	const { user_id } = props.user
	const res = await getSupervision(user_id)
	if (res.code === 0) {
		state3.value = res.data
	}
}

initData()
initData1()
initData2()
initData3()
</script>

<style lang="less" scoped>
.personal {
	padding: 36px 50px 36px 70px;
	width: 100%;
	height: 100%;
	background: #ffffff;
	overflow: auto;

	.title {
		font-size: 28px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 600;
		color: #000000;
		line-height: 33px;
	}
	.radio-box {
		margin-top: 38px;
		display: flex;
		.radio {
			border: 1px solid rgba(229, 37, 27, 0.4);
			margin-right: 34px;
			padding: 8px 10px;
			background: rgba(229, 37, 27, 0.03);
			border-radius: 4px;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #e5251b;
			line-height: 23px;
		}
		.active {
			background: #e5251b;
			color: #ffffff;
		}
	}
	.table-box {
		margin-top: 24px;
		.matters {
			.container {
				padding: 16px 0px;
				flex: 1;
				width: 100%;
				.content {
					padding: 0px 0px;
					width: 100%;
					height: 100%;
					overflow-y: auto;
					background-color: #ffffff;
					.card {
						.info-title {
							display: flex;
							align-items: center;
							font-size: 22px;
							font-family: Source Han Sans CN-Medium, Source Han Sans CN;
							font-weight: 500;
							color: #000000;
							line-height: 22px;
							&::before {
								margin-right: 10px;
								content: '';
								display: inline-block;
								width: 12px;
								height: 12px;
								background-color: #ec4224;
								border-radius: 50%;
							}
						}
						.table {
							margin-top: 17px;
						}
					}
				}
			}
		}
	}
}

:deep(.ant-table-container) {
	.ant-table-cell {
		font-size: 20px !important;
	}
	.ant-table-tbody {
		.ant-table-cell {
			font-size: 18px !important;
		}
	}
}
</style>
