<template>
	<div class="matters">
		<div class="title">{{ user.name }}</div>
		<div class="container">
			<div class="content">
				<div class="card">
					<div class="info-title">婚姻状况</div>
					<div class="table">
						<p-table :columns="columns1" :data-source="state.marriages" />
					</div>
				</div>
				<div class="card margin-top-72">
					<div class="info-title">出国（境）证件记录</div>
					<div class="table">
						<p-table :columns="columns2" :data-source="state.certs" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import PTable from '@/components/Table.vue'

import { getPeronelMatter } from '@/apis/cadre-portrait/home'

const props = defineProps({
	user: {
		type: Object,
		default: () => ({}),
		required: true,
	},
})

const state = ref({
	marriages: [],
	certs: [],
})

const columns1 = [
	{
		dataIndex: 'native_place',
		key: 'native_place',
		title: '籍贯',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'position',
		key: 'position',
		title: '现任职务',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'birthday',
		key: 'birthday',
		title: '出生年月',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'content',
		key: 'content',
		title: '婚姻状况',
		align: 'center',
		width: '20%',
	},
]

const columns2 = [
	{
		dataIndex: 'cert_name',
		key: 'cert_name',
		title: '证件名称',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'cert_number',
		key: 'cert_number',
		title: '证件号码',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'cert_validity',
		key: 'cert_validity',
		title: '证件有效期',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'situation',
		key: 'situation',
		title: '使用情况',
		align: 'center',
		width: '20%',
	},
]

const initData = async () => {
	const { user_id } = props.user
	const res = await getPeronelMatter(user_id)
	if (res.code === 0) {
		state.value = res.data
	}
}

initData()
</script>

<style scoped lang="less">
.matters {
	padding: 32px 16px;
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	background-color: #f6f8fc;
	.title {
		display: flex;
		align-items: center;
		font-size: 30px;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		line-height: 38px;
		&::before {
			content: '';
			margin-right: 7px;
			display: inline-block;
			width: 10px;
			height: 35px;
			background: url('@/assets/images/rect.png') no-repeat center / cover;
		}
	}
	.container {
		padding: 16px;
		flex: 1;
		width: 100%;
		.content {
			padding: 42px 37px;
			width: 100%;
			height: 100%;
			overflow-y: auto;
			background-color: #ffffff;
			.card {
				.info-title {
					display: flex;
					align-items: center;
					font-size: 22px;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 500;
					color: #000000;
					line-height: 22px;
					&::before {
						margin-right: 10px;
						content: '';
						display: inline-block;
						width: 12px;
						height: 12px;
						background-color: #ec4224;
						border-radius: 50%;
					}
				}
				.table {
					margin-top: 14px;
				}
			}
		}
	}
}
.margin-top-72 {
	margin-top: 72px;
}
</style>
