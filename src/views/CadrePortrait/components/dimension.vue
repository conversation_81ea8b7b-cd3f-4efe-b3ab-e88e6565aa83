<template>
	<div class="dimension">
		<div class="title">{{ user.name }}</div>
		<div class="content">
			<div class="label">标注信息（限200字）</div>
			<div class="input-box">
				<a-textarea :rows="7" :maxLength="200" v-model:value="textareValue"></a-textarea>
			</div>
			<div class="button-box">
				<button html-type="cancel" @click="onCancel">取消</button>
				<button html-type="save" @click="onSave">保存</button>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, toRefs, unref } from 'vue'
import { addCallout, getCalloutDetail, updateCallout } from '@/apis/user-center'
import { getUserInfoItem } from '@/utils/utils'

const emits = defineEmits(['save', 'close'])

const uid = getUserInfoItem('_uid')

const props = defineProps({
	user: {
		type: Object,
		default: () => ({}),
		required: true,
	},
})

const recordData = ref<any>({})

const isEditor = ref(false)
// 是否提交
const submiting = ref(false)

const textareValue = ref('')

const onSave = async () => {
	const _user = props.user

	if (submiting.value) return

	submiting.value = true
	try {
		if (unref(isEditor)) {
			await updateCallout(recordData.value.calloutId, textareValue.value)
		} else {
			await addCallout(_user.user_id, _user.name, uid, textareValue.value)
		}
	} catch (err) {
		submiting.value = false
	}

	emits('close')
}
const onCancel = () => {
	emits('close')
}
// 编辑初始化信息
const initData = async () => {
	const res = await getCalloutDetail(uid, props.user.user_id)
	if (res.data.data) {
		recordData.value = res.data

		textareValue.value = res.data.data

		isEditor.value = true
	}
}

initData()
</script>

<style lang="scss" scoped>
.dimension {
	padding: 50px 56px;
	width: 100%;
	height: 100%;
	background: #ffffff;
	.title {
		display: flex;
		align-items: center;
		font-size: 32px;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		line-height: 38px;
		&::before {
			content: '';
			margin-right: 7px;
			display: inline-block;
			width: 10px;
			height: 35px;
			background: url('@/assets/images/rect.png') no-repeat center / cover;
		}
	}
	.content {
		margin-top: 50px;
		.label {
			font-size: 28px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: rgba(0, 0, 0, 0.9);
			line-height: 33px;
		}
		.input-box {
			margin-top: 17px;
		}
	}
	.button-box {
		margin-top: 28px;
		display: flex;
		justify-content: flex-end;
		button {
			width: 134px;
			height: 62px;
			border-radius: 4px 4px 4px 4px;
			opacity: 1;
			font-size: 26px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
		}
		button[html-type='cancel'] {
			margin-right: 20px;
			background: #eeeeee;
			color: rgba(0, 0, 0, 0.9);
		}
		button[html-type='save'] {
			background: #e5251b;
			color: #ffffff;
		}
	}
}
</style>
