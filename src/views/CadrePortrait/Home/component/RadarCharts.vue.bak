<template>
	<Card class="" title="干部评价" header size-type="2">
		<v-chart :option="option" autoresize :loading="false" />
	</Card>
</template>
<script lang="ts">
import { defineComponent, reactive, computed } from 'vue'
import { RadarDataType } from '@/types/user'
import { getAssetsData } from '@/apis/cadre-portrait/home'
export default defineComponent({
	name: 'RadarChart',
	setup() {
		const data = reactive<RadarDataType>({
			avg_index: [],
			user_index: [],
		})
		// const indicator = [
		// 	{ name: '政治力', min: 80, max: 100, axisLabel: { show: true }, color: '#939FBD', value: 0 },
		// 	{ name: '正派', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '责任', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '担当', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '业绩', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '群众工作能力', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '执行力', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '决策力', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '学习力', min: 80, max: 100, color: '#939FBD', value: 0 },
		// ]
		const customLegendIcon = `path://M304.43 532.76H221.4c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h83.03c11.47 0 20.76 9.3 20.76 20.76s-9.29 20.76-20.76 20.76zM581.19 532.76H442.81c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h138.38c11.47 0 20.76 9.3 20.76 20.76s-9.3 20.76-20.76 20.76zM802.59 532.76h-83.03c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h83.03c11.47 0 20.76 9.3 20.76 20.76s-9.29 20.76-20.76 20.76z`

		const option = computed(() => {
			const indicator = data.user_index.map((item, index) => {
				return {
					min: 80,
					max: 100,
					axisLabel: { show: index === 0 },
					color: '#939FBD',
					value: item.value,
					name: item.label,
				}
			})
			return {
				tooltip: {
					show: false,
				},
				legend: {
					show: true,
					right: 10,
					itemStyle: {
						borderWidth: 1,
					},
					itemHeight: 2,
					orient: 'vertical',
					formatter: (value: string) => {
						if (value === '平均值') {
							// 给文字添加空格
							value = value.split('').join(' ')
						}
						return value
					},
					data: [
						{
							name: '个人指数',

							icon: 'rect',

							textStyle: {
								color: '#DCE3F6',
								fontSize: 12,
								fontStyle: 'italic',
							},
						},
						{
							name: '平均值',
							icon: customLegendIcon,
							textStyle: {
								color: '#DCE3F6',
								fontSize: 12,
							},
							// itemStyle: {
							// 	color: '#ffffff',
							// },
						},
					],
				},
				grid: {
					left: 0,
					right: 0,
					top: 10,
					bottom: 0,
				},
				radar: {
					shape: 'circle',
					center: ['40%', '50%'],
					radius: '70%',
					splitNumber: 4,
					splitArea: {
						areaStyle: {
							color: ['transparent'],
						},
					},
					axisLabel: {
						show: false,
						color: '#cccccc',
					},
					axisName: {
						formatter: function (_value: any, indicator: any) {
							return `{a|${indicator.name}}  {b|${indicator.value}}`
						},
						rich: {
							a: {
								color: '#ffffff',
								fontSize: 14,
								align: 'center',
								lineHeight: 19,
							},
							b: {
								color: '#00FFF6',
								fontSize: 14,
								align: 'center',
							},
						},
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#163F91',
							width: 2,
							type: 'solid',
						},
					},
					splitLine: {
						show: true,
						lineStyle: {
							type: 'solid',
							color: '#163F91',
							width: 3,
						},
					},
					name: {
						show: true,
					},
					indicator,
				},
				series: [
					{
						name: '',
						type: 'radar',
						data: [
							{
								value: data.user_index.map((item: any) => item.value),
								name: '个人指数',
								label: {
									show: false,
									formatter: function (a: any) {
										return indicator[a.dimensionIndex].name + a.value
									},
								},
								symbol: 'none',
								itemStyle: {
									color: '#00FFFF',
								},
							},
							{
								value: data.avg_index.map((item: any) => item.value),
								name: '平均值',
								itemStyle: {
									color: '#ccc',
								},
								symbol: 'none',
								lineStyle: {
									type: 'dashed',
								},
							},
						],
					},
				],
			}
		})
		const reverseData = (data: any[]) => {
			const [index1, ...others] = data
			return [index1].concat(others.reverse())
		}
		const loadData = async () => {
			const { user_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			const res = await getAssetsData({ user_id })
			if (res.code === 0) {
				const { avg_index, user_index } = res.data
				data.user_index = reverseData(user_index)
				data.avg_index = reverseData(avg_index)
			}
		}
		loadData()
		return {
			option,
		}
	},
})
</script>

<style less="scss"></style>
