<template>
	<Card title="个人测评分析" header size-type="5">
		<div class="personal-analysis">
			<div class="box-content__line">
				<v-chart :option="option" autoresize></v-chart>
			</div>
			<div class="box-content__table">
				<DataTable :data-source="dataSource" :columns="columns" />
			</div>
		</div>
	</Card>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import DataTable from '@/components/Table.vue'
import Card from '@/components/Card.vue'
import { getPersonelResult } from '@/apis/cadre-portrait/home'
import { decreaseOpacity } from '@/utils/utils'
import { transformDataToValue, transformXlabelData, createMarkLine } from '../utils/echarts'
export default defineComponent({
	components: {
		Card,
		DataTable,
	},
	setup() {
		const data = ref<any>({
			xlabel: [],
			upper: [],
			same: [],
			down: [],
		})

		const createData = (origin_data: [], title: string) => {
			const data: any = {}
			data.name = title
			for (let i = 0; i < origin_data.length; i++) {
				data[`name${i + 1}`] = origin_data[i]
			}

			return data
		}
		const dataSource = computed(() => {
			return [createData(data.value.upper, '上级测评'), createData(data.value.same, '同级测评'), createData(data.value.down, '下级测评')]
		})
		const option = computed(() => {
			// const XName = ['政治三力', '学习力', '决策力', '执行力', '群众工作能力', '担当', '责任', '正派']
			const [_sanli1, ...upper] = data.value.upper
			const [_sanli2, ...same] = data.value.same
			const [_sanli3, ...down] = data.value.down
			const [_x1, ...xlabel] = data.value.xlabel
			// 数据改造，将数据改造为x/y轴均为 value轴所需的数据格式
			// 因为x轴刻度位置不是均匀的，所以需要自定义
			const position = [10, 21, 32, 43, 53, 64, 75, 85, 90, 100]
			const positionMap = transformXlabelData(position, xlabel)
			let upperData: any = transformDataToValue(position, upper)
			let sameData: any = transformDataToValue(position, same)
			let downData: any = transformDataToValue(position, down)
			/**
			 * @description: 根据定位生成与其对应的 label
			 * @param {*} position
			 * @param {*} xlabel
			 * @return {*}
			 */

			// const data1 = [upper, same, down]
			const data1 = [upperData, sameData, downData]
			// 表格数据
			const Line = ['上级测评', '同级测评', '下级测评']

			const color = ['#60CA71', '#47D3FF', '#FF1313']

			const datas: any[] = []
			Line.map((item, index) => {
				let innerColor = '#FFFFFF'
				if (color[index] == '#FFFFFF') {
					innerColor = '#ffff00'
				}
				let markLine = {}

				if (index === 0) {
					markLine = createMarkLine(positionMap)
				}
				datas.push({
					symbolSize: 12,
					symbol: 'circle',
					name: item,
					type: 'line',
					yAxisIndex: 1,
					data: data1[index],
					smooth: false,
					itemStyle: {
						borderWidth: 4,
						borderColor: color[index],
						color: '#ffffff',
						shadowColor: decreaseOpacity(color[index], 0.5),
						shadowBlur: 5,
					},
					lineStyle: {
						color: color[index],
						width: 3,
					},
					markLine,
					z: 10,
				})
			})

			const option = {
				// backgroundColor: '#0e2147',
				color: ['#17EBC7'],
				grid: {
					left: '2%',
					top: '23%',
					bottom: '0%',
					right: '3%',
					containLabel: true,
				},
				legend: {
					show: true,
					top: 15,
					left: 'center',
					// type: 'scroll',
					// data: Line,
					icon: 'circle',
					itemWidth: 12,
					itemHeight: 12,
					textStyle: {
						color: '#333333',
						fontSize: 14,
					},
					itemStyle: {
						borderWidth: 4,
					},
					itemGap: 24,
				},
				yAxis: [
					{
						type: 'value',
						position: 'right',
						splitLine: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					{
						type: 'value',
						position: 'left',
						scale: true,
						nameTextStyle: {
							color: '#00FFFF',
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#EEEEEE',
							},
						},
						// min: 60,
						// max: 100,
						// splitNumber: 7,
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
								'arrow',
							],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							formatter: '{value}',
							color: '#666666',
							fontSize: 14,
							interval: 5,
							showMaxLabel: false,
						},
					},
				],
				xAxis: [
					{
						type: 'value',
						axisTick: {
							show: false,
						},
						splitLine: {
							show: false,
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M384 883.08c-8.19 0-16.38-3.12-22.62-9.38-12.5-12.5-12.5-32.75 0-45.25L677.83 512 361.38 195.55c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L734.4 478.07c18.72 18.72 18.72 49.16 0 67.88L406.62 873.7c-6.24 6.25-14.43 9.38-22.62 9.38z m305.14-359.77h0.31-0.31z',
								'arrow',
							],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisLabel: {
							inside: false,
							textStyle: {
								color: 'transparent', // x轴颜色
								fontWeight: 'normal',
								fontSize: '14',
								lineHeight: 22,
							},
						},
						min: 0,
						max: 100,
						data: xlabel,
					},
					{
						type: 'category',
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
						splitArea: {
							show: false,
						},
						splitLine: {
							show: false,
						},
						//-----
					},
				],
				series: datas,
			}
			return option
		})

		const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
			const columns: Array<any> = []

			list.forEach((item: any, index: number) => {
				const key = `name`
				if (index === 0) {
					columns[index] = {
						key,
						title,
						width: '9%',
					}
				}
				columns[index + 1] = {
					key: `${key}${index + 1}`,
					title: item,
					width: 100 / (list.length + 1) + '%',
					align: 'center',
					sort: true,
				}
			})
			return columns
		}
		const columns = computed(() => {
			// const _columns = [
			// 	{
			// 		key: 'name',
			// 		align: 'center',
			// 		width: '8%',
			// 		title: '',
			// 	},
			// 	{
			// 		key: 'name1',
			// 		align: 'center',
			// 		width: '10%',
			// 		title: '政治三力',
			// 	},
			// 	{
			// 		key: 'name2',
			// 		align: 'center',
			// 		width: '10%',
			// 		title: '学习力',
			// 	},
			// 	{
			// 		key: 'name3',
			// 		align: 'center',
			// 		width: '10%',
			// 		title: '决策力',
			// 	},
			// 	{
			// 		key: 'name4',
			// 		align: 'center',
			// 		width: '10%',
			// 		title: '执行力',
			// 	},
			// 	{
			// 		key: 'name5',
			// 		align: 'center',
			// 		width: '15%',
			// 		title: '群众工作能力',
			// 	},
			// 	{
			// 		key: 'name6',
			// 		align: 'center',
			// 		width: '10%',
			// 		title: '担当',
			// 	},
			// 	{
			// 		key: 'name7',
			// 		align: 'center',
			// 		width: '10%',
			// 		title: '责任',
			// 	},
			// 	{
			// 		key: 'name8',
			// 		align: 'center',
			// 		width: '10%',
			// 		title: '正派',
			// 	},
			// ]
			const _columns = dynamicTableHeader(data.value.xlabel, '#00ffff')
			_columns.map((item) => {
				if (item.title === '政治三力') {
					item.customization = 'CustomBlock'
				}
			})
			return _columns
		})

		const loadData = async () => {
			const { user_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			const res = await getPersonelResult({ user_id })
			if (res.code === 0) {
				data.value = res.data
			}
		}
		loadData()

		const colorList = ['#15EAC2', '#24c5e5', '#FDA22C']

		const rowColor = (_record: any, index: number) => {
			return colorList[index]
		}
		return {
			option,
			columns,
			dataSource,
			rowColor,
		}
	},
})
</script>

<style scoped lang="less">
.personal-analysis {
	display: flex;
	flex-direction: column;
	.box-content__line {
		height: 294px;
	}
	.box-content__table {
		margin-top: 30px;
		flex: 1;
	}
}
</style>
