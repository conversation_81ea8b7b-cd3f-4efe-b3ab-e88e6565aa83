<template>
	<Card title="综合分析" header size-type="3">
		<div class="histoical-performance">
			<!-- <div class="box-content__table">
				<div class="title">历史业绩</div>
			</div> -->
			<div class="box-content__line margin-top-30">
				<v-chart :option="option" autoresize></v-chart>
			</div>
			<!-- <div class="box-content__table">
				<DataTable :data-source="performanceTable.datasource" :columns="performanceTable.columns" />
			</div> -->
			<div class="box-content__table">
				<div class="title">年度考核</div>
				<DataTable :data-source="assessment.datasource" :columns="assessment.columns" />
			</div>
			<div class="box-content__table biaozhang">
				<div class="title"><span>表彰表扬</span> <span class="label-icon" @click="openRuleModal(3)"></span></div>
				<DataTable :data-source="honorary.datasource" :columns="honorary.columns" />
			</div>
		</div>
	</Card>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, inject, ref } from 'vue'
import DataTable from '@/components/Table.vue'
import { getAchievement, getEvalData, getGloryData, getCadreIndexAndTestScore } from '@/apis/cadre-portrait/home'
import { convertPxToRem, debounce } from '@/utils/utils'
export default defineComponent({
	name: 'HistoricalPerformance',
	components: {
		DataTable,
	},
	setup() {
		const user_id = inject('user_id')
		const color = ['#6AAEFB', '#FE533A', '#60CA71']
		const apiData = reactive<any>({
			echarts: {
				achievement: [],
				xAxis: [],
				average: [],
				tableData: [],
				colums: [],
				sequence_avg: [],
				man_avg: [],
			},
			newCharts: {
				time: [],
				cadre_index_ranks: [],
				eval_org_ranks: [],
			},
			evalData: {
				year: [],
				achievement: [],
				rank: [],
				tabelData: [],
			},
			gloryData: [],
			negativeData: [],
		})

		const dynamicTableHeader = (list: Array<any> = [], color?: any, title = '') => {
			const columns: Array<any> = []

			list.forEach((item: any, index: number) => {
				const key = `name`
				if (index === 0) {
					columns[index] = {
						key,
						title,
						width: '18%',
						color,
					}
				}
				columns[index + 1] = {
					key: `${key}${index + 1}`,
					title: item,
					width: 82 / (list.length + 1) + '%',
					align: 'center',
					color,
				}
			})
			return columns
		}
		const refreshOption = ref(0)

		window.addEventListener(
			'resize',
			debounce(() => {
				refreshOption.value++
			}, 100)
		)
		/**
		 * @description: 折线图数据
		 * @param {*} computed
		 * @return {*}
		 */
		const colorMap: any = {
			部门平均值: '#60CA71',
			乡镇平均值: '#FF81E3',
			序列平均值: '#60CA71',
			'乡镇/部门平均值': '#FF81E3',
			同序列平均值: '#60CA71',
		}

		const option = computed(() => {
			{
				refreshOption.value
			}
			const { xAxis, sequence_avg, achievement, man_avg } = apiData.echarts

			const { time, cadre_index_ranks, eval_org_ranks } = apiData.newCharts

			const cadreIndex = cadre_index_ranks.map((item: any) => {
				return item.numerators
			})
			const evalOrgRanks = eval_org_ranks.map((item: any) => {
				return item.numerators
			})

			// x轴数据
			const XName = time
			// 折线图数据
			const data1 = [achievement]
			// legend数据
			// const Line = ['业绩']
			const Line = ['干部指数序列排名', '干部测评班子内排名']
			const orgindataMap: any = {
				干部指数序列排名: cadre_index_ranks,
				干部测评班子内排名: eval_org_ranks,
			}
			const dataMap: any = {
				干部指数序列排名: cadreIndex,
				干部测评班子内排名: evalOrgRanks,
			}
			// sequence_avg && (Line.push('同序列平均值'), data1.push(sequence_avg))

			// man_avg && (Line.push('乡镇/部门平均值'), data1.push(man_avg))

			// 每条线的数据
			const datas: any[] = []
			Line.map((item, index) => {
				datas.push({
					symbolSize: convertPxToRem(11),
					symbol: 'circle',
					name: item,
					type: 'line',
					yAxisIndex: index,
					data: dataMap[item], // 数据
					itemStyle: {
						borderWidth: convertPxToRem(4),
						borderColor: colorMap[item] || color[index],
						color: '#ffffff',
					},
					lineStyle: {
						type: item.includes('平均值') ? 'dashed' : 'solid',
						color: colorMap[item] || color[index],
					},
					label: {
						show: true,
						fontSize: convertPxToRem(16),
						formatter: (params: any) => {
							const { seriesName, dataIndex } = params
							const data = orgindataMap?.[seriesName]?.[dataIndex]
							if (data) {
								const { numerators, denominators } = data

								return `${numerators}/${denominators}`
							}
						},
					},
				})
			})

			const option = {
				// backgroundColor: '#0e2147',

				grid: {
					left: '5%',
					top: '20%',
					bottom: '1%',
					containLabel: true,
				},
				legend: {
					show: true,
					top: 0,
					left: 'center',
					// type: 'scroll',
					// data: Line,
					icon: 'circle',
					itemWidth: convertPxToRem(12),
					itemHeight: convertPxToRem(12),
					textStyle: {
						color: '#333333',
						fontSize: convertPxToRem(18),
					},
					itemStyle: {
						borderWidth: convertPxToRem(4),
					},
					itemGap: convertPxToRem(60),
				},
				yAxis: [
					{
						type: 'value',
						name: '干部指数序列排名',
						position: 'left',
						inverse: true,
						nameTextStyle: {
							color: '#000000',
							fontSize: convertPxToRem(16),
						},
						nameLocation: 'end',
						// scale: true,
						max: (value: any) => {
							return value.max + 6
						},
						// min: (value: any) => {
						// 	let min = Math.floor(value.min - 3)
						// 	return min < 0 ? 0 : min
						// },
						min: 1,
						// interval: 3,
						// splitNumber: 5,
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#EEEEEE',
							},
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
								'arrow',
							],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							color: '#666666',
							fontSize: convertPxToRem(14),
							formatter: (value: any) => {
								value = Number(value)
								return value
							},
						},
					},
					{
						type: 'value',
						name: '干部测评班子内排名',
						nameTextStyle: {
							color: '#000000',
							fontSize: convertPxToRem(16),
						},
						position: 'right',
						splitLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						min: 1,
						max: (value: any) => value.max + 5,
						inverse: true,
						axisLabel: {
							show: true,

							textStyle: {
								color: '#000000',
								fontSize: convertPxToRem(14),
							},
						},
						nameLocation: 'end',
					},
				],
				xAxis: [
					{
						type: 'category',
						axisTick: {
							show: false,
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
								'arrow',
							],
							onZero: false,
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							inside: false,
							textStyle: {
								color: '#666666', // x轴颜色
								fontWeight: 'normal',
								fontSize: convertPxToRem(16),
								lineHeight: convertPxToRem(22),
							},
						},
						position: 'bottom',
						data: XName,
					},
					// {
					// 	type: 'category',
					// 	axisLine: {
					// 		show: false,
					// 	},
					// 	axisTick: {
					// 		show: false,
					// 	},
					// 	axisLabel: {
					// 		formatter: '{value}',
					// 		color: '#2898E5',
					// 		fontSize: 14,
					// 	},
					// 	splitArea: {
					// 		show: false,
					// 	},
					// 	splitLine: {
					// 		show: false,
					// 	},
					// 	//-----
					// 	data: ['1月', '2月', '3月', '4月', '5月', '6月'],
					// },
				],
				series: datas,
			}

			return option
		})
		/**
		 * @description: 历史业绩表格数据
		 * @param {*} computed
		 * @return {*}
		 */
		const performanceTable = computed(() => {
			const { xAxis } = apiData.echarts

			const columns = dynamicTableHeader(xAxis, '', '年度')

			const datasource = apiData.echarts.tableData
			const rowColor = (_item: any, index: number) => {
				return color[index]
			}

			return { columns, datasource, rowColor }
		})
		/**
		 * @description: 年度考核表格数据
		 * @param {*} computed
		 * @return {*}
		 */
		const assessment = computed(() => {
			// 年度考核表格header
			//根据年份动态生成表格header
			const xAxis = apiData.evalData.year
			const datasource = apiData.evalData.tableData
			// 动态表格数据颜色
			const color = (value: any) => {
				return value === '优秀' ? '#60CA71' : ''
			}
			// 动态表格header

			const columns = dynamicTableHeader(xAxis, color, '年度')
			return { columns, datasource }
		})
		/**
		 * @description: 荣誉表彰
		 * @param {*} computed
		 * @return {*}
		 */
		const honorary = computed(() => {
			const color = () => '#E5231A'
			const color1 = () => '#000000'
			const columns = [
				{
					key: 'type',
					align: 'center',
					width: '30%',
					title: '类型',
					color: color1,
				},
				{
					key: 'score',
					align: 'center',
					width: '15%',
					title: '加减分',
					color,
				},
				{
					key: 'item',
					align: 'left',
					width: '55%',
					title: '事项',
					color: color1,
				},
			]

			const datasource = apiData.gloryData
			return { columns, datasource }
		})
		/**
		 * @description: 将数据转换为table所需的数据格式
		 * @param {*} origin_data
		 * @param {*} title
		 * @return {*}
		 */
		const createData = (origin_data: any[] = [], title: string) => {
			const data: any = {}
			title && (data.name = title)
			for (let i = 0; i < origin_data.length; i++) {
				data[`name${i + 1}`] = origin_data[i]
			}

			return data
		}
		// 业绩分析接口
		const loadAchieveData = async () => {
			if (!user_id) return

			const res = await getAchievement({ user_id })
			if (res.code === 0 && Object.keys(res.data || {}).length > 0) {
				const { year, achievement, avg, sequence_avg, man_avg } = res.data
				apiData.echarts = {
					xAxis: year,
					achievement,
					average: avg,
					sequence_avg,
					man_avg,
					tableData: [],
				}
				apiData.echarts.tableData.push(createData(achievement, '业绩'))
				sequence_avg && apiData.echarts.tableData.push(createData(sequence_avg, '同序列平均值'))
				man_avg && apiData.echarts.tableData.push(createData(man_avg, '乡镇/部门平均值'))
			}
		}
		// 考核数据接口
		const loadEvalData = async () => {
			if (!user_id) return

			const res = await getEvalData({ user_id })
			if (res.code === 0) {
				const { year, achievement, cadre_index_rank = [], item_rank = [] } = res.data
				const tableData = [
					/**createData(year, '年度'),*/ createData(achievement, '结果'),
					createData(cadre_index_rank, '干部指数排名（同序列）'),
					createData(item_rank, '测评排名（班子）'),
				]
				apiData.evalData = {
					tableData,
					year,
					cadre_index_rank,
					item_rank,
					achievement,
				}
			}
		}
		// 荣誉表彰接口
		const loadGlory = async () => {
			if (!user_id) return

			const res = await getGloryData({ user_id })
			if (res.code === 0) {
				apiData.gloryData = res.data
			}
		}

		const loadCadreIndexAndTestScore = async () => {
			const res = await getCadreIndexAndTestScore({ user_id })

			if (res.code === 0) {
				apiData.newCharts = res.data
			}
		}
		/**
		 * @description: 加载数据
		 * @return {*}
		 */
		const loadData = () => {
			loadAchieveData()
			loadEvalData()
			loadGlory()
			loadCadreIndexAndTestScore()
		}
		user_id && loadData()

		const openRuleModal: any = inject('openRuleModal')

		return { option, performanceTable, assessment, honorary, apiData, openRuleModal }
	},
})
</script>

<style scoped lang="less">
.histoical-performance {
	height: 100%;
}
.box-content__line {
	height: 298px;
}
.box-content__table {
	margin-top: 25px;
	flex: 1;
	.title {
		display: flex;
		align-items: center;
		font-size: 20px;
		font-family: PingFang SC-Heavy, PingFang SC;
		font-weight: 800;
		color: #333333;
		line-height: 24px;
		padding: 7px 0 20px;

		&::before {
			margin-right: 17px;
			content: '';
			display: inline-block;
			width: 16px;
			height: 16px;
			background: #ec4224;
			opacity: 1;
			border-radius: 50%;
		}
	}
}
.label-icon {
	margin-left: 11px;
	display: inline-block;
	width: 22px;
	height: 22px;
	line-height: 1;
	background: url('@/assets/images/question-mark.png') no-repeat center / 100% 100%;
	cursor: pointer;
}
// .margin-top-30 {
// 	margin-top: -30px;
// }
.biaozhang {
	::v-deep(.table-column-max) {
		color: #000000;
	}
}
</style>
