<template>
	<a-modal class="responsibility-modal" :visible="tagVisible" @cancel="onTagModalClose" title="分管领域" :footer="null">
		<div class="responsibility-list">
			<div class="responsibility-item" v-for="(responsibility, index) in data || []" :key="index">
				<div class="time">
					<span class="text">
						{{ responsibility.time }}
					</span>
				</div>
				<div class="text">{{ responsibility.charge_range }}</div>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const tagVisible = ref(false)
const data = ref<any>([])
const onTagModalClose = () => {
	tagVisible.value = false

	data.value = []
}

const onTagModalOpen = (_data: any) => {
	tagVisible.value = true

	data.value = _data
}

defineExpose({
	close: onTagModalClose,
	open: onTagModalOpen,
})
</script>

<style lang="less">
.responsibility-modal {
	width: 902px !important;
	.ant-modal-title {
		font-size: 24px;
		font-family: Source <PERSON>, Source <PERSON>N;
		font-weight: 500;
		color: #000000;
		text-align: center;
	}
}
</style>
<style lang="less" scoped>
.responsibility-list {
	padding: 32px 38px;
	width: 100%;
	height: 679px;
	overflow: auto;
	.responsibility-item {
		width: 100%;
		display: flex;

		.notimeline {
			&::after {
				content: unset !important;
			}
		}
		.time {
			flex: 0 0 260px;
			position: relative;
			font-size: 21px;
			line-height: 21px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			color: rgba(0, 142, 255, 1);
			.text {
				margin-right: 20px;
				color: rgba(0, 142, 255, 1);
				font-weight: bold;
			}
			&::before {
				content: '';
				position: absolute;
				top: 4px;
				right: 0px;
				width: 15px;
				height: 15px;
				background-color: #fff;
				border: 2px solid rgba(0, 142, 255, 1);
				border-radius: 50%;
				z-index: 1;
			}
			&::after {
				content: '';
				position: absolute;
				top: 2px;
				right: 0px;
				height: 100%;
				width: 1px;
				transform: translate(-7px);
				background-color: rgba(0, 142, 255, 1);
			}
		}
		.text {
			margin-left: 23px;
			font-size: 21px;
			line-height: 25px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.85);
			padding-bottom: 52px;
		}
	}
}
</style>
