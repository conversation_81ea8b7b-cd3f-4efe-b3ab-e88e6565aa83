<template>
	<Card class="" title="干部评价" header size-type="2">
		<!-- <v-chart :option="option" autoresize :loading="false" /> -->
		<!-- v-if="powerList.length && orderList.length && villagesList.length"  -->
		<v-chart :option="_option" autoresize :loading="false" />
		<!-- <div class="bg_img" v-if="!powerList.length && !orderList.length && !villagesList.length">
			<div class="bg_text">即将上线</div>
		</div> -->
		<template v-slot:right>
			<div class="data-menu">
				<div :class="['menu-item', currentIndex === item.key && 'menu-active']" v-for="item in menu" :key="item.key"
					@click="onMenu(item.key)">
					{{ item.label }}
				</div>
			</div>
		</template>
	</Card>
</template>
<script lang="ts">
import { defineComponent, reactive, computed, ref, onMounted } from 'vue'
// import { RadarDataType } from '@/types/user'
import { getAssetsData, getBarData } from '@/apis/cadre-portrait/home'
// import * as echarts from 'echarts'
const menu = [
	{
		label: '能力',
		key: 1,
	},
	{
		label: '业绩',
		key: 2,
	},
	{
		label: '口碑',
		key: 3,
	},
]
export default defineComponent({
	name: 'RadarChart',
	setup() {
		const currentIndex = ref(1)
		const xData = ref(['学习', '决策', '执行', '沟通', '群众'])
		const _option = ref({})
		const powerList = ref([]) //能力
		const orderList = ref([]) //序列
		const villagesList = ref([]) //乡镇
		const flag = ref(1) // 1正职2副职
		// const data = reactive<RadarDataType>({
		// 	avg_index: [],
		// 	user_index: [],
		// })
		const onMenu = (key: number) => {
			switch (key) {
				case 1:
					xData.value = ['学习', '决策', '执行', '沟通', '群众']
					break
				case 2:
					xData.value = ['前3年', '前2年', '前1年', '今年',]
					break
				default:
					xData.value = ['奉献度', '勤奋度', '正直度']
			}
			currentIndex.value = key
			getBarTypeData()
		}
		const getBarTypeData = async () => {
			const { user_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			const params: any = {
				user_id,
				flag: currentIndex.value,
			}
			const { data, code } = await getBarData(params)

			if (code == 0) {
				powerList.value = data.user_index
				orderList.value = data.avg_rindex
				villagesList.value = data.avg_yindex
				flag.value = data.flag
			}
			getOption()
		}
		// 生成option方法
		const getOption = () => {
			_option.value = {
				// backgroundColor: '#1a1f76',
				tooltip: {},
				grid: {
					top: '15%',
					left: '1%',
					right: '1%',
					bottom: '8%',
					containLabel: true,
				},
				legend: {
					top: '2%',
					// padding: 10,
					// itemGap: 50,
					itemHeight: 10,
					itemWidth: 20,
					rich: {
						a: {
							verticalAlign: 'middle',
						},
					},
					data: flag.value == 1 ? (powerList.value.length > 0 ? ['能力值', '同序列、同职务平均分', '所有乡镇、同职务平均分'] : ['业绩', '平均分']) : powerList.value.length > 0 ? ['能力值', '班子内副职平均分', '同序列副职平均分'] : ['业绩', '平均分'],
					textStyle: {
						color: '#f9f9f9',
						borderColor: '#fff',
					},
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: true,
						axisLine: {
							//坐标轴轴线相关设置。数学上的x轴
							show: true,
							lineStyle: {
								color: '#222f83',
							},
						},
						axisLabel: {
							//坐标轴刻度标签的相关设置
							textStyle: {
								color: '#3c84cd',
								margin: 15,
							},
						},
						axisTick: {
							show: true,
							alignWithLabel: true,
						},
						// data: ['学习', '决策', '执行', '沟通', '群众',],
						data: xData.value,
					},
				],
				yAxis: [
					{
						type: 'value',
						min: 20,
						// max: 100,
						splitNumber: 5,
						splitLine: {
							show: true,
							lineStyle: {
								color: '#0a3256',
							},
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#222f83',
							},
						},
						axisLabel: {
							margin: 10,
							textStyle: {
								color: '#3c84cd',
							},
							formatter: function (value: any) {
								return value.toFixed(2)
							},
						},
						axisTick: {
							show: false,
						},
					},
				],
				series: [
					{
						name: flag.value == 1 ? powerList.value.length > 0 ? '同序列、同职务平均分' : '平均分' : powerList.value.length > 0 ? '班子内副职平均分' : '平均分',
						type: 'line',
						// smooth: true, //是否平滑曲线显示
						// 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
						showAllSymbol: true,
						symbol: 'emptyCircle',
						symbolSize: 6,
						lineStyle: {
							normal: {
								color: '#f84a23', // 线条颜色
							},
							borderColor: '#f84a23',
						},
						label: {
							show: false,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						itemStyle: {
							normal: {
								color: '#f84a23',
							},
						},
						tooltip: {
							show: false,
						},
						data: orderList.value.length > 0 ? orderList.value : [88, 89, 94, 95],
					},
					{
						name: powerList.value.length > 0 ? '能力值' : '业绩',
						type: 'bar',
						barWidth: 40,
						tooltip: {
							show: false,
						},
						label: {
							show: true,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						itemStyle: {
							normal: {
								// barBorderRadius: 5,
								// color: new echarts.graphic.LinearGradient(
								//     0, 0, 0, 1,
								//     [{
								//             offset: 0,
								//             color: '#14c8d4'
								//         },
								//         {
								//             offset: 1,
								//             color: '#43eec6'
								//         }
								//     ]
								// )
								// color: function (params) {
								// 	var colorList = ['#0ec1ff', '#10cdff', '#12daff', '#15ebff', '#17f8ff', '#1cfffb', '#1dfff1']
								// 	return colorList[params.dataIndex]
								// },
								color: '#37aeff',
							},
						},
						data: powerList.value.length > 0 ? powerList.value : [{
							"label": "前3年",
							"value": 91.80
						},
						{
							"label": "前2年",
							"value": 92.94
						},
						{
							"label": "前1年",
							"value": 95.28
						},
						{
							"label": "今年",
							"value": 94.52
						},
						],
					},
					{
						name: flag.value == 1 ? '所有乡镇、同职务平均分' : '同序列副职平均分',
						type: 'line',
						// smooth: true, //是否平滑曲线显示
						// 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
						showAllSymbol: true,
						symbol: 'emptyCircle',
						symbolSize: 6,
						lineStyle: {
							normal: {
								color: '#f0c706', // 线条颜色
							},
							borderColor: '#f0f',
						},
						label: {
							show: false,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						itemStyle: {
							normal: {
								color: '#f0c706',
								lineStyle: {
									width: 3,
									type: 'dashed', //'dotted'点型虚线 'solid'实线 'dashed'线性虚线
								},
							},
						},
						tooltip: {
							show: false,
						},
						data: villagesList.value,
					},
				],
			}
		}
		onMounted(() => {
			getBarTypeData()
			// getOption()
		})

		// const indicator = [
		// 	{ name: '政治力', min: 80, max: 100, axisLabel: { show: true }, color: '#939FBD', value: 0 },
		// 	{ name: '正派', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '责任', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '担当', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '业绩', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '群众工作能力', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '执行力', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '决策力', min: 80, max: 100, color: '#939FBD', value: 0 },
		// 	{ name: '学习力', min: 80, max: 100, color: '#939FBD', value: 0 },
		// ]
		// const customLegendIcon = `path://M304.43 532.76H221.4c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h83.03c11.47 0 20.76 9.3 20.76 20.76s-9.29 20.76-20.76 20.76zM581.19 532.76H442.81c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h138.38c11.47 0 20.76 9.3 20.76 20.76s-9.3 20.76-20.76 20.76zM802.59 532.76h-83.03c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h83.03c11.47 0 20.76 9.3 20.76 20.76s-9.29 20.76-20.76 20.76z`

		// const option = computed(() => {
		// 	const indicator = data.user_index.map((item, index) => {
		// 		return {
		// 			min: 80,
		// 			max: 100,
		// 			axisLabel: { show: index === 0 },
		// 			color: '#939FBD',
		// 			value: item.value,
		// 			name: item.label,
		// 		}
		// 	})
		// 	return {
		// 		tooltip: {
		// 			show: false,
		// 		},
		// 		legend: {
		// 			show: true,
		// 			right: 10,
		// 			itemStyle: {
		// 				borderWidth: 1,
		// 			},
		// 			itemHeight: 2,
		// 			orient: 'vertical',
		// 			formatter: (value: string) => {
		// 				if (value === '平均值') {
		// 					// 给文字添加空格
		// 					value = value.split('').join(' ')
		// 				}
		// 				return value
		// 			},
		// 			data: [
		// 				{
		// 					name: '个人指数',

		// 					icon: 'rect',

		// 					textStyle: {
		// 						color: '#DCE3F6',
		// 						fontSize: 12,
		// 						fontStyle: 'italic',
		// 					},
		// 				},
		// 				{
		// 					name: '平均值',
		// 					icon: customLegendIcon,
		// 					textStyle: {
		// 						color: '#DCE3F6',
		// 						fontSize: 12,
		// 					},
		// 					// itemStyle: {
		// 					// 	color: '#ffffff',
		// 					// },
		// 				},
		// 			],
		// 		},
		// 		grid: {
		// 			left: 0,
		// 			right: 0,
		// 			top: 10,
		// 			bottom: 0,
		// 		},
		// 		radar: {
		// 			shape: 'circle',
		// 			center: ['40%', '50%'],
		// 			radius: '70%',
		// 			splitNumber: 4,
		// 			splitArea: {
		// 				areaStyle: {
		// 					color: ['transparent'],
		// 				},
		// 			},
		// 			axisLabel: {
		// 				show: false,
		// 				color: '#cccccc',
		// 			},
		// 			axisName: {
		// 				formatter: function (_value: any, indicator: any) {
		// 					return `{a|${indicator.name}}  {b|${indicator.value}}`
		// 				},
		// 				rich: {
		// 					a: {
		// 						color: '#ffffff',
		// 						fontSize: 14,
		// 						align: 'center',
		// 						lineHeight: 19,
		// 					},
		// 					b: {
		// 						color: '#00FFF6',
		// 						fontSize: 14,
		// 						align: 'center',
		// 					},
		// 				},
		// 			},
		// 			axisLine: {
		// 				show: true,
		// 				lineStyle: {
		// 					color: '#163F91',
		// 					width: 2,
		// 					type: 'solid',
		// 				},
		// 			},
		// 			splitLine: {
		// 				show: true,
		// 				lineStyle: {
		// 					type: 'solid',
		// 					color: '#163F91',
		// 					width: 3,
		// 				},
		// 			},
		// 			name: {
		// 				show: true,
		// 			},
		// 			indicator,
		// 		},
		// 		series: [
		// 			{
		// 				name: '',
		// 				type: 'radar',
		// 				data: [
		// 					{
		// 						value: data.user_index.map((item: any) => item.value),
		// 						name: '个人指数',
		// 						label: {
		// 							show: false,
		// 							formatter: function (a: any) {
		// 								return indicator[a.dimensionIndex].name + a.value
		// 							},
		// 						},
		// 						symbol: 'none',
		// 						itemStyle: {
		// 							color: '#00FFFF',
		// 						},
		// 					},
		// 					{
		// 						value: data.avg_index.map((item: any) => item.value),
		// 						name: '平均值',
		// 						itemStyle: {
		// 							color: '#ccc',
		// 						},
		// 						symbol: 'none',
		// 						lineStyle: {
		// 							type: 'dashed',
		// 						},
		// 					},
		// 				],
		// 			},
		// 		],
		// 	}
		// })
		// const reverseData = (data: any[]) => {
		// 	const [index1, ...others] = data
		// 	return [index1].concat(others.reverse())
		// }
		// const loadData = async () => {
		// 	const { user_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
		// 	const res = await getAssetsData({ user_id })
		// 	if (res.code === 0) {
		// 		const { avg_index, user_index } = res.data
		// 		data.user_index = reverseData(user_index)
		// 		data.avg_index = reverseData(avg_index)
		// 	}
		// }
		// loadData()
		return {
			// option,
			// TODO 7/25 改版
			_option,
			currentIndex,
			menu,
			onMenu,
			powerList,
			orderList,
			villagesList,
		}
	},
})
</script>

<style less="scss" scoped>
.bg_img {
	background-image: url('@/assets/images/bg-img.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 183px;
	height: 138px;
	position: relative;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.bg_text {
	position: absolute;
	bottom: -30px;
	left: 50%;
	transform: translateX(-50%);
	font-size: 14px;
	font-family: Source Han Sans CN;
	font-weight: 500;
	color: #fefefe;
}

.data-menu {
	display: flex;
}

.menu-item {
	margin-left: 15px;
	font-size: 14px;
	font-weight: 400;
	color: #9e9e9e;
	cursor: pointer;
}

.menu-active {
	color: #00eaff;
}
</style>
