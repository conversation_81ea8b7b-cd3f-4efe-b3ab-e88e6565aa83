<template>
	<Card title="能力测评对比" header size-type="3">
		<div class="box-content__line">
			<v-chart :option="option" autoresize></v-chart>
		</div>
		<div class="box-content__table">
			<DataTable :data-source="dataSource" :columns="columns" />
		</div>
	</Card>
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue'
import DataTable from '@/components/Table.vue'
import { getAblityResult } from '@/apis/cadre-portrait/home'
import useUser from '@/store/user.ts'
import { decreaseOpacity } from '@/utils/utils'
import { transformDataToValue, transformXlabelData, createMarkLine } from '../utils/echarts'
export default defineComponent({
	name: 'AbilityAnalysis',
	components: {
		DataTable,
	},
	setup() {
		const user: any = useUser()
		const data: any = ref({
			xlabel: [],
			other_result: [],
			my_result: {},
			avg_list: {},
		})
		const apiData = ref<any>({
			line: [],
			table: [],
			data: [],
			xlabel: [],
		})
		const color = ['#FF6C47', '#6AAEFB', '#6ADDE4', '#CBADFF', '#F06292', '#AED581', '#66BB6A']
		const createData = (origin_data: [], title: string) => {
			const data: any = {}
			data.name = title
			for (let i = 0; i < origin_data.length; i++) {
				data[`name${i + 1}`] = origin_data[i]
			}

			return data
		}
		const dataSource = computed(() => {
			const datasource: any = []
			apiData.value.line.map((item: any, index: number) => {
				if (apiData.value.data[index]) {
					datasource.push(createData(apiData.value.data[index], item))
				}
			})
			return datasource
		})
		const position = [12, 23, 33, 44, 55, 65, 76, 87]
		const option = computed(() => {
			// const XName = ['政治三力', '学习力', '决策力', '执行力', '群众工作能力', '担当', '责任', '正派']
			const [_, ...XName] = apiData.value.xlabel
			// const Line = ['向成言', '程永忠', '谭亚军', '文海波', '秦春霞', '蒋舰', '平均值']
			const Line = apiData.value.line
			const { user_id, name } = user.detail
			const datas: any[] = []
			let max = 0
			let min = 0
			let _index = 0
			const positionMap = transformXlabelData(position, XName)
			const markLine = createMarkLine(positionMap)
			Line.map((item: any, index: number) => {
				const [_, ...data] = apiData.value.data[index]
				let _color = ''
				let lineType = 'solid'
				if (item === '平均值') {
					lineType = 'dashed'
					_color = '#FE533A'
				} else if (item === name) {
					_color = '#FFA300'
				} else {
					_color = color[_index++]
				}
				const sortData = [...data].sort((a: number, b: number) => Number(a) - Number(b))
				let _min = Number(sortData[0])
				let _max = Number(sortData[sortData.length - 1])
				if (min === 0) {
					min = _min
				}
				if (max === 0) {
					max = _max
				}

				if (_min < min) {
					min = _min
				}
				if (_max > max) {
					max = _max
				}
				let symbolSize = name === item ? 14 : 8
				let lineWidth = name === item ? 4 : 2
				datas.push({
					symbolSize,
					symbol: 'circle',
					name: item,
					type: 'line',
					yAxisIndex: 1,
					data: transformDataToValue(position, data),
					color: _color,
					smooth: false,
					itemStyle: {
						borderWidth: 4,
						borderColor: _color,
						color: '#fff',
						shadowColor: decreaseOpacity(_color, 0.5),
						shadowBlur: 13,
					},
					lineStyle: {
						color: _color,
						type: lineType,
						width: lineWidth,
					},
					z: 10,
					markLine: index === 0 ? markLine : {},
				})
			})
			// console.log('max', max, 'min', min)
			max = Math.ceil(max + 5)
			max >= 100 && (max = 100)

			min = Math.floor(min - 5)
			min <= 0 && (min = 0)

			// console.log('max', max, 'min', min)
			const option = {
				// backgroundColor: '#0e2147',

				grid: {
					left: '0%',
					top: '13%',
					bottom: '0%',
					right: '3%',
					containLabel: true,
				},
				legend: {
					show: true,
					top: 0,
					left: 'center',
					// type: 'scroll',
					// data: Line,
					icon: 'circle',
					itemWidth: 12,
					itemHeight: 12,
					textStyle: {
						color: '#333333',
						fontSize: 18,
					},
					itemStyle: {
						borderWidth: 4,
					},
					itemGap: 28,
				},
				yAxis: [
					{
						type: 'value',
						position: 'right',
						splitLine: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					{
						type: 'value',
						position: 'left',
						// scale: true,
						min,
						max,
						nameTextStyle: {
							color: '#00FFFF',
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#EEEEEE',
							},
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: ['none', 'arrow'],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisTick: {
							show: false,
						},
						// splitNumber: 4,
						// interval: 1,
						axisLabel: {
							color: '#666666',
							fontSize: 16,
						},
					},
				],
				xAxis: [
					{
						type: 'value',
						axisTick: {
							show: false,
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
								'arrow',
							],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							inside: false,
							textStyle: {
								color: 'transparent', // x轴颜色
								fontWeight: 'normal',
								fontSize: 16,
								lineHeight: 22,
							},
							interval: 0,
						},
						min: 0,
						max: 100,
						data: XName,
					},
					{
						type: 'category',
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
						splitArea: {
							show: false,
						},
						splitLine: {
							show: false,
						},
						//-----
						data: ['1月', '2月', '3月', '4月', '5月', '6月'],
					},
				],
				series: datas,
			}
			return option
		})
		const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
			const columns: Array<any> = []

			list.forEach((item: any, index: number) => {
				const key = `name`
				if (index === 0) {
					columns[index] = {
						key,
						title,
						width: '10%',
					}
				}
				columns[index + 1] = {
					key: `${key}${index + 1}`,
					title: item,
					width: 100 / (list.length + 1) + '%',
					align: 'center',
					sort: true,
				}
				if (item === '政治三力') {
					columns[index + 1].customization = 'CustomBlock'
				}
			})
			return columns
		}
		const columns = computed(() => {
			return dynamicTableHeader(apiData.value.xlabel)
		})
		// const columns = [
		// 	{
		// 		key: 'name',
		// 		align: 'center',
		// 		width: '10%',
		// 		title: '',
		// 	},
		// 	{
		// 		key: 'name1',
		// 		align: 'center',
		// 		sort: true,
		// 		width: '10%',
		// 		title: '政治三力',
		// 	},
		// 	{
		// 		key: 'name2',
		// 		align: 'center',
		// 		sort: true,
		// 		width: '10%',
		// 		title: '学习力',
		// 	},
		// 	{
		// 		key: 'name3',
		// 		align: 'center',
		// 		sort: true,
		// 		width: '10%',
		// 		title: '决策力',
		// 	},
		// 	{
		// 		key: 'name4',
		// 		align: 'center',
		// 		sort: true,
		// 		width: '10%',
		// 		title: '执行力',
		// 	},
		// 	{
		// 		key: 'name5',
		// 		align: 'center',
		// 		sort: true,
		// 		width: '14%',
		// 		title: '群众工作能力',
		// 	},
		// 	{
		// 		key: 'name6',
		// 		align: 'center',
		// 		sort: true,
		// 		width: '10%',
		// 		title: '担当',
		// 	},
		// 	{
		// 		key: 'name7',
		// 		align: 'center',
		// 		sort: true,
		// 		width: '10%',
		// 		title: '责任',
		// 	},
		// 	{
		// 		key: 'name8',
		// 		align: 'center',
		// 		sort: true,
		// 		width: '10%',
		// 		title: '正派',
		// 	},
		// ]

		const loadData = async () => {
			const { user_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			const res = await getAblityResult({ user_id })
			if (res.code === 0) {
				data.value = res.data

				// const Line = ['向成言', '程永忠', '谭亚军', '文海波', '秦春霞', '蒋舰', '平均值']

				apiData.value = formmaterData(res.data)
			}
		}
		// 格式化数据
		const formmaterData = (data: any) => {
			const { other_result, my_result, avg_list, xlabel } = data
			const apiData: any = {}
			// line
			apiData.line = [...other_result, my_result, avg_list].map((item: any) => {
				return item.name
			})
			// data
			apiData.data = [...other_result, my_result, avg_list].map((item: any) => item.list)

			apiData.xlabel = xlabel
			return apiData
		}
		loadData()
		// 每一行颜色渲染
		const rowColor = (_item: any, index: number) => {
			return color[index]
		}

		return { option, columns, rowColor, dataSource }
	},
})
</script>

<style scoped lang="less">
.box-content__line {
	height: 330px;
}
.box-content__table {
	margin-top: 39px;

	flex: 1;
}
</style>
