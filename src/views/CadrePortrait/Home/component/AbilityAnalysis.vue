<template>
	<Card title="测评对比" header size-type="3">
		<div class="box-content__line">
			<v-chart :option="option" autoresize></v-chart>
		</div>
		<div class="box-content__table">
			<DataTable :data-source="dataSource" :columns="columns" :show-max-min="showMaxMin" filter-row-name-in-max-min="平均值,序列平均值,部门平均值" />
		</div>
	</Card>
</template>

<script lang="ts">
import { computed, defineComponent, ref, inject } from 'vue'
import DataTable from '@/components/Table.vue'
import { getAblityResult } from '@/apis/cadre-portrait/home'
import useUser from '@/store/user.ts'
import { decreaseOpacity, convertPxToRem, debounce } from '@/utils/utils'
import { historyPush } from '@/utils/history'
import { Item } from 'ant-design-vue/lib/menu'
export default defineComponent({
	name: 'AbilityAnalysis',
	components: {
		DataTable,
	},
	setup() {
		const user_id = inject('user_id')

		const user: any = useUser()
		const data: any = ref({
			xlabel: [],
			other_result: [],
			avg_list: {},
		})
	
		const apiData = ref<any>({
			line: [],
			table: [],
			data: [],
			xlabel: [],
			source: [],
		})
		const color = [
			'#FF6C47',
			'#6AAEFB',
			'#6ADDE4',
			'#CBADFF',
			'#F06292',
			'#AED581',
			'#66BB6A',
			'#E6EE9C',
			'#BF360C',
			'#9FA8DA',
			'#80CBC4',
			'#9CCC65',
			'#03A9F4',
			'#00BCD4',
			'#A1887F',
			'#CDAD00',
			'#32CD32',
			'#CD5B45',
			'#A020F0',
			'#BDB76B',
		]
		const createData = (origin_data: [], title: string) => {
			const data: any = {}
			data.name = title
			for (let i = 0; i < origin_data.length; i++) {
				data[`name${i + 1}`] = origin_data[i]
			}

			return data
		}
		const dataSource = computed(() => {
			const datasource: any = []

			apiData.value.line.map((item: any, index: number) => {
				if (apiData.value.data[index]) {
					const _data: any[] = [].concat(apiData.value.data[index])

					// const _ = _data.shift()

					// _data.push(_)

					datasource.push(createData(_data as any, item))
				}
			})

			return datasource
		})
		const refreshOption = ref(0)
		// 刷新option 重新渲染echarts
		window.addEventListener(
			'resize',
			debounce(() => {
				refreshOption.value++
			}, 100)
		)
		const option = computed(() => {
			{
				refreshOption.value
			}
			// const XName = ['政治三力', '学习力', '决策力', '执行力', '群众工作能力', '担当', '责任', '正派']
			const [...XName] = apiData.value.xlabel
			
			// 弹出政治三力
			XName?.shift()

			// const Line = ['向成言', '程永忠', '谭亚军', '文海波', '秦春霞', '蒋舰', '平均值']
			const Line = apiData.value.line
			const { user_id, name } = user.detail
			const datas: any[] = []
			const selectedMap: any[]=[]
			let max = 0
			let min = 0
			let _index = 0
			Line.map((item: any, index: number) => {
				const [...data] = apiData.value.data[index]
				// 弹出政治三力
				data?.shift()
				let _color = ''
				let lineType = 'solid'
				let symbolSize = name === item ? 14 : 8
				let lineWidth = name === item ? 4 : 2

				const colorMap: any = {
					部门平均值: '#60CA71',
					乡镇平均值: '#FF81E3',
					序列平均值: '#60CA71',
					'乡镇/部门平均值': '#FF81E3',
				}

				if (item === '平均值') {
					lineType = 'dashed'
					_color = '#FE533A'
					lineWidth = 4
				} else if (item === name) {
					_color = '#FFA300'
				} else {
					_color = color[_index++]
				}
				const sortData = [...data].sort((a: number, b: number) => Number(a) - Number(b))
				let _min = Number(sortData[0])
				let _max = Number(sortData[sortData.length - 1])
				if (min === 0) {
					min = _min
				}
				if (max === 0) {
					max = _max
				}

				if (_min < min) {
					min = _min
				}
				if (_max > max) {
					max = _max
				}

				datas.push({
					symbolSize: convertPxToRem(symbolSize),
					symbol: 'circle',
					name: item,
					type: 'line',
					yAxisIndex: 1,
					data: data,
					color: colorMap[item] || _color,
					smooth: false,
					itemStyle: {
						borderWidth: convertPxToRem(4),
						borderColor: colorMap[item] || _color,
						color: '#fff',
						shadowColor: _color && decreaseOpacity(colorMap[item] || _color, 0.5),
						shadowBlur: 13,
					},
					lineStyle: {
						color: colorMap[item] || _color,
						type: item.includes('平均值') ? 'dashed' : 'solid',
						width: convertPxToRem(lineWidth),
					},
				})
			})
			max = Math.ceil(max + 5)
			// max >= 100 && (max = 100)

			min = Math.floor(min - 5)
			min <= 0 && (min = 0)
			
			
			const option = {
				// backgroundColor: '#0e2147',

				grid: {
					left: '1%',
					top: datas.length > 11 ? '23%' : '13%',
					bottom: '0%',
					right: '3%',
					containLabel: true,
				},
				legend: {
					show: true,
					top: 0,
					left: 'center',
					// type: 'scroll',
					// data: Line,
					icon: 'circle',
					itemWidth: convertPxToRem(12),
					itemHeight: convertPxToRem(12),
					textStyle: {
						color: '#333333',
						fontSize: convertPxToRem(18),
					},
					itemStyle: {
						borderWidth: convertPxToRem(4),
					},
					itemGap: convertPxToRem(28),
					selected: {
						
					}
				},
				yAxis: [
					{
						type: 'value',
						position: 'right',
						splitLine: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					{
						type: 'value',
						position: 'left',
						// scale: true,
						min: (value: any) => (value.min < 5 ? 0 : value.min - 5),
						max: (value: any) => value.max + 5,
						nameTextStyle: {
							color: '#00FFFF',
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#EEEEEE',
							},
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: ['none', 'arrow'],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisTick: {
							show: false,
						},
						// splitNumber: 4,
						// interval: 1,
						axisLabel: {
							color: '#666666',
							fontSize: convertPxToRem(16),
							formatter: (value: any) => {
								value = Number(value)
								return value > 100 ? '' : value.toFixed(2)
							},
						},
					},
				],
				xAxis: [
					{
						type: 'category',
						axisTick: {
							show: false,
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
								'arrow',
							],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							inside: false,
							textStyle: {
								color: '#666666', // x轴颜色
								fontWeight: 'normal',
								fontSize: convertPxToRem(16),
								lineHeight: convertPxToRem(22),
							},
							interval: 0,
						},
						data: XName,
					},
				],
				series: datas,
			}
			let selected_item :any= {};
			datas.map((i:any)=>{
				if(i.name==name || i.name=='序列平均值'){
					selected_item[i.name] = true;
				}else{
					selected_item[i.name] = false;
				}
			})
			option.legend.selected =selected_item ;
			return option
		})

		
		const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
			const columns: Array<any> = []

			list.forEach((item: any, index: number) => {
				const key = `name`
				if (index === 0) {
					columns[index] = {
						key,
						title,
						width: '10%',
						colClick: (value: any) => {
							const user = apiData.value.source.find((item: any) => item.name === value.name)

							historyPush(`/cadre-portrait/home?user_id=${user.user_id}`)
						},
					}
				}
				columns[index + 1] = {
					key: `${key}${index + 1}`,
					title: item,
					width: 100 / (list.length + 1) + '%',
					align: 'center',
					sort: true,
					showMax: true,
					showMin: true,
				}
				if (item === '政治三力') {
					columns[index + 1].customization = 'CustomBlock'
				}
			})
			return columns
		}
		const columns = computed(() => {
			const xlabel: any[] = [].concat(apiData.value.xlabel)

			// const _ = xlabel.shift()

			// xlabel.push(_)

			// console.log('🚀 ~ columns ~ xlabel:', xlabel)

			return dynamicTableHeader(xlabel)
		})
		const showMaxMin = ref(false)
		const loadData = async () => {
			if (!user_id) return

			const res: any = await getAblityResult({ user_id })
			if (res.code === 0 && Object.keys(res.data || {}).length > 0) {
				data.value = res.data

				// const Line = ['向成言', '程永忠', '谭亚军', '文海波', '秦春霞', '蒋舰', '平均值']
				const { other_result = [] } = res.data
				if ([].concat(other_result).length > 1) {
					showMaxMin.value = true
				}

				apiData.value = formmaterData(res.data, showMaxMin.value)
			}
		}
		// 格式化数据
		const formmaterData = (data: any, showAvg: boolean) => {
			const { other_result, avg_list, xlabel, avg_sequence_list, avg_men_list } = data
			const apiData: any = {}
			const _data = [...other_result]
			showAvg && _data.push(avg_list)
			avg_sequence_list && _data.push(avg_sequence_list)
			avg_men_list && _data.push(avg_men_list)
			// line
			apiData.line = _data.map((item: any) => {
				return item.name
			})
			// data
			apiData.data = _data.map((item: any) => item.list)

			apiData.xlabel = xlabel

			apiData.source = _data
			return apiData
		}
		loadData()
		// 每一行颜色渲染
		const rowColor = (_item: any, index: number) => {
			return color[index]
		}

		return { option, columns, rowColor, dataSource, showMaxMin }
	},
})
</script>

<style scoped lang="less">
.box-content__line {
	height: 440px;
}
.box-content__table {
	margin-top: 39px;

	flex: 1;
}
</style>
