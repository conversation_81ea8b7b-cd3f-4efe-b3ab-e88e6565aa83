<template>
	<div class="cadre-box">
		<div class="left-box">
			<div class="top top-1">
				<div class="left">
					<span>干部指数</span>
					<span class="icon" @click="openRuleModal(1)"></span>
				</div>
				<div class="right" @click="onShow">查看历史>></div>
			</div>
			<div class="middle">
				<div class="inline-box">
					<div class="index">{{ leader_index || '0.0' }}</div>
					<div class="rank">
						<div class="text">同序列</div>
						<div class="index_rank">{{ leader_index_rank }}</div>
					</div>
				</div>
			</div>
			<div class="bottom">
				<div class="progress" :style="`width:${leaderIndexPercent}%`"></div>
			</div>
		</div>
		<div class="middle-box" :style="`background-image: ${riskColor.backgroundColor}`">
			<div class="top top-2 no-wrap">
				<div class="left">
					<span>风险指数</span>
					<span class="icon" @click="openRuleModal(2)"></span>
				</div>
				<div
					@click="onDropVisible"
					v-if="warnData.length"
					:class="{ 'warn-right': true, 'drop-icon-rotate': dropMenuVisible, 'drop-icon': true, 'warn-right-4': warnData?.[0] == 4 }"
				>
					<!-- <span>{{ warnData.length === 1 ? dataMap[warnData[0]] : warnData.length + '条' }}</span> -->
					<span>{{ warnData.length + '条' }}</span>
					<Transition name="drop">
						<div class="drop-menu" v-if="dropMenuVisible" @click.stop>
							<div v-for="({ type_flag, expand, type, detail }, index) in warnData" class="menu-item" :key="type_flag">
								<div class="level-normal" :class="`level-${type_flag} ${!!detail ? 'effect-icon' : ''}`">
									<div class="level-content">{{ type }}</div>
									<span @click="onExpand(index, expand)" :class="{ iconRotateZ: expand }"></span>
								</div>
								<div :class="`expand-box expand-box-${type_flag}`" v-if="expand" v-html="splitStr(detail)"></div>
							</div>
						</div>
					</Transition>
				</div>
			</div>
			<div class="middle">
				<div class="inline-box">
					<span class="index" :style="`color: ${riskColor.textColor}`">{{ riskIndex || '0.0' }}</span>
					<span class="rank" :style="`background-color: ${riskColor.textColor}`"></span>
				</div>
			</div>
			<div class="bottom">
				<div class="progress" :style="`width:${riskIndexPercent}%;background-color: ${riskColor.textColor}`"></div>
			</div>
		</div>
		<!-- <div class="right-box" @click="onRouter()" :style="`background: ${inspectionIndexStyle.backgroundColor}!important`">
			<div class="top top-1">
				<div class="left">
					<span>巡察指数</span>
					<span class="icon"></span>
				</div>
			</div>
			<div class="middle">
				<div class="inline-box">
					<div class="index">
						<span :style="inspectionIndexStyle">
							{{ inspectionIndex.inspection_index }}
						</span>
					</div>
					<div
						class="rank"
						:style="{
							backgroundColor: inspectionIndexStyle.color,
						}"
					>
						<div class="text">{{ inspectionIndex.leader === 1 ? '同序列' : '班子内' }}</div>
						<div class="index_rank">{{ inspectionIndex.inspection_index_rank === '0/0' ? '-' : inspectionIndex.inspection_index_rank }}</div>
					</div>
				</div>
			</div>
			<div class="bottom">
				<div class="progress" :style="`width:${inspectionIndex.inspectionIndexPercent}%;background-color: ${inspectionIndexStyle.color}`"></div>
			</div>
		</div> -->
	</div>
	<a-modal v-model:visible="visible" title="干部指数变化" :footer="null" class="cadre-box-modal">
		<div class="chart-box">
			<v-chart :option="option" autoresize></v-chart>
		</div>
		<div class="table-box">
			<a-table :columns="columns" :data-source="dataSource" bordered :pagination="false"></a-table>
		</div>
	</a-modal>
</template>

<script lang="ts">
import { defineComponent, inject, computed, ref, toRefs, watch } from 'vue'
import useUser from '@/store/user.ts'
import { convertPxToRem, decreaseOpacity } from '@/utils/utils'
import { getAlertUser } from '@/apis/cadre-portrait/home'
import { getInspectionIndex } from '@/apis/inspection-index'
import { useRoute } from 'vue-router'
import router from '@/router'
export default defineComponent({
	props: {
		userInfo: {
			type: Object,
			default: () => ({}),
		},
		risk_index: {
			type: Number,
			default: 0,
		},
		leader_index: {
			type: Number,
			default: 0,
		},
		leader_index_rank: {
			type: String,
			default: '',
		},
	},
	setup(props) {
		const user: any = useUser()
		const openRuleModal: any = inject('openRuleModal')
		const isEffect = ref()
		const user_id = useRoute().query.user_id

		const visible = ref(false)
		const dropMenuVisible = ref(false)

		const riskIndexPercent = computed(() => (props.risk_index ? (props.risk_index > 100 ? 100 : props.risk_index) : 0))
		const leaderIndexPercent = computed(() => (props.leader_index ? (props.leader_index > 100 ? 100 : props.leader_index) : 0))

		const riskColor = computed(() => {
			// const riskIndex = props.risk_index || 0
			const riskIndex = props.risk_index || 0
			const colorMap = {
				textColor: '',
				backgroundColor: '',
			}
			if (riskIndex >= 0 && riskIndex < 10) {
				colorMap.textColor = '#60CA71'
				colorMap.backgroundColor = 'linear-gradient(305deg, #DFFFE0 0%, #FFFFFF 100%);'
			} else if (riskIndex >= 10 && riskIndex < 40) {
				colorMap.textColor = '#F6DD00'
				colorMap.backgroundColor = 'linear-gradient(305deg, #FFFFDD 0%, #FFFFFF 100%)'
			} else if (riskIndex >= 40 && riskIndex < 70) {
				colorMap.textColor = '#FF9900'
				colorMap.backgroundColor = 'linear-gradient(305deg, #FEF0DA 0%, #FFFFFF 100%)'
			} else {
				colorMap.textColor = '#FF0000'
				colorMap.backgroundColor = 'linear-gradient(305deg, #FFE2E2 0%, #FFFFFF 100%)'
			}
			return colorMap
		})
		const riskIndex = computed(() => {
			return props.risk_index == -1 ? '一票否决' : props.risk_index
		})
		const colorMap: any = ['#6AAEFB', '#B5B5B5']

		const datas = ['我的', '序列平均值']

		const option = ref()
		const dataSource: any = ref([])

		const columns = ref([])

		const onShow = () => {
			visible.value = true
		}
		const determineColor = (value: number) => {
			const colorMap = {
				color: '',
				backgroundColor: '',
			}

			if (value >= -10) {
				colorMap.color = 'rgba(96, 202, 113, 1)'
				colorMap.backgroundColor = 'linear-gradient(270deg, #EAFFEB 0%, #FFFFFF 100%);'
			} else if (value >= -15) {
				colorMap.color = 'rgba(255, 128, 0, 1)'
				colorMap.backgroundColor = 'linear-gradient( 270deg,#FFF6ED 0%, #FFFFFF 100%);'
			} else {
				colorMap.color = 'rgba(180, 105, 0, 1)'
				colorMap.backgroundColor = 'linear-gradient( 270deg, #F6EDE0 0%, rgba(255,255,255,0) 100%);'
			}
			console.log(colorMap)
			return colorMap
		}

		const onExpand = (index: number, expand: boolean) => {
			warnData.value.map((item: any, _index: number) => {
				if (index === _index) {
					item.expand = !expand
				}
			})
		}

		const splitStr = (str: string) => {
			return str
				.split(';')
				.map((item: any) => {
					return `<div>${item}</div>`
				})
				.join(' ')
		}

		const setOption = (params: any) => {
			const { years, my, sequence_avg } = params
			const series = [my, sequence_avg].map((item, index) => {
				return {
					symbolSize: convertPxToRem(8),
					symbol: 'circle',
					name: datas[index],
					type: 'line',
					yAxisIndex: 1,
					data: item,
					color: colorMap[index],
					smooth: false,
					itemStyle: {
						borderWidth: convertPxToRem(4),
						borderColor: colorMap[index],
						color: '#fff',
						shadowColor: decreaseOpacity(colorMap[index], 0.5),
						shadowBlur: 13,
					},
					lineStyle: {
						color: colorMap[index],
						type: item.includes('平均值') ? 'dashed' : 'solid',
						width: convertPxToRem(2),
					},
				}
			})
			option.value = {
				// backgroundColor: '#0e2147',

				grid: {
					left: '0%',
					top: '14%',
					bottom: '6%',
					right: '6%',
					containLabel: true,
				},
				legend: {
					show: true,
					top: 0,
					left: 'center',
					// type: 'scroll',
					// data: Line,
					icon: 'circle',
					itemWidth: convertPxToRem(12),
					itemHeight: convertPxToRem(12),
					textStyle: {
						color: '#333333',
						fontSize: convertPxToRem(16),
						lineHeight: convertPxToRem(16),
					},
					itemStyle: {
						borderWidth: convertPxToRem(4),
					},
					itemGap: convertPxToRem(28),
				},
				yAxis: [
					{
						type: 'value',
						position: 'right',
						splitLine: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					{
						type: 'value',
						position: 'left',
						// scale: true,
						min: (value: any) => (value.min < 5 ? 0 : value.min - 5),
						max: (value: any) => value.max + 5,
						nameTextStyle: {
							color: '#00FFFF',
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#EEEEEE',
							},
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: ['none', 'arrow'],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisTick: {
							show: false,
						},
						// splitNumber: 4,
						// interval: 1,
						axisLabel: {
							color: '#666666',
							fontSize: convertPxToRem(16),
							formatter: (value: any) => {
								value = Number(value)
								return value > 100 ? '' : value.toFixed(2)
							},
						},
					},
				],
				xAxis: [
					{
						type: 'category',
						axisTick: {
							show: false,
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
								'arrow',
							],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							inside: false,
							textStyle: {
								color: '#666666', // x轴颜色
								fontWeight: 'normal',
								fontSize: convertPxToRem(16),
								lineHeight: convertPxToRem(22),
							},
							interval: 0,
						},
						data: years,
					},
				],
				series,
			}
		}

		const setDatasource = (data: []) => {
			const cadreIndex: any = {
				title: '干部指数',
			}
			const avg: any = {
				title: '序列平均值',
			}
			const singrank: any = {
				title: '同序列排名',
			}
			const _columns: any = [
				{
					dataIndex: 'title',
					title: '',
					key: 'title',
					align: 'center',
				},
			]
			data.map((item: any) => {
				const { cadre_index, sequence_avg, sequence_rank, year } = item
				// 干部指数
				cadreIndex[year] = cadre_index
				avg[year] = sequence_avg
				singrank[year] = sequence_rank
				_columns.push({
					dataIndex: year,
					title: year,
					key: year,
					align: 'center',
				})
				// 序列平均值
				// 同序列排名
			})

			dataSource.value = [cadreIndex, avg, singrank]

			columns.value = _columns
		}

		const onDropVisible = () => {
			dropMenuVisible.value = !dropMenuVisible.value
		}
		const warnData = ref([])

		const dataMap: any = {
			// 1-处分影响期内，2-试用期即将届满，3-任现职时间4年，4-任现职时间9年，5-任职回避
			1: '处分影响期内',
			2: '试用期即将届满',
			3: '任现职时间4年',
			4: '任现职时间9年',
			5: '任职回避',
		}
		const loadWarnInfo = () => {
			getAlertUser({ user_id }).then((res: any) => {
				if (res.code === 0 && res.data) {
					warnData.value = res.data?.map((item: any) => {
						item.expand = false
						return item
					})
				}
			})
		}
		const inspectionIndex = ref({
			leader: 1,
			inspection_index: 0,
			inspection_index_rank: '0/0',
			inspectionIndexPercent: 0,
		})

		const loadInspection = () => {
			getInspectionIndex({ user_id }).then((res: any) => {
				if (res.code === 0 && res.data) {
					const { inspection_index } = res.data

					inspectionIndex.value = { ...res.data, inspectionIndexPercent: inspection_index ? (inspection_index > 100 ? 100 : inspection_index) : 0 }
				}
			})
		}

		watch(
			props,
			(newV: any, oldV) => {
				const { cadre_index_history } = newV.userInfo

				if (cadre_index_history) {
					const { years, my, sequence_avg, history_index } = cadre_index_history

					setOption({ years, my, sequence_avg })

					setDatasource(history_index)
				}
			},
			{
				deep: true,
			}
		)

		loadWarnInfo()
		loadInspection()

		const onRouter = () => {
			router.push({ path: '/inspection-index', query: { user_id } })
		}

		const inspectionIndexStyle = computed(() => {
			const inspect_index = inspectionIndex.value.inspection_index || 0

			return determineColor(inspect_index)
		})
		return {
			user,
			option,
			columns,
			visible,
			riskColor,
			dropMenuVisible,
			dataMap,
			riskIndex,
			warnData,
			isEffect,
			dataSource,
			openRuleModal,
			onDropVisible,
			inspectionIndexStyle,
			inspectionIndex,
			riskIndexPercent,
			leaderIndexPercent,
			onShow,
			splitStr,
			onRouter,
			onExpand,
		}
	},
})
</script>

<style scoped lang="less">
.cadre-box {
	display: flex;
	justify-content: space-between;
	gap: 0px 20px;
	width: 100%;
	.left-box,
	.middle-box,
	.right-box {
		padding: 33px 32px;
		width: 49%;
		height: 216px;
		background: linear-gradient(305deg, #e5fee6 0%, #ffffff 100%);
		border-radius: 8px 8px 8px 8px;
		opacity: 1;
		box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
		.no-wrap {
			white-space: nowrap;
		}
		.top {
			font-size: 24px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #333333;
			line-height: 28px;

			display: flex;
			align-items: center;
			.right {
				font-size: 22px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #2462ff;
				cursor: pointer;
			}
			.warn-right {
				display: flex;
				justify-content: space-between;
				align-items: center;
				min-width: 113px;
				padding: 10px;
				height: 40px;
				background: #ffa300;
				border-radius: 6px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 20px;
				color: #ffffff;
				line-height: 23px;
				&::before {
					content: '';
					display: inline-block;
					height: 24px;
					width: 24px;
					background: url('../../images/cadre-card-icon.png') center / cover no-repeat;
					transition: all linear 0.3s;
				}
			}
			.warn-right-4 {
				background: #d23122 !important;
				// &::before {
				// 	background: url('../../images/cadre-card-icon2.png') center / cover no-repeat !important;
				// }
			}
			.drop-icon-rotate {
				&::after {
					transform: rotateZ(180deg);
				}
			}
			.drop-icon {
				position: relative;
				&::after {
					content: '';
					display: inline-block;
					width: 24px;
					height: 24px;
					background: url('../../images/cadre-card-icon1.png') center / cover no-repeat;
				}
				.drop-menu {
					position: absolute;
					top: 100%;
					right: 0px;
					display: flex;
					flex-direction: column;
					gap: 14px 0px;
					width: 244px;
					max-height: 600px;
					overflow: auto;
					min-height: 131px;
					padding: 18px 12px;
					background: #ffffff;
					box-shadow: 0px 0 8px 0px rgba(0, 0, 0, 0.1);
					border-radius: 6px 6px 6px 6px;
					.menu-item {
						border-radius: 6px 6px 6px 6px;
						user-select: none;
					}

					.level {
						display: flex;
						align-items: center;
						width: 90%;
						&::before {
							margin-right: 5px;
						}
					}
					.effect-icon {
						display: flex;
						width: 100%;
						.iconRotateZ {
							transform: rotateZ(0deg);
						}
						span {
							justify-items: end;
							display: inline-block;
							width: 24px;
							height: 24px;
							background: url('../../images/<EMAIL>') center / cover no-repeat;
							transform: rotateZ(180deg);
						}
					}
					.level-1,
					.level-4,
					.level-6 {
						font-size: 20px;
						color: #d23122 !important;
						&::before {
							content: '';
							display: inline-block;
							background-image: url('../../images/cadre-card-icon2.png') !important;
							vertical-align: top;
						}
					}
					.expand-box {
						margin-top: 5px;
						padding: 6px;
						border-radius: 6px 6px 6px 6px;
						background: rgba(255, 163, 0, 0.1);
						color: rgba(255, 163, 0, 1);
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 18px;
						line-height: 21px;
						white-space: normal;
					}

					.expand-box-1,
					.expand-box-4,
					.expand-box-6 {
						background: #fff8f8 !important;
						color: rgba(210, 49, 34, 0.95) !important;
					}

					.level-normal {
						font-size: 20px;
						color: #ffa300;
						.level-content {
							flex: 1;
						}

						&::before {
							margin-right: 12px;
							content: '';
							display: inline-block;
							width: 26px;
							height: 26px;
							border-radius: 50%;
							background: url('../../images/cadre-card-icon3.png') rgba(255, 163, 0, 0.1) center center/ 20px 20px no-repeat;
							vertical-align: top;
						}
					}
				}
			}
		}
		.top-1 {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		.top-2 {
			display: flex;
			justify-content: space-between;
		}
		.middle {
			width: 100%;
			height: 50px;
			margin-top: 26px;
			color: #60ca71;
			.inline-box {
				display: flex;
				align-items: flex-end;
				width: 100%;
				height: 100%;
				span {
				}
			}
			.index {
				line-height: 48px;
				font-size: 50px;
				font-weight: bold;
				font-family: 'BAKHUM';
			}
			.rank {
				display: flex;
				flex-direction: column;
				justify-content: center;
				margin-left: 27px;
				padding: 4px 0px;
				width: 96px;
				text-align: center;
				background: #60ca71;
				border-radius: 2px 2px 2px 2px;
				opacity: 1;
				color: #ffffff;
				.text {
					font-size: 14px;
					line-height: 1;
				}
				.index_rank {
					margin-top: 4px;
					font-size: 20px;
					line-height: 20px;
				}
			}
		}
		.bottom {
			margin-top: 16px;
			width: 100%;
			height: 12px;
			background: #f5f5f5;
			border-radius: 30px 30px 30px 30px;
			opacity: 1;
			overflow: hidden;
			.progress {
				height: 12px;
				width: 50%;
				background: #60ca71;
				border-radius: 30px 30px 30px 30px;
				opacity: 1;
			}
		}
	}
	.middle-box {
		.middle {
			.inline-box {
				align-items: flex-end;
			}
			.index {
				line-height: 40px !important;
			}
			.rank {
				width: 79px;
				height: 24px;
			}
		}
		.bottom {
			.progress {
				width: 79px;
				height: 12px;
				border-radius: 30px 30px 30px 30px;
				opacity: 1;
			}
		}
	}
	.icon {
		margin-left: 12px;
		display: inline-block;
		width: 40px;
		height: 40px;
		background: url('@/assets/images/question-mark.png') no-repeat center center / 100% 100%;
		vertical-align: middle;
		cursor: pointer;
	}
	.right-box {
		background: linear-gradient(305deg, #ffe9e7 0%, #ffffff 100%);
		.middle {
			.inline-box {
				.index {
					color: rgba(210, 49, 34, 1);
				}
				.rank {
					background-color: rgba(210, 49, 34, 1);
					color: rgba(255, 255, 255, 1);
				}
				.no-data {
					font-size: 30px;
					color: rgb(173, 173, 173);
				}
			}
		}
		.bottom {
			.progress {
				background-color: rgba(210, 49, 34, 1);
			}
		}
	}
}
.chart-box {
	height: 219px;
	width: 100%;
}
.drop-enter,
.drop-leave-to {
	opacity: 0;
}
.drop-enter-active,
.drop-leave-active {
	transition: all 0.3s;
}
</style>
<style lang="less">
.cadre-box-modal {
	width: 902px !important;

	.ant-modal-title {
		font-size: 24px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		text-align: center;
	}
}
</style>
