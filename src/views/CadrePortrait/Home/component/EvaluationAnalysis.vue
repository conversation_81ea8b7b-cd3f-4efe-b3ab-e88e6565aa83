<template>
	<Card title="测评分析" header size-type="5">
		<template #right>
			<a-button type="primary" class="detail-btn" @click="onDetail" :disabled="!data?.length">详情</a-button>
		</template>

		<div class="evaluation-analysis">
			<PersonalAnalysis :line-data="data3" v-if="data3.xlabel" />
		</div>
		<a-modal :visible="visible" width="80%" :footer="null" @cancel="visible = false" destoryOnClose>
			<TimeLine :times="times_computed" v-model:value="time" class="m-top-20" />
			<template v-if="time == thirdMap.label">
				<div class="evaluation-analysis p-20">
					<div class="box-content__line">
						<v-chart :option="option" autoresize ref="echarts"></v-chart>
					</div>
					<div class="m-top-0 box-content__table">
						<DataTable :data-source="dataSource()" :columns="columns" />
					</div>
				</div>
			</template>
			<template v-else>
				<PersonalAnalysis :line-data="data1" v-if="data1.xlabel" />
			</template>
		</a-modal>
	</Card>
</template>

<script lang="ts" setup>
import { ref, inject, computed, watch, reactive } from 'vue'
import { decreaseOpacity, convertPxToRem } from '@/utils/utils'
import DataTable, { CustomBlock } from '@/components/Table.vue'
import { getPersonelResult, getTestResultDetail, getAnalysis } from '@/apis/cadre-portrait/home'
import TimeLine from '@/components/Timeline.vue'
import PersonalAnalysis from './PersonalAnalysis.vue'
const user_id = inject('user_id')

const option = ref({})

const visible = ref(false)

const datasource = ref([])

const data = ref<any>([])

const data1: any = ref({})
const data3: any = ref({
	label: [],
})

const time = ref('')

const data2 = {
	xlabel: ['政治三力', '学习能力', '决策能力', '执行能力', '沟通协调能力', '群众工作能力', '奉献度', '勤奋度', '正直度', '测评分数', '干部指数'],
	other_result: [
		{ user_id: 3555, name: '谭晓斌', list: ['99.23', '97.36', '96.76', '96.38', '96.48', '97.91', '96.92', '98.08', '86.32', '94.23', '91.13'] },
		{ user_id: 3431, name: '罗立力', list: ['99.23', '97.36', '96.76', '96.38', '96.48', '97.91', '96.92', '98.08', '86.32', '86.62', '87.33'] },
		{ user_id: 3804, name: '郑澈', list: ['99.23', '97.36', '96.76', '96.38', '96.48', '97.91', '96.92', '98.08', '86.32', '95.46', '91.75'] },
		{ user_id: 3465, name: '乔聪聪', list: ['99.23', '97.36', '96.76', '96.38', '96.48', '97.91', '96.92', '98.08', '86.32', '97.58', '92.81'] },
	],
	my_result: {
		user_id: 3433,
		name: '罗燊',
		list: ['99.23', '97.36', '96.76', '96.38', '96.48', '97.91', '96.92', '98.08', '86.32', '96.05', '92.05'],
	},
	avg_list: {
		user_id: -1000,
		name: '平均值',
		list: ['99.23', '97.36', '96.76', '96.38', '96.48', '97.91', '96.92', '98.08', '86.32', '93.99', '91.01'],
	},
	avg_sequence_list: {
		user_id: -1002,
		name: '序列平均值',
		list: ['98.79', '96', '87.91', '85.23', '95.55', '85.44', '86.15', '94.89', '85', '91.73', '89.9'],
	},
}

// const columns = [
// 	{
// 		key: '0',
// 		title: '',
// 		align: 'center',
// 		rowSpan: (span: any) => {
// 			return span
// 		},
// 		width: '9%',
// 	},
// 	{
// 		key: '1',
// 		title: '政治三力',
// 		align: 'center',
// 		sort: true,
// 		customization: 'CustomBlock',
// 		width: '9%',
// 	},
// 	{
// 		key: '2',
// 		title: '学习能力',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// 	{
// 		key: '3',
// 		title: '决策能力',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// 	{
// 		key: '4',
// 		title: '执行能力',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// 	{
// 		key: '5',
// 		title: '沟通协调能力',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// 	{
// 		key: '6',
// 		title: '群众工作能力',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// 	{
// 		key: '7',
// 		title: '奉献度',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// 	{
// 		key: '8',
// 		title: '勤奋度',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// 	{
// 		key: '9',
// 		title: '正直度',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// 	{
// 		key: '10',
// 		title: '测评分数',
// 		align: 'center',
// 		sort: true,
// 		width: '9%',
// 	},
// ]

const columns1 = computed(() => {
	const total = data.value.all.length

	return [
		{
			key: '0',
			dataIndex: '0',
			title: '',
			align: 'center',
			customCell: (record: any) => {
				if (record['0']) {
					return {
						rowSpan: total > 4 ? total / 2 : total,
					}
				} else {
					return {
						rowSpan: 0,
					}
				}
			},
			width: '8%',
		},
		{
			key: '1',
			dataIndex: '1',
			title: '',
			align: 'center',
			width: '8%',
		},
		{
			key: '2',
			dataIndex: '2',
			title: '政治三力',
			align: 'center',
			sort: true,
			customization: 'CustomBlock',
			width: '8%',
		},
		{
			key: '3',
			dataIndex: '3',
			title: '学习能力',
			align: 'center',
			sort: true,
			width: '8%',
		},
		{
			key: '4',
			dataIndex: '4',
			title: '决策能力',
			align: 'center',
			sort: true,
			width: '8%',
		},
		{
			key: '5',
			dataIndex: '5',
			title: '执行能力',
			align: 'center',
			sort: true,
			width: '8%',
		},
		{
			key: '6',
			dataIndex: '6',
			title: '沟通协调能力',
			align: 'center',
			sort: true,
			width: '8%',
		},
		{
			key: '7',
			dataIndex: '7',
			title: '群众工作能力',
			align: 'center',
			sort: true,
			width: '8%',
		},
		{
			key: '8',
			dataIndex: '8',
			title: '奉献度',
			align: 'center',
			sort: true,
			width: '8%',
		},
		{
			key: '9',
			dataIndex: '9',
			title: '勤奋度',
			align: 'center',
			sort: true,
			width: '8%',
		},
		{
			key: '10',
			dataIndex: '10',
			title: '正直度',
			align: 'center',
			sort: true,
			width: '8%',
		},
		{
			key: '11',
			dataIndex: '11',
			title: '测评分数',
			align: 'center',
			sort: true,
			width: '8%',
		},
	]
})
const thirdMap = {
	key: '3',
	label: '近三次平均',
}
const times_computed = computed(() => {
	const timeData = data.value.map((item: any) => item.time).filter((item: any) => item !== 'avg')

	timeData.push(thirdMap.label)

	return timeData
})

const dataSource = () => {
	const _data = [...data.value].map((item: any) => {
		return [item.time === 'avg' ? '近三次平均' : item.time, ...(item?.data[item.time === 'avg' ? 'avg' : 'total'] || [])]
	})

	function addKeyWithIndex(arr: any[]) {
		return arr.map((item) => {
			const obj: any = {}

			item.map((item: any, index: number) => {
				return (obj[index] = item)
			})

			return obj
		})
	}
	console.log(addKeyWithIndex(_data))
	return addKeyWithIndex(_data)
}
const dealData = (data: any) => {
	console.log('🚀 ~ dealData ~ yearData:', data)
	const keys = Object.keys(data)
	console.log('🚀 ~ dealData ~ keys:', keys)

	// 过滤出为xlabel的数据
	const yearData = keys.filter((item: any) => item !== 'xlabel')

	let _data: any = []
	let _sing_data: any = []
	let _origin_data: any = []

	yearData.map((item: any) => {
		const current_year_data = data[item]
		// 每条数据分为upper上级，same 同级，down 下级，total 综合 几个字段
		const { upper, same, down, total } = current_year_data
		// 为每条数据添加年份
		// 判断每条数据是否存在
		if (upper) {
			_data.push([item, '上级测评', ...upper])
		}
		if (same) {
			_data.push(['', '同级测评', ...same])
		}
		if (down) {
			_data.push(['', '下级测评', ...down])
		}
		if (total) {
			_data.push(['', '综合得分', ...total])
		}

		_sing_data.push([item, ...total])

		_origin_data.push(total)
	})
	// 根据下标生成key

	_sing_data = addKeyWithIndex(_sing_data)

	_data = addKeyWithIndex(_data)

	function addKeyWithIndex(arr: any[]) {
		return arr.map((item) => {
			const obj: any = {}

			item.map((item: any, index: number) => {
				return (obj[index] = item)
			})

			return obj
		})
	}

	return {
		all: _data,
		sing: _sing_data,
		year_label: yearData,
		origin_data: _origin_data,
	}
}

const onDetail = () => {
	visible.value = true
}
const label = [/**'政治三力', */ '学习能力', '决策能力', '执行能力', '沟通协调能力', '群众工作能力', '奉献度', '勤奋度', '正直度', '测评分数']

const _label = reactive({
	charts: [],
	table: [],
})

const loadOption = (data = [] as Array<any>) => {
	const color = ['#6AAEFB', '#60CA71', '#FF6C47', '#FFA300']

	const series = data.map((item: any, index: number) => {
		const total = item.data?.total?.slice(1)

		return {
			symbolSize: convertPxToRem(12),
			symbol: 'circle',
			name: item.time,
			type: 'line',
			yAxisIndex: 1,
			data: total,
			smooth: false,
			itemStyle: {
				borderWidth: convertPxToRem(4),
				borderColor: color[index],
				color: '#ffffff',
				shadowColor: decreaseOpacity(color[index], 0.5),
				shadowBlur: 5,
			},
			connectNulls: true,
			lineStyle: {
				color: color[index],
				width: convertPxToRem(3),
				type: item.type || 'solid',
			},
		}
	})
	option.value = {
		color: ['#17EBC7'],
		grid: {
			left: '0%',
			top: '18%',
			bottom: '10%',
			right: '3%',
			containLabel: true,
		},
		legend: {
			// selected: {
			// 	// 选中'系列1'
			// 	上级测评: legendSelect.value.upper,
			// 	// 不选中'系列2'
			// 	同级测评: legendSelect.value.same,
			// 	下级测评: legendSelect.value.down,
			// 	综合平均分: legendSelect.value.composite_avg,
			// 	同序列综合平均分: legendSelect.value.overall_same_sequence_avg_score,
			// 	'乡镇/部门平均分': legendSelect.value.overall_same_department_avg_score,
			// },
			show: true,
			top: 5,
			left: 'center',
			// type: 'scroll',
			// data: legendData.value,
			icon: 'circle',
			itemWidth: convertPxToRem(12),
			itemHeight: convertPxToRem(12),
			textStyle: {
				color: '#333333',
				fontSize: convertPxToRem(18),
			},
			itemStyle: {
				borderWidth: convertPxToRem(4),
			},
			itemGap: convertPxToRem(24),
			// selectedMode: false,
		},
		yAxis: [
			{
				type: 'value',
				position: 'right',
				splitLine: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
			},
			{
				type: 'value',
				position: 'left',
				scale: true,
				nameTextStyle: {
					color: '#00FFFF',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',
						color: '#EEEEEE',
					},
				},
				// min: (value: any) => (value.min <script 5 ? 0 : value.min - 5),
				// max: (value: any) => value.max + 5,
				// splitNumber: 7,
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					formatter: (value: any) => {
						value = Number(value)
						return value > 100 ? '' : value.toFixed(2)
					},
					color: '#666666',
					fontSize: convertPxToRem(16),
					interval: 5,
					showMaxLabel: false,
				},
			},
		],
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M384 883.08c-8.19 0-16.38-3.12-22.62-9.38-12.5-12.5-12.5-32.75 0-45.25L677.83 512 361.38 195.55c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L734.4 478.07c18.72 18.72 18.72 49.16 0 67.88L406.62 873.7c-6.24 6.25-14.43 9.38-22.62 9.38z m305.14-359.77h0.31-0.31z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				axisLabel: {
					inside: false,
					textStyle: {
						color: '#666666', // x轴颜色
						fontWeight: 'normal',
						fontSize: convertPxToRem(16),
						lineHeight: convertPxToRem(22),
					},
					interval: 0,
				},
				data: _label.charts,
			},
			{
				type: 'category',
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				splitArea: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				//-----
			},
		],
		series,
	}
}

// const loadData = async () => {
// 	const res = await getPersonelResult({ user_id: user_id })
// 	if (res.code === 0 && res.data) {
// 		data1.value = res.data

// 		data.value = dealData(res.data)

// 		loadOption(data.value, res.data)
// 	}
// }

// loadData()
const loadData = async () => {
	const res = await getTestResultDetail({ user_id })

	if (res.code === 0 && res.data) {
		// 返回原始数据，生成同序列的值
		// res.data.forEach((item: any) => {
		// 	_data.push(item, {
		// 		time: item.time + '(序列平均)',
		// 		type: 'dashed',
		// 		data: {
		// 			total: item.data.overall_same_sequence_avg_score,
		// 		},
		// 	})
		// })

		data.value = res.data

		const _data = res.data.map((item: any) => {
			const isAvg = item.time === 'avg'
			return {
				time: isAvg ? '近三次平均' : item.time,
				type: 'dashed',
				data: {
					total: item.data[isAvg ? 'avg' : 'total'],
				},
			}
		})

		loadOption(_data)

		// 初始化数据
		const _tmp = res.data.filter((item: any) => item.time !== 'avg')

		const lastData = _tmp[_tmp.length - 1]

		if (lastData) {
			time.value = lastData.time

			data3.value = { ...lastData.data, xlabel: _label.charts, tableColumns: _label.table }
		}
	}
}

const columns = ref<any>([])

const loadLabel = () => {
	getAnalysis({ user_id }).then((res: any) => {
		if (res.code === 0) {
			_label.charts = res.data?.slice(1)
			_label.table = res.data
			const _columns = dynamicTableHeader(_label.table, '#00ffff')
			_columns.map((item) => {
				if (item.title === '政治三力') {
					item.customization = 'CustomBlock'
				}
			})

			columns.value = _columns

			loadData()
		} else {
			console.error('获取失败')
		}
	})
}

user_id && loadLabel()

const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
	const columns: Array<any> = []

	list.forEach((item: any, index: number) => {
		const key = `name`
		if (index === 0) {
			columns[index] = {
				key: '0',
				title: '',
				align: 'center',
				rowSpan: (span: any) => {
					return span
				},
				width: '9%',
			}
		}
		columns[index + 1] = {
			key: index + 1,
			title: item,
			width: 100 / (list.length + 1) + '%',
			align: 'center',
			sort: true,
		}
	})
	return columns
}
watch(
	time,
	(newVal: any) => {
		const _data = data.value.find((item: any) => item.time === newVal)
		if (newVal === thirdMap.label) {
			return
		}
		if (_data) {
			data1.value = { ..._data.data, xlabel: _label.charts, tableColumns: _label.table }
		}
	},
	{
		immediate: true,
	}
)
user_id && getPersonelResult({ user_id })
</script>

<style lang="scss" scoped>
.evaluation-analysis {
	.box-content__line {
		height: 294px;
	}
	.box-content__table {
		flex: 1;
	}
}

:deep(.ant-table-cell) {
	font-size: 20px;
	line-height: 23px;
	font-weight: 400;
}
.detail-btn {
	font-size: 14px;
	font-family: Source Han Sans CN;
	font-weight: 400;
	border-radius: 4px;
	line-height: 19px;
	border: none;
}
button:not([disabled]) {
	color: #ffffff;
	background: #e5251b;
}
</style>
