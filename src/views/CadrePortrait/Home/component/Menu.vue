<template>
	<div class="data-menu" v-if="user.detail.promotion_type !== 4">
		<div
			:class="['menu-item', coor_menu_selected === item.key && 'menu-active']"
			v-for="item in menuComputed"
			:key="item.key"
			@click="onMenu(item.key)"
		>
			{{ item.label }}
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed, toRefs } from 'vue'
import useUser from '@/store/user'
import { useHomeStore } from '@/store/home'

const user = useUser()
const store = useHomeStore()
const { menu, coor_menu_selected, coor_user_id } = toRefs(store)

const emit = defineEmits(['menu-click'])

const onMenu = (key: number) => {
	// currentIndex.value = key
	store.updateCoorMenuSelected(key)
}

const menuComputed = computed(() => {
	const type = user.detail.promotion_type
	if (type === 2) {
		return store.menuFilterSame
	} else if (type === 3) {
		return store.menuFilterSpecial
	} else {
		return store.menu
	}
})
</script>

<style lang="less" scoped>
.data-menu {
	display: flex;
	align-items: center;
	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		line-height: 18px;
		font-weight: 500;
		color: #666666;
		// line-height: 28px;
		cursor: pointer;
		line-height: 28px;
	}
	.menu-active {
		color: #333333;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #ff2121;
		border-radius: 2px 2px 2px 2px;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translateX(-50%);
	}
}
</style>
