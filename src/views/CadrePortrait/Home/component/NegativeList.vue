<template>
	<Card title="负面清单" header size-type="6">
		<template #right>
			<div class="button-box" :class="{ disabled: isNegativeDataEmpty }" @click="handleDetailClick">查看详情</div>
		</template>
		<div class="negative-list">
			<div class="box-content__table">
				<DataTable :data-source="negative.datasource" :columns="negative.columns">
					<template v-slot:score_str="{ value }">
						<span>{{ value == -1 ? '一票否决' : value }}</span>
					</template>
				</DataTable>
			</div>
		</div>
	</Card>
	<a-modal class="negative-modal" v-model:visible="visible" title="负面清单" @ok="handleOk" :footer="null">
		<div class="list">
			<div class="negative-detail" v-for="(item, index) in apiData.list" :key="index">
				<div class="title">{{ item.punishment_time }} {{ item.type }}</div>
				<div class="desc-item">
					<div class="label">
						<div class="label-text">时任职务</div>
						：
					</div>
					<div class="text">{{ item.job }}</div>
				</div>
				<div class="desc-item">
					<div class="label">
						<div class="label-text">文书文号</div>
						：
					</div>
					<div class="text">{{ item.attachment }}</div>
				</div>
				<div class="desc-item">
					<div class="label">
						<div class="label-text">主要内容或处理情况</div>
						：
					</div>
					<div class="text">{{ item.reason }}</div>
				</div>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts">
import { getNegativeData, getPunishment } from '@/apis/cadre-portrait/home'
import DataTable from '@/components/Table.vue'
import { computed, defineComponent, inject, reactive, ref } from 'vue'

export default defineComponent({
	components: {
		DataTable,
	},
	setup() {
		const visible = ref(false)
		const user_id = inject('user_id')
		const apiData = reactive<any>({
			negativeData: [],
			list: [],
		})
		const negative = computed(() => {
			const columns = [
				{
					key: 'type',
					align: 'center',
					width: '24.346%',
					title: '类型',
				},
				{
					key: 'score_str',
					align: 'center',
					width: '15%',
					title: '加减分',
				},
				{
					key: 'punishment_time',
					align: 'center',
					width: '15.76%',
					title: '时间',
				},
			]
			const datasource = apiData.negativeData
			return { columns, datasource }
		})

		// 计算属性：判断负面清单数据是否为空
		const isNegativeDataEmpty = computed(() => {
			return !apiData.negativeData || apiData.negativeData.length === 0
		})

		// 处理查看详情按钮的点击事件
		const handleDetailClick = () => {
			if (!isNegativeDataEmpty.value) {
				visible.value = true
			}
		}

		const handleOk = () => {}

		// 加载负面清单数据
		const loadNegative = async () => {
			if (!user_id) return
			const res = await getNegativeData({ user_id })
			if (res.code === 0) {
				apiData.negativeData = res.data
			}
		}
		loadNegative()

		// 加载处罚记录数据
		const loadPunishment = () => {
			if (!user_id) return
			getPunishment(user_id).then((res) => {
				if (res.code === 0) {
					apiData.list = res.data
				}
			})
		}
		loadPunishment()

		return {
			visible,
			negative,
			apiData,
			handleOk,
			isNegativeDataEmpty,
			handleDetailClick,
		}
	},
})
</script>

<style scoped lang="less">
.negative-list {
	// margin-top: 20px;
	// ::v-deep(.table-column-max) {
	// 	color: #000000 !important;
	// }
}
.button-box {
	padding: 8px 11px;
	text-align: center;
	font-size: 14px;
	font-family: Source Han Sans CN;
	font-weight: 400;
	color: #ffffff;
	border-radius: 4px;
	line-height: 19px;
	background: #e5251b;
	cursor: pointer;
}
.button-box.disabled {
	color: #fff;
	background: #ccc;
	cursor: not-allowed;
}
</style>
<style lang="less">
.negative-modal {
	width: 1000px !important;
	.ant-modal-header {
		padding: 24px;

		.ant-modal-title {
			font-size: 36px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			color: #000000;
		}
	}
	.ant-modal-body {
		padding: 24px 24px 122px !important;
	}
	.list {
		.negative-detail {
			.title {
				display: flex;
				align-items: center;
				margin-bottom: 20px;
				font-size: 28px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				color: #000000;
				&::before {
					content: '';
					margin-right: 12px;
					display: inline-block;
					width: 12px;
					height: 12px;
					background: #e5251b;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
					border-radius: 50%;
				}
			}
			.desc-item {
				padding-left: 24px;
				display: flex;
				margin-bottom: 10px;
				.label {
					font-size: 26px;
					display: flex;
					flex: 0 0 260px;
					color: #000000;
					&-text {
						flex: 1;
						text-align-last: justify;
					}
				}
				.text {
					flex: 1;
					font-size: 26px;
				}
			}
		}
	}
}
</style>
