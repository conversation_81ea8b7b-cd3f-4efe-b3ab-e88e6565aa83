<template>
	<Card title="个人测评分析" header size-type="5">
		<div class="personal-analysis">
			<div class="box-content__line">
				<v-chart :option="option" autoresize ref="echarts"></v-chart>
			</div>
			<div class="box-content__table">
				<DataTable :data-source="dataSource" :columns="columns" show-max-min />
			</div>
		</div>
	</Card>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, inject } from 'vue'
import DataTable from '@/components/Table.vue'
import Card from '@/components/Card.vue'
import { getPersonelResult } from '@/apis/cadre-portrait/home'
import { decreaseOpacity, convertPxToRem, debounce } from '@/utils/utils'
import useUser from '@/store/user'
export default defineComponent({
	components: {
		Card,
		DataTable,
	},
	setup() {
		const user_id = inject('user_id')

		const echarts: any = ref()
		const currentSelectLevel = ref('')
		const user = useUser()
		const legendSelect = ref({
			upper: true,
			same: true,
			down: true,
			composite_avg: true,
			overall_same_sequence_avg_score: true,
			overall_same_department_avg_score: true,
		})
		const data = ref<any>({
			xlabel: [],
			upper: [],
			same: [],
			down: [],
			composite_avg: [],
			upper_avg: [],
			same_avg: [],
			down_avg: [],
			type: -1,
			overall_same_sequence_avg_score: [],
			overall_same_department_avg_score: [],
			supervisor_same_sequence_avg_score: [],
			supervisor_same_department_avg_score: [],
			peer_same_sequence_avg_score: [],
			peer_same_department_avg_score: [],
			subordinate_same_sequence_avg_score: [],
			subordinate_same_department_avg_score: [],
		})

		const createData = (origin_data: [], title: string) => {
			const data: any = {}
			data.name = title
			for (let i = 0; i < origin_data.length; i++) {
				data[`name${i + 1}`] = origin_data[i]
			}

			return data
		}
		const dataSource = computed(() => {
			const { has_divided = 2 } = user.detail
			return has_divided === 1
				? [createData(data.value.upper, '上级测评'), createData(data.value.down, '下级测评')]
				: [createData(data.value.upper, '上级测评'), createData(data.value.same, '同级测评'), createData(data.value.down, '下级测评')]
		})
		// 用于刷新option
		const refreshOption = ref(0)
		// 刷新option
		window.addEventListener(
			'resize',
			debounce(() => {
				refreshOption.value++
			}, 100)
		)
		// 表格数据
		const Line = ref(['上级测评', '同级测评', '下级测评', '综合平均分', '同序列综合平均分', '乡镇/部门平均分'])
		const UPPERLINE = ['上级测评']
		const SMAELINE = ['同级测评']
		const DOWNLINE = ['下级测评']
		const avgLine = [
			'上级测评平均分',
			'同级测评平均分',
			'下级测评平均分',
			'同序列上级综合平均分',
			'乡镇/部门上级平均分',
			'同序列同级综合平均分',
			'乡镇/部门同级平均分',
			'同序列下级综合平均分',
			'乡镇/部门下级平均分',
		]
		// 显示哪几个legend，默认展示Line中数据, tips: 不能直接赋值 Line，否则会修改原数据
		let legendData: any = ref([...Line.value])
		watch(
			user,
			() => {
				const { has_divided = 2, promotion_type = 1 } = user.detail
				const line = has_divided === 1 ? ['上级测评', '下级测评', '综合平均分'] : ['上级测评', '同级测评', '下级测评', '综合平均分']

				if (![3, 4].includes(promotion_type)) {
					line.push('同序列综合平均分')
				}
				line.push('乡镇/部门平均分')

				legendData.value = [...line]

				Line.value = [...line]
			},
			{
				immediate: true,
			}
		)
		// 几条平均线
		const option = computed(() => {
			{
				refreshOption.value
			}
			// const XName = ['政治三力', '学习力', '决策力', '执行力', '群众工作能力', '担当', '责任', '正派']
			// 不能直接赋值，下方table还需要用到政治三力的数据，pop后会修改原数据
			const [...upper] = data.value.upper || []
			const [...upper_avg] = data.value.upper_avg || []
			// 同序列上级平均分
			const [...supervisor_same_sequence_avg_score] = data.value.supervisor_same_sequence_avg_score || []
			// 同部门乡镇上级平均分
			const [...supervisor_same_department_avg_score] = data.value.supervisor_same_department_avg_score || []

			const [...same] = data.value.same || []
			const [...same_avg] = data.value.same_avg || []
			// 同序列平级级平均分
			const [...peer_same_sequence_avg_score] = data.value.peer_same_sequence_avg_score || []
			// 同部门乡镇平级平均分
			const [...peer_same_department_avg_score] = data.value.peer_same_department_avg_score || []

			const [...down] = data.value.down || []
			const [...down_avg] = data.value.down_avg || []
			// 同序列下级平均分
			const [...subordinate_same_sequence_avg_score] = data.value.subordinate_same_sequence_avg_score || []
			// 同部门乡镇下级平均分
			const [...subordinate_same_department_avg_score] = data.value.subordinate_same_department_avg_score || []

			const [...composite_avg] = data.value.composite_avg || []
			// 同序列综合平均分
			const [...overall_same_sequence_avg_score] = data.value.overall_same_sequence_avg_score || []
			// 乡镇（部门）平均分
			const [...overall_same_department_avg_score] = data.value.overall_same_department_avg_score || []

			const [...xlabel] = data.value.xlabel
			// const avgLine = ['上级测评平均线', '同级测评平均线', '下级测评平均线']
			// 最后一项为政治三力,弹出政治三力所有数据
			xlabel?.pop()
			upper?.pop()
			same?.pop()
			down?.pop()
			composite_avg?.pop()
			upper_avg?.pop()
			same_avg?.pop()
			down_avg?.pop()
			overall_same_sequence_avg_score?.pop()
			overall_same_department_avg_score?.pop()
			supervisor_same_sequence_avg_score.pop()
			supervisor_same_department_avg_score.pop()
			peer_same_sequence_avg_score.pop()
			peer_same_department_avg_score.pop()
			subordinate_same_sequence_avg_score.pop()
			subordinate_same_department_avg_score.pop()
			const dataArray = [
				{
					data: upper,
					label: '上级测评',
					color: '#6AAEFB',
					lineType: 'solid',
				},
				// {
				// 	data: upper_avg,
				// 	// data: down,
				// 	label: '上级测评平均分',
				// 	peerLabel: '上级测评',
				// 	color: '#6AAEFB',
				// 	lineType: 'dashed',
				// },
				{
					data: supervisor_same_sequence_avg_score,
					// data: down,
					label: '同序列上级综合平均分',
					peerLabel: '上级测评',
					color: '#60CA71',
					lineType: 'dashed',
				},
				{
					data: supervisor_same_department_avg_score,
					// data: down,
					label: '乡镇/部门上级平均分',
					peerLabel: '上级测评',
					color: '#FF81E3',
					lineType: 'dashed',
				},
				{
					data: same,
					label: '同级测评',
					color: '#47D3FF',
					lineType: 'solid',
				},
				// {
				// 	data: same_avg,
				// 	// data: same,
				// 	label: '同级测评平均分',
				// 	peerLabel: '同级测评',
				// 	color: '#47D3FF',
				// 	lineType: 'dashed',
				// },
				{
					data: peer_same_sequence_avg_score,
					// data: down,
					label: '同序列同级综合平均分',
					peerLabel: '同级测评',
					color: '#60CA71',
					lineType: 'dashed',
				},
				{
					data: peer_same_department_avg_score,
					// data: down,
					label: '乡镇/部门同级平均分',
					peerLabel: '同级测评',
					color: '#FF81E3',
					lineType: 'dashed',
				},
				{
					data: down,
					label: '下级测评',
					color: '#FFA300',
					lineType: 'solid',
				},
				// {
				// 	data: down_avg,
				// 	// data: upper,
				// 	label: '下级测评平均分',
				// 	peerLabel: '下级测评',
				// 	color: '#FFA300',
				// 	lineType: 'dashed',
				// },
				{
					data: subordinate_same_sequence_avg_score,
					// data: down,
					label: '同序列下级综合平均分',
					peerLabel: '下级测评',
					color: '#60CA71',
					lineType: 'dashed',
				},
				{
					data: subordinate_same_department_avg_score,
					// data: down,
					label: '乡镇/部门下级平均分',
					peerLabel: '下级测评',
					color: '#FF81E3',
					lineType: 'dashed',
				},
				{
					data: composite_avg,
					label: '综合平均分',
					color: '#878787',
					lineType: 'dashed',
				},
				{
					data: overall_same_sequence_avg_score,
					// data: upper,
					label: '同序列综合平均分',
					color: '#60CA71',
					lineType: 'dashed',
				},
				{
					data: overall_same_department_avg_score,
					// data: upper,
					label: '乡镇/部门平均分',
					color: '#FF81E3',
					lineType: 'dashed',
				},
			]

			const datas: any[] = []
			// 基础线
			dataArray
				.filter((item) => legendData.value.includes(item.label))
				.map((item) => {
					const { data, label, color, lineType } = item
					datas.push({
						symbolSize: convertPxToRem(12),
						symbol: 'circle',
						name: label,
						type: 'line',
						yAxisIndex: 1,
						data: data,
						smooth: false,
						itemStyle: {
							borderWidth: convertPxToRem(4),
							borderColor: color,
							color: '#ffffff',
							shadowColor: decreaseOpacity(color, 0.5),
							shadowBlur: 5,
						},
						lineStyle: {
							color: color,
							width: convertPxToRem(3),
							type: lineType,
						},
					})
				})

			const option = {
				color: ['#17EBC7'],
				grid: {
					left: '0%',
					top: '23%',
					bottom: '0%',
					right: '3%',
					containLabel: true,
				},
				legend: {
					selected: {
						// 选中'系列1'
						上级测评: legendSelect.value.upper,
						// 不选中'系列2'
						同级测评: legendSelect.value.same,
						下级测评: legendSelect.value.down,
						综合平均分: legendSelect.value.composite_avg,
						同序列综合平均分: legendSelect.value.overall_same_sequence_avg_score,
						'乡镇/部门平均分': legendSelect.value.overall_same_department_avg_score,
					},
					show: true,
					top: 15,
					left: 'center',
					// type: 'scroll',
					data: legendData.value,
					icon: 'circle',
					itemWidth: convertPxToRem(12),
					itemHeight: convertPxToRem(12),
					textStyle: {
						color: '#333333',
						fontSize: convertPxToRem(18),
					},
					itemStyle: {
						borderWidth: convertPxToRem(4),
					},
					itemGap: convertPxToRem(24),
					// selectedMode: false,
				},
				yAxis: [
					{
						type: 'value',
						position: 'right',
						splitLine: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					{
						type: 'value',
						position: 'left',
						scale: true,
						nameTextStyle: {
							color: '#00FFFF',
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#EEEEEE',
							},
						},
						// min: 60,
						// max: 100,
						// splitNumber: 7,
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
								'arrow',
							],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							formatter: (value: any) => {
								value = Number(value)
								return value > 100 ? '' : value.toFixed(2)
							},
							color: '#666666',
							fontSize: convertPxToRem(16),
							interval: 5,
							showMaxLabel: false,
						},
					},
				],
				xAxis: [
					{
						type: 'category',
						axisTick: {
							show: false,
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: [
								'none',
								// 'path://M384 883.08c-8.19 0-16.38-3.12-22.62-9.38-12.5-12.5-12.5-32.75 0-45.25L677.83 512 361.38 195.55c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L734.4 478.07c18.72 18.72 18.72 49.16 0 67.88L406.62 873.7c-6.24 6.25-14.43 9.38-22.62 9.38z m305.14-359.77h0.31-0.31z',
								'arrow',
							],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisLabel: {
							inside: false,
							textStyle: {
								color: '#666666', // x轴颜色
								fontWeight: 'normal',
								fontSize: convertPxToRem(16),
								lineHeight: convertPxToRem(22),
							},
							interval: 0,
						},
						data: xlabel,
					},
					{
						type: 'category',
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
						splitArea: {
							show: false,
						},
						splitLine: {
							show: false,
						},
						//-----
					},
				],
				series: datas,
			}
			return option
		})

		const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
			const columns: Array<any> = []

			list.forEach((item: any, index: number) => {
				const key = `name`
				if (index === 0) {
					columns[index] = {
						key,
						title,
						width: '9%',
					}
				}
				columns[index + 1] = {
					key: `${key}${index + 1}`,
					title: item,
					width: 100 / (list.length + 1) + '%',
					align: 'center',
					sort: true,
				}
			})
			return columns
		}
		const columns = computed(() => {
			const _columns = dynamicTableHeader(data.value.xlabel, '#00ffff')
			_columns.map((item) => {
				if (item.title === '政治三力') {
					item.customization = 'CustomBlock'
				}
			})
			return _columns
		})

		const loadData = async () => {
			const res: any = await getPersonelResult({ user_id })
			if (res.code === 0) {
				const { overall_same_department_avg_score, overall_same_sequence_avg_score } = res.data

				if (overall_same_sequence_avg_score) {
					UPPERLINE.push('同序列上级综合平均分')
					SMAELINE.push('同序列同级综合平均分')
					DOWNLINE.push('同序列下级综合平均分')
				}
				if (overall_same_department_avg_score) {
					UPPERLINE.push('乡镇/部门上级平均分')
					SMAELINE.push('乡镇/部门同级平均分')
					DOWNLINE.push('乡镇/部门下级平均分')
				}
				data.value = res.data
			}
		}
		loadData()

		const colorList = ['#15EAC2', '#24c5e5', '#FDA22C']

		const rowColor = (_record: any, index: number) => {
			return colorList[index]
		}
		watch(echarts, () => {
			if (echarts.value?.chart) {
				const instance = echarts.value?.chart
				instance.on('legendselectchanged', (event: any) => {
					const legend: any = legendSelect.value
					// 情况一
					// 点击的这一项为false，说明正在查看其他的某一项，需要将当前点击项改为true,其他改为false
					// 情况二
					// 点击的这一项为true，其他的所有也为true，需要将其他的改为false，自己依旧为true
					// 情况三
					// 点击的这一项为true，其他为false，需要将其他的改为true
					// legendSelect.value = { ...legendSelect.value }
					if (avgLine.includes(event['name'])) {
						return
					}
					const status = [2, 3].includes(legendData.value.length)
					if (event['name'] === '上级测评') {
						if (status) {
							legendData.value = [...Line.value]
						} else {
							legendData.value = [...UPPERLINE]
						}
						legendSelect.value = legendVisibleController({ ...legend }, 'upper')
					} else if (event['name'] === '同级测评') {
						if (status) {
							legendData.value = [...Line.value]
						} else {
							legendData.value = [...SMAELINE]
						}
						legendSelect.value = legendVisibleController({ ...legend }, 'same')
					} else if (event['name'] === '下级测评') {
						if (status) {
							legendData.value = [...Line.value]
						} else {
							legendData.value = [...DOWNLINE]
						}
						legendSelect.value = legendVisibleController({ ...legend }, 'down')
					} else if (event['name'] === '综合平均分') {
						legendSelect.value = legendVisibleController({ ...legend }, 'composite_avg')
					} else if (event['name'] === '同序列综合平均分') {
						legendSelect.value = legendVisibleController({ ...legend }, 'overall_same_sequence_avg_score')
					} else if (event['name'] === '乡镇/部门平均分') {
						legendSelect.value = legendVisibleController({ ...legend }, 'overall_same_department_avg_score')
					}
					if (!Line.value.includes(event['name'])) {
						currentSelectLevel.value = ''
						// 复位
						legendSelect.value = {
							upper: true,
							same: true,
							down: true,
							composite_avg: true,
							overall_same_sequence_avg_score: true,
							overall_same_department_avg_score: true,
						}
					} else if (currentSelectLevel.value === event['name']) {
						currentSelectLevel.value = ''
					} else {
						currentSelectLevel.value = event['name']
					}
					/**
					 * @description: 生成legend 选中/切换后的数据
					 * @param {*} legend
					 * @param {*} key
					 * @return {*}
					 */
					function legendVisibleController(legend: any, key: string): any {
						// 点击项未选中
						if (!legend[key]) {
							for (let _key of Object.keys(legend)) {
								if (_key === key) {
									legend[_key] = true
								} else {
									legend[_key] = false
								}
							}
						} else {
							// 其他为不为true，需要全选
							if (Object.keys(legend).some((_key: any) => _key !== key && !legend[_key])) {
								for (let _key of Object.keys(legend)) {
									legend[_key] = true
								}
							} else {
								for (let _key of Object.keys(legend)) {
									if (_key === key) {
										legend[_key] = true
									} else {
										legend[_key] = false
									}
								}
							}
						}

						return legend
					}
				})
			}
		})
		return {
			echarts,
			option,
			columns,
			dataSource,
			rowColor,
		}
	},
})
</script>

<style scoped lang="less">
.personal-analysis {
	display: flex;
	flex-direction: column;
	.box-content__line {
		height: 294px;
	}
	.box-content__table {
		margin-top: 30px;
		flex: 1;
	}
}
</style>
