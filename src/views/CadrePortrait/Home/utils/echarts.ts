/**
 * @description: 给定x轴的定位，返回与其label对应的map
 * @param {any} position
 * @param {any} xlabel
 * @return {*}
 */
export function transformXlabelData(position: any, xlabel: any) {
	const positionMap: any = {}
	xlabel.forEach((item: any, index: number) => {
		const _p: any = [position[index]]
		positionMap[_p] = item
	})
	return positionMap
}
/**
 * @description: 给定x轴的定位，返回[value,value]格式的数据。适用于双数值轴，第一个值是x轴的定位，第二个值是y轴的值
 * @param {any} position
 * @param {any} data
 * @return {*}
 */
export function transformDataToValue(position: any, data: any) {
	return data.map((item: any, index: number) => {
		return [position[index], item]
	})
}
/**
 * @description: 根据定位label map 返回新x轴， 需要结合markline使用
 * @param {any} positionMap
 * @return {*}
 */
export function createMarkLine(positionMap: any) {
	return {
		symbol: false, // 取消箭头
		silent: true, // 取消鼠标hover事件
		label: {
			color: '#666666',
			fontSize: 16,
			position: 'start', // 改变label位置
			formatter: (obj: any) => positionMap[obj.value], // 格式化标签文本
		},
		lineStyle: { color: '#EEEEEE', type: 'dashed' },
		data: Object.keys(positionMap).map((val) => {
			return {
				xAxis: val,
			}
		}),
	}
}
