<template>
	<div class="cadre-home">
		<div class="left-box">
			<div class="user-box" v-if="!$route.meta.layoutFull">
				<Card class="user-info" size-type="1">
					<div class="info-box">
						<div class="user-base__info">
							<div class="top-info">
								<div class="avatar" :style="{ backgroundImage: avatarUrl }"></div>
								<div class="info">
									<div class="user-name">
										{{ userInfo.name }}
										<!-- <span v-if="userInfo.source" class="source">({{ userInfo.source }})</span> -->
									</div>

									<div class="info-item">
										<div class="label">
											<span type="label">出生年月</span>
											<span>:</span>
										</div>
										<div class="value">{{ userInfo.birthday_age }}</div>
									</div>
									<div class="info-item">
										<div class="label">
											<span type="label">现任职务</span>
											<span>:</span>
										</div>
										<div class="value omit-text">
											<a-popover title="现任职务" placement="rightTop">
												<template #content>{{ userInfo.current_position }}</template>
												{{ userInfo.current_position }}
											</a-popover>
										</div>
									</div>
								</div>
							</div>
							<div class="bottom-info">
								<div class="info-item">
									<div class="label">
										<span type="label">任现职务时间</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.position_time }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">干部类别</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.category }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">干部身份</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.identity }}</div>
								</div>
								<div class="info-item" v-if="userInfo.source">
									<div class="label">
										<span type="label">干部来源</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.source }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">全日制学历</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.education }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">毕业院校及专业</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.school_major }}</div>
								</div>
							
						</div>
						<div class="label-container">
							<!-- <div class="label-info-box margintop-47">
								<div class="label-info">
									<div class="top-content">
										<div class="label">兴趣爱好</div>
									</div>
									<div class="content">
										<div class="tag-item" v-for="(item, index) in userInfo.like" :key="index">{{ item }}</div>
										<div class="tag-item" v-if="userInfo.like"></div>
										<span class="text">{{ userInfo.like }}</span>
									</div>
								</div>
							</div> -->
							<div class="label-info-box">
								<div class="label-info">
									<div class="top-content">
										<div class="label">分管领域</div>
									</div>
									<div class="content">
										<span class="text">
											{{ userInfo.areas_responsibility }}
										</span>
									</div>
								</div>
							</div>
							<!-- <div class="label-info-box">
								<div class="label-info">
									<div class="top-content">
										<div class="label">熟悉领域和特长</div>
									</div>

									<div class="content">
										<div class="tag-item" v-for="(item, index) in userInfo.goods_range" :key="index">{{ item }}</div>
										<span class="text"> {{ userInfo.goods_range }} </span>
									</div>
								</div>
							</div> -->
							<div class="label-info-box">
								<div class="label-info">
									<div class="top-content">
										<div class="label">特征标签</div>
										<div class="more a-pointer" @click="onTagModal"><span>更多</span></div>
									</div>
									<div class="content" style="display: flex; flex-direction: column">
										<!-- <div class="tag-item" v-for="(item, index) in userInfo.feature" :key="index">{{ item }}</div> -->
										<div class="text">干部特点：{{ userInfo.positive_feature?.join('，') }}</div>
										<div class="text" style="padding-top: 0px">主要不足：{{ userInfo.negative_feature?.join('，') }}</div>
									</div>
								</div>
							</div>
							<div class="label-info-box">
								<div class="label-info">
									<div class="top-content">
										<div class="label">一言素描</div>
										<div class="more a-pointer" @click="onModal"><span>更多</span></div>
									</div>

									<div class="content">
										<span class="text"> {{ userInfo.sketch }} </span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</Card>
			</div>
		</div>
		<div class="right-box">
			<CadreBox :leader_index="userInfo.leader_index" :leader_index_rank="userInfo.leader_index_rank" :risk_index="userInfo.risk_index" />
			<TeamMembers />

			<div class="coordinate-box">
				<div class="left-box">
					<!-- <div>三点</div> -->
					<LeaderIndexCharts />
				</div>
				<div class="left-box">
					<Coordinate />
				</div>
			</div>
			<div class="index-box common">
				<LeaderIndex />
			</div>
			<div class="ability-box common">
				<PersonalAnalysis />
			</div>
			<div class="ability-box-1 common">
				<!-- <div class="box-content__line">
					<Ability :option="capabilityOption" />
				</div>
				<div class="box-content__table">
					<DataTable :data-source="capabilityTable.datasource" :columns="capabilityTable.columns" :row-color="capabilityTable.rowColor" />
				</div> -->
				<!-- <PersonalAnalysis /> -->
				<AbilityAnalysis />
			</div>
			<div class="ability-box-2 common">
				<HistoricalPerformance />
			</div>
			<div class="ability-box-3 common">
				<NegativeList />
			</div>
		</div>

		<Modal :visible="visible" @close="onClose">
			<Card :header="false" class="modal-card" title="一言素描" size-type="3">
				<div class="modal-card-box">
					<div class="modal-title">一言素描</div>
					<div class="modal-content">
						<div class="content-item" v-for="(item, index) in userInfo.sketch_list" :key="index">
							<div class="text">{{ item.hobby }}</div>
							<!-- <div class="text">敢担当，能攻坚，分管场镇建设、征地拆迁成效好，农村工作经验丰 富，干群口碑较好。不足：性格直，快人快语。</div> -->
							<!-- <div class="user-name">评价人：{{ item.name }}</div> -->
							<div class="user-name">评价人：* * *</div>
						</div>
						<!-- <div class="content-item">
							<div class="text">敢担当，能攻坚，分管场镇建设、征地拆迁成效好，农村工作经验丰 富，干群口碑较好。不足：性格直，快人快语。</div>
							<div class="user-name">评价人：李四</div>
						</div>
						<div class="content-item">
							<div class="text">敢担当，能攻坚，分管场镇建设、征地拆迁成效好，农村工作经验丰 富，干群口碑较好。不足：性格直，快人快语。</div>
							<div class="user-name">评价人：王五</div>
						</div> -->
					</div>
				</div>
			</Card>
		</Modal>
		<Modal :visible="tagVisible" @close="onTagModal">
			<Card :header="false" class="modal-card tag-modal" title="特征标签" size-type="3">
				<div class="modal-card-box">
					<div class="modal-title">特征标签</div>
					<div class="modal-content">
						<div class="content-item" v-for="(item, index) in userInfo.feature_list" :key="index">
							<div class="tag-box">
								<!-- <div class="tag-item" v-for="(_item, _index) in item.feature" :key="_index">{{ _item }}</div> -->
								<!-- <div class="text">{{ item.feature?.join('，') }}</div> -->
								<div class="text">正面标签：{{ item.positive_feature?.join('，') }}</div>
								<div class="text">负面标签：{{ item.negative_feature?.join('，') }}</div>
							</div>
							<!-- <div class="user-name">评价人：{{ item.name }}</div> -->
							<div class="user-name">评价人：* * *</div>
						</div>
						<!-- <div class="content-item">
							<div class="tag-box">
								<div class="tag-item">担当型</div>
							</div>
							<div class="user-name">评价人：王五</div>
						</div>
						<div class="content-item">
							<div class="tag-box">
								<div class="tag-item">担当型</div>
							</div>
							<div class="user-name">评价人：王五</div>
						</div> -->
					</div>
				</div>
			</Card>
		</Modal>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import CadreBox from './component/CadreBox.vue'
import AbilityAnalysis from './component/AbilityAnalysis.vue'
import PersonalAnalysis from './component/PersonalAnalysis.vue'
import LeaderIndex from './component/LeaderIndex.vue'
import HistoricalPerformance from './component/HistoricalPerformance.vue'
import NegativeList from './component/NegativeList.vue'
import Coordinate from './component/Coordinate.vue'
import LeaderIndexCharts from './component/LeaderIndexCharts.vue'
import TeamMembers from './component/TeamMembers.vue'

import { getLeaderBaseInfo } from '@/apis/cadre-portrait/home'

import useUser from '@/store/user'

import { useHomeStore, SAME } from '@/store/home'

import type { UserInfo } from '@/types/user'

const CDN_URL = import.meta.env.VITE_CDN_URL

const user = useUser()
const route = useRoute()
const home = useHomeStore()

const { user_id } = route.query

const visible = ref(false)

const userInfo = ref<UserInfo>({
	user_id: 0,
	name: '',
	birthday_age: '',
	education: '',
	current_position: '',
	position_time: '',
	leader_index: 0,
	leader_index_rank: '',
	risk_index: 0,
	risk_index_avg: 0,
	feature: [],
	goods_range: '',
	like: '',
	sketch: '',
	areas_responsibility: '',
	feature_list: [],
	sketch_list: [],
	promotion_type: -1,
})

const tagVisible = ref(false)

const avatarUrl = computed(() => (userInfo.value.avatar ? `url("${CDN_URL}/fr_img/${userInfo.value.avatar}")` : ''))

const onClose = () => {
	visible.value = false
}
const onModal = () => {
	visible.value = true
}

const onTagModal = () => {
	tagVisible.value = !tagVisible.value
}

/**
 * @description: 获取干部基本信息
 * @return {*}
 */
const initLeaderBaseInfoData = async () => {
	const res = await getLeaderBaseInfo({ user_id })
	userInfo.value = res.data
	user.updateDetail(res.data)
	let id
	if (res.data.promotion_type === 2) {
		id = 2
	} else if (user.detail.promotion_type === 3) {
		id = 3
	} else {
		id = 1
	}
	home.updateCoorMenuSelected(id)
}

initLeaderBaseInfoData()
</script>

<style scoped lang="less">
.cadre-home {
	width: 100%;
	height: 100%;
	background-color: transparent;
	position: relative;
	display: flex;
	padding: 16px 0px;
	& > .left-box {
		margin-right: 16px;
		.user-box {
			width: 468px;
			height: 100%;
			overflow: hidden;
			padding: 11px 12px;
			background-color: #ffffff;
			border-radius: 8px;
			.user-info {
				padding: 12px 8px;
				width: 100%;
				height: 100%;
				background-color: #ecf5ff;
				border-radius: 8px;
				.info-box {
					height: 100%;
					display: flex;
					flex-direction: column;
				}
				.user-base__info {
					display: flex;
					flex-direction: column;
					.avatar {
						flex-shrink: 0;
						margin-right: 18px;
						width: 100px;
						height: 134px;
						background: url('@/assets/images/avatar.png') no-repeat center center / contain;
						// background-color: rgba(204, 204, 204, 0.2);
					}
					.top-info {
						display: flex;
					}
					.bottom-info,
					.info {
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						.user-name {
							font-size: 28px;
							font-family: PingFang SC-Bold, PingFang SC;
							font-weight: bold;
							color: #000000;
							line-height: 33px;
						}
						.source {
							font-size: 20px;
							font-weight: bold;
						}
						.info-item {
							display: flex;
							font-size: 20px;
							font-family: PingFang SC-Medium, PingFang SC;
							font-weight: 500;
							color: #333333;
							line-height: 38px;
							letter-spacing: 1px;
							.label {
								flex-shrink: 0;
								margin-right: 10px;
								span[type='label'] {
									display: inline-block;
									min-width: 85px;
									text-align-last: justify;
								}
							}

							.info {
							}
						}
					}
					.bottom-info {
						.info-item {
							.label {
								flex-shrink: 0;
								margin-right: 10px;
								span[type='label'] {
									display: inline-block;
									min-width: 203px;
									text-align-last: justify;
								}
							}
						}
					}
				}
				.label-container {
					flex: 1;
					overflow-y: auto;
					&::-webkit-scrollbar {
						display: none;
					}
				}
				.number-card {
					margin-top: 36px;
					display: flex;
					justify-content: space-between;
					.card-1,
					.card-2 {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 252px;
						height: 45px;
						// background: url('@/assets/images/card1-bg.png') no-repeat center / cover;

						.label-icon {
							margin-right: 18px;
							display: inline-block;
							width: 16px;
							height: 16px;
							line-height: 1;
							// background: url('@/assets/images/icon-1.png') no-repeat center / 100% 100%;
							cursor: pointer;
						}
						.label {
							margin-right: 7px;
							font-size: 16px;
							font-weight: 400;
							// color: #96e9ff;
							line-height: 16px;
						}
						.label-1 {
							font-size: 20px;
							font-weight: bold;
							color: #f6e81f;
							line-height: 20px;
						}
						.label-2 {
							font-size: 15px;
							font-weight: bold;
							color: #f6e81f;
							// line-height: 15px;
						}
					}
					.card-1 {
						.label-1 {
							font-size: 20px;
							font-weight: bold;
							color: #f6e81f;
							line-height: 15px;
						}
						.label-2 {
							position: relative;
							font-weight: bold;
							color: #f6e81f;
							line-height: 15px;
							&::after {
								position: absolute;
								bottom: -5px;
								left: 16px;
								content: '';
								display: inline-block;
								width: 6px;
								height: 2px;
								background: #05b0ff;
							}
						}
					}
					.card-2 {
						.label-1 {
							font-size: 19px;
							font-weight: bold;
							color: #ff3b1b;
							line-height: 15px;
						}
					}
				}
				.label-info-box {
					.top-content {
						padding: 4px 12px 4px 18px;
						display: flex;
						justify-content: space-between;
						background: url('@/assets/images/card-header-bg1.png') no-repeat left center / 100% 100%;

						.more {
							display: flex;
							align-items: center;
							span {
								font-size: 18px;
								font-family: PingFang SC-Medium, PingFang SC;
								font-weight: 500;
								color: #666666;
								line-height: 18px;
							}
							&::after {
								content: '';
								display: inline-block;
								width: 24px;
								height: 24px;
								background: url('@/assets/images/chevron-right.png') no-repeat center / 100% 100%;
							}
						}
					}
					.label-info {
						.label {
							display: flex;
							align-items: center;
							font-size: 20px;
							font-family: PingFang SC-Bold, PingFang SC;
							font-weight: bold;
							color: #222222;
							line-height: 24px;
						}
						.content {
							display: flex;
							.text {
								padding: 14px 11px 20px 18px;
								font-size: 20px;
								font-family: PingFang SC-Medium, PingFang SC;
								font-weight: 500;
								color: #333333;
								line-height: 31px;
							}
						}
					}
				}
			}
		}
	}
	// :deep(.card-box) {
	// 	padding: 20px 20px;
	// }
	.right-box {
		flex: 1;
		overflow-y: auto;
		&::-webkit-scrollbar {
			width: 0;
			height: 0;
			display: none;
		}

		.coordinate-box {
			margin-top: 16px;
			display: flex;
			justify-content: space-between;
			width: 100%;
			height: 521px;
			border-radius: 8px;
			.left-box {
				width: 49%;
				border-radius: 8px;
				// .card-box:first-child(){
				// 	background: red !important;
				// }
				// ::v-deep .card-box:first-child {
				// 	background: red;
				// }
			}

			.right-box {
				flex: 48%;
				border-radius: 8px;
			}
		}
		// 干部指数
		.index-box {
			margin-top: 16px;
			border-radius: 8px;
		}

		.ability-box {
			margin-top: 16px;
			min-height: 566px;
			border-radius: 8px;
		}

		.ability-box-1 {
			margin-top: 16px;
			display: flex;
			flex-direction: columns;
			border-radius: 8px;
			:deep(.card-box) {
				height: unset;
			}
		}

		.ability-box-2 {
			margin-top: 16px;
			display: flex;
			flex-direction: columns;
			// .box-content__line {
			// 	height: 191px;
			// }
			// .box-content__table {
			// 	margin-top: 16px;
			// 	flex: 1;
			// }
		}

		.ability-box-3 {
			margin-top: 16px;
			width: 100%;
			min-height: 179px;
			.content {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100%;
				font-size: 14px;
				font-weight: 400;
				color: #00cbff;
				line-height: 22px;
			}
		}
	}
}

.modal-card {
	width: 900px;
	height: 750px;
	.modal-card-box {
		padding: 20px;
	}
	.modal-title {
		font-size: 20px;
		font-weight: bold;
		color: #000000;
		text-align: center;
	}
	.modal-content {
		padding: 4px 0;
		font-size: 16px;
		color: #ffffff;
		.content-item {
			padding: 10px 0;
			margin-top: 20px;
			border-bottom: 1px solid rgba(0, 234, 255, 0.1);
			&:nth-last-child(1) {
				border-bottom: none;
			}
			.text {
				font-size: 16px;
				font-weight: 400;
				color: #000000;
				line-height: 25px;
			}
			.user-name {
				line-height: 20px;
				margin: 12px 20px 0 0;
				// float: right;
				text-align: right;
				font-size: 14px;
				font-weight: 500;
				color: #000000;
				line-height: 14px;
			}
		}
	}
}
.tag-modal {
	.content-item {
		.tag-box {
			display: flex;
			flex-direction: column;
			flex-wrap: wrap;
			.tag-item {
				margin-bottom: 10px;
			}
		}
		.text {
			font-size: 16px;
			font-weight: 400;
			color: #00eaff;
			line-height: 25px;
		}
	}
}

.tag-item {
	margin-right: 29px;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0 18px;
	min-width: 131px;
	height: 36px;

	font-size: 18px;
	font-weight: 500;
	color: #00eaff;
	line-height: 14px;

	border: 1px solid #00eaff;
	border-radius: 5px;

	background: linear-gradient(0deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%),
		linear-gradient(90deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
		linear-gradient(180deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
		linear-gradient(270deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%);
}
</style>
