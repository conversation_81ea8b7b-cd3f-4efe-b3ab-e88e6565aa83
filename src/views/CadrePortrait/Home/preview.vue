<template>
	<div class="cadre-home">
		<CadreBox />
		<TeamMembers />

		<div class="coordinate-box">
			<div class="left-box">
				<!-- <div>三点</div> -->
				<LeaderIndexCharts />
			</div>
			<div class="left-box">
				<Coordinate />
			</div>
		</div>
		<div class="index-box common">
			<LeaderIndex />
		</div>
		<div class="ability-box common">
			<PersonalAnalysis />
		</div>
		<div class="ability-box-1 common">
			<!-- <div class="box-content__line">
					<Ability :option="capabilityOption" />
				</div>
				<div class="box-content__table">
					<DataTable :data-source="capabilityTable.datasource" :columns="capabilityTable.columns" :row-color="capabilityTable.rowColor" />
				</div> -->
			<!-- <PersonalAnalysis /> -->
			<AbilityAnalysis />
		</div>
		<div class="ability-box-2 common">
			<HistoricalPerformance />
		</div>
		<div class="ability-box-3 common">
			<NegativeList />
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import CadreBox from './component/CadreBox.vue'
import AbilityAnalysis from './component/AbilityAnalysis.vue'
import PersonalAnalysis from './component/PersonalAnalysis.vue'
import LeaderIndex from './component/LeaderIndex.vue'
import HistoricalPerformance from './component/HistoricalPerformance.vue'
import NegativeList from './component/NegativeList.vue'
import Coordinate from './component/Coordinate.vue'
import LeaderIndexCharts from './component/LeaderIndexCharts.vue'
import TeamMembers from './component/TeamMembers.vue'
export default defineComponent({
	components: {
		CadreBox,
		Coordinate,
		TeamMembers,
		NegativeList,
		AbilityAnalysis,
		PersonalAnalysis,
		LeaderIndex,
		HistoricalPerformance,
		LeaderIndexCharts,
	},
	setup() {
		const { org_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
		return {
			org_id: Number(org_id),
		}
	},
})
</script>

<style scoped lang="less">
.cadre-home {
	width: 100%;
	height: 100%;
	background-color: transparent;
	overflow-y: auto;
	position: relative;
	// :deep(.card-box) {
	// 	padding: 20px 20px;
	// }
	&::-webkit-scrollbar {
		width: 0;
		height: 0;
		display: none;
	}

	.coordinate-box {
		margin-top: 16px;
		display: flex;
		justify-content: space-between;
		width: 100%;
		height: 511px;
		border-radius: 8px;
		.left-box {
			width: 624px;
			border-radius: 8px;
			// .card-box:first-child(){
			// 	background: red !important;
			// }
			// ::v-deep .card-box:first-child {
			// 	background: red;
			// }
		}

		.right-box {
			width: 624px;
			border-radius: 8px;
		}
	}
	// 干部指数
	.index-box {
		margin-top: 16px;
		border-radius: 8px;
	}

	.ability-box {
		margin-top: 16px;
		min-height: 566px;
		border-radius: 8px;
	}

	.ability-box-1 {
		margin-top: 16px;
		display: flex;
		flex-direction: columns;
		border-radius: 8px;
		:deep(.card-box) {
			height: unset;
		}
	}

	.ability-box-2 {
		margin-top: 16px;
		display: flex;
		flex-direction: columns;
		// .box-content__line {
		// 	height: 191px;
		// }
		// .box-content__table {
		// 	margin-top: 16px;
		// 	flex: 1;
		// }
	}

	.ability-box-3 {
		margin-top: 16px;
		width: 100%;
		min-height: 179px;
		.content {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100%;
			font-size: 14px;
			font-weight: 400;
			color: #00cbff;
			line-height: 22px;
		}
	}
}

// .common {
// 	:deep(.box-content__table) {
// 		.title {
// 			margin-bottom: 10px;
// 			font-size: 16px;
// 			line-height: 16px;
// 			font-family: Source Han Sans CN;
// 			font-weight: 500;
// 			color: #00fff6;
// 		}
// 	}

// 	// .box-content__table {
// 	// 	padding: 0 27px;
// 	// 	.title {
// 	// 		margin-bottom: 10px;
// 	// 		font-size: 16px;
// 	// 		line-height: 16px;
// 	// 		font-family: Source Han Sans CN;
// 	// 		font-weight: 500;
// 	// 		color: #00fff6;
// 	// 	}
// 	// }
// 	.box-content__line {
// 	}
// }
</style>
