<template>
	<div class="cadre-home">
		<div class="left-box">
			<div class="user-box" v-if="!$route.meta.layoutFull">
				<Card class="user-info" size-type="1">
					<div class="info-box">
						<div class="user-base__info">
							<div class="top-info">
								<CodeAvatar :user_id="userInfo.user_id" :head_url="userInfo.avatar" />
								<div class="info">
									<div class="user-name">
										{{ userInfo.name }}
										<!-- <span v-if="userInfo.source" class="source">({{ userInfo.source }})</span> -->
									</div>

									<div class="info-item">
										<div class="label">
											<span type="label">出生年月</span>
											<span>:</span>
										</div>
										<div class="value">{{ userInfo.birthday_age }}</div>
									</div>
									<div class="info-item">
										<div class="label">
											<span type="label">现任职务</span>
											<span>:</span>
										</div>
										<div class="value omit-text">
											<a-popover title="现任职务" placement="rightTop">
												<template #content>{{ userInfo.current_position || '' }}</template>
												{{ userInfo.current_position || '' }}
											</a-popover>
										</div>
									</div>
								</div>
							</div>
							<div class="bottom-info">
								<div class="info-item">
									<div class="label">
										<span type="label">任现职务时间</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.position_time }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">干部类别</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.category }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">干部身份</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.identity }}</div>
								</div>
								<div class="info-item" v-if="userInfo.source">
									<div class="label">
										<span type="label">干部来源</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.source }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">全日制学历</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.education }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">毕业院校及专业</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo.school_major }}</div>
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">在职学历</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo?.education_in_service }}</div>
									<!--  education_in_service等后端添加字段 -->
								</div>
								<div class="info-item">
									<div class="label">
										<span type="label">毕业院校及专业</span>
										<span>:</span>
									</div>
									<div class="value">{{ userInfo?.school_major_in_service }}</div>
									<!--  school_major_in_service等后端添加字段 -->
								</div>
							</div>
						</div>
						<div class="label-container">
							<!-- <div class="label-info-box margintop-47">
								<div class="label-info">
									<div class="top-content">
										<div class="label">兴趣爱好</div>
									</div>
									<div class="content">
										<div class="tag-item" v-for="(item, index) in userInfo.like" :key="index">{{ item }}</div>
										<div class="tag-item" v-if="userInfo.like"></div>
										<span class="text">{{ userInfo.like }}</span>
									</div>
								</div>
							</div> -->
							<div class="label-info-box" v-if="userInfo.user_type != 3">
								<div class="label-info">
									<div class="top-content">
										<div class="label">分管领域</div>
										<div class="more a-pointer"><span @click="onResponModal" v-if="responsibilityData">更多</span></div>
									</div>
									<div class="content">
										<span class="text">
											{{ responsibilityData }}
										</span>
									</div>
								</div>
							</div>
							<!-- <div class="label-info-box">
								<div class="label-info">
									<div class="top-content">
										<div class="label">熟悉领域和特长</div>
									</div>

									<div class="content">
										<div class="tag-item" v-for="(item, index) in userInfo.goods_range" :key="index">{{ item }}</div>

										<span class="text"> {{ userInfo.goods_range }} </span>
									</div>
								</div>
							</div> -->
							<div class="label-info-box">
								<div class="label-info">
									<div class="top-content">
										<div class="label">特征标签</div>
										<div class="more a-pointer" @click="onTagModalShow"><span>更多</span></div>
									</div>
									<div class="content" style="display: flex; flex-direction: column">
										<!-- <div class="tag-item" v-for="(item, index) in userInfo.feature" :key="index">{{ item }}</div> -->
										<div class="text"><TagBox :data="userInfo.positive_feature_map" color="#008EFF" /></div>
										<div class="text" style="padding-top: 0px">
											<TagBox :data="userInfo.negative_feature_map" color="#FF6A16" />
										</div>
										<div class="text" style="padding-top: 0px">
											<TagBox :data="userInfo.corruption_risk_feature_map" color="#FF0000" />
										</div>
									</div>
								</div>
							</div>
							<div class="label-info-box">
								<div class="label-info">
									<div class="top-content">
										<div class="label">一言素描</div>
										<div class="more a-pointer" @click="onModal"><span>更多</span></div>
									</div>

									<div class="content">
										<span class="text"> {{ userInfo.sketch }} </span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</Card>
			</div>
		</div>
		<div class="right-box">
			<CadreBox
				:leader_index="userInfo.leader_index"
				:user-info="userInfo"
				:leader_index_rank="userInfo.leader_index_rank"
				:risk_index="userInfo.risk_index"
			/>
			<TeamMembers />

			<div class="coordinate-box">
				<div class="left-box">
					<!-- <div>三点</div> -->
					<LeaderIndexCharts />
				</div>
				<div class="left-box">
					<Coordinate />
				</div>
			</div>
			<div class="index-box common">
				<LeaderIndex />
			</div>
			<div class="ability-box common">
				<EvaluationAnalysis />
				<!-- <PersonalAnalysis /> -->
			</div>
			<div class="ability-box-1 common">
				<!-- <div class="box-content__line">
					<Ability :option="capabilityOption" />
				</div>
				<div class="box-content__table">
					<DataTable :data-source="capabilityTable.datasource" :columns="capabilityTable.columns" :row-color="capabilityTable.rowColor" />
				</div> -->
				<!-- <PersonalAnalysis /> -->
				<AbilityAnalysis />
			</div>
			<div class="ability-box-2 common">
				<HistoricalPerformance />
			</div>
			<div class="ability-box-3 common">
				<NegativeList />
			</div>
		</div>

		<Modal :visible="visible" @close="onClose">
			<Card :header="false" class="modal-card" title="一言素描" size-type="3">
				<div class="modal-card-box">
					<div class="modal-title">一言素描</div>
					<div class="modal-content">
						<div class="content-item" v-for="(item, index) in userInfo.sketch_list" :key="index">
							<div class="text">{{ item.hobby }}</div>
							<!-- <div class="text">敢担当，能攻坚，分管场镇建设、征地拆迁成效好，农村工作经验丰 富，干群口碑较好。不足：性格直，快人快语。</div> -->
							<!-- <div class="user-name">评价人：{{ item.name }}</div> -->
							<div class="user-name">评价人：* * *</div>
						</div>
						<!-- <div class="content-item">
							<div class="text">敢担当，能攻坚，分管场镇建设、征地拆迁成效好，农村工作经验丰 富，干群口碑较好。不足：性格直，快人快语。</div>
							<div class="user-name">评价人：李四</div>
						</div>
						<div class="content-item">
							<div class="text">敢担当，能攻坚，分管场镇建设、征地拆迁成效好，农村工作经验丰 富，干群口碑较好。不足：性格直，快人快语。</div>
							<div class="user-name">评价人：王五</div>
						</div> -->
					</div>
				</div>
			</Card>
		</Modal>
		<a-modal class="tag-ant-modal" :visible="tagVisible" @cancel="onTagModalClose" title="特征标签" :footer="null">
			<div class="modal-content">
				<time-line :times="tagReactive.tagYearList" v-model:value="tagReactive.tagYear" />
				<div class="tag-content">
					<div class="inner">
						<!-- <template v-for="item in userInfo.feature_list">
							<TagBox color="#008EFF" :data="item.positive_feature_map" />
							<TagBox color="#FF6A16" :data="item.negative_feature_map" />
						</template> -->
						<TagBox color="#008EFF" :data="tagMap?.positiveTag || []" />
						<TagBox color="#FF6A16" :data="tagMap?.negativeTag || []" />
						<TagBox color="#FF0000" :data="tagMap?.corruptionRiskTag || []" />
					</div>
					<div class="legend">
						<div class="tag-1">干部特点</div>
						<div class="tag-2">主要不足</div>
						<div class="tag-3">廉政风险</div>
					</div>
				</div>
				<div class="other-content">
					<div class="title">其他标签</div>
					<div class="inner" style="border-bottom: none">
						<!-- <template v-for="item in userInfo.feature_list">
							<TagBox color="#008EFF" :data="item.positive_feature_map" />
							<TagBox color="#FF6A16" :data="item.negative_feature_map" />
						</template> -->
						<TagBox color="#60CA71" :data="tagMap?.otherTag || []" />
					</div>
				</div>
				<!-- <div class="content-item">
							<div class="tag-box">
								<div class="tag-item">担当型</div>
							</div>
							<div class="user-name">评价人：王五</div>
						</div>
						<div class="content-item">
							<div class="tag-box">
								<div class="tag-item">担当型</div>
							</div>
							<div class="user-name">评价人：王五</div>
						</div> -->
			</div>
		</a-modal>
		<ResponsibilityModal ref="responsibilityModalRef" />
	</div>
</template>

<script lang="ts" setup>
import { getFeature, getLeaderBaseInfo } from '@/apis/cadre-portrait/home'
import CodeAvatar from '@/components/CodeAvatar.vue'
import TagBox from '@/components/TagBox.vue'
import TimeLine from '@/components/Timeline.vue'
import useUser from '@/store/user'
import { computed, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import AbilityAnalysis from './component/AbilityAnalysis.vue'
import CadreBox from './component/CadreBox.vue'
import Coordinate from './component/Coordinate.vue'
import EvaluationAnalysis from './component/EvaluationAnalysis.vue'
import HistoricalPerformance from './component/HistoricalPerformance.vue'
import LeaderIndex from './component/LeaderIndex.vue'
import LeaderIndexCharts from './component/LeaderIndexCharts.vue'
import NegativeList from './component/NegativeList.vue'
import ResponsibilityModal from './component/ResponsibilityModal.vue'
import TeamMembers from './component/TeamMembers.vue'

import { useHomeStore } from '@/store/home'

import type { UserInfo } from '@/types/user'
import { message } from 'ant-design-vue'

const CDN_URL = import.meta.env.VITE_CDN_URL

const user = useUser()
const route = useRoute()
const home = useHomeStore()
const responsibilityModalRef = ref()

const { user_id } = route.query

const visible = ref(false)

const userInfo = ref<UserInfo>({
	user_id: 0,
	name: '',
	birthday_age: '',
	education: '',
	current_position: '',
	position_time: '',
	leader_index: 0,
	leader_index_rank: '',
	risk_index: 0,
	risk_index_avg: 0,
	feature: [],
	goods_range: '',
	like: '',
	sketch: '',
	areas_responsibility: [],
	feature_list: [],
	sketch_list: [],
	promotion_type: -1,
	user_type: undefined,
})

const tagVisible = ref(false)

const avatarUrl = computed(() => (userInfo.value.avatar ? `url("${CDN_URL}/fr_img/${userInfo.value.avatar}")` : ''))

const getAvatar = (avatar: string) => {
	return avatar || defa
}

const onClose = () => {
	visible.value = false
}
const onModal = () => {
	visible.value = true
}
const onResponModal = () => {
	responsibilityModalRef.value.open(userInfo.value.areas_responsibility)
}
const onTagModalShow = () => {
	tagVisible.value = true
}
const onTagModalClose = () => {
	tagVisible.value = false
}
const convertMap = (data: Array<any>) => {
	const positive_feature_map: any = []
	data.map((item: any) => {
		const _index = positive_feature_map?.find((item1: any) => item1.label === item)
		if (!_index) {
			positive_feature_map.push({
				label: item,
				count: 1,
			})
		} else {
			_index.count++
		}
	})

	return positive_feature_map
}

const responsibilityData = computed(() => {
	const rbl = userInfo.value?.areas_responsibility
	if (!rbl) return ''

	const lastindex = rbl.length - 1
	const rblObj = rbl[lastindex]

	return rblObj ? rblObj?.charge_range : ''
})
// 初始化特征标签
const tagReactive = reactive({
	tagYearList: [],
	tagYear: '',
	feature: [],
})
const initFeature = async () => {
	const res = await getFeature({ user_id })
	if (res.code === 0) {
		tagReactive.feature = res.data

		tagReactive.tagYear = res.data[0]?.time

		tagReactive.tagYearList = res.data.map((item: any) => item.time)
	} else {
		message.error(res.message)
	}
}
// 获取标签数据
const tagMap = computed(() => {
	const _data: any = tagReactive.feature?.find((item: any) => item.time === tagReactive.tagYear)
	/**
 *  negativeTag	list	-	Y	负面标签
	positiveTag	list	-	Y	正面标签
	corruptionRiskTag list	-	Y	廉政风险标签
	otherTag	list	-	Y	其他
 */
	const tags: any = {}
	if (_data?.negativeTag) {
		tags.negativeTag = convertMap(_data.negativeTag).sort((a: any, b: any) => b.count - a.count)
	}
	if (_data?.positiveTag) {
		tags.positiveTag = convertMap(_data.positiveTag).sort((a: any, b: any) => b.count - a.count)
	}
	if (_data?.otherTag) {
		tags.otherTag = convertMap(_data.otherTag).sort((a: any, b: any) => b.count - a.count)
	}
	if (_data?.corruptionRiskTag) {
		tags.corruptionRiskTag = convertMap(_data.corruptionRiskTag).sort((a: any, b: any) => b.count - a.count)
	}

	return tags
})

initFeature()
/**
 * @description: 获取干部基本信息
 * @return {*}
 */
const initLeaderBaseInfoData = async () => {
	const res: any = await getLeaderBaseInfo({ user_id })
	if (res.code === 0) {
		if (res.data.positive_feature) {
			res.data.positive_feature_map = convertMap(res.data.positive_feature)
				.sort((a: any, b: any) => b.count - a.count)
				.slice(0, 3)
		}
		if (res.data.negative_feature) {
			res.data.negative_feature_map = convertMap(res.data.negative_feature)
				.sort((a: any, b: any) => b.count - a.count)
				.slice(0, 2)
		}
		if (res.data.corruption_risk_feature) {
			res.data.corruption_risk_feature_map = convertMap(res.data.corruption_risk_feature)
				.sort((a: any, b: any) => b.count - a.count)
				.slice(0, 2)
		}
		const feature_list = res.data.feature_list || []

		const positive_feature: any = []
		const negative_feature: any = []
		const other_feature: any = []
		const corruption_risk_feature: any = []
		// 和该字段和res.data.feature_list 数据一样
		feature_list.map((item: any) => {
			if (item.positive_feature) {
				item.positive_feature_map = convertMap(item.positive_feature)

				positive_feature.push(...item.positive_feature)
			}
			if (item.negative_feature) {
				item.negative_feature_map = convertMap(item.negative_feature)

				negative_feature.push(...item.negative_feature)
			}
			if (item.other_feature) {
				item.other_feature_map = convertMap(item.other_feature)

				other_feature.push(...item.other_feature)
			}
			if (item.corruption_risk_feature) {
				item.corruption_risk_feature_map = convertMap(item.corruption_risk_feature)

				corruption_risk_feature.push(...item.corruption_risk_feature)
			}
		})
		// 正面标签
		res.data.positive_feature_detail = convertMap(positive_feature).sort((a: any, b: any) => b.count - a.count)
		// 负面标签
		res.data.negative_feature_detail = convertMap(negative_feature).sort((a: any, b: any) => b.count - a.count)
		// 其他标签
		if (res.data.other_feature) {
			res.data.other_feature_detail = convertMap(res.data.other_feature).sort((a: any, b: any) => b.count - a.count)
		}
		// 廉政风险标签
		if (corruption_risk_feature.length > 0) {
			res.data.corruption_risk_feature_detail = convertMap(corruption_risk_feature).sort((a: any, b: any) => b.count - a.count)
		}

		userInfo.value = res.data
		user.updateDetail(res.data)
		let id
		if (res.data.promotion_type === 2) {
			id = 2
		} else if (user.detail.promotion_type === 3) {
			id = 3
		} else {
			id = 1
		}
		home.updateCoorMenuSelected(id)
	}
}

initLeaderBaseInfoData()
</script>

<style scoped lang="less">
.cadre-home {
	width: 100%;
	height: 100%;
	background-color: transparent;
	position: relative;
	display: flex;
	padding: 16px 0px;
	& > .left-box {
		margin-right: 16px;
		.user-box {
			width: 468px;
			height: 100%;
			overflow: hidden;
			padding: 11px 12px;
			background-color: #ffffff;
			border-radius: 8px;
			.user-info {
				padding: 12px 8px;
				width: 100%;
				height: 100%;
				background-color: #ecf5ff;
				border-radius: 8px;
				.info-box {
					height: 100%;
					display: flex;
					flex-direction: column;
				}
				.user-base__info {
					display: flex;
					flex-direction: column;
					.avatar {
						flex-shrink: 0;
						margin-right: 18px;
						width: 100px;
						height: 134px;
						object-fit: cover;
						background: url('@/assets/images/avatar.png') no-repeat center center / cover;
						// background-color: rgba(204, 204, 204, 0.2);
					}
					.top-info {
						display: flex;
					}
					.bottom-info,
					.info {
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						.user-name {
							font-size: 28px;
							font-family: PingFang SC-Bold, PingFang SC;
							font-weight: bold;
							color: #000000;
							line-height: 33px;
						}
						.source {
							font-size: 20px;
							font-weight: bold;
						}
						.info-item {
							display: flex;
							font-size: 20px;
							font-family: PingFang SC-Medium, PingFang SC;
							font-weight: 500;
							color: #333333;
							line-height: 38px;
							letter-spacing: 1px;
							.label {
								flex-shrink: 0;
								margin-right: 10px;
								span[type='label'] {
									display: inline-block;
									min-width: 85px;
									text-align-last: justify;
								}
							}

							.info {
							}
						}
					}
					.bottom-info {
						.info-item {
							.label {
								flex-shrink: 0;
								margin-right: 10px;
								span[type='label'] {
									display: inline-block;
									min-width: 203px;
									text-align-last: justify;
								}
							}
						}
					}
				}
				.label-container {
					flex: 1;
					overflow-y: auto;
					&::-webkit-scrollbar {
						display: none;
					}
				}
				.number-card {
					margin-top: 36px;
					display: flex;
					justify-content: space-between;
					.card-1,
					.card-2 {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 252px;
						height: 45px;
						// background: url('@/assets/images/card1-bg.png') no-repeat center / cover;

						.label-icon {
							margin-right: 18px;
							display: inline-block;
							width: 16px;
							height: 16px;
							line-height: 1;
							// background: url('@/assets/images/icon-1.png') no-repeat center / 100% 100%;
							cursor: pointer;
						}
						.label {
							margin-right: 7px;
							font-size: 16px;
							font-weight: 400;
							// color: #96e9ff;
							line-height: 16px;
						}
						.label-1 {
							font-size: 20px;
							font-weight: bold;
							color: #f6e81f;
							line-height: 20px;
						}
						.label-2 {
							font-size: 15px;
							font-weight: bold;
							color: #f6e81f;
							// line-height: 15px;
						}
					}
					.card-1 {
						.label-1 {
							font-size: 20px;
							font-weight: bold;
							color: #f6e81f;
							line-height: 15px;
						}
						.label-2 {
							position: relative;
							font-weight: bold;
							color: #f6e81f;
							line-height: 15px;
							&::after {
								position: absolute;
								bottom: -5px;
								left: 16px;
								content: '';
								display: inline-block;
								width: 6px;
								height: 2px;
								background: #05b0ff;
							}
						}
					}
					.card-2 {
						.label-1 {
							font-size: 19px;
							font-weight: bold;
							color: #ff3b1b;
							line-height: 15px;
						}
					}
				}
				.label-info-box {
					.top-content {
						padding: 4px 12px 4px 18px;
						display: flex;
						justify-content: space-between;
						background: url('@/assets/images/card-header-bg1.png') no-repeat left center / 100% 100%;

						.more {
							display: flex;
							align-items: center;
							span {
								font-size: 18px;
								font-family: PingFang SC-Medium, PingFang SC;
								font-weight: 500;
								color: #666666;
								line-height: 18px;
							}
							&::after {
								content: '';
								display: inline-block;
								width: 24px;
								height: 24px;
								background: url('@/assets/images/chevron-right.png') no-repeat center / 100% 100%;
							}
						}
					}
					.label-info {
						.label {
							display: flex;
							align-items: center;
							font-size: 20px;
							font-family: PingFang SC-Bold, PingFang SC;
							font-weight: bold;
							color: #222222;
							line-height: 24px;
						}
						.content {
							display: flex;
							.text {
								padding: 14px 11px 20px 18px;
								font-size: 20px;
								font-family: PingFang SC-Medium, PingFang SC;
								font-weight: 500;
								color: #333333;
								line-height: 31px;
							}
						}
					}
				}
			}
		}
	}
	// :deep(.card-box) {
	// 	padding: 20px 20px;
	// }
	.right-box {
		flex: 1;
		overflow-y: auto;
		&::-webkit-scrollbar {
			width: 0;
			height: 0;
			display: none;
		}

		.coordinate-box {
			margin-top: 16px;
			display: flex;
			justify-content: space-between;
			width: 100%;
			height: 521px;
			border-radius: 8px;
			.left-box {
				width: 49%;
				border-radius: 8px;
				// .card-box:first-child(){
				// 	background: red !important;
				// }
				// ::v-deep .card-box:first-child {
				// 	background: red;
				// }
			}

			.right-box {
				flex: 48%;
				border-radius: 8px;
			}
		}
		// 干部指数
		.index-box {
			margin-top: 16px;
			border-radius: 8px;
		}

		.ability-box {
			margin-top: 16px;
			min-height: 566px;
			border-radius: 8px;
			background-color: #ffffff;
		}

		.ability-box-1 {
			margin-top: 16px;
			display: flex;
			flex-direction: columns;
			border-radius: 8px;
			:deep(.card-box) {
				height: unset;
			}
		}

		.ability-box-2 {
			margin-top: 16px;
			display: flex;
			flex-direction: columns;
			// .box-content__line {
			// 	height: 191px;
			// }
			// .box-content__table {
			// 	margin-top: 16px;
			// 	flex: 1;
			// }
		}

		.ability-box-3 {
			margin-top: 16px;
			width: 100%;
			min-height: 179px;
			.content {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100%;
				font-size: 14px;
				font-weight: 400;
				color: #00cbff;
				line-height: 22px;
			}
		}
	}
}

.modal-card {
	width: 900px;
	height: 750px;
	.modal-card-box {
		padding: 20px;
	}
	.modal-title {
		font-size: 20px;
		font-weight: bold;
		color: #000000;
		text-align: center;
	}
	.modal-content {
		padding: 4px 0;
		font-size: 16px;
		color: #ffffff;
		.content-item {
			padding: 10px 0;
			margin-top: 20px;
			border-bottom: 1px solid rgba(0, 234, 255, 0.1);
			&:nth-last-child(1) {
				border-bottom: none;
			}
			.text {
				font-size: 16px;
				font-weight: 400;
				color: #000000;
				line-height: 25px;
			}
			.user-name {
				line-height: 20px;
				margin: 12px 20px 0 0;
				// float: right;
				text-align: right;
				font-size: 14px;
				font-weight: 500;
				color: #000000;
				line-height: 14px;
			}
		}
	}
}

.tag-modal {
	.content-item {
		.tag-box {
			display: flex;
			flex-direction: column;
			flex-wrap: wrap;
			.tag-item {
				margin-bottom: 10px;
			}
		}
		.text {
			font-size: 16px;
			font-weight: 400;
			color: #00eaff;
			line-height: 25px;
		}
	}
}

.tag-item {
	margin-right: 29px;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0 18px;
	min-width: 131px;
	height: 36px;

	font-size: 18px;
	font-weight: 500;
	color: #00eaff;
	line-height: 14px;

	border: 1px solid #00eaff;
	border-radius: 5px;

	background: linear-gradient(0deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%),
		linear-gradient(90deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
		linear-gradient(180deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
		linear-gradient(270deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%);
}
</style>
<style lang="less">
.tag-ant-modal {
	width: 1300px !important;
	.ant-modal-title {
		font-size: 24px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		text-align: center;
	}
	.tag-content,
	.other-content {
		background: #f7fbff;
		.inner {
			padding: 24px 24px 50px;
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
		}
		.legend {
			padding: 12px 0px;
			display: flex;
			justify-content: center;
			gap: 30px;
			.tag-1,
			.tag-2,
			.tag-3 {
				display: flex;
				align-items: center;
				font-size: 16px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				&::before {
					content: '';
					display: inline-block;
					width: 8px;
					height: 8px;
					margin-right: 4px;
				}
			}
			.tag-1 {
				&::before {
					background: #008eff;
				}
			}
			.tag-2 {
				&::before {
					background: #ff6a16;
				}
			}
			.tag-3 {
				&::before {
					background: #ff0000;
				}
			}
		}
	}
	.other-content {
		padding: 24px;
		margin-top: 20px;
		height: 220px;
		background: #f6fff7;
		border-radius: 8px 8px 8px 8px;
		.title {
			display: flex;
			align-items: center;
			font-size: 22px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			line-height: 22px;
			&::before {
				content: '';
				margin-right: 7px;
				display: inline-block;
				width: 8px;
				height: 24px;
				background: url('@/assets/images/card-icon.png') no-repeat center / cover;
			}
		}
	}
}
</style>
