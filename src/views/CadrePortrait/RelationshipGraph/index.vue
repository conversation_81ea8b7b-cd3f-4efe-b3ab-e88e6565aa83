<template>
	<div class="relationship-graph">
		<RelationGraph ref="seeksRelationGraph" :options="options.userGraphOptions" :on-node-click="onNodeClick">
			<template v-slot:node="{ node }">
				<NodeTop :node="node" v-if="node.data.slotType === 'top'" :avatar="user.detail.avatar" />
				<Vnode v-else-if="node.data.whether === 1" />
				<NodeNormal :node="node" v-else :select="selectId" />
			</template>
		</RelationGraph>
		<div class="tips-box">
			<div class="tips-item tips-area">
				<div class="tips-item__icon"></div>
				<div class="tips-item__text">行政机构体系内</div>
			</div>
			<div class="tips-item unit">
				<div class="tips-item__icon"></div>
				<div class="tips-item__text">本人同单位</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onBeforeUnmount, computed, inject } from 'vue'

import RelationGraph from 'relation-graph/vue3'

import NodeTop from './components/node-top.vue'
import Vnode from './components/v-node.vue'
import NodeNormal from './components/node-normal.vue'

import useUser from '@/store/user'

import { getRelationship } from '@/apis/cadre-portrait/relationship'

import { getUserInfoItem } from '@/utils/utils'

import { convertDataRecursive, flattenAndModifyData, createUnikey } from './util/index'

const CDN_URL = import.meta.env.VITE_CDN_URL

const user = useUser()

const selectId = ref('')

const seeksRelationGraph = ref()

const options = reactive({
	slotTeamplateId: 'slot1',
	userGraphOptions: {
		allowSwitchLineShape: true,
		allowSwitchJunctionPoint: true,
		defaultJunctionPoint: 'border',
		defaultNodeShape: 0,
		defaultNodeBorderWidth: 0,
		defaultLineColor: '#448FFF',
		defaultLineWidth: 4,
		disableDragNode: false,
		isMoveByParentNode: true,
		disableDragCanvas: false,
		debug: false,
		layouts: [
			{
				label: '中心布局',
				layoutName: 'center',
				layoutClassName: 'seeks-layout-force',
				distance_coefficient: 1,
			},
		],
		defaultLineMarker: {
			markerWidth: '0',
			markerHeight: '0',
			refX: 6,
			refY: 6,
			data: 'M2,2 L10,6 L2,10 L6,6 L2,2',
		},
		// 这里可以参考"Graph 图谱"中的参数进行设置
	},
})

const avatarUrl = computed(() => (user.detail.avatar ? `${CDN_URL}/fr_img/${user.detail.avatar}` : ''))

const showSeeksGraph = (__graph_json_data: any) => {
	seeksRelationGraph.value.setJsonData(__graph_json_data)
}

const addClassToList = (list: any) => {
	return list.map((item: any, index: number) => {
		const newItem: any = { styleClass: '' }
		if (index === 0) {
			newItem.styleClass = 'c-node-menu-item__top'
			newItem.disableDrag = true
		} else if (item.whether === 1) {
			newItem.styleClass = 'c-node-menu-item__vnode'
		} else {
			newItem.styleClass = 'c-node-menu-item__normal'
		}
		return {
			...item,
			...newItem,
		}
	})
}
const onNodeClick = (data: any) => {
	selectId.value = data.data.id
}
const createData = (data: any) => {
	const __d: any = createUnikey([data])

	// __d[0].children = computePoint(__d[0].children, 100)

	const nodes = addClassToList(flattenAndModifyData(__d))

	const lines = convertDataRecursive(__d)
	return {
		nodes,
		lines,
		rootId: nodes[0].id,
	}
}
const initData = async () => {
	const user_id = inject('user_id')
	const res = await getRelationship({ user_id })

	if (res.code === 0) {
		const _d = res.data
		if (!_d) return

		showSeeksGraph(createData(_d))
	}
}
//-- start 初始化
{
	initData()
}
//-- end

onBeforeUnmount(() => {
	seeksRelationGraph.value.getInstance().stopAutoLayout()
})
</script>

<style scoped lang="less">
.relationship-graph {
	width: 100%;
	height: 100%;
}
.tips-box {
	position: absolute;
	bottom: 30px;
	left: 200px;
	.tips-item {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		.tips-item__icon {
			width: 10px;
			height: 10px;
			border-radius: 50%;
			margin-right: 10px;
		}
		.tips-item__text {
			font-size: 12px;
			color: #333333;
		}
	}
	.tips-area {
		.tips-item__icon {
			background-color: #ff6e41;
		}
	}
	.unit {
		.tips-item__icon {
			background-color: #ff1212;
		}
	}
}
::v-deep(.c-node-menu-item__top) {
	width: 186px !important;
	height: 186px !important;
	background-color: #008eff !important;
	box-shadow: 0px 0px 23px 0px #3496e2 !important;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	border-radius: 50%;
}

::v-deep(.c-node-menu-item__normal) {
	width: 90px !important;
	height: 90px !important;
	background: linear-gradient(140deg, #75c2ff 3%, #39a4fa 19%, #108ff3 100%);
	box-shadow: 0px 4px 20px 0px rgba(0, 142, 255, 0.25);
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	border-radius: 50%;
}
::v-deep(.c-node-menu-item__vnode) {
	width: 30px !important;
	height: 30px !important;
	background: linear-gradient(173deg, #74deff 13%, #4aafff 89%, #41abff 95%);
	box-shadow: 0px 4px 20px 0px rgba(0, 142, 255, 0.25);
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	border-radius: 50%;
}
::v-deep(.rel-linediv) {
	z-index: unset !important;
}
::v-deep(.rel-nodediv) {
	z-index: unset !important;
}
</style>
