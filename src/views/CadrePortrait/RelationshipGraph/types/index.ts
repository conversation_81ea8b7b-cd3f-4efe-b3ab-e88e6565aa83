export type ChildData = {
	whether: number
	id?: string
	user_id: number
	name: string
	organization_name: string
	position: string
	code_name: string
	has_child: number
	with_institution?: number
	with: number
	children?: ChildData[]
}

export type OriginalData = {
	whether: number
	id?: string
	user_id: number
	name: string
	organization_name: string
	position: string
	code_name: string
	has_child: number
	with_institution?: number
	with: number
	children?: ChildData[]
}
