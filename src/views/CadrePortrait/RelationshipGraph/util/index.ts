import { OriginalData } from '../types/index'
import { generateUUID, getRadian } from '@/utils/utils'
interface newItem {
	data?: any
	styleClass?: string
	x?: number
	y?: number
}
export const convertDataRecursive = (data: OriginalData[]): Array<{ from: string; to: string }> => {
	const result: Array<{ from: string; to: string }> = []

	for (const item of data) {
		if (item.children && item.children.length > 0) {
			// 处理当前层级的映射
			for (const child of item.children) {
				result.push({ from: String(item.id), to: String(child.id) })
			}

			// 递归处理子层级数据
			const childMappings = convertDataRecursive(item.children)
			result.push(...childMappings)
		}
	}

	return result
}

export const flattenAndModifyData = (data: OriginalData[], isMainCall = true): OriginalData[] => {
	const flattenedData: OriginalData[] = []

	for (const item of data) {
		// 创建新对象以保持原始数据不变
		const newItem: OriginalData & newItem = { ...item }
		// 第一层
		// 如果存在子项，则递归扁平化
		if (newItem.children) {
			const children = flattenAndModifyData(newItem.children, false)
			delete newItem.children // 删除原始数据中的 "children" 字段
			flattenedData.push(newItem) // 添加当前项到扁平化数组
			flattenedData.push(...children) // 添加子项到扁平化数组
		} else {
			// 如果没有子项，直接添加到扁平化数组
			flattenedData.push(newItem)
		}
		// 添加 "data" 属性
		newItem.data = {
			id: newItem.id,
			user_id: newItem.user_id,
			name: newItem.name,
			organization_name: newItem.organization_name,
			position: newItem.position,
			code_name: newItem.code_name,
			slotType: isMainCall ? 'top' : 'normal',
			whether: newItem.whether,
			with_institution: newItem.with_institution,
		}
	}

	return flattenedData
}
/**
 * @description: 计算坐标
 * @param {OriginalData} data
 * @param {number} startDegrees
 * @return {*}
 */
export const computePoint = (data: OriginalData[], hypotenuse: number) => {
	let count = 0

	const run = (data: OriginalData[], hypotenuse: number): OriginalData[] => {
		if (!data.length) return []

		const degrees_avg = 360 / data.length
		let startDegrees = 0

		return data.map((item, index) => {
			const degrees = degrees_avg * index

			let point = { x: 0, y: 0 }
			switch (degrees) {
				case 0:
					point = { x: hypotenuse, y: 0 }
					break
				case 90:
					point = { x: 0, y: hypotenuse }
					break
				case 180:
					point = { x: -hypotenuse, y: 0 }
					break
				case 270:
					point = { x: 0, y: -hypotenuse }
					break
			}

			if (degrees > 90 && degrees < 0) {
				// 第一象限 x为正值， y为负值
				point = getPoint(degrees, hypotenuse)
			} else if (degrees > 90 && degrees < 180) {
				// 第二象限 x, y 都为负值
				const { x, y } = getPoint(90 - degrees, hypotenuse)

				point = { x: -x, y: -y }
			} else if (degrees > 180 && degrees < 270) {
				// 第三象限， x为负 y为正
				const { x, y } = getPoint(270 - degrees, hypotenuse)

				point = { x: -x, y }
			} else if (degrees > 270 && degrees < 360) {
				// 第四象限 x 为正 y 为正
				const { x, y } = getPoint(360 - degrees, hypotenuse)

				point = { x, y }
			}

			if (item.children && item.children.length > 0) {
				count += 1
				item.children = run(item.children, hypotenuse * count)
			} else {
				count = 0
			}
			startDegrees += degrees
			return {
				...point,
				...item,
			}
		})
	}

	if (!data) return
	return run(data, hypotenuse)
}
// 为每条数据添加唯一标识
export const createUnikey = (data: OriginalData[]): any => {
	data = JSON.parse(JSON.stringify(data)) || []

	for (const item of data) {
		// 为数组
		item.id = generateUUID()

		if (Array.isArray(item.children)) {
			item.children = createUnikey(item.children)
		}
	}
	return data
}
// 正弦函数
export const sin = (x: number) => {
	return Math.sin(x)
}
// 余弦函数
export const cos = (x: number) => {
	return Math.cos(x)
}
// 获取坐标
export const getPoint = (degrees: number, hypotenuse: number) => {
	const x = cos(getRadian(degrees)) * hypotenuse
	const y = sin(getRadian(degrees)) * hypotenuse
	return { x, y }
}
