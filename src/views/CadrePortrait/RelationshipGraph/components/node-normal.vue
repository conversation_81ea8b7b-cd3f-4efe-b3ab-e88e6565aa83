<template>
	<div :class="`node-normal ${className}`" @mouseenter="onMouseEnter" @mouseleave="onMouseLeave">
		<div :class="activeBoxClassName" v-if="activeVisible || select === node.data.id">
			<div class="org-name">{{ node.data.organization_name }}</div>
			<div class="position">{{ node.data.position }}</div>
		</div>
		<div class="color-box"></div>
		<div class="text-box">
			<div class="name">{{ node.data.name }}</div>
			<div class="relationship">
				{{ node.data.code_name }}
			</div>
		</div>
	</div>
</template>

<script setup>
import { computed, toRefs, unref, ref } from 'vue'

const props = defineProps({
	node: {
		mustUseProp: true,
		default: null,
		type: Object,
	},
	select: {
		mustUseProp: true,
		default: '',
		type: String,
	},
})
// 控制 activeBox 的显示
const activeVisible = ref(false)

// const sameUnit = computed(() => {
// 	const { select, node } = toRefs(props)
// 	const with_institution = unref(node).data.with_institution

// 	return with_institution === 1
// })

const className = computed(() => {
	const { node } = toRefs(props)

	const with_institution = unref(node).data.with_institution

	let className = ''
	switch (with_institution) {
		case 0:
			className = 'node-normal__area'
			break
		case 1:
			className = 'area'
			break
		case 2:
			className = 'same-unit'
			break
	}
	return className
})

const activeBoxClassName = computed(() => {
	return unref(props.node).x > 0 ? 'right-active-box' : 'left-active-box'
})

const onMouseEnter = () => {
	activeVisible.value = true
}

const onMouseLeave = () => {
	activeVisible.value = false
}
</script>

<style lang="less">
.node-normal__area {
	.color-box {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(140deg, #75c2ff 3%, #39a4fa 19%, #108ff3 100%);
		border-radius: 50%;
	}
}
.node-normal {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	overflow: hidden;
	.text-box {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		.name {
			font-size: 18px;
			font-family: Source Han Sans CN-Bold, Source Han Sans CN;
			font-weight: bold;
			color: #ffffff;
			line-height: 24px;
			text-align: center;
		}
		.relationship {
			margin-top: 5px;
			font-size: 14px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
			line-height: 24px;
			text-align: center;
		}
	}
}
.same-unit {
	background: linear-gradient(128deg, #ff8383 8%, #ff1212 40%, #ff1212 95%) !important;
	box-shadow: 0px 4px 20px 0px rgba(98, 209, 59, 0.25) !important;
	opacity: 1;
}
.area {
	background: linear-gradient(127deg, #ffb648 10%, #ff754a 39%, #ff6e41 100%) !important;
	box-shadow: 0px 4px 20px 0px rgba(255, 110, 65, 0.25) !important;
	border-radius: 50%;
}
.right-active-box {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 403px;
	height: 90px;
	background: linear-gradient(90deg, rgba(255, 124, 68, 0.21) 0%, rgba(255, 112, 67, 0) 100%);
	border-radius: 10px 10px 10px 10px;
	opacity: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	transform: translate(0px, -50%);
	.org-name,
	.position {
		padding-left: 66px;
		font-size: 18px;
		font-family: PingFang SC-Medium, PingFang SC;
		font-weight: 500;
		color: #000000;
		line-height: 21px;
		text-align: left;
	}
	.org-name {
	}
	.position {
		margin-top: 10px;
	}
}
.left-active-box {
	position: absolute;
	top: 50%;
	right: 50%;
	width: 403px;
	height: 90px;
	background: linear-gradient(270deg, rgba(255, 124, 68, 0.21) 0%, rgba(255, 112, 67, 0) 100%);
	border-radius: 10px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	transform: translate(0px, -50%);
	.org-name,
	.position {
		padding-right: 66px;
		font-size: 18px;
		font-family: PingFang SC-Medium, PingFang SC;
		font-weight: 500;
		color: #000000;
		line-height: 21px;
		text-align: right;
	}
	.org-name {
	}
	.position {
		margin-top: 10px;
	}
}
</style>
