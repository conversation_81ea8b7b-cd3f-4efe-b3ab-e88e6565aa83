<template>
	<div class="node-top">
		<div class="avatar">
			<CodeAvatar :head_url="avatar">
				<template #avatar="{ avatar }">
					<img :src="avatar" />
				</template>
			</CodeAvatar>
		</div>
		<div class="bg-box">
			<div class="bg-1"></div>
			<div class="bg-2"></div>
			<div class="bg-3"></div>
			<div class="bg-4"></div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import CodeAvatar from '@/components/CodeAvatar.vue'
defineProps({
	node: {
		mustUseProp: true,
		default: null,
		type: Object,
	},
	avatar: String,
})
</script>

<style scoped lang="less">
.node-top {
	height: 100%;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	.avatar {
		width: 60%;
		height: 60%;
		border-radius: 50%;
		background: url('@/assets/images/avatar.png') center / cover no-repeat;
		img {
			width: 100%;
			height: 100%;
			border-radius: 50%;
			transform: scale(1.1);
		}
	}
	.bg-box {
		position: absolute;
		inset: 0;
		border-radius: 50%;
		z-index: -2;
		.bg-1 {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 398px;
			height: 398px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			transform: translate(-50%, -50%);
			border-radius: 50%;
			border: 27px solid #eff7ff;
			z-index: 4;
		}
		.bg-2 {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 552px;
			height: 552px;
			background: #ffffff;
			opacity: 1;
			border-radius: 50%;
			transform: translate(-50%, -50%);
			z-index: 3;
		}
		.bg-3 {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 867px;
			height: 867px;
			background: rgba(206, 229, 248, 0.15);
			opacity: 1;
			border: 1px dashed #9cd0fa;
			border-radius: 50%;
			transform: translate(-50%, -50%);
			z-index: 2;
		}
		.bg-4 {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 1872px;
			height: 1872px;
			background: rgba(156, 208, 250, 0.15);
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			transform: translate(-50%, -50%);
			border-radius: 50%;
			z-index: 1;
			border: 1px dashed #9cd0fa;
		}
	}
}
</style>
