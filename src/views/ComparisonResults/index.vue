<template>
	<div class="comparison-results ignore-min-width" ref="scrollContainRef">
		<div class="card-header header">
			<div class="card-bg">
				<span class="back" @click="onBack"></span>
				<span class="title">对比结果</span>
			</div>
		</div>
		<div class="table-container">
			<ScrollProvider>
				<div class="content-box fixed-box" :style="{ zIndex: showFiexdHeader ? 101 : -1, opacity: showFiexdHeader ? 1 : 0 }">
					<ReportTable
						:scoll-container="scrollContainRef"
						:columns="columns.slice(0, 1)"
						:data-source="dataSource"
						border
						:row-class="rowClass"
						drag
						@scroll="ontableScroll"
					>
						<template #top>
							<div class="table-top">
								<div class="avatar-box" v-for="(item, index) in dataSource" :key="index">
									<div class="avatar" :style="{ backgroundImage: `url('${CDN_URL}/fr_img/${item.headUrl}')` }">
										<span class="delete-icon" @click="onDeleteColumn(item, index)"></span>
									</div>
								</div>
							</div>
						</template>
					</ReportTable>
				</div>
				<div class="content-box" ref="reportTableRef">
					<div class="border-box">
						<ReportTable
							:scoll-container="scrollContainRef"
							:columns="columns.slice(0, 1)"
							:data-source="dataSource"
							border
							:row-class="rowClass"
							drag
							@scroll="ontableScroll"
							:fixedRow="1"
						>
							<template #top>
								<div class="table-top">
									<div class="avatar-box" v-for="(item, index) in dataSource" :key="index">
										<div class="avatar" :style="{ backgroundImage: `url('${CDN_URL}/fr_img/${item.headUrl}')` }">
											<span class="delete-icon" @click="onDeleteColumn(item, index)"></span>
										</div>
									</div>
								</div>
							</template>
						</ReportTable>
						<ReportTable :columns="fixedColumns" :data-source="dataSource" border>
							<template #pointVO="{ value }">
								<Coordinate :data="value" />
							</template>
							<template #leaderIndexResult="{ value }">
								<AbilityAnalysis :data="value" />
								<!-- <TestResult :data="value" /> -->
							</template>
						</ReportTable>
						<ReportTable
							:scoll-container="scrollContainRef"
							:columns="columns.slice(1, columns.length)"
							:data-source="dataSource"
							border
							:row-class="rowClass"
							drag
							@scroll="ontableScroll"
							:fixedRow="1"
						>
							<!-- <template #top>
							<div class="table-top">
								<div class="avatar-box" v-for="(item, index) in dataSource" :key="index">
									<div class="avatar" :style="{ backgroundImage: `url('${CDN_URL}/fr_img/${item.headUrl}')` }">
										<span class="delete-icon" @click="onDeleteColumn(item, index)"></span>
									</div>
								</div>
							</div>
						</template> -->
							<template #leaderIndex="{ value, index }">
								<div class="leader-index">
									<!-- <div class="left-box" v-if="index === 0">
										<div class="year" v-for="(item, _index) in value" :key="_index">
											{{ item.year }}
										</div>
									</div> -->
									<div class="right-box">
										<div :class="`row ${_index % 2 !== 0 ? 'light-row' : ''}`" v-for="(item, _index) in value" :key="_index">
											<div class="index">{{ item.cadreIndex }}</div>
											<div class="rank">同序列排名({{ item.sequencesRank }})</div>
											<!-- | -->
											<!-- <div class="model">金字塔模型{{ primaryModel[item.pyramidIndex] }}</div> -->
										</div>
									</div>
								</div>
								<!-- <template v-if="typeof value === 'number'">
						<span> {{ value }}</span>
					</template>
					<template v-else>
						<CadreIndex />
					</template> -->
							</template>
							<template #pointVO="{ value }">
								<Coordinate :data="value" />
							</template>
							<template #traitTag="{ value }">
								<div class="text-box">
									<div>正面标签：{{ value.positive_feature?.join('，') }}</div>
									<div style="margin-top: 5px">负面标签：{{ value.negative_feature?.join('，') }}</div>
								</div>
							</template>
							<template #annualAssessmentResults="{ value }">
								<div class="text-box">
									{{ value.join('；') }}
								</div>
							</template>
							<template #partTimeEducation="{ value }">
								<div class="text-box">
									{{ value.join('；') }}
								</div>
							</template>
							<template #fullTime="{ value }">
								<div class="text-box">
									{{ value.join('；') }}
								</div>
							</template>
							<template #sketch="{ value }">
								<div class="text-box sketch-box">
									<div class="sketch-item" v-for="(item, index) in value" :key="index">
										{{ item }}
									</div>
								</div>
							</template>
							<template #resume="{ value }">
								<div class="resume-box">
									<div class="resume-row" v-for="(item, index) in value" :key="index">
										<div class="year">{{ item.timeDifference }}</div>
										<div class="remark">{{ item.remark }}</div>
									</div>
								</div>
							</template>
							<template #commendInfo="{ value }">
								<div class="text-box">
									{{ value }}
								</div>
							</template>
							<template #riskItems="{ value }">
								<div class="text-box">
									{{ value.join ? value.join('，') : value }}
								</div>
							</template>
							<template #leaderIndexResult="{ value }">
								<TestResult :data="value" />
							</template>
						</ReportTable>
						<transition name="tip-ani">
							<div class="tips" v-if="tipShow"><span>往左滑动查看更多</span> <img src="@/assets/images/right.png" alt="" srcset="" /></div>
						</transition>
					</div>
				</div>
			</ScrollProvider>
			<!-- <div class="content-box padding-top-0">
				<ReportTable :columns="fixedColumns" :data-source="dataSource" border :row-class="rowClass">
					<template #pointVO="{ value }">
						<Coordinate :data="value" />
					</template>
					<template #leaderIndexResult="{ value }">
						<TestResult :data="value" />
					</template>
				</ReportTable>
			</div> -->
			<div class="loading" v-if="loading">
				<a-spin />
			</div>
		</div>
	</div>
</template>
<script lang="ts">
// import Coordinate from './Coordinate.vue'
import { defineComponent, ref, unref, onErrorCaptured, onMounted } from 'vue'
import ReportTable from '@/components/ReportTable.vue'
import CadreIndex from './components/CadreIndex.vue'
import Coordinate from './components/Coordinate.vue'
import TestResult from './components/TestResult.vue'
import AbilityAnalysis from './components/AbilityAnalysis.vue'
import ScrollProvider from './components/ScrollProvider.vue'
// api
import { getComparisonResult } from '@/apis/comparisonResults'
// import { getBarData } from '@/apis/cadre-portrait/home'
import { useRoute } from 'vue-router'
import { debounce } from '@/utils/utils'
import router from '@/router'
const CDN_URL = import.meta.env.VITE_CDN_URL

const columnsConfig: any = [
	{
		name: '对比维度',
		dataIndex: 'name',
		key: 'name',
		style: {
			backgroundColor: '#F7F8FA',
		},
		align: 'center',
	},
	{
		name: '年龄',
		dataIndex: 'age',
		key: 'age',
		align: 'center',
	},
	{
		name: '性别',
		dataIndex: 'gender',
		key: 'gender',
		align: 'center',
	},
	{
		name: '民族',
		// 民族
		key: 'ethnic',
		dataIndex: 'ethnic',
		align: 'center',
	},
	{
		name: '出生地',
		key: 'birthPlace',
		dataIndex: 'birthPlace',
	},
	{
		name: '政治面貌',
		key: 'politicalType',
		dataIndex: 'politicalType',
		align: 'center',
	},
	{
		name: '入党时间',
		key: 'partyJoinDate',
		dataIndex: 'partyJoinDate',
	},
	{
		name: '毕业院校(全日制)	',
		key: 'partTimeEducation',
		dataIndex: 'partTimeEducation',
	},
	{
		name: '专业(全日制)',
		key: 'fullTime',
		dataIndex: 'fullTime',
	},

	{
		name: '干部类别',
		key: 'category',
		dataIndex: 'category',
	},
	{
		name: '现任职级时间',
		key: 'currentRankTime',
		dataIndex: 'currentRankTime',
	},

	{
		name: '现任职务',
		// 职务
		key: 'position',
		dataIndex: 'position',
		align: 'center',
	},
	{
		name: '初始学历',
		// 学历
		key: 'minEdu',
		dataIndex: 'minEdu',
		align: 'center',
	},
	{
		name: '最高学历',
		// 学历
		key: 'proMaxEdu',
		dataIndex: 'proMaxEdu',
		align: 'center',
	},
	{
		name: '党龄',
		// 学历
		key: 'politicalAge',
		dataIndex: 'politicalAge',
		align: 'center',
	},
	{
		name: '现任职务时间',
		// 职务
		key: 'positionStr',
		dataIndex: 'positionStr',
		align: 'center',
	},
	{
		name: '简历',
		// 指数
		key: 'resume',
		dataIndex: 'resume',
		contentStyle: {
			padding: '0px',
		},
	},

	{
		name: '年度考核(近5年)',
		key: 'annualAssessmentResults',
		dataIndex: 'annualAssessmentResults',
	},

	{
		name: '表扬表彰',
		// 荣誉
		key: 'commendInfo',
		dataIndex: 'commendInfo',
	},
	{
		name: '兴趣爱好',
		key: 'hobby',
		dataIndex: 'hobby',
		align: 'center',
	},
	{
		name: '分管领域',
		key: 'branchingArea',
		dataIndex: 'branchingArea',
		align: 'center',
	},
	{
		name: '擅长领域',
		key: 'expertiseArea',
		dataIndex: 'expertiseArea',
		align: 'center',
	},
	{
		name: '特征标签',
		// 特征标签
		key: 'traitTag',
		dataIndex: 'traitTag',
		align: 'center',
	},
	{
		name: '一言素描',
		key: 'sketch',
		dataIndex: 'sketch',
	},
	{
		name: '躺平式干部（得票）',
		// 得票
		key: 'lieFlatTicket',
		dataIndex: 'lieFlatTicket',
		headerClassName: 'lieFlatTicket-header',
	},
	{
		name: '干部指数',
		// 指数
		key: 'leaderIndex',
		dataIndex: 'leaderIndex',
		align: 'center',
		contentStyle: {
			padding: '0px',
		},
	},
	{
		name: '风险指数',
		// 风险指数
		key: 'riskIndex',
		dataIndex: 'riskIndex',
	},
	{
		name: '风险项',
		// 风险
		key: 'riskItems',
		dataIndex: 'riskItems',
	},

	{
		name: '干部指数排名',
		// 指数
		key: 'cadreIndexRank',
		dataIndex: 'cadreIndexRank',
		align: 'center',
	},

	{
		name: '最高学历',
		key: 'maxEdu',
		dataIndex: 'maxEdu',
		align: 'center',
	},
]
const fixedColumnsConfig = [
	{
		name: '能力-业绩坐标图',
		// 能力-业绩坐标图
		key: 'pointVO',
		dataIndex: 'pointVO',
		merge: true,
		headerClassName: 'coordinate-header',
		fixed: true,
	},
	{
		name: '干部测评结果',
		// 能力-业绩坐标图
		key: 'leaderIndexResult',
		dataIndex: 'leaderIndexResult',
		merge: true,
		headerClassName: 'leaderIndexResult-header',
	},
]
export default defineComponent({
	components: {
		ReportTable,
		CadreIndex,
		Coordinate,
		TestResult,
		ScrollProvider,
		AbilityAnalysis,
	},
	setup() {
		const { query } = useRoute()
		const loading = ref(false)
		const columns = ref<any>([])
		const tipShow = ref<any>(false)
		const dataSource = ref<any>([])
		const fixedColumns = ref<any>([])
		const scrollContainRef = ref<any>([])
		const reportTableRef = ref<any>()

		const { user_id, config_ids }: any = query
		const primaryModel = {
			0: '10%',
			1: '20%',
			2: '30%',
			3: '40%',
			'-': '-',
		}

		const rowClass = (index: number) => {
			return index % 2 === 0 ? 'table-striped' : ''
		}
		// 单数
		const rowClassSingle = (index: number) => {
			return index % 2 !== 0 ? 'table-striped' : ''
		}
		const onDeleteColumn = (data: any, index: number) => {
			if (unref(dataSource).length < 3) return
			dataSource.value.splice(index, 1)
		}
		const _f = debounce((offsetLeft: number) => {
			if (offsetLeft > 0) {
				tipShow.value = false
			} else {
				tipShow.value = true
			}
		}, 100)

		const ontableScroll = (offsetLeft: number) => {
			_f(offsetLeft)
		}

		const onBack = () => {
			router.back()
		}

		const loadData = async () => {
			loading.value = true

			const _config_ids = config_ids.split(',')

			if (_config_ids.includes(8)) {
				_config_ids.push(12, 5)
			}

			const params = {
				user_ids: user_id.split(','),
				config_ids: _config_ids,
			}
			try {
				const { data, code } = await getComparisonResult(params)

				if (code === 0) {
					const _c: any = []
					const _c1: any = []
					data?.map((item: any, index: number) => {
						for (let c of columnsConfig) {
							const col = Object.keys(item).findIndex((key: any) => c.dataIndex === key)
							index === 0 && col !== -1 && _c.push(c)

							if (c.key === 'leaderIndex' && item.leaderIndex) {
								const { cadreIndex, sequencesRank, pyramidIndex } = item.leaderIndex[0] || {}
								const _d = [
									{
										year: '2023',
										cadreIndex: cadreIndex || '-',
										sequencesRank: sequencesRank || '-',
										pyramidIndex: pyramidIndex || '-',
									},
									// {
									// 	year: '2022',
									// 	cadreIndex: '-',
									// 	sequencesRank: '-',
									// 	pyramidIndex: '-',
									// },
									// {
									// 	year: '2021',
									// 	cadreIndex: '-',
									// 	sequencesRank: '-',
									// 	pyramidIndex: '-',
									// },
									// {
									// 	year: '2020',
									// 	cadreIndex: '-',
									// 	sequencesRank: '-',
									// 	pyramidIndex: '-',
									// },
									// {
									// 	year: '2019',
									// 	cadreIndex: '-',
									// 	sequencesRank: '-',
									// 	pyramidIndex: '-',
									// },
								]
								item.leaderIndex = _d
							}
						}
						for (let c of fixedColumnsConfig) {
							const col = Object.keys(item).findIndex((key: any) => c.dataIndex === key)
							index === 0 && col !== -1 && _c1.push(c)

							// if (c.key === 'leaderIndexResult' && item.leaderIndexResult) {
							// 	const leaderIndex = item.leaderIndexResult.other_result?.find((_item: any) => {
							// 		return _item.user_id === item.userId
							// 	})
							// 	leaderIndex && (item.leaderIndexResult = { leaderIndex, xlabel: item.leaderIndexResult.xlabel })
							// }
						}
					})

					columns.value = _c
					dataSource.value = data
					fixedColumns.value = _c1

					tipShow.value = data.length > 2
				}
			} catch (err) {
				console.log(err)
			} finally {
				loading.value = false
			}
		}

		onErrorCaptured(() => {
			loading.value = false
		})
		const showFiexdHeader = ref(false)
		onMounted(() => {
			document.addEventListener('scroll', (e) => {
				const top = reportTableRef.value?.getClientRects()?.[0]?.top

				showFiexdHeader.value = top <= 0
			})
		})
		loadData()
		return {
			showFiexdHeader,
			reportTableRef,
			scrollContainRef,
			loading,
			CDN_URL,
			tipShow,
			columns,
			dataSource,
			fixedColumns,
			primaryModel,
			rowClass,
			rowClassSingle,
			onDeleteColumn,
			onBack,
			ontableScroll,
		}
	},
})
</script>
<style scoped lang="less">
.ignore-min-width {
	width: 1000px;
}
.comparison-results {
	position: relative;
	z-index: 1;
	padding-top: 60px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
	//ignore
	width: 100%;
	background-color: #f6f8fc;
	// overflow: auto;
	// 隐藏滚动条
	&::-webkit-scrollbar {
		display: none;
	}
	.table-container {
		margin: 0 auto;
		width: 1520px;
		// height: 100vh;
		position: sticky;
		top: 0px;
	}
	.item {
		width: 700px;
		height: 700px;
	}
	.text-box {
		text-align: left;
		font-size: 18px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #000000;
		line-height: 21px;
	}
	.sketch-box {
		height: 100%;
		.sketch-item {
			display: flex;
			margin-top: 8px;
			&::before {
				margin-top: 6px;
				content: '';
				display: inline-block;
				flex-shrink: 0;
				width: 8px;
				height: 8px;
				margin-right: 8px;
				background-color: #000000;
				border-radius: 50%;
			}
		}
	}

	.card-header {
		position: relative;
		z-index: 1;
		margin-bottom: 24px;
		line-height: 44px;
		height: 44px;
		width: 100%;
		overflow: hidden;
		.card-bg {
			margin: 0 auto;
			width: 1520px;
			display: flex;
			.back {
				display: inline-block;
				width: 40px;
				height: 40px;
				background: url('@/assets/images/arrow-left.png') no-repeat center / contain;
				cursor: pointer;
			}
			.title {
				margin-left: 16px;
				font-size: 32px;
				font-family: PingFang SC-Bold, PingFang SC;
				font-weight: bold;
				color: #000000;
				line-height: 38px;
			}
		}
	}
	.content-box {
		position: relative;
		padding: 24px 24px 0 24px;
		margin: 0 auto;
		width: 1520px;
		// min-height: 50vh;
		background-color: #ffffff;
		.border-box {
			border: 1px solid #ebebeb;
		}
		.tips {
			position: absolute;
			top: 40vh;
			right: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			min-width: 186px;
			height: 52px;
			background: #404040;
			border-radius: 2px 2px 2px 2px;
			z-index: 99;
			span {
				font-size: 18px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
			}
			img {
				margin-left: 4px;
				width: 14px;
				height: 14px;
			}
		}
		.table-top {
			display: flex;
			padding: 14px 0 14px 140px;
			padding-left: 140px;
			width: fit-content;
			min-width: 100%;
			background-color: #fff;
			.avatar-box {
				flex: 1;
				min-width: 666px;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				.avatar {
					position: relative;
					width: 100px;
					height: 134px;
					background: url('@/assets/images/avatar.png') center / cover;
					.delete-icon {
						position: absolute;
						top: -10px;
						right: -10px;
						width: 16px;
						height: 16px;
						border-radius: 50%;
						background: url('@/assets/images/delete-1.png') center / 100% 100% no-repeat;
						cursor: pointer;
					}
				}
			}
		}
	}
	.leader-index {
		display: flex;
		width: 100%;
		height: 100%;
		.left-box {
			width: 72px;
			.year {
				height: 52px;
				line-height: 52px;
				text-align: center;
				background-color: #ebebeb;
				border-right: 1px solid #ebebeb;
			}
		}
		.right-box {
			flex: 1;
			height: 100%;
			.row {
				display: flex;
				justify-content: center;
				width: 100%;
				height: 52px;
				line-height: 52px;
				.index {
					margin-right: 20px;
				}
				.rank {
					margin-right: 10px;
				}
				.model {
					margin-left: 5px;
				}
			}
			.light-row {
				background-color: #ffffff;
			}
		}
	}
	.resume-box {
		padding: 13px;
		height: 100%;
		.resume-row {
			display: flex;
			.year {
				flex-shrink: 0;
				width: 200px;
			}
			.remark {
				flex: 1;
			}
		}
	}
}

.padding-top-0 {
	padding-top: 0px !important;
}

:deep(.ant-table) {
	background-color: transparent !important;
	.ant-table-cell {
		background-color: transparent !important;
	}
}
:deep(.table-striped) {
	background-color: #f7f8fa;
}

:deep(.report-table-vertical) {
	.leaderIndexResult-header {
		text-align-last: center !important;
	}
	.coordinate-header {
		padding: 19px 19px !important;
		text-align-last: center !important;
	}
	.lieFlatTicket-header {
		padding: 19px 19px !important;
		text-align-last: center !important;
	}
}
.tip-ani-enter-active,
.tip-ani-leave-active {
	transition: all 0.5s ease;
}

.tip-ani-enter-from,
.tip-ani-leave-to {
	opacity: 0;
}
.loading {
	position: absolute;
	inset: 0;
	background-color: rgba(255, 255, 255, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.fixed-box {
	position: fixed !important;
	top: 0px;
	min-height: auto !important;
	z-index: -101;
}
</style>
@/apis/comparisonResults
