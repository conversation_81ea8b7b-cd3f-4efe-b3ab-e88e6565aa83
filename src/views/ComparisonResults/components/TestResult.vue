<template>
	<div class="test-result">
		<a-table :columns="columns" :data-source="datasource" bordered class="table-box" :pagination="false" resizable>
			<template #bodyCell="{ column, record }">
				<template v-if="column.key === 'politics'">
					<span class="block" :style="{ backgroundColor: getBlockColor(record.politics) }"></span>
				</template>
			</template>
		</a-table>
	</div>
</template>
<script lang="ts">
import { defineComponent, toRefs, watchEffect, ref } from 'vue'

const columns = [
	{
		title: '姓名',
		dataIndex: 'name',
		key: 'name',
		width: '10%',
		align: 'center',
	},
	{
		title: '政治',
		width: '10%',
		align: 'center',
		children: [
			{
				title: '政治三力',
				dataIndex: 'politics',
				key: 'politics',
				align: 'center',
			},
		],
	},
	{
		title: '能力',
		width: '50%',
		align: 'center',
		// responsive: ['md'],
		children: [
			{
				title: '学习',
				dataIndex: 'study',
				key: 'study',
				align: 'center',
			},
			{
				title: '决策',
				dataIndex: 'decision',
				key: 'decision',
				align: 'center',
			},
			{
				title: '执行',
				dataIndex: 'execution',
				key: 'execution',
				align: 'center',
			},
			{
				title: '沟通',
				dataIndex: 'communication',
				key: 'companyName',
				align: 'center',
			},
			{
				title: '群众',
				dataIndex: 'masses',
				key: 'masses',
				align: 'center',
			},
		],
	},
	{
		title: '口碑',
		align: 'center',
		children: [
			{
				title: '奉献度',
				dataIndex: 'contribution',
				key: 'contribution',
				align: 'center',
				width: '10%',
			},
			{
				title: '勤奋度',
				dataIndex: 'diligence',
				key: 'diligence',
				align: 'center',
				width: '10%',
			},
			{
				title: '正直度',
				dataIndex: 'integrity',
				key: 'integrity',
				align: 'center',
				width: '10%',
			},
		],
	},
]
const _data = [...Array(3)].map((_, i) => ({
	key: i,
	name: '杨亚蓉',
	age: i + 1,
	politics: 93.54,
	study: 93.54,
	// 决策
	decision: 93.54,
	// 执行力
	execution: 93.54,
	// 沟通
	communication: 93.54,
	// 群众
	masses: 93.54,
	// 奉献度
	contribution: 93.54,
	// 勤奋度
	diligence: 93.54,
	// 正直度
	integrity: 93.54,
}))

const getBlockColor = (value: any) => {
	let _value = Number(value)
	let color = '#FF9900'
	if (_value >= 80) {
		color = '#60CA71'
	}
	// else if (_value < 90 && _value >= 80) {
	// 	color = '#FDD100'
	// }
	else if (_value < 80 && _value >= 70) {
		color = '#FFA300'
	}
	return color
}

export default defineComponent({
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	setup(props) {
		const { data } = toRefs(props)
		const datasource = ref([])
		watchEffect(() => {
			if (!data.value) return
			// const { other_result } = _d
			datasource.value = data.value?.map((item: any, index: any) => {
				const { list } = item
				return {
					name: item.name,
					politics: list[0],
					study: list[1],
					// 决策
					decision: list[2],
					// 执行力
					execution: list[3],
					// 沟通
					communication: list[4],
					// 群众
					masses: list[6],
					// 奉献度
					contribution: list[5],
					// 勤奋度
					diligence: list[7],
					// 正直度
					integrity: list[8],
				}
			})

			// columns.map((item, index) => {
			// 	for (let label in xlabel) {
			// 		if (item.children) {
			// 			if()
			// 		}
			// 	}
			// })
		})
		return {
			_data,
			columns,
			datasource,
			getBlockColor,
		}
	},
})
</script>
<style lang="less" scoped>
.test-result {
	width: 100%;
	padding-right: 5%;
	min-height: 192px;
	overflow: hidden;
	:deep(.ant-table) {
		* {
			border-color: #eeeeee !important;
		}
		.ant-table-thead {
			.ant-table-cell {
				color: #3d3d3d;
				font-size: 18px;
			}
		}
		.ant-table-tbody {
			.ant-table-cell {
				color: #3d3d3d;
				font-size: 16px;
			}
		}
	}
}
.block {
	display: inline-block;
	width: 40px;
	height: 16px;
	background: #60ca71;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
}
</style>
