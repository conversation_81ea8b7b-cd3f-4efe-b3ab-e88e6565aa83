<template>
	<Card title="能力测评对比" header size-type="3">
		<div class="box-content__line">
			<v-chart :option="option" autoresize></v-chart>
		</div>
		<div class="box-content__table">
			<DataTable :data-source="dataSource" :columns="columns" :show-max-min="true" />
		</div>
	</Card>
</template>

<script lang="ts" setup>
import { computed, ref, inject } from 'vue'
import DataTable from '@/components/Table.vue'
import { getAblityResult } from '@/apis/cadre-portrait/home'
import useUser from '@/store/user.ts'
import { decreaseOpacity, convertPxToRem, debounce } from '@/utils/utils'
import { historyPush } from '@/utils/history'

const props = defineProps({
	data: {
		type: Object,
		default: () => ({}),
	},
})

const showMaxMin = ref(false)

const user: any = useUser()

const apiData = ref<any>({
	line: [],
	table: [],
	data: [],
	xlabel: [],
	source: [],
})

const color = [
	'#FF6C47',
	'#6AAEFB',
	'#6ADDE4',
	'#CBADFF',
	'#F06292',
	'#AED581',
	'#66BB6A',
	'#E6EE9C',
	'#BF360C',
	'#9FA8DA',
	'#80CBC4',
	'#9CCC65',
	'#03A9F4',
	'#00BCD4',
	'#A1887F',
	'#CDAD00',
	'#32CD32',
	'#CD5B45',
	'#A020F0',
	'#BDB76B',
]
const refreshOption = ref(0)
// 刷新option 重新渲染echarts
window.addEventListener(
	'resize',
	debounce(() => {
		refreshOption.value++
	}, 100)
)
const option = computed(() => {
	{
		refreshOption.value
	}
	const currentData = props.data?.[0] || {}

	const [...XName] = currentData.xlabel
	// 线
	const [...lineData] = currentData.other_result

	// 弹出政治三力
	XName?.pop()

	const datas: any[] = []

	let _index = 0
	lineData.map(({ name, list }: any) => {
		// 弹出政治三力
		list = list.slice(0, list.length - 1)
		let _color = color[_index++]

		datas.push({
			symbolSize: convertPxToRem(8),
			symbol: 'circle',
			name: name,
			type: 'line',
			yAxisIndex: 1,
			data: list,
			color: _color,
			smooth: false,
			itemStyle: {
				borderWidth: convertPxToRem(4),
				borderColor: _color,
				color: '#fff',
				shadowColor: decreaseOpacity(_color, 0.5),
				shadowBlur: 13,
			},
			lineStyle: {
				color: _color,
				type: 'solid',
				width: convertPxToRem(2),
			},
		})
	})

	const option = {
		// backgroundColor: '#0e2147',

		grid: {
			left: '3%',
			top: datas.length > 11 ? '23%' : '13%',
			bottom: '0%',
			right: '3%',
			containLabel: true,
		},
		legend: {
			show: true,
			top: 0,
			left: 'center',
			// type: 'scroll',
			// data: Line,
			icon: 'circle',
			itemWidth: convertPxToRem(12),
			itemHeight: convertPxToRem(12),
			textStyle: {
				color: '#333333',
				fontSize: convertPxToRem(18),
			},
			itemStyle: {
				borderWidth: convertPxToRem(4),
			},
			itemGap: convertPxToRem(28),
		},
		yAxis: [
			{
				type: 'value',
				position: 'right',
				splitLine: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
			},
			{
				type: 'value',
				position: 'left',
				// scale: true,
				min: ({ min }: any) => {
					return min < 5 ? 0 : min - 5
				},
				max: ({ max }: any) => {
					return max > 95 ? 100 : max + 5
				},
				nameTextStyle: {
					color: '#00FFFF',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',
						color: '#EEEEEE',
					},
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: ['none', 'arrow'],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				axisTick: {
					show: false,
				},
				// splitNumber: 4,
				// interval: 1,
				axisLabel: {
					color: '#666666',
					fontSize: convertPxToRem(16),
					formatter: (value: any) => {
						value = Number(value)
						return value > 100 ? '' : value.toFixed(2)
					},
				},
			},
		],
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					inside: false,
					textStyle: {
						color: '#666666', // x轴颜色
						fontWeight: 'normal',
						fontSize: convertPxToRem(16),
						lineHeight: convertPxToRem(22),
					},
					interval: 0,
				},
				data: XName,
			},
		],
		series: datas,
	}
	return option
})
const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
	const columns: Array<any> = []

	list.forEach((item: any, index: number) => {
		const key = `name`
		if (index === 0) {
			columns[index] = {
				key,
				title,
				width: '10%',
				colClick: (value: any) => {
					const user = apiData.value.source.find((item: any) => item.name === value.name)

					historyPush(`/cadre-portrait/home?user_id=${user.user_id}`)
				},
			}
		}
		columns[index + 1] = {
			key: `${key}${index + 1}`,
			title: item,
			width: 100 / (list.length + 1) + '%',
			align: 'center',
			sort: true,
			showMax: true,
			showMin: true,
		}
		if (item === '政治三力') {
			columns[index + 1].customization = 'CustomBlock'
		}
	})
	return columns
}
const columns = computed(() => {
	return dynamicTableHeader(props.data[0]?.xlabel || [])
})

const createData = (origin_data: [], title: string) => {
	const data: any = {}
	data.name = title
	for (let i = 0; i < origin_data.length; i++) {
		data[`name${i + 1}`] = origin_data[i]
	}

	return data
}
const dataSource = computed(() => {
	const datasource: any = []
	props.data[0]?.other_result?.map((item: any) => {
		datasource.push(createData(item.list, item.name))
	})
	return datasource
})
</script>

<style scoped lang="less">
.box-content__line {
	height: 440px;
}
.box-content__table {
	margin-top: 39px;

	flex: 1;
}
</style>
