<template>
	<!-- <v-chart :option="option" autoresize :loading="false" /> -->
	<div class="cadre-index">
		<v-chart :option="_option" autoresize :loading="false" v-if="powerList.length && orderList.length && villagesList.length" />
	</div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { getBarData } from '@/apis/cadre-portrait/home'
const menu = [
	{
		label: '能力',
		key: 1,
	},
	{
		label: '业绩',
		key: 2,
	},
	{
		label: '口碑',
		key: 3,
	},
]
export default defineComponent({
	name: 'Radar<PERSON>hart',
	props: {
		datasource: {
			type: Array,
			default: () => [],
		},
	},
	setup() {
		const currentIndex = ref(1)
		const xData = ref(['2019', '2020', '2021', '2022', '2023'])
		const _option = ref({})
		const powerList = ref([]) //能力
		const orderList = ref([]) //序列
		const villagesList = ref([]) //乡镇
		const flag = ref(1) // 1正职2副职

		const getBarTypeData = async () => {
			const { user_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			const params: any = {
				user_id,
				flag: currentIndex.value,
			}
			const { data, code } = await getBarData(params)

			if (code == 0) {
				powerList.value = data.user_index
				orderList.value = data.avg_rindex
				villagesList.value = data.avg_yindex
				flag.value = data.flag
			}
			getOption()
		}
		// 生成option方法
		const getOption = () => {
			_option.value = {
				// backgroundColor: '#1a1f76',
				tooltip: {},
				grid: {
					top: '15%',
					left: '1%',
					right: '1%',
					bottom: '8%',
					containLabel: true,
				},
				legend: {
					top: '2%',
					// padding: 10,
					// itemGap: 50,
					itemHeight: 8,
					itemWidth: 18,
					rich: {
						a: {
							verticalAlign: 'middle',
						},
					},
					data: ['干部指数', '指数排名（同序列）'],
					textStyle: {
						color: '#f9f9f9',
						fontSize: 12,
					},
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: true,
						axisLine: {
							//坐标轴轴线相关设置。数学上的x轴
							show: true,
							lineStyle: {
								color: '#222f83',
							},
						},
						axisLabel: {
							//坐标轴刻度标签的相关设置
							textStyle: {
								color: '#3c84cd',
								margin: 15,
							},
						},
						axisTick: {
							show: true,
							alignWithLabel: true,
						},
						// data: ['学习', '决策', '执行', '沟通', '群众',],
						data: xData.value,
					},
				],
				yAxis: [
					{
						type: 'value',
						min: 20,
						// max: 100,
						splitNumber: 5,
						splitLine: {
							show: true,
							lineStyle: {
								color: '#0a3256',
							},
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#222f83',
							},
						},
						axisLabel: {
							margin: 10,
							textStyle: {
								color: '#3c84cd',
							},
							formatter: function (value: any) {
								return value.toFixed(2)
							},
						},
						axisTick: {
							show: false,
						},
					},
				],
				series: [
					{
						name: '指数排名（同序列）',
						type: 'line',
						// smooth: true, //是否平滑曲线显示
						// 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
						symbol: 'emptyCircle',
						symbolSize: 6,
						lineStyle: {
							color: '#FDD100', // 线条颜色
							width: 1,
							type: 'solid',
						},
						label: {
							show: false,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						itemStyle: {
							color: '#FDD100',
						},
						tooltip: {
							show: false,
						},
						data: orderList.value,
					},
					{
						name: '干部指数',
						type: 'bar',
						barWidth: 24,
						tooltip: {
							show: false,
						},
						label: {
							show: false,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						itemStyle: {
							color: '#37aeff',
						},
						data: powerList.value,
					},
				],
			}
		}
		onMounted(() => {
			getBarTypeData()
			// getOption()
		})

		return {
			_option,
			currentIndex,
			powerList,
			orderList,
			villagesList,
		}
	},
})
</script>

<style less="scss" scoped>
.cadre-index {
	width: 100%;
	height: 322px;
}
</style>
