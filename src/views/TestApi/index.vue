<template>
	<div class="test-api">
		<div class="api-debugger">
			<h1>API 调试工具</h1>

			<!-- 输入 URL 和 请求方法 -->
			<div class="input-group">
				<select v-model="method" class="method-select">
					<option value="GET">GET</option>
					<option value="POST">POST</option>
					<option value="PUT">PUT</option>
					<option value="DELETE">DELETE</option>
				</select>
				<input v-model="url" type="text" placeholder="请输入 URL" class="url-input" />
			</div>

			<!-- Header 参数 -->
			<div class="section">
				<h3>Header 参数</h3>
				<div v-for="(header, index) in headers" :key="index" class="header-item">
					<input v-model="header.key" placeholder="参数名" />
					<input v-model="header.value" placeholder="参数值" />
					<button @click="removeHeader(index)">删除</button>
				</div>
				<button @click="addHeader">添加 Header 参数</button>
			</div>

			<!-- Body 参数 -->
			<div v-if="method !== 'GET'" class="section">
				<h3>Body 参数</h3>
				<textarea v-model="body" placeholder="请输入 JSON 格式的请求体"></textarea>
			</div>
			<div v-if="method === 'GET'" class="section">
				<h3>Body 参数</h3>
				<textarea v-model="getbody" placeholder="Get参数 例如：a=1&b=2"></textarea>
			</div>

			<!-- 发送请求按钮 -->
			<button @click="sendRequest" class="send-btn">发送请求</button>

			<!-- 响应展示 -->
			<div class="section">
				<h3>响应结果</h3>
				<div class="result-box"></div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import axios from 'axios'
import { createHeaders } from '@/utils/axios'
import JSONFormatter from 'json-formatter-js'

// URL, 请求方法，Header 和 Body 数据
const url = ref('')
const method = ref('GET')
const headers = ref([{ key: '', value: '' }])
const body = ref('')
const getbody = ref('')

// 响应结果
const response = ref('')

// 添加和删除 Header 参数
const addHeader = () => headers.value.push({ key: '', value: '' })
const removeHeader = (index: number) => headers.value.splice(index, 1)

// 发送请求函数
const sendRequest = async () => {
	const resBoxDom = document.querySelector('.result-box')
	try {
		// 组装 Header
		const headersObject = headers.value.reduce((acc, header) => {
			if (header.key && header.value) acc[header.key] = header.value
			return acc
		}, {})
		const header = {}

		Object.assign(header, createHeaders(), headersObject)
		if (!url.value.startsWith('/')) {
			url.value = '/' + url.value
		}

		// 发送请求
		const res = await axios({
			url: import.meta.env.VITE_APP_BASE_API + url.value,
			method: method.value,
			headers: header,
			data: body.value ? JSON.parse(`${body.value}`) : getbody.value ? `?${getbody.value}` : getbody.value,
		})

		const formatter = new JSONFormatter(res.data)
		if (resBoxDom) {
			resBoxDom.textContent = ''
			resBoxDom.appendChild(formatter.render())
		}
	} catch (error) {
		// 错误处理
		if (resBoxDom) {
			resBoxDom.textContent = error?.message || '请求失败'
		}
	}
}
</script>

<style scoped lang="less">
@common-font-size: 20px;
@common-font-color: #ffffff;
.test-api {
	height: 100%;
	width: 100%;
	overflow: auto;
	background: #1a1a1a;

	h1 {
		margin-top: 60px;

		color: @common-font-color;
	}
	.api-debugger {
		width: 900px;
		margin: auto;
		font-family: Arial, sans-serif;
	}
}

.input-group {
	display: flex;
	margin-bottom: 10px;
}

.method-select,
.url-input {
	height: 50px;
	font-size: @common-font-size;
	padding: 8px;
	margin-right: 5px;
	flex: 1;
	background: @common-font-color;
	color: #000000;
}

.section {
	margin: 10px 0;
	font-size: @common-font-size;
	h3 {
		color: @common-font-color;
	}
	button {
		color: @common-font-color;
	}
	textarea {
		min-height: 300px;
	}
	.result-box {
		padding: 10px 20px;
		color: #000000;
		font-size: @common-font-size;
		background: @common-font-color;
	}
}

.header-item {
	display: flex;
	margin-bottom: 5px;
	input {
		background: #ffffff;
		height: 50px;
		color: #000000;
	}
}

.header-item input {
	padding: 5px;
	margin-right: 5px;
	flex: 1;
}

.send-btn {
	background-color: #007bff;
	color: white;
	padding: 10px;
	border: none;
	cursor: pointer;
	width: 100%;
	margin: 10px 0;
}

.send-btn:hover {
	background-color: #0056b3;
}

textarea {
	width: 100%;
	height: 100px;
	padding: 8px;
	color: @common-font-color;
}

pre {
	background: #f8f8f8;
	padding: 10px;
	white-space: pre-wrap;
	word-wrap: break-word;
}
</style>
