<script lang="ts" setup>
import { reactive, ref, onMounted, unref, onDeactivated, onActivated } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { getPmsLeader } from '@/apis/search'
import { useRouter } from 'vue-router'
import { Base64, getUserInfo, appendParamsToUrl } from '@/utils/utils'
import { getDimensionConfig } from '@/apis/cadre-portrait/home'
import useKeepalive from '@/store/keepalive'

const router = useRouter()
const keepAlive = useKeepalive()

interface FormState {
	name: string
	birthday: [string, string]
	ethic: [string, string]
	gender: [string, string]
	political: [string, string]
	join_time: [string, string]
	cadre_category: [string, string]
	identity: [string, string]
	full_time_education: [string, string]
	on_job_education: [string, string]
	full_time_school: [string, string]
	current_rank: [string, string]
	major: [string, string]
	profession_specialty: string
	technical_position: string
	current_job: string
	current_job_time_gte: string
	current_job_time_lte: string
	current_rank_time_gte: string
	current_rank_time_lte: string
}
const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 18 },
}
const formRef = ref<FormInstance>()
const selectedRowKeys = ref<any[]>([])
const formState = reactive({} as FormState)
const pagination = reactive({
	showSizeChanger: false,
	showQuickJumper: true,
	showTotal: (total: number) => `共${total}条数据`,
} as any)
const visible = ref<boolean>(false)
const searchVisible = ref<boolean>(false)
const drawerVisible = ref<boolean>(false)
const basicMessage = ref<any>([])
const cadreMessage = ref<any>([])
const dataSource = ref<any>([])
const tableLoading = ref<boolean>(false)
const quotaList = ref<any>([])
const columns = [
	{
		title: '姓名',
		dataIndex: 'username',
		width: 80,
		align: 'center',
	},
	{
		title: '现任职务',
		dataIndex: 'current_job',
	},
	{
		title: '出生年月',
		dataIndex: 'birthday',
		width: 100,
	},
	{
		title: '全日制学历',
		dataIndex: 'diploma',
		width: 120,
		align: 'center',
	},
	{
		title: '全日制学历毕业院校及专业',
		dataIndex: 'school',
	},
	{
		title: '干部指数',
		dataIndex: 'cadre_index',
		width: 100,
		align: 'center',
	},
	{
		title: '操作',
		key: 'action',
		align: 'center',
		width: 220,
	},
]

onMounted(() => {
	// onFinish({})
	getDimensionConfig({}).then(({ data, code }: any) => {
		if (code == 0) {
			data.map((item: any) => {
				if (item.name == '基础信息') {
					basicMessage.value = item.children
				} else {
					cadreMessage.value = item.children
				}
			})
		}
	})
})

const resetForm = () => {
	formRef?.value?.resetFields()
	/* setTimeout(() => {
		onFinish({})
	}, 100) */
}

const showModal = () => {
	if (unref(selectedRowKeys).length > 10) {
		return message.error('最多可选择10人')
	}
	visible.value = true
}
const onCloseDrawer = () => {
	drawerVisible.value = false
	selectedRowKeys.value = []
}

const handleOk = () => {
	const config_ids: any[] = []
	basicMessage.value.forEach((element: any) => {
		if (element.is_select) {
			config_ids.push(element.config_id)
		}
	})
	cadreMessage.value.forEach((element: any) => {
		if (element.is_select) {
			config_ids.push(element.config_id)
		}
	})
	goPage({ path: '/comparison-results', user_id: selectedRowKeys.value.join(','), config_ids: config_ids.join(',') })
}

const goPage = ({ path, ...parmas }: any) => {
	const userInfo = JSON.stringify(getUserInfo())

	const _h = Base64.encode(userInfo)

	setPageAlive()

	router.push({
		path,
		query: {
			...parmas,
			_h,
		},
	})
}

// location 跳转
const onLocation = ({ path, ...parmas }: any) => {
	const userInfo = JSON.stringify(getUserInfo())

	// window.location.href = url
}

const setPageAlive = () => {
	keepAlive.push('CadreSearch')
}

const removePageAlive = () => {
	keepAlive.remove('CadreSearch')
}

onActivated(() => {
	removePageAlive()
})
onDeactivated(() => {
	visible.value = false
})

const handlePaginationChange = (params: any) => {
	onFinish({ page: params.current })
}

const onSelectChange = (rowKeys: any[]) => {
	selectedRowKeys.value = rowKeys
}

const onFinish = ({ page = 1 }: any) => {
	drawerVisible.value = true
	const params: any = { ...formState, page }
	if (formState.birthday) {
		params.birthday_start = formState.birthday[0]
		params.birthday_end = formState.birthday[1]
		delete params.birthday
	}
	if (formState.join_time) {
		params.join_time_start = formState.join_time[0]
		params.join_time_end = formState.join_time[1]
		delete params.join_time
	}
	if (formState.join_time) {
		params.join_time_start = formState.join_time[0]
		params.join_time_end = formState.join_time[1]
		delete params.join_time
	}
	const _params: any = {}
	Object.entries(params).forEach(([key, value]) => {
		if (Array.isArray(value)) {
			if (value[0] !== undefined) {
				_params[key] = value
			}
			return false
		}
		_params[key] = value
	})
	tableLoading.value = true
	getPmsLeader(_params).then((res: any) => {
		tableLoading.value = false
		if (res.code === 0) {
			const { content, totalElements }: any = res.data || {}
			dataSource.value = content || []
			pagination.total = totalElements
		}
	})
}

/**
 * @description: 选中项
 * @param {*} record
 * @return {*}
 */
let _selectedRowKeys: any = []
const state = reactive<any>({
	selectedRowKeys: [],
})
const onSelect = (record: any) => {
	const index = _selectedRowKeys.findIndex((item: any) => {
		return item === record.user_id
	})
	if (index == -1 && _selectedRowKeys.length < 10) {
		_selectedRowKeys.push(record.user_id)
		quotaList.value.push(record)
	} else {
		if (index == -1) {
			return void 0
		}
		_selectedRowKeys.splice(index, 1)
		quotaList.value.splice(index, 1)
	}
	selectedRowKeys.value = [..._selectedRowKeys]
}
// 删除选中项中的数据
const deleteName = (index: number) => {
	quotaList.value.splice(index, 1)
	selectedRowKeys.value.splice(index, 1)
	_selectedRowKeys.splice(index, 1)
}

const onExpand = () => {
	searchVisible.value = !searchVisible.value
}
</script>

<template>
	<div class="search-wrap">
		<div class="form-wrap">
			<a-form ref="formRef" v-bind="layout" name="advanced_search" class="search-form" :model="formState" @finish="onFinish">
				<a-card :bordered="false">
					<div class="search-title">基本信息</div>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="姓名" name="name">
								<a-input v-model:value="formState.name" placeholder="请输入" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="性别" name="gender">
								<a-checkbox-group v-model:value="formState.gender">
									<a-checkbox value="1">男性</a-checkbox>
									<a-checkbox value="2">女性</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="民族" name="ethic">
								<a-checkbox-group v-model:value="formState.ethic">
									<a-checkbox value="1">汉族</a-checkbox>
									<a-checkbox value="2">少数民族</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="出生年月" name="birthday">
								<a-range-picker picker="month" v-model:value="formState['birthday']" value-format="YYYY-MM-DD" />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="入党时间" name="join_time">
								<a-range-picker picker="month" v-model:value="formState.join_time" value-format="YYYY-MM-DD" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="政治面貌" name="political">
								<a-checkbox-group v-model:value="formState.political">
									<a-checkbox value="1">中共党员</a-checkbox>
									<a-checkbox value="2">非党员</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="熟悉专业和特长" name="profession_specialty">
								<a-input v-model:value="formState.profession_specialty" placeholder="请输入" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="专业技术职务" name="technical_position">
								<a-input v-model:value="formState.technical_position" placeholder="请输入" />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<div class="tip-link">
							<div class="link" @click="onExpand">
								<svg
									v-if="searchVisible"
									t="1695798910365"
									class="icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="20054"
									width="16"
									height="16"
								>
									<path
										d="M526.78880522 465.76944925c-3.97682157-3.97682157-9.32067555-5.96523235-14.54025387-5.96523236-5.34385398 0-10.5634323 1.98841078-14.54025387 5.96523236L197.20971757 766.14375348c-7.95364315 7.95364315-7.95364315 20.87831325 0 28.83195639 7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0l286.2068774-286.20687739 286.33115307 286.33115307c7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0 7.95364315-7.95364315 7.95364315-20.87831325 0-28.8319564L526.78880522 465.76944925z m0 0"
										p-id="20055"
										fill="#1296db"
									></path>
									<path
										d="M197.0854419 558.35482643c7.95364315 7.95364315 20.87831325 7.95364315 28.83195638 0L512.12427568 272.14794903l286.33115307 286.33115307c7.95364315 7.95364315 20.87831325 7.95364315 28.83195637 0 7.95364315-7.95364315 7.95364315-20.87831325 0-28.83195638L526.78880522 229.02429013c-3.97682157-3.97682157-9.32067555-5.96523235-14.54025387-5.96523235-5.34385398 0-10.5634323 1.98841078-14.54025387 5.96523235L197.20971757 529.52287005c-7.95364315 7.82936747-7.95364315 20.87831325-0.12427567 28.83195638z m0 0"
										p-id="20056"
										fill="#1296db"
									></path>
								</svg>
								<svg
									v-else
									t="1695796990911"
									class="icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="18534"
									width="16"
									height="16"
								>
									<path
										d="M497.3568 558.592c3.9936 3.9936 9.3184 6.0416 14.6432 5.9392 5.3248 0 10.6496-1.9456 14.6432-5.9392l302.1824-302.1824c7.9872-7.9872 7.9872-20.992 0-28.9792-7.9872-7.9872-20.992-7.9872-28.9792 0L512 515.2768 224.0512 227.328c-7.9872-7.9872-20.992-7.9872-28.9792 0-7.9872 7.9872-7.9872 20.992 0 28.9792l302.2848 302.2848z"
										p-id="18535"
										fill="#1296db"
									></path>
									<path
										d="M828.928 465.408c-7.9872-7.9872-20.992-7.9872-28.9792 0L512 753.3568 224.0512 465.408c-7.9872-7.9872-20.992-7.9872-28.9792 0-7.9872 7.9872-7.9872 20.992 0 28.9792L497.3568 796.672c3.9936 3.9936 9.3184 6.0416 14.6432 5.9392 5.3248 0 10.6496-1.9456 14.6432-5.9392l302.1824-302.1824c7.9872-7.9872 7.9872-21.0944 0.1024-29.0816z"
										p-id="18536"
										fill="#1296db"
									></path>
								</svg>
								<span class="text"> 高级查询 </span>
							</div>
						</div>
					</a-row>
				</a-card>
				<Transition name="moresearch">
					<div class="more-seach" v-show="searchVisible">
						<a-card :bordered="false">
							<div class="search-title">职务信息</div>
							<a-row :gutter="24">
								<a-col :span="24">
									<a-form-item label="干部类别" name="cadre_category" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
										<a-checkbox-group v-model:value="formState.cadre_category" class="cadre_category">
											<a-checkbox value="1">市管领导</a-checkbox>
											<a-checkbox value="2">区管正职</a-checkbox>
											<a-checkbox value="3">区管副职</a-checkbox>
											<a-checkbox value="200108">乡镇正职</a-checkbox>
											<a-checkbox value="200101">乡镇副职</a-checkbox>
											<a-checkbox value="200102">部门正职</a-checkbox>
											<a-checkbox value="200103">部门副职</a-checkbox>
											<a-checkbox value="200104">企业正职</a-checkbox>
											<a-checkbox value="200105">企业副职</a-checkbox>
											<a-checkbox value="200106">街道正职</a-checkbox>
											<a-checkbox value="200107">街道副职</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="现任职务" name="current_job">
										<a-input v-model:value="formState.current_job" placeholder="请输入" />
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="24">
									<a-form-item label="任现职务时间" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
										<a-row :gutter="24" class="item-h">
											<span>大于&nbsp;</span>
											<a-form-item name="current_job_time_gte">
												<a-input-number v-model:value="formState.current_job_time_gte" />
											</a-form-item>
											<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
											<a-form-item name="current_job_time_lte">
												<a-input-number v-model:value="formState.current_job_time_lte" />
											</a-form-item>
											<span>&nbsp;年</span>
										</a-row>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="24">
									<a-form-item label="干部职级" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }" name="current_rank">
										<a-checkbox-group v-model:value="formState.current_rank">
											<a-checkbox value="1">正处</a-checkbox>
											<a-checkbox value="200301">副处</a-checkbox>
											<a-checkbox value="200302">保留副处</a-checkbox>
											<a-checkbox value="20030">正科</a-checkbox>
											<a-checkbox value="5">保留正科</a-checkbox>
											<a-checkbox value="6">副科</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="24">
									<a-form-item label="现职级任职时间" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
										<a-row :gutter="24" class="item-h">
											<span>大于&nbsp;</span>
											<a-form-item name="current_rank_time_gte">
												<a-input-number v-model:value="formState.current_rank_time_gte" />
											</a-form-item>
											<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
											<a-form-item name="current_rank_time_lte">
												<a-input-number v-model:value="formState.current_rank_time_lte" />
											</a-form-item>
											<span>&nbsp;年</span>
										</a-row>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="24">
									<a-form-item label="干部身份" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }" name="identity">
										<a-checkbox-group v-model:value="formState.identity">
											<a-checkbox value="200201">行政</a-checkbox>
											<a-checkbox value="200202">事业</a-checkbox>
											<a-checkbox value="200203">参公</a-checkbox>
											<a-checkbox value="200204">国企</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
							</a-row>
						</a-card>
						<a-card :bordered="false">
							<div class="search-title">教育信息</div>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="初始学历" name="full_time_education">
										<a-checkbox-group v-model:value="formState.full_time_education">
											<a-checkbox value="1">研究生</a-checkbox>
											<a-checkbox value="2">大学本科</a-checkbox>
											<a-checkbox value="3">大学专科</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="最高学历" name="on_job_education">
										<a-checkbox-group v-model:value="formState.on_job_education">
											<a-checkbox value="1">研究生</a-checkbox>
											<a-checkbox value="2">大学本科</a-checkbox>
											<a-checkbox value="4">大学专科</a-checkbox>
										</a-checkbox-group>
									</a-form-item>
								</a-col>
							</a-row>
							<a-row :gutter="24">
								<a-col :span="12">
									<a-form-item label="全日制院校" name="full_time_school">
										<a-input v-model:value="formState.full_time_school" placeholder="请输入" />
									</a-form-item>
								</a-col>
								<a-col :span="12">
									<a-form-item label="专业" name="major">
										<a-input v-model:value="formState.major" placeholder="请输入" />
									</a-form-item>
								</a-col>
							</a-row>
						</a-card>
					</div>
				</Transition>
				<a-card :bordered="false">
					<a-row>
						<a-col :span="24" style="text-align: center" class="btn-wrap">
							<a-button style="margin-right: 60px" value="large" @click="resetForm">重置</a-button>
							<a-button type="primary" value="large" html-type="submit">查询</a-button>
						</a-col>
					</a-row>
				</a-card>
			</a-form>
			<a-card :bordered="false">
				<a-row :gutter="24">
					<a-col :span="20">
						<a-table
							rowKey="user_id"
							:loading="tableLoading"
							:columns="columns"
							:data-source="dataSource"
							:pagination="pagination"
							@change="handlePaginationChange"
							:scroll="{ y: 470 }"
							:row-selection="{ selectedRowKeys: selectedRowKeys, onSelect: onSelect, columnTitle: ' ', hideDefaultSelections: true }"
						>
							<template #bodyCell="{ column, record }">
								<template v-if="column.key === 'action'">
									<a @click="goPage({ path: '/cadre-form', user_id: record.user_id })">干部任免审批表</a>
									<a-divider type="vertical" />
									<a @click="goPage({ path: '/cadre-portrait/home', user_id: record.user_id })">干部画像</a>
								</template>
							</template>
						</a-table>
					</a-col>
					<a-col :span="4">
						<div class="already-have">
							<div class="have-nubmer">
								<span class="label">已选: </span>
								<span class="select-number">{{ quotaList.length }}/10</span>
							</div>
							<div class="have-name">
								<div class="inner-box">
									<div v-for="(item, index) in quotaList" :key="index" class="select-box">
										<span>{{ item.username }}</span
										><span class="have-delete" @click.stop="deleteName(index)"></span>
									</div>
								</div>
							</div>
							<div class="button-box">
								<a-button type="primary" class="my-button" :disabled="selectedRowKeys.length < 2" @click="showModal">对比分析</a-button>
							</div>
						</div>
					</a-col>
				</a-row>
			</a-card>
			<!-- <a-drawer
				title=""
				placement="right"
				width="100%"
				:visible="drawerVisible"
				:get-container="false"
				:style="{ position: 'absolute' }"
				@close="onCloseDrawer"
			>
				<template #extra>
					<div style="text-align: right">
						<a-button :disabled="selectedRowKeys.length < 2" type="primary" @click="showModal">对比分析</a-button>
					</div>
				</template>
				<a-table
					rowKey="user_id"
					:loading="tableLoading"
					:columns="columns"
					:data-source="dataSource"
					:pagination="pagination"
					@change="handlePaginationChange"
					:scroll="{ y: 470 }"
					:row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
				>
					<template #bodyCell="{ column, record }">
						<template v-if="column.key === 'action'">
							<a @click="onLocation({ path: '/cadre-form', user_id: record.user_id })">干部任免审批表</a>
							<a-divider type="vertical" />
							<a @click="onLocation({ path: '/cadre-portrait/home', user_id: record.user_id })">干部画像</a>
						</template>
					</template>
				</a-table>
			</a-drawer> -->
		</div>
		<a-modal v-model:visible="visible" width="70%" title="分析维度" @ok="handleOk" class="search-modal">
			<div class="veidoo">
				<div class="veidoo-item">
					<div class="title">基础信息</div>
					<div class="data">
						<div class="check-item" v-for="(item, index) in basicMessage" :key="index">
							<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
						</div>
					</div>
				</div>
				<div class="veidoo-item">
					<div class="title">干部画像</div>
					<div class="data">
						<div class="check-item" v-for="(item, index) in cadreMessage" :key="index">
							<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
						</div>
					</div>
				</div>
			</div>
		</a-modal>
	</div>
</template>

<style lang="less" scoped>
.search-wrap {
	padding-top: 200px;
	display: flex;
	justify-content: center;
	width: 100%;
	min-height: 100%;
	overflow-y: auto;
	background: #f6f9fc url(./back.png) no-repeat top center/contain;
	.form-wrap {
		position: relative;
		width: 90%;
	}
	.search-form {
		user-select: none;
		::v-deep(.ant-card-body) {
			padding: 24px 24px 5px !important;
		}
		width: 100%;
		.ant-card {
			margin-bottom: 16px;
			.ant-row {
				margin-bottom: 10px;
			}
			.item-h {
				align-items: center;
				margin: 0 !important;
				.ant-form-item {
					margin-bottom: 0;
				}
			}
		}
		.ant-checkbox-group {
			.ant-checkbox-wrapper {
				margin-left: 0;
				margin-right: 10px;
			}
		}
		.cadre_category {
			::v-deep(.ant-checkbox-wrapper) {
				// margin-bottom: 10px;
			}
		}
	}
	.search-title {
		font-size: 20px;
		font-weight: bold;
		display: flex;
		align-items: center;
		&::before {
			margin-right: 8px;
			content: '';
			display: inline-block;
			width: 8px;
			height: 24px;
			background: url(../image/left-header-icon.png) center / cover no-repeat;
		}
	}

	.btn-wrap {
		margin-bottom: 10px;
		.ant-btn {
			height: 48px;
			padding: 0 64px;
			font-size: 16px;
		}
	}

	.already-have {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;

		.have-nubmer {
			display: flex;
			align-items: center;
			padding: 28px 20px;
			width: 100%;
			height: 76px;
			background: #f7f8fa;
			border-radius: 4px 4px 4px 4px;
			opacity: 1;
			font-size: 14px;
			font-weight: 400;
			color: #00d2ff;
			.label {
				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #222222;
				line-height: 24px;
			}
			.select-number {
				margin-left: 10px;
				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #2462ff;
			}
		}

		.have-name {
			padding: 10px;
			// height: 470px;
			flex: 1;
			background: #f7f8fa;
			border-radius: 4px 4px 4px 4px;
			opacity: 1;
			.inner-box {
				padding: 10px;
				height: 100%;
				background-color: #ffffff;
				.select-box {
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
			div {
				margin-bottom: 13px;
			}

			span {
				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #222222;
			}

			.have-delete {
				width: 20px;
				height: 20px;
				display: inline-block;
				background-image: url('@/assets/images/delete.png');
				background-size: 100% 100%;
				margin-left: 15px;
				vertical-align: middle;
				cursor: pointer;
			}
		}
		.button-box {
			width: 100%;
			.my-button {
				width: 100%;
			}
		}
	}
	.tip-link {
		width: 100%;
		display: flex;
		justify-content: center;
		.link {
			display: flex;
			align-items: center;
			cursor: pointer;
			.text {
				margin-left: 10px;
				color: #1296db;
			}
		}
	}
}
.search-modal {
	margin-top: 24px;
	padding: 24px;
	width: 100%;
	background: #ffffff;
	.veidoo {
		margin-top: 10px;
		width: 100%;
		.top-content {
			display: flex;
			margin-top: 49px;
			margin-bottom: 31px;

			.label {
				display: flex;
				align-items: center;
				font-size: 18px;
				font-weight: bold;
				color: #00fff6;
				line-height: 18px;
				text-shadow: 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2),
					0 0 4px rgba(35, 219, 252, 0.2);
				background: linear-gradient(0deg, #3bdeff 6.1279296875%, #d1fbff 55.4443359375%, #ddf9ff 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;

				&::after {
					margin-left: 7px;
					content: '';
					display: inline-block;
					width: 24px;
					height: 16px;
					background: url('@/assets/images/label-icon.png') no-repeat center / cover;
				}
			}
		}
		.top-content-two {
			margin-bottom: 22px;
		}
		.veidoo-item {
			display: flex;
			border: 1px solid #ebebeb;
			.title {
				display: flex;
				align-items: center;
				justify-content: center;

				width: 100px;
				height: 192px;
				background: #f3f3f3;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;

				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #222222;
				line-height: 24px;
			}

			.data {
				flex: 1;
				padding: 28px 84px;

				display: flex;
				flex-wrap: wrap;
				.check-item {
					width: 25%;
				}
			}

			.two-title {
				display: flex;
				width: 169px;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 68px 0;
			}

			div {
				font-size: 14px;
				font-family: Source Han Sans CN;
				font-weight: 500;
				color: #00d2ff;
			}
		}
	}
}
/* 下面我们会解释这些 class 是做什么的 */
.moresearch-enter-active {
	transition: opacity 0.5s ease;
}

.moresearch-enter-from {
	opacity: 0;
}
</style>
