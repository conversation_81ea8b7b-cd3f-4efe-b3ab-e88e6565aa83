<template>
	<div class="new-home">
		<div class="menu-box">
			<div class="menu" v-for="(item, index) in menuList" :key="index">
				<div class="inner">
					<img class="icon" :src="item.img" />
					<div class="right">
						<div class="label">{{ item.label }}</div>
						<div
							class="router-text"
							@click="
								() => {
									item.click ? item.click() : onRouterLink(item.path)
								}
							"
						>
							{{ item.routerText }}
						</div>
					</div>
				</div>
				<div class="tips" v-if="item.tips">当前方案：{{ item.tips }}</div>
			</div>
		</div>
		<div class="tab-box">
			<div class="tab" :class="{ active: activeKey === item.key }" v-for="item in tabs" @click="onChangeTab(item.key)" :key="item.key">
				{{ item.title }}
			</div>
		</div>
		<div class="org-box">
			<div class="org-list">
				<div class="org-item" v-for="item in state.list" :key="item.orgId">
					<div class="org-name-box">
						<div class="org-name" :class="{ active: currentOrgId === item.orgId }" @click="onSelect(item)">{{ item.orgName }}</div>
					</div>
					<div class="warn-box">
						<div class="warn" :class="{ active: currentOrgId === item.orgId }">
							<div class="warn-1 flex-col-center">
								班子结构
								<span
									class="warn-icon"
									:style="{
										backgroundColor: svgColor[item.light],
									}"
								></span>
							</div>
							<div class="warn-2">
								职数配置：<template v-if="item.vacancy"
									>缺配
									<span
										class="num"
										:style="{
											color: currentOrgId ? '#FF9900' : '',
										}"
									>
										{{ item.vacancy }} </span
									>人</template
								>
								<template v-else>正常</template>
							</div>
							<div class="warn-3" @click="onSelect(item)" v-if="!currentOrgId">立即查看>></div>
						</div>
					</div>
				</div>
			</div>
			<div class="org-container" v-if="currentOrgId">
				<div class="title-header">
					<div class="title">班子队伍</div>
					<div class="btn-box">
						<a-button type="primary" @click="onUserSelect" v-if="type != 3">前往调整</a-button>
					</div>
				</div>
				<div class="bottom-box">
					<div class="flex">
						<div class="group">
							应配：{{ userInfoList.deserve }}人，缺配：<span class="yellow-text">{{ userInfoList.vacancy }}</span
							>人
						</div>
						<div class="group">班子结构预警项：<span class="yellow-text"> 年龄、学历、专业</span></div>
					</div>
					<span class="link" @click="onStuctureModal">班子结构分析 &gt;</span>
				</div>
				<div class="lf-user-box">
					<div :class="`user-item`" v-for="(item, index) in userInfoList.users" :key="item.userId" @click="onUserSelect(item)">
						<img :src="`${item.headUrl ? `${CDN_URL}/fr_img/${item.headUrl}` : defaultAvatar}`" class="avatar" />
						<div class="user-name">{{ item.userName || '---' }}</div>
						<div class="user-position">{{ item.jobName }}</div>
					</div>
					<!-- <div :class="`user-item ${pageStatus.newAdd ? 'active-select' : ''}`" @click="onAdd" v-if="userInfoList.vacancy">
							<div class="img-box">
								<img src="../images/vacancy.png" class="vacancy" />
							</div>
							<div class="user-name">---</div>
							<div class="user-position">---</div>
						</div> -->
				</div>
			</div>
		</div>
		<a-button @click="onSyncJob">数据同步</a-button>
		<div class="loading-box" v-if="loading">
			<a-spin :indicator="indicator" tips="数据同步中..." />
		</div>
	</div>
</template>
<script lang="ts">
// 页面保活
const page_cache: any = {
	isClear: false,
	state: {
		currentOrg: undefined,
		currentOrgId: undefined,
		activeKey: undefined,
	},
}
</script>
<script lang="ts" setup>
import { ref, computed, reactive, inject, h, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import quePeiPng from '../images/quepei.png'
import zhixuanPng from '../images/zhixuan.png'
import yujinPng from '../images/yujin.png'
import daipeiPng from '../images/daipei.png'
import lishiPng from '../images/lishi.png'
import { MOCK_STATUS } from '@/types/sandtable-exercise'
import { CDN_URL } from '@/config/env'
import { debounce, getUserInfoItem } from '@/utils/utils'
import { structTableModal } from '../components/Drawer'
import defaultAvatar from '@/assets/images/avatar-width.png'
import { getHomePage, getOrgTeam, deleteUserMock, syncJob, createMock, resetMock, saveMock, getHomePageCount } from '@/apis/sand-table-exercise'
import { useRouter, useRoute } from 'vue-router'
import { LoadingOutlined } from '@ant-design/icons-vue'
import useMock from '@/store/sandbox'
import { savePlanModal } from '../components/Drawer'

type OrgInfo = {
	orgId?: number
	orgName?: string
	deserve: number
	vacancy: number
	users: UserInfo[]
}

type UserInfo = {
	userId: number
	userName: string
	headUrl?: string
	pmsJobId: number
	jobName: string
	currentJob?: string
	currentJobTime?: string
	birthday?: string
	age?: number
	initDegree?: string
	initSchool?: string
	specialty?: string
	cadreIndex?: string
	cadreIndexSort?: string
	userType?: number // 1 - 市管领导，2 - 区管领导，3 - 中层干部
	index?: number
}
interface ListHomePageVo {
	mockId?: number
	mockName?: string
	list: Array<OrgData>
}
// 亮灯 0=绿色 1=黄色 2=橙色 3=红色
type OrgLightColor = 0 | 1 | 2 | 3

interface OrgData {
	orgId: number
	orgName?: string
	seq: number
	light: OrgLightColor
	vacancy: number
}
// 每次进入页面重置状态
page_cache.isClear = true
// 路由
const route = useRoute()
const router = useRouter()
// 当type === 1 表示暂存状态 不支持操作： 另存
let { mock_id, mock_name, type } = route.query as any
const _uid = getUserInfoItem('_uid')
// pinia 状态管理
const mock = useMock()
// 页面状态
const page_type = ref(type)

const updateMockId: any = inject('updateMockId')

mock_id === 'null' && (mock_id = undefined)
// 顶级mockId保存
mock_id && updateMockId(mock_id)
// 保存mockId
mock_id && mock.setMockId(mock_id)
// 保存方案名
mock_name && mock.setMockName(mock_name)
// 方案id
const mockId = ref(mock_id || mock.mockId)
// 选中的orgId
const currentOrgId = ref(page_cache.state.currentOrgId)
const currentOrg = ref(page_cache.state.currentOrg)
// 数量相关
const homepageCount = ref({
	vacancy: 0,
	warning: 0,
	toSubmit: 0,
	mockCount: 0,
	mockName: 0,
})
// 每个序列下对应组织数据
// 生成随机整数函数
function getRandomInt(min, max) {
	return Math.floor(Math.random() * (max - min + 1)) + min
}

// 生成假数据函数
function generateFakeOrgData() {
	return {
		orgId: getRandomInt(1, 1000),
		orgName: Math.random() < 0.5 ? undefined : 'Organization ' + getRandomInt(1, 100),
		seq: getRandomInt(1, 100),
		light: getRandomInt(0, 3), // 0, 1, 2, 3 中的随机整数
		vacancy: getRandomInt(0, 10),
	}
}

const data = () => {
	const _ = []
	for (let i = 0; i < 10; i++) {
		_.push(generateFakeOrgData())
	}
	return _
}
const state = reactive<ListHomePageVo>({
	list: [],
} as any)

const menuList = computed(() =>
	[
		{
			label: homepageCount.value.vacancy,
			img: quePeiPng,
			routerText: '缺配职数',
			click() {
				page_cache.isClear = false

				router.push({
					path: '/sand-table-exercise/job-vacancy',
					query: {
						mock_id: mockId.value,
						type: activeKey.value,
					},
				})
			},
		},
		{
			label: '一键智选',
			img: zhixuanPng,
			routerText: '人找岗',
			path: '/sand-table-exercise/smart-selection',
		},
		{
			label: homepageCount.value.warning,
			img: yujinPng,
			routerText: '岗位预警',
			path: '/sand-table-exercise/early-warning',
		},
		{
			label: homepageCount.value.toSubmit,
			img: daipeiPng,
			routerText: '待配名单',
			path: '/sand-table-exercise/wait-matched',
		},
		{
			label: homepageCount.value.mockCount,
			img: lishiPng,
			routerText: '历史方案',
			tips: mock.mockName || homepageCount.value.mockName,
			path: '/sand-table-exercise/historical-plan',
		},
	].filter((item) => {
		// 方案为生效状态 只保留一个方案分析
		if (type == MOCK_STATUS.EFFECTIVE) {
			return item.routerText === '历史方案'
		} else {
			return true
		}
	})
)

const tabs = [
	{
		title: '乡镇（街道）',
		key: '1',
	},
	{
		title: '保障部门',
		key: '4',
	},
	{
		title: '经济部门',
		key: '5',
	},
	{
		title: '执纪执法部门',
		key: '6',
	},
	{
		title: '中央市级在丰单位及重点企业',
		key: '7',
	},
	{
		title: '区属国有企业',
		key: '8',
	},
	{
		title: '学校',
		key: '9',
	},
	{
		title: '医院',
		key: '10',
	},
]
// 打印生成的假数据
const svgColor = ['#60CA71', '#FFA300', '#FFA300', '#FF0000']
// tab 切换
const activeKey = ref(page_cache.state.activeKey || '1')
// loading
const loading = ref(false)

const indicator = h(LoadingOutlined, {
	style: {
		fontSize: '24px',
	},
	spin: true,
})
const userInfoList = ref<OrgInfo>({
	orgId: undefined,
	orgName: `机构`,
	deserve: 0, //应配
	vacancy: 0, // 缺配
	users: [],
})

const onChangeTab = (key: any) => {
	activeKey.value = key

	currentOrgId.value = undefined

	debounceLoad(activeKey.value)

	userInfoList.value.users = []
}
// 选中状态切换
const onSelect = (org: any) => {
	currentOrgId.value = org.orgId
	currentOrg.value = org
	loadOrgTeam(org.orgId, mockId.value)
}
//
const onLookFor = (user?: any) => {
	const { orgId, orgName } = currentOrg.value

	const query: any = {
		org_id: orgId,
		org_name: orgName,
		mock_id: mockId.value,
	}

	if (user) {
		const { userId } = user
		query['user_id'] = userId
	}

	router.push({
		path: '/sand-table-exercise/look-for',
		query,
	})
}

/**
 * @description: 缺配跳转
 * @param {*} org
 * @param {*} job
 * @return {*}
 */
const onVacancy = (org: any) => {
	const { orgId, orgName } = currentOrg.value

	const { pmsJobId, jobName } = org

	const query: any = {
		org_id: orgId,
		org_name: orgName,
		mock_id: mockId.value,
		job_id: pmsJobId,
		job_name: jobName,
	}

	router.push({
		path: '/sand-table-exercise/look-for',
		query,
	})
}
const onUserSelect = (user: any) => {
	if (type == 3) {
		return
	}

	page_cache.isClear = false

	if (!user.userId) {
		onVacancy(user)
	} else {
		onLookFor(user)
	}
}

// 路由跳转
const onRouterLink = (path: any) => {
	page_cache.isClear = false

	if (path) {
		router.push({
			path,
			query: {
				mock_id: mockId.value,
			},
		})
	}
}

// 同步
const onSyncJob = async () => {
	loading.value = true

	const res: any = await syncJob()
	if (res.code === 0) {
		message.success(res.message)

		loadData('1')
		loadHomeCount(mockId.value)
	} else {
		message.error(res.message)
	}

	loading.value = false
}

const onStuctureModal = () => {
	structTableModal.openModal({
		mockId: mockId.value,
		orgId: currentOrgId.value,
	})
}

const debounceLoad = debounce((key: any) => {
	loadData(key)
}, 100)

const loadData = async (key: any) => {
	const _uid = getUserInfoItem('_uid')

	const res: any = await getHomePage(key || activeKey.value, mockId.value, _uid)

	if (res.code === 0) {
		// orgjobuservos: 职务列表 orgJobVacancyVos: 缺配职数
		let { list, mockName, mockId: _mockId } = res.data

		console.log('🚀 ~ loadData ~ _mockId:', _mockId, typeof _mockId)
		_mockId === 'null' && (_mockId = '')
		_mockId === null && (_mockId = '')

		state.list = list

		state.mockName = mockName

		// 顶级mockId保存
		updateMockId(_mockId)
		// 保存mockId
		mock.setMockId(_mockId)

		mockId.value = _mockId || ''

		!mockName && list?.length && onCreate()

		// 加载主页数量
		loadHomeCount(_mockId)
	}
}
loadData(activeKey.value)

const onCreate = () => {
	savePlanModal.openModal({
		name: '',
		submit: async (name: string, close: () => void) => {
			// onSave(name)
			mock.setMockName(name)

			// 参数 1 表示保存 2 为暂存
			const res: any = await createMock(name, _uid)

			if (res.code === 0) {
				message.success('提交成功')

				const _mockId = res.data

				mock.setMockId(_mockId)

				mockId.value = _mockId

				close()

				loadHomeCount(_mockId)
			} else {
				message.error(res.message)
			}
		},
	})
}
const loadHomeCount = async (mockId: any) => {
	const adminId = getUserInfoItem('_uid')
	const res: any = await getHomePageCount(mockId, adminId)
	if (res.code === 0) {
		homepageCount.value = res.data
	}
}

// 数据初始化
const loadOrgTeam = async (org_id: any, mock_id: any) => {
	const res: any = await getOrgTeam(org_id, mock_id)
	if (res.code === 0) {
		res.data.users = res.data.users.map((item: any, index: number) => {
			const user = {
				...item,
				index,
			}

			return user
		})

		userInfoList.value = res.data
	}
}

if (currentOrgId.value) {
	loadOrgTeam(currentOrgId.value, mockId.value)
}
onUnmounted(() => {
	if (page_cache.isClear) {
		page_cache.state = {}
	} else {
		page_cache.state = {
			activeKey: activeKey.value,
			currentOrg: currentOrg.value,
			currentOrgId: currentOrgId.value,
		}
	}
})
</script>

<style lang="less" scoped>
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.white-bg {
	background: #ffffff;
}
.flex {
	display: flex;
}
.new-home {
	position: relative;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow: hidden;
	.menu-box {
		padding: 12px;
		width: 100%;
		display: flex;
		gap: 12px;
		background: #fff;
		.menu {
			max-width: 369px;
			height: 143px;
			overflow: hidden;
			width: 100%;
			&:nth-child(2n) .inner {
				background-image: url('../images/menu-box-2.png');
			}
			.inner {
				padding: 0px 30px;
				display: flex;
				align-items: center;
				flex: 1;
				height: 100%;
				background: url('../images/menu-box-1.png') no-repeat center / 100%;

				.icon {
					width: 51px;
					height: 51px;
				}
				.right {
					margin-left: 18px;
					.label {
						font-size: 35px;
						line-height: 1.4;
						font-family: DIN, DIN;
						font-weight: bold;
						color: #000000;
					}
					.router-text {
						display: flex;
						align-items: center;
						font-size: 21px;
						line-height: 21px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.85);
						cursor: pointer;
						&::after {
							content: '';
							display: inline-block;
							width: 18px;
							height: 18px;
							background: url('../images/right.png') no-repeat center / 100%;
						}
					}
				}
			}
			.tips {
				padding: 0px 30px;
				margin-top: -27px;
				font-size: 19px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.65);
				line-height: 22px;
			}
		}
	}
	.tab-box {
		margin-top: 18px;
		padding: 30px 18px;
		display: flex;
		gap: 18px;
		background-color: #fff;
		.tab {
			padding: 12px 38px;
			font-size: 24px;
			line-height: 24px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			background: #f8f8f8;
			border-radius: 6px 6px 6px 6px;
			cursor: pointer;
		}
		.active {
			color: #ffffff;
			background: #008eff;
		}
	}
	.org-box {
		width: 100%;
		flex: 1;
		margin-top: 12px;
		display: flex;
		overflow: hidden;
		.active {
			background: #edf7ff !important;
		}
		.org-list {
			padding: 15px 9px;
			flex: 1;
			height: 100%;
			overflow: auto;
			background-color: #fff;
			.org-item {
				flex: 1;
				display: flex;
				border-bottom: 1px solid rgba(0, 0, 0, 0.1);
				.org-name-box {
					padding-right: 9px;
					background-color: #fff;
					.org-name {
						display: flex;
						align-items: center;
						width: 358px;
						padding: 21px 20px;
						background: #fff;

						font-size: 24px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.85);
						&::before {
							content: '';
							margin-right: 5px;
							display: inline-block;
							width: 27px;
							height: 27px;
							background: url(../images/org-icon.png) center / cover no-repeat;
						}
					}
				}
				.warn-box {
					flex: 1;
					padding-left: 9px;
					.warn {
						height: 100%;
						width: 100%;
						position: relative;
						padding-left: 75px;
						display: flex;
						align-items: center;
						background: #f8f9fa;
						font-size: 24px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						line-height: 28px;
						.warn-icon {
							margin-left: 9px;
							display: inline-block;
							width: 24px;
							height: 24px;
							background: #ffa300;
							border-radius: 50%;
						}
						.warn-1 {
							font-size: 24px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: rgba(0, 0, 0, 0.9);
							line-height: 28px;
						}
						.warn-2 {
							margin-left: 66px;
							font-size: 24px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: rgba(0, 0, 0, 0.9);
							line-height: 28px;
							.num {
								font-size: 20px;
								color: #008eff;
							}
						}
						.warn-3 {
							position: absolute;
							right: 66px;
							top: 50%;
							transform: translate(0, -50%);
							font-size: 24px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #008eff;
							cursor: pointer;
						}
					}
				}
			}
		}
		.org-container {
			display: flex;
			flex-direction: column;
			width: 1035px;
			height: 100%;
			padding: 18px 18px;
			background: #ffffff;
			.bottom-box {
				padding: 0px 15px;
				margin-top: 20px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 50px;
				background: #fcf6ec;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				.group {
					display: flex;
					align-items: center;
					font-size: 21px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #000000;
					&:not(:last-child) {
						margin-right: 60px;
					}
					&::before {
						content: '';
						margin-right: 9px;
						display: inline-block;
						width: 9px;
						height: 9px;
						background: #e6a23c;
						border-radius: 50%;
						opacity: 1;
					}
					.yellow-text {
						color: #e6a23c;
					}
				}
				.link {
					float: right;
					font-size: 18px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #008eff;
					cursor: pointer;
				}
			}
			.title-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.title {
					display: flex;
					align-items: center;
					font-size: 24px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					color: #222222;
					line-height: 28px;
					&::before {
						margin-right: 12px;
						content: '';
						display: inline-block;
						width: 6px;
						height: 28px;
						background: url('../images/title-1.png') no-repeat center / cover;
					}
				}
				.btn-box {
					button {
						width: 126px;
						height: 51px;
						font-size: 21px;
						background: #008eff;
						border-radius: 3px 3px 3px 3px;
						opacity: 1;
						color: #ffffff;
					}
				}
			}

			.lf-user-box {
				.white-bg;
				display: flex;
				flex-wrap: wrap;
				align-content: flex-start;
				padding: 32px 0px;
				flex: 1;
				overflow-y: auto;
				gap: 0px 20px;

				.user-item {
					.white-bg;
					display: flex;
					align-items: center;
					flex-direction: column;
					width: 183px;
					height: 288px;
					// overflow: hidden;
					border-radius: 3px 3px 3px 3px;
					opacity: 1;
					.avatar {
						width: 122px;
						height: 152px;
						object-fit: fill;
					}
					.img-box {
						.flex-center;
						width: 122px;
						height: 152px;
						background-color: #eeeeee;
						.vacancy {
							width: 41px;
							height: 41px;
							object-fit: contain;
						}
					}
					.user-name {
						margin-top: 10px;
						text-align: center;
						font-size: 23px;
						line-height: 26px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						color: #000000;
					}
					.user-position {
						margin-top: 10px;
						text-align: center;
						font-size: 20px;
						line-height: 23px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						// 超过两行省略
						display: -webkit-box;
						text-overflow: -o-ellipsis-lastline;
						overflow: hidden;
						text-overflow: ellipsis;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 3;
					}
				}
				.active-select {
					background: #edf7ff;
					.user-position {
						color: #008eff;
					}
					.user-name {
						color: #008eff;
					}
				}
			}
		}
	}
}
.flex-col-center {
	display: flex;
	align-items: center;
}
.loading-box {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(255, 255, 255, 0.5);
}
</style>
