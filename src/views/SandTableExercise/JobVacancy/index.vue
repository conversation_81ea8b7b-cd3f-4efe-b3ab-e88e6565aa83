<template>
	<div class="job-vaceancy">
		<header-back title="缺配职务" />
		<div class="org-box">
			<div class="org-item" v-for="item in state.orgJobVacancyVos" :key="item.orgId" :data-id="item.orgId">
				<div class="org-info">
					<span class="org-name">{{ item.orgName }}</span>
					<!-- <span class="desc"
					>（总职数 {{ item.total }} 人、正职 {{ item.realLeader }} 人、正职空缺 {{ item.realLeaderVacancy }} 人、副职
					{{ item.viceLeader }} 人、副职空缺 {{ item.viceLeaderVacancy }} 人）</span
				> -->
					<span class="link" @click="onOpenModal(item.orgId)">班子结构对比分析 &gt;</span>
				</div>
				<div class="job-box">
					<div class="user-item" v-for="(vacancy1, index) in item.jobVos" :key="index">
						<vacancy @select="onSelect(vacancy1, item)" :job="vacancy1" />
						<user-info-box
							show-delete
							show-confirm
							v-for="user in vacancy1.alternativeUsers"
							:user-info="user"
							:key="user.userId"
							:light-selected="selectUser.userId === user.userId"
							@click="onSelectUser(user)"
							@confirm="
								() => {
									onUserConfirm(user, vacancy1)
								}
							"
							@delete="
								(user) => {
									onDeleteAlternativeUser(user, vacancy1.pmsJobId)
								}
							"
						/>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { getLackjob, deleteAlternativeUser, addAlternativeUser } from '@/apis/sand-table-exercise'
import { getUserInfoItem } from '@/utils/utils'
import Vacancy from '../components/Vacancy.vue'
import UserInfoBox from '../components/UserInfoBox.vue'
import { selectUserDrawer, compareModal, submitConfigModal, positionTypeDrawer } from '../components/Drawer'
import useMock from '@/store/sandbox'
type JobUserVo = {
	userId: number
	username: string
	hasWarn: number
	warnInfo: string
	deleteAble: number
}

type JobVo = {
	pmsJobId: number
	jobName: string
	jobUserVo: JobUserVo[]
}

type OrgJobUserVo = {
	orgId: number
	orgName?: string
	total: number
	realLeader: number
	realLeaderVacancy: number
	viceLeader: number
	viceLeaderVacancy: number
	jobVos: JobVo[]
}

type JobUserInfoType = {
	userId: number
	username?: string
	headUrl?: string
	birthday?: string
	age?: number
	cadreIndex?: number
	cadreIndexSort?: string
	currentJob?: string
}

type JobVacancyVo = {
	pmsJobId: number
	jobName: string
	vacancyNum: number
	alternativeUsers: JobUserInfoType[]
}

type VacancyNumber = {
	orgId: number
	orgName?: string
	num: number
	jobVos: JobVacancyVo[]
}

const _uid = getUserInfoItem('_uid')

const state = reactive<{ orgJobUserVos: Array<OrgJobUserVo>; orgJobVacancyVos: Array<VacancyNumber> }>({
	orgJobUserVos: [],
	orgJobVacancyVos: [],
})

const route = useRoute()

const mock = useMock()

const type = route.query.type as string

const mock_id = route.query.mock_id as string
// 选中的用户
const selectUser = ref<any>({})

// 弹出岗找人
const onSelect = (job: JobVacancyVo, org: VacancyNumber) => {
	const { pmsJobId } = job
	// 当前组织下除开当前点击的空缺岗外被选配的所有人员，不允许再出现在待配名单中
	const allData = org.jobVos.flatMap((item) => (job.pmsJobId !== item.pmsJobId ? item.alternativeUsers || [] : []))

	selectUserDrawer.openDrawer({
		selectJobList: job.alternativeUsers || [],
		canNotSelectUser: allData || [],
		onConfirm: (users: any) => {
			if (users && users.length > 0) {
				job.alternativeUsers = users
				const data = JSON.stringify(
					users.map(({ userId }: any) => {
						return { userId, pmsJobId }
					})
				)
				console.log('🚀 ~ file: index.vue:104 ~ onSelect ~ data:', data)

				onAddAlternativeUser(data)
			}
		},
	})
}

const onSelectUser = (user: JobUserInfoType) => {
	selectUser.value = user
}

const onOpenModal = (orgId: any) => {
	const userIds = [selectUser.value].map((item: any) => item.userId).join(',')

	compareModal.openModal({
		mockId: mock_id || mock.mockId,
		orgId,
		userIds,
		type: 1,
	})
}
/**
 * @description: 新增预配名单
 * @param {*} data
 * @return {*}
 */
const onAddAlternativeUser = async (data: any) => {
	const res: any = await addAlternativeUser(data)
	if (res.code === 0) {
		loadData()
	}
}
/**
 * @description: 删除名单
 * @param {*} data
 * @return {*}
 */
const onDeleteAlternativeUser = async ({ userId }: any, pmsJobId: any) => {
	if (!userId) return
	const res: any = await deleteAlternativeUser(JSON.stringify([{ pmsJobId, userId }]))
	if (res.code === 0) {
		loadData()
	}
}

const onUserConfirm = (user: any, job: any) => {
	submitConfigModal.openDrawer({
		user,
		job,
		mockId: mock_id || mock.mockId,
		callBack: () => {
			loadData()
		},
	})
}

// 加载数据
const loadData = async () => {
	const res: any = await getLackjob(type, mock_id, _uid)

	if (res.code === 0) {
		// orgjobuservos: 职务列表 orgJobVacancyVos: 缺配职数
		const { orgJobUserVos, orgJobVacancyVos, mockId: _mockId } = res.data
		state.orgJobUserVos = orgJobUserVos
		state.orgJobVacancyVos = orgJobVacancyVos
		console.log('🚀 ~ file: index.vue:306 ~ loadData ~ mockId:', _mockId)
	}
}

loadData()
</script>

<style lang="less" scoped>
.job-vaceancy {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	.org-item {
	}
	.org-info {
		padding: 0px 18px;
		width: 100%;
		height: 50px;
		line-height: 50px;
		background: #edf7ff;
		border-radius: 3px 3px 3px 3px;
		opacity: 1;
		.org-name {
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #008eff;
			line-height: 23px;
		}
		.desc {
			font-size: 18px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #008eff;
		}
		.link {
			float: right;
			font-size: 18px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #008eff;
			cursor: pointer;
		}
	}
	.org-box {
		padding: 27px;
		flex: 1;
		background-color: #ffffff;
		overflow-y: auto;
		.job-box {
			padding: 18px 21px;

			.user-item {
				display: grid;
				grid-template-columns: 1fr 1fr 1fr 1fr;
				gap: 42px;
				&:not(:first-child) {
					margin-top: 20px;
				}
				& > div {
					width: auto !important;
				}
			}
		}
	}
}
</style>
