<template>
	<div class="page3">
		<div class="tips">本次干部调整共涉及单位 {{ data.count }} 个 <span class="lightheight">（注：亮灯提示为人员调配前后的班子结构）</span></div>
		<div class="page3-content">
			<div class="left">
				<div class="list">
					<div
						:class="`item ${activeKey === item.orgId ? 'active' : ''}`"
						v-for="item in data.list"
						:key="item.orgId"
						@click="onSelectMenu(item.orgId)"
					>
						<svg
							t="1697450179918"
							class="icon"
							viewBox="0 0 1024 1024"
							version="1.1"
							xmlns="http://www.w3.org/2000/svg"
							p-id="4025"
							width="20"
							height="20"
						>
							<path
								d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
								:fill="svgColor[item.beforeLight]"
								p-id="4026"
							></path>
						</svg>
						<svg
							t="1697450179918"
							class="icon"
							viewBox="0 0 1024 1024"
							version="1.1"
							xmlns="http://www.w3.org/2000/svg"
							p-id="4025"
							width="20"
							height="20"
						>
							<path
								d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
								:fill="svgColor[item.afterLight]"
								p-id="4026"
							></path>
						</svg>
						{{ item.orgName }}
					</div>
				</div>
			</div>
			<div class="right">
				<compare-table :columns="columns" :table-data="tableData"> </compare-table>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, inject } from 'vue'
import CompareTable from '../../components/CompareTable.vue'
// import RowTable from '@/components/RowTable.vue'
import { orgAnalyze } from '@/apis/sand-table-exercise'

const mock_id = inject('mock_id')

interface OrgStructureDetail {
	jobNum: number
	jobVacancy: number
	age: Standards[]
	gender: Standards[]
	educational: Standards[]
	specialty: Standards[]
	experience: Standards[]
	cadreIndex: Standards[]
	withdraw: Standards[]
}

interface OrgStructureVo {
	orgId: number
	orgName: string
	beforeLight: number
	afterLight: number
	before: OrgStructureDetail
	after: OrgStructureDetail
}

interface Standards {
	standard: number
	info: string
}

interface OrgStatistics {
	count: number
	list: OrgStructureVo[]
}
// 生成假数据的辅助函数
function generateStandards(): Standards[] {
	return [
		{ standard: Math.random() < 0.5 ? 0 : 1, info: '35岁以下：3人' },
		// 可以根据需要添加更多的数据
	]
}

function generateOrgStructureDetail(): OrgStructureDetail {
	return {
		jobNum: Math.floor(Math.random() * 100),
		jobVacancy: Math.floor(Math.random() * 20),
		age: generateStandards(),
		gender: generateStandards(),
		educational: generateStandards(),
		specialty: generateStandards(),
		experience: generateStandards(),
		cadreIndex: generateStandards(),
		withdraw: generateStandards(),
	}
}

function generateOrgStructureVo(): OrgStructureVo {
	return {
		orgId: Math.floor(Math.random() * 1000),
		orgName: `机构${Math.floor(Math.random() * 10)}`,
		beforeLight: Math.floor(Math.random() * 4),
		afterLight: Math.floor(Math.random() * 4),
		before: generateOrgStructureDetail(),
		after: generateOrgStructureDetail(),
	}
}

// 生成十条假数据
const fakeData: OrgStatistics = {
	count: 10,
	list: [],
	// list: Array.from({ length: 10 }, () => generateOrgStructureVo()),
}

const data = ref({})
// 打印生成的假数据
const svgColor = ['#ff473e', '#ffa300', '#f6dd00', '#60ca71']

const columns = [
	{
		title: '方案分析',
		dataIndex: 'title',
		key: 'title',
		align: 'center',
		rowClass: 'table-title-gray',
	},
	{
		title: '职数配置情况',
		dataIndex: 'position_should',
		key: 'position_should',
		align: 'center',
	},
	{
		title: '年龄结构',
		dataIndex: 'age',
		key: 'age',
		align: 'center',
	},
	{
		title: '性别结构',
		dataIndex: 'gender',
		key: 'gender',
		align: 'center',
	},
	{
		title: '学历结构',
		dataIndex: 'educational',
		key: 'educational',
		align: 'center',
	},
	{
		title: '专业结构',
		dataIndex: 'specialty',
		key: 'specialty',
		align: 'center',
	},
	{
		title: '经历结构',
		dataIndex: 'experience',
		key: 'experience',
		align: 'center',
	},
	{
		title: '干部指数',
		dataIndex: 'cadreIndex',
		key: 'cadreIndex',
		align: 'center',
	},
	{
		title: '任职回避',
		dataIndex: 'withdraw',
		key: 'withdraw',
		align: 'center',
	},
]

const tableData = ref<OrgStructureVo[]>([])

const activeKey = ref(1)

const onSelectMenu = (key: any) => {
	activeKey.value = key

	const list = data.value.list

	const org = list.find((item: any) => {
		return item.orgId === key
	})

	if (org) {
		const { before, after } = org as any

		tableData.value = [
			{ title: '人员调配前', ...before },
			{
				title: '人员调配后',
				...after,
			},
		] as OrgStructureVo[]
	}
}

const loadData = async () => {
	const res: any = await orgAnalyze(mock_id as string)

	if (res.code === 0) {
		data.value = res.data
	}
}

loadData()
</script>

<style lang="less" scoped>
.page3 {
	display: flex;
	flex-direction: column;
	height: 100%;
	padding: 24px 0px 0px;
	.tips {
		font-size: 20px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #222222;
		.lightheight {
			color: #ff0000;
		}
	}
	.page3-content {
		flex: 1;
		display: flex;
		margin-top: 24px;
		overflow: hidden;
		.left {
			padding: 12px;
			height: 100%;
			// width: ;
			flex: 332px 0 0;
			background: #f4faff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			overflow-y: auto;
			.list {
				width: 100%;
				display: flex;
				flex-direction: column;
				gap: 18px;
				.item {
					display: flex;
					align-items: center;
					padding: 11px 15px;
					background: #f4faff;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: #333333;
					line-height: 23px;
					transition: all 0.1s linear;
					cursor: pointer;
					.icon {
						margin-right: 9px;
					}
				}
				.active {
					color: #ffffff;
					background: #008eff;
				}
			}
		}
		.right {
			flex: 1;
			margin-left: 32px;
			overflow-y: auto;
		}
	}
}

.full-container {
	width: 100%;
	height: 100%;
}
.block-common-style {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
.age-col {
	width: 100%;
	height: 100%;
	.age-item {
		.block-common-style;
	}
}

.status {
	margin-right: 9px;
	width: 18px;
	height: 18px;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	background: no-repeat center / cover;
}
.status-success {
	.status;
	background-image: url('../../images/success-green.png');
}
.status-pre-warn {
	.status;
	background-image: url('../../images/pre-warn.png');
}
</style>
