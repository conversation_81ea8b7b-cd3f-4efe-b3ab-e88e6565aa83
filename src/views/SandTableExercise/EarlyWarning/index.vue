<template>
	<div class="early-warning">
		<header-back title="预警名单" />
		<div class="table-box">
			<position-table
				:columns="columns"
				:datasource="datasource"
				:scroll="{ y: tableScrollY }"
				:pagination="false"
				:loading="loading"
				:on-location="onLocation"
				:cdn-url="CDN_URL"
				:table-scroll-y="tableScrollY"
				@dismissChange="onDismissChange"
				@aspireChange="onAsaspireChange"
				@drop-change="
					(keys, records) => {
						onDropChange(keys, records, 1)
					}
				"
			/>
		</div>
		<div class="confirm">
			<a-button type="primary" @click="onSubmit">提交</a-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { shallowRef, ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { CDN_URL } from '@/config/env'
import { historyPush } from '@/utils/history'
import { useSubmit } from '../mixins/event'
import { getUserInfoItem } from '@/utils/utils'
import { warnList, submitMock } from '@/apis/sand-table-exercise'
import useMock from '@/store/sandbox'

import PositionTable from '../components/PositionTable.vue'

type UserInfo = {
	userId: number
	userName: string
	currentJob?: string
	currentJobTime?: string
	currentRankTime?: string
	warnInfo?: string
}

const columns = [
	{
		key: 'userName',
		dataIndex: 'userName',
		align: 'center',
		width: '7%',
		title: '预警干部',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},

	// {
	// 	key: 'portrait',
	// 	align: 'center',
	// 	width: '9%',
	// 	title: '干部画像',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'currentJob',
		dataIndex: 'currentJob',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'birthday',
	// 	dataIndex: 'birthday',
	// 	align: 'center',
	// 	width: '10%',
	// 	title: '出生年月（年龄）',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'currentJobTime',
		dataIndex: 'currentJobTime',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'currentRankTime',
		dataIndex: 'currentRankTime',
		align: 'center',
		width: '10%',
		title: '现职级任职时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'warnInfo',
		dataIndex: 'warnInfo',
		align: 'center',
		width: '10%',
		title: '预警原因',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'operate',
		dataIndex: 'operate',
		align: 'center',
		width: '15%',
		title: '操作',
	},
]
// 路由
const route = useRoute()

type RouteQuery = {
	mock_id: string
	mock_name: string
}
const adminId = getUserInfoItem('_uid')
//
const query = route.query as RouteQuery
//
const mock = useMock()
// 表格加载
const loading = ref(false)
//
const datasource = ref<UserInfo[]>([])
// 表格滚动值
const scrollTop = ref(0)
// 表格可滚动
const tableScrollY = ref(0)

const submit = useSubmit()
//
const mockId = ref<string>(query.mock_id)

const onLocation = (data: any) => {
	// keepalive.push()

	historyPush(`/cadre-portrait/home?user_id=${data.userId}`)
}

// 拟免职务
const onDismissChange = (record: any, data: any) => {
	const { offPmsJobId, offProposedAppointJobSupple } = data
	record.offPmsJobId = offPmsJobId
	record.offProposedAppointJobSupple = offProposedAppointJobSupple
}

const onDropChange = (key: any, record: any, type: any) => {
	record.operation = key
}

// 拟任职务变化
const onAsaspireChange = (record: any, data: any) => {
	const { onPmsJobId, onProposedAppointJobSupple, orgId } = data
	record.onPmsJobId = onPmsJobId
	record.onProposedAppointJobSupple = onProposedAppointJobSupple
	record.orgId = orgId
}

const onSubmit = async () => {
	const hasSelect: any = datasource.value.find((item: any) => {
		return item.operation === undefined
	})

	if (!!hasSelect) {
		return message.error(`${hasSelect.userName}未选择调整类型`)
	}
	submit({
		datasource: datasource.value,
		mockId: mockId.value,
		mockName: query.mock_name,
	})
}
// 加载数据
const loadData = async () => {
	const res: any = await warnList(mockId.value)
	if (res.code === 0) {
		datasource.value = res.data
	}
}

loadData()

onMounted(() => {
	const head: any = document.querySelector('.ant-table-thead')
	const body: any = document.querySelector('.ant-table-body')
	const table: any = document.querySelector('.table-box')

	tableScrollY.value = table?.offsetHeight - head?.offsetHeight

	body?.addEventListener('scroll', (e: any) => {
		scrollTop.value = e.target.scrollTop
	})
})
</script>

<style lang="less" scoped>
.early-warning {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background-color: #ffffff;
	.table-box {
		flex: 1;
		width: 100%;
		padding: 27px;
		overflow-y: auto;
	}
}

.confirm {
	margin-top: 28px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #ffffff;
	width: 100%;
	height: 102px;
	button {
		font-size: 21px;
		padding: 0px;
		width: 150px;
		height: 54px;
		background: #008eff;
		border-radius: 3px 3px 3px 3px;
		opacity: 1;
	}
}
</style>
