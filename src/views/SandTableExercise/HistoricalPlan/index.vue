<template>
	<div class="wait-matched">
		<header-back title="历史方案" />
		<div class="btn-box">
			<a-button type="primary" @click="onCreate">新建方案</a-button>
		</div>
		<div class="table-box">
			<a-table :columns="columns" :data-source="datasource" :scroll="{ y: tableScrollY }" :pagination="false" :loading="loading">
				<template #bodyCell="{ column, record }">
					<template v-if="column.key === 'operate'">
						<a :class="`ant-dropdown-link ${record.status === 4 ? 'disabled' : ''}`" @click="onLookPlan(record)"> 查看方案</a>
						<a class="ant-dropdown-link" @click="onSchemeAnalysis(record)"> 方案分析 </a>
						<a class="ant-dropdown-link" @click="onAdjust(record)"> 调整人员名单 </a>
						<a-dropdown :trigger="['click']">
							<a class="ant-dropdown-link" @click.prevent>
								输出
								<DownOutlined />
							</a>
							<template #overlay>
								<a-menu>
									<a-menu-item v-for="item in dropList" :key="item.key" @click="onWordView(record.mockId, item.key)">
										<a>{{ item.label }}</a>
									</a-menu-item>
								</a-menu>
							</template>
						</a-dropdown>
						<a :class="`ant-dropdown-link ${record.status !== 2 ? 'disabled' : ''}`" @click="onConfirmMock(record)"> 确定为最终方案 </a>
					</template>
				</template>
			</a-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import type { MOCK_STATUS } from '@/types/sandtable-exercise'
import { shallowRef, ref, onMounted, inject } from 'vue'
import useMock from '@/store/sandbox'
import { historyPush } from '@/utils/history'
import { DownOutlined } from '@ant-design/icons-vue'
import { userlistModal } from '../components/Drawer'
import { getUserInfoItem } from '@/utils/utils'
import { mockList, confirmMock, createMock } from '@/apis/sand-table-exercise'
import router from '@/router'
import { message } from 'ant-design-vue'
import { savePlanModal } from '../components/Drawer'

interface PlanData {
	mockId: number // 方案id
	mockName?: string // 方案名称 (可选)
	date?: string // 更新时间 (可选)
	status: MOCK_STATUS // 状态（1-暂存 2-保存 3.生效 4.失效）状态=4的时候没有不可查看和操作
}

const columns = [
	// {
	// 	key: 'head_url',
	// 	dataIndex: 'head_url',
	// 	align: 'center',
	// 	width: '7%',
	// 	title: '头像',
	// },
	{
		key: 'mockName',
		dataIndex: 'mockName',
		align: 'center',
		width: '7%',
		title: '方案名称',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
	},
	{
		key: 'date',
		dataIndex: 'date',
		align: 'center',
		width: '7%',
		title: '更新时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'operate',
		dataIndex: 'operate',
		align: 'center',
		width: '15%',
		title: '操作',
	},
]

const dropList = [
	{
		label: '研判名单',
		key: 1,
	},
	{
		label: '干部人事酝酿名单（书记专题会）',
		key: 2,
	},
	{
		label: '部务会/常委会表决票',
		key: 3,
	},
	{
		label: '干部人事任免说明',
		key: 4,
	},
	{
		label: '提拔交流统计表',
		key: 5,
	},
	// {
	// 	label: '重要干部任免征求区委委员意见表',
	// 	key: 6,
	// },
]

const _uid: any = getUserInfoItem('_uid')
// 表格加载
const loading = ref(false)
//
const mock = useMock()
// 表格滚动值
const scrollTop = ref(0)
// 表格可滚动
const tableScrollY = ref(0)
//
const notClearPinia: any = inject('notClearPinia')

const datasource = shallowRef<PlanData[]>([])

const onLocation = (data: any) => {
	// keepalive.push()

	historyPush(`/cadre-portrait/home?user_id=${data.userId}`)
}

/**
 * @description: 人员名单调整
 * @return {*}
 */
const onAdjust = (record: any) => {
	userlistModal.openModal({
		mockId: record.mockId,
		title: record.mockName + '调整人员名单',
	})
}

const onLookPlan = (record: PlanData) => {
	if ([4].includes(record.status)) {
		return
	}
	router.push({
		path: '/sand-table-exercise/home',
		query: {
			mock_id: record.mockId,
			mock_name: record.mockName,
			type: record.status,
		},
	})
}
// 方案分析
const onSchemeAnalysis = (record: any) => {
	router.push({
		path: '/sand-table-exercise/scheme-analysis',
		query: {
			mock_id: record.mockId,
		},
	})
}
const onConfirmMock = async (record: PlanData) => {
	const { mockId, status } = record

	if (status !== 2) {
		return
	}
	const res: any = await confirmMock(mockId as any, _uid)
	if (res.code === 0) {
		message.success('确定为最终方案成功')

		loadData()
	}
}
// 获取文件地址
const onWordView = (mock_id: any, type: any) => {
	notClearPinia()

	router.push({
		path: '/word-preview',
		query: {
			mock_id,
			type,
		},
	})
}

const onCreate = () => {
	savePlanModal.openModal({
		name: '',
		submit: async (name: string, close: () => void) => {
			// onSave(name)
			mock.setMockName(name)

			// 参数 1 表示保存 2 为暂存
			const res: any = await createMock(name, _uid)

			if (res.code === 0) {
				message.success('提交成功')

				const mockId = res.data

				mock.setMockId(mockId)

				close()

				onLookPlan({
					mockId,
					mockName: name,
				} as any)
			} else {
				message.error(res.message)
			}
		},
	})
}

// 加载数据
const loadData = async () => {
	const res: any = await mockList(_uid)
	if (res.code === 0) {
		datasource.value = res.data
	}
}

loadData()

// 表格滚动
onMounted(() => {
	const head: any = document.querySelector('.ant-table-thead')
	const body: any = document.querySelector('.ant-table-body')
	const table: any = document.querySelector('.table-box')

	tableScrollY.value = table?.offsetHeight - head?.offsetHeight

	body?.addEventListener('scroll', (e: any) => {
		scrollTop.value = e.target.scrollTop
	})
})
</script>

<style lang="less" scoped>
.btn-box {
	padding: 27px 0px 0px 27px;
	button {
		width: 155px;
		height: 50px;
		background: #008eff;
		border-radius: 6px 6px 6px 6px;

		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 20px;
		color: #ffffff;
	}
}
.wait-matched {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background-color: #ffffff;
	overflow: hidden;
	.table-box {
		width: 100%;
		flex: 1;
		padding: 27px 27px 47px 27px;
		.ant-dropdown-link:not(:last-child) {
			margin-right: 47px;
		}
	}
	.disabled {
		color: #ccc;
	}
}
</style>
