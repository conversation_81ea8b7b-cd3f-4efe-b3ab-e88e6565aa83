<template>
	<div class="sand-table-home">
		<div class="card-top">
			<div class="left-box">
				<img class="icon" :src="blockImg" @click="onJobVacancy" />
				<a-dropdown :trigger="['click']" :overlayStyle="{ maxHeight: '50vh', overflowY: 'auto' }">
					<div class="info">
						<div class="number">{{ vacancyNumber }}</div>
						<div class="subtitle">缺配职数 <i class="icon-right"></i></div>
					</div>
					<template #overlay>
						<a-menu>
							<a-menu-item v-for="item in state.orgJobVacancyVos" :key="item.orgId" @click="onDropdown(item)">
								{{ item.orgName }}
							</a-menu-item>
						</a-menu>
					</template>
				</a-dropdown>
			</div>
			<div class="right-box">
				<template v-for="item in leftSiderMenu">
					<div
						:key="item.path"
						v-if="item.show !== false"
						class="menu-item"
						@click="
							() => {
								item.click ? item.click() : onRouterLink(item.path)
							}
						"
					>
						<img :src="item.img" class="icon" />
						<div class="title">{{ item.title }}</div>
					</div>
				</template>
			</div>
		</div>
		<div class="card-content">
			<div class="tab-box">
				<div class="tab">
					<div
						class="tab-items"
						@click="onChangeTab(item.key)"
						v-for="item in tabs"
						:key="item.key"
						:class="{ 'tab-items-active': activeKey === item.key }"
					>
						<span>
							{{ item.title }}
						</span>
					</div>
				</div>
			</div>
			<div class="org-list">
				<div class="org-item" v-for="item in state.orgJobUserVos" :key="item.orgId" :data-id="item.orgId">
					<div class="org-info">
						<span class="org-name">{{ item.orgName }}</span>
						<span class="desc"
							>（总职数 {{ item.total }} 人、正职 {{ item.realLeader }} 人、正职空缺 {{ item.realLeaderVacancy }} 人、副职
							{{ item.viceLeader }} 人、副职空缺 {{ item.viceLeaderVacancy }} 人）</span
						>
						<span class="link" @click="onStuctureModal(item.orgId)">班子结构分析 &gt;</span>
					</div>
					<div class="org-card-box">
						<template v-if="item.jobVos">
							<div class="org-card" v-for="item1 in item.jobVos" :key="item1.pmsJobId" @click.stop="onLookFor(item)">
								<div class="position-name">{{ item1.jobName }}</div>
								<div class="user-name">
									<template v-if="item1.jobUserVo">
										<div :class="{ warn: user.hasWarn, stash: user.deleteAble }" v-for="user in item1.jobUserVo" :key="user.userId">
											<span @click.stop="onLookFor(item, user)" v-if="user.hasWarn"><i class="warn-icon"></i> {{ user.username }}</span>
											<span v-else-if="user.deleteAble">
												{{ user.username }}
												<i class="delete-icon" @click.stop="onDeleteJobUser(item.orgId, user.userId, item1.pmsJobId)"></i>
											</span>
											<span v-else>
												{{ user.username }}
											</span>
										</div>
									</template>
									<template v-if="item1.vacancyNum > 0">
										<span class="warn" v-for="i in item1.vacancyNum" :key="i" @click.stop="onVacancy(item, item1)"> 空缺 </span>
									</template>
								</div>
							</div>
						</template>
					</div>
				</div>
			</div>
		</div>
		<a-button @click="onSyncJob">数据同步</a-button>
		<div class="loading-box" v-if="loading">
			<a-spin :indicator="indicator" tips="数据同步中..." />
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'sandTableExerciseHome',
}
</script>

<script lang="ts" setup>
import { MOCK_STATUS } from '@/types/sandtable-exercise'
import { ref, reactive, computed, inject, h } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { getHomePage, deleteUserMock, syncJob, submitMock, resetMock, saveMock } from '@/apis/sand-table-exercise'
import useMock from '@/store/sandbox'
import { structTableModal, savePlanModal } from '../components/Drawer'
import { getUserInfoItem, debounce } from '@/utils/utils'
// 图片
import icon1 from '../images/icon-1.png'
import icon2 from '../images/icon-2.png'
import icon3 from '../images/icon-3.png'
import icon4 from '../images/icon-4.png'
import icon5 from '../images/icon-5.png'
import icon6 from '../images/icon-6.png'
import icon7 from '../images/icon-7.png'
import icon8 from '../images/icon-8.png'
import blockImg from '../images/block.png'
import { message } from 'ant-design-vue'

type JobUserVo = {
	userId: number
	username: string
	hasWarn: number
	warnInfo: string
	deleteAble: number
}

type JobVo = {
	pmsJobId: number
	jobName: string
	vacancyNum: number
	jobUserVo: JobUserVo[]
}

type OrgJobUserVo = {
	orgId: number
	orgName?: string
	orgShortName: string
	total: number
	realLeader: number
	realLeaderVacancy: number
	viceLeader: number
	viceLeaderVacancy: number
	jobVos: JobVo[]
}
type VacancyNumber = {
	orgId: number
	orgName?: string
	num: number
}

// 路由
const route = useRoute()
// 当type === 1 表示暂存状态 不支持操作： 另存
const { mock_id, mock_name, type } = route.query as any

const refresh: any = inject('refreshPage')

const tabs = [
	{
		title: '乡镇（街道）',
		key: '1',
	},
	{
		title: '保障部门',
		key: '4',
	},
	{
		title: '经济部门',
		key: '5',
	},
	{
		title: '执纪执法部门',
		key: '6',
	},
	{
		title: '中央市级在丰单位及重点企业',
		key: '7',
	},
	{
		title: '区属国有企业',
		key: '8',
	},
	{
		title: '学校',
		key: '9',
	},
	{
		title: '医院',
		key: '10',
	},
]
const _uid = getUserInfoItem('_uid')
// pinia 状态管理
const mock = useMock()
// 页面状态
const page_type = ref(type)

const updateMockId: any = inject('updateMockId')
// 顶级mockId保存
mock_id && updateMockId(mock_id)
// 保存mockId
mock_id && mock.setMockId(mock_id)
// 保存方案名
mock_name && mock.setMockName(mock_name)

const router = useRouter()
// 方案id
const mockId = ref(mock_id || mock.mockId)
// 方案名
const mockName = ref(mock_name)
// tab 切换
const activeKey = ref('1')
// 加载
const loading = ref(false)
// 每个序列下对应组织数据
const state = reactive<{ orgJobUserVos: Array<OrgJobUserVo>; orgJobVacancyVos: Array<VacancyNumber> }>({
	orgJobUserVos: [],
	orgJobVacancyVos: [],
})
const indicator = h(LoadingOutlined, {
	style: {
		fontSize: '24px',
	},
	spin: true,
})
let leftSiderMenu: any = [
	{
		title: '一键智选',
		img: icon1,
		path: '/sand-table-exercise/smart-selection',
		key: '1',
	},
	{
		title: '方案分析',
		img: icon2,
		path: '/sand-table-exercise/scheme-analysis',
		key: '2',
	},
	{
		title: '预警',
		img: icon3,
		path: '/sand-table-exercise/early-warning',
		key: '3',
	},
	{
		title: ' 待配',
		img: icon4,
		path: '/sand-table-exercise/wait-matched',
		key: '4',
	},
	{
		title: '重置方案',
		img: icon6,
		path: '',
		click() {
			onResetMock()
		},
		key: '5',
	},
	{
		title: '保存方案',
		img: icon7,
		path: '',
		click() {
			// 保存方案
			console.log('onSave')
			savePlanModal.openModal({
				name: mock.mockName,
				submit: (name: string) => {
					mockName.value = name
					// onSave(name)
					mock.setMockName(name)

					onSaveMock(name, '1')
				},
			})
		},
		key: '6',
	},
	{
		title: '另存为方案',
		img: icon5,
		path: '',
		click() {
			savePlanModal.openModal({
				submit: (name: string) => {
					// onSave(name)
					onSaveMock(name, '2')
					refresh && refresh()
				},
			})
		},
		key: '7',
	},
	{
		title: '历史方案',
		img: icon8,
		path: '/sand-table-exercise/historical-plan',
		key: '8',
	},
].filter((item) => {
	// 方案为生效状态 只保留一个方案分析
	if (type == MOCK_STATUS.EFFECTIVE) {
		return item.key === '2'
	} else {
		return true
	}
})
// 空缺职数统计
const vacancyNumber = computed(() => {
	return state.orgJobVacancyVos.reduce((pre: number, next: any) => {
		return pre + next.num
	}, 0)
})

const debounceLoad = debounce((key: any) => {
	loadData(key)
}, 100)

const onChangeTab = (key: any) => {
	activeKey.value = key

	debounceLoad(activeKey.value)
}
// 重置方案
const onResetMock = async () => {
	const res: any = await resetMock(mockId.value, _uid)
	if (res.code === 0) {
		message.success('重置成功')

		loadData()
	} else {
		message.error('当前无可重置方案')
	}
}
// 保存方案
const onSaveMock = async (name: any, type: string) => {
	// 参数 1 表示保存 2 为暂存
	const res: any = await saveMock(mockId.value, _uid, name || mock_name, type)

	if (res.code === 0) {
		if (res.data.mockId !== mock_id) {
			message.success('提交成功')
		} else {
			// 保存
			message.error(res.message)
		}

		updateMockId(res.data.mockId)

		mock.setMockId(res.data.mockId)

		mockId.value = res.data.mockId
	} else {
		message.error(res.message)
	}
}

const saveAs = () => {}
//
const onJobVacancy = () => {
	router.push({
		path: '/sand-table-exercise/job-vacancy',
		query: {
			mock_id: mockId.value,
			type: activeKey.value,
		},
	})
}
// 路由跳转
const onRouterLink = (path: string) => {
	if (path) {
		router.push({
			path,
			query: {
				mock_id: mockId.value,
				mock_name:
			},
		})
	}
}
// 加载数据
const loadData = async (key?: any) => {
	const _uid = getUserInfoItem('_uid')
	console.log('🚀 ~ file: index.vue:350 ~ loadData ~ mockId.value, mock.mockId:', mockId.value, mock.mockId)
	const res: any = await getHomePage(key || activeKey.value, mockId.value, _uid)

	if (res.code === 0) {
		// orgjobuservos: 职务列表 orgJobVacancyVos: 缺配职数
		const { orgJobUserVos, orgJobVacancyVos, mockId: _mockId } = res.data
		console.log('🚀 ~ file: index.vue:351 ~ loadData ~  orgJobUserVos, orgJobVacancyVos:', orgJobUserVos, orgJobVacancyVos)
		state.orgJobUserVos = orgJobUserVos
		state.orgJobVacancyVos = orgJobVacancyVos
		console.log('🚀 ~ file: index.vue:306 ~ loadData ~ mockId:', _mockId)

		// 顶级mockId保存
		updateMockId(mockId)
		// 保存mockId
		mock.setMockId(mockId)

		mockId.value = _mockId || ''
	}
}

const onDeleteJobUser = (orgId: any, userId: any, pmsJobId: any) => {
	// 生效
	if (page_type.value == 3) {
		return
	}
	const res: any = deleteUserMock(mock_id, orgId, userId, pmsJobId)
	if (res.code === 0) {
		loadData()
	}
}
// 下拉选中
const onDropdown = (org: any) => {
	const { orgId } = org

	const targetDom: any = document.querySelector(`.org-list div[data-id='${orgId}']`)

	const orgList: any = document.querySelector('.org-list')

	orgList.scrollTo({
		top: targetDom.offsetTop,
		behavior: 'smooth',
	})
}
//
const onStuctureModal = (orgId: any) => {
	structTableModal.openModal({
		mockId: mockId.value,
		orgId,
	})
}
/**
 * @description: 缺配跳转
 * @param {*} org
 * @param {*} job
 * @return {*}
 */
const onVacancy = (org: any, job?: JobVo) => {
	if (type == 3) {
		return
	}
	console.log('🚀 ~ file: index.vue:444 ~ onVacancy ~ job:', job)

	const { orgId, orgName } = org

	const query: any = {
		org_id: orgId,
		org_name: orgName,
		mock_id: mockId.value,
	}
	if (job) {
		const { pmsJobId, jobName } = job

		query['job_id'] = pmsJobId
		query['job_name'] = jobName
	}

	router.push({
		path: '/sand-table-exercise/look-for',
		query,
	})
}
//
const onLookFor = (org: any, user?: any) => {
	if (type == 3) {
		return
	}

	const { orgId, orgName } = org

	const query: any = {
		org_id: orgId,
		org_name: orgName,
		mock_id: mockId.value,
	}

	if (user) {
		const { userId } = user
		query['user_id'] = userId
	}

	router.push({
		path: '/sand-table-exercise/look-for',
		query,
	})
}
// 同步
const onSyncJob = async () => {
	loading.value = true

	const res: any = await syncJob()
	if (res.code === 0) {
		message.success(res.message)

		loadData()
	} else {
		message.error(res.message)
	}

	loading.value = false
}

// 保存方案
const onSave = async (mockName: string) => {
	const mockUser = mock.getMockList
	const adminId = getUserInfoItem('_uid')

	let params: any = {
		adminId,
		mockId: mock_id,
		mockName: mockName || mock_name,
		toMockFlag: 1,
		mockUser: mockUser,
	}

	params = JSON.stringify(params)

	console.log(params)
	const res: any = await submitMock(params)

	if (res.code === 0) {
		const mockId = res.data.mockId
		updateMockId(mockId)
		mock.setMockId(mockId)

		message.success('方案保存成功')
	} else {
		message.error(res.msg)
	}
}

loadData()
</script>

<style lang="less" scoped>
.flex-row-center {
	display: flex;
	justify-content: center;
	align-items: center;
}
.flex-col-center {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.sand-table-home {
	position: relative;
	height: 100%;
	display: flex;
	flex-direction: column;
	.card-top {
		display: flex;
		width: 100%;
		.left-box {
			.flex-row-center;
			width: 401px;
			height: 125px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			.icon {
				width: 36px;
				height: 36px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
			}
			.info {
				margin-left: 24px;
				.number {
					font-size: 30px;
					font-family: ArTarumianBakhum, ArTarumianBakhum;
					font-weight: 400;
					color: #008eff;
					line-height: 35px;
				}
				.subtitle {
					margin-top: 5px;
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #000000;
					line-height: 20px;
					.icon-right {
						display: inline-block;
						width: 20px;
						height: 20px;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
						background: url('@/assets/images/right-icon.png') center / contain no-repeat;
						vertical-align: middle;
					}
				}
			}
		}
		.right-box {
			padding: 0 104px;
			margin-left: 12px;
			flex: 1;
			height: 125px;
			background-color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.menu-item {
				.flex-col-center;
				width: auto;
				height: 74px;
				cursor: pointer;
				.icon {
					width: 34px;
					height: 34px;
					object-fit: contain;
				}
				.title {
					margin-top: 13px;
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #000000;
					line-height: 1;
				}
			}
		}
	}
	.card-content {
		display: flex;
		flex-direction: column;
		margin-top: 17px;
		flex: 1;
		width: 100%;
		background-color: #ffffff;
		overflow: hidden;
		.tab-box {
			padding: 27px 27px 23px;
			border-bottom: 2px solid #f5f5f5;
			.tab {
				display: flex;
				.tab-items {
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-right: 44px;
					font-size: 21px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					color: #000000;
					font-weight: 500;
					line-height: 25px;
					cursor: pointer;
				}
				.tab-items-active {
					color: #008eff;

					&::after {
						margin-top: 9px;
						content: '';
						display: inline-block;
						width: 35px;
						height: 3px;
						background: #008eff;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
					}
				}
			}
		}
		.org-list {
			position: relative;
			padding: 27px;
			flex: 1;
			overflow-y: auto;
			&::-webkit-scrollbar {
				display: none;
			}
			.org-item {
				.org-info {
					padding: 0px 18px;
					width: 100%;
					height: 50px;
					line-height: 50px;
					background: #edf7ff;
					border-radius: 3px 3px 3px 3px;
					opacity: 1;
					.org-name {
						font-size: 20px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						color: #008eff;
						line-height: 23px;
					}
					.desc {
						font-size: 18px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
					}
					.link {
						float: right;
						font-size: 18px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
						cursor: pointer;
					}
				}
				.org-card-box {
					width: 100%;
					padding: 9px 0px;
					display: flex;
					flex-wrap: wrap;
					// grid-template-columns: repeat(7, 1fr);
					gap: 10px;
					.org-card {
						.flex-col-center;
						padding: 10px 10px;
						flex-shrink: 0;
						min-width: 255px;
						height: 128px;
						background: #f9f9f9;
						border-radius: 3px 3px 3px 3px;
						opacity: 1;
					}
					.position-name {
						font-size: 18px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						text-align: center;
						// 最多两行， 超出显示省略号
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}
					.user-name {
						display: flex;
						align-items: center;
						margin-top: 23px;
						font-size: 17px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						line-height: 17px;
						text-align: center;
						div {
							display: flex;
							align-items: center;
							&:not(:last-child) {
								margin-right: 35px;
							}
						}
						.stash {
							color: #ff7a00;
							.delete-icon {
								margin-left: 9px;
								display: inline-block;
								width: 18px;
								height: 18px;
								background: url('../images/delete.png') center / contain no-repeat;
							}
						}
						.warn-icon {
							margin-right: 6px;
							display: inline-block;
							width: 18px;
							height: 18px;
							border-radius: 0px 0px 0px 0px;
							opacity: 1;
							background: url('../images/warn.png') center / cover no-repeat;
							vertical-align: middle;
						}
						.warn {
							margin-right: 6px;
							color: #f20e0e;
						}
					}
				}
			}
		}
	}
	.loading-box {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(255, 255, 255, 0.5);
	}
}
</style>
