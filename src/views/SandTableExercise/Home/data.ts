export default {
	orgId: 123,
	orgName: '某某组织',
	total: 1000,
	realLeader: 50,
	realLeaderVacancy: 5,
	viceLeader: 100,
	viceLeaderVacancy: 10,
	jobVos: [
		{
			pmsJobId: 1,
			jobName: '某某组织职位',
			jobUserVo: [
				{
					userId: 101,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 102,
					username: '用户2',
					hasWarn: 0,
					warnInfo: '',
					deleteAble: 0,
				},
				// Add more user entries as needed
			],
		},
		{
			pmsJobId: 2,
			jobName: '某某组织职位',
			jobUserVo: [
				{
					userId: 201,
					username: '用户1',
					hasWarn: 0,
					warnInfo: 'This user has a warning.',
					deleteAble: 1,
				},
				{
					userId: 202,
					username: '用户2',
					hasWarn: 0,
					warnInfo: '',
					deleteAble: 0,
				},
				// Add more user entries as needed
			],
		},
		{
			pmsJobId: 3,
			jobName: '某某组织职位',
			jobUserVo: [
				{
					userId: 301,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 302,
					username: '用户2',
					hasWarn: 0,
					warnInfo: '',
					deleteAble: 0,
				},
				// Add more user entries as needed
			],
		},
		{
			pmsJobId: 3,
			jobName: '某某组织职位',
			jobUserVo: [
				{
					userId: 301,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 302,
					username: '用户2',
					hasWarn: 0,
					warnInfo: '',
					deleteAble: 0,
				},
				// Add more user entries as needed
			],
		},
		{
			pmsJobId: 3,
			jobName: '某某组织职位',
			jobUserVo: [
				{
					userId: 301,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 302,
					username: '用户2',
					hasWarn: 0,
					warnInfo: '',
					deleteAble: 0,
				},
				// Add more user entries as needed
			],
		},
		{
			pmsJobId: 3,
			jobName: '某某组织职位',
			jobUserVo: [
				{
					userId: 301,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 302,
					username: '用户2',
					hasWarn: 0,
					warnInfo: '',
					deleteAble: 0,
				},
				// Add more user entries as needed
			],
		},
		{
			pmsJobId: 3,
			jobName: '某某组织职位',
			jobUserVo: [
				{
					userId: 301,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 302,
					username: '用户2',
					hasWarn: 0,
					warnInfo: '',
					deleteAble: 0,
				},
				// Add more user entries as needed
			],
		},
		{
			pmsJobId: 3,
			jobName: '某某组织职位',
			jobUserVo: [
				{
					userId: 301,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 301,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 301,
					username: '用户1',
					hasWarn: 1,
					warnInfo: 'This user has a warning.',
					deleteAble: 0,
				},
				{
					userId: 302,
					username: '用户2',
					hasWarn: 0,
					warnInfo: '',
					deleteAble: 0,
				},
				// Add more user entries as needed
			],
		},
		// Add more job entries as needed
	],
}

const vanmockData = [
	{
		orgId: 1,
		orgName: '机构A',
		num: 5,
	},
	{
		orgId: 2,
		orgName: '机构B',
		num: 8,
	},
	{
		orgId: 3,
		orgName: '机构C',
		num: 3,
	},
	// Add more entries as needed
]
export { vanmockData }
