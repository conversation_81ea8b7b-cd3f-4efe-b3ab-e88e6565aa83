<template>
	<div class="smart-selection">
		<header-back title="一键智选" />
		<div class="content">
			<SelectMenu @change="onSelectMenuChange" :menu="menu" :tab-active-key="tabKeys" />
			<div class="search-page" v-show="isSearch">
				<condition ref="condition" />
			</div>
			<template v-if="!isSearch">
				<div class="controll">
					<label class="checkbox-custom">
						<template v-if="tabKeys !== 5">
							<a-checkbox v-model:checked="checked" @change="loadUserList"></a-checkbox><span class="select-label">屏蔽处分影响期</span>
						</template>
					</label>
					<div class="panne-c">
						<div :class="`tile ${panneType === panneMenu[0].key ? 'panne-active' : ''}`" @click="onPanneChange(panneMenu[0].key)">
							<img :src="panneType === panneMenu[0].key ? panneMenu[0].active_icon : panneMenu[0].icon" alt="" srcset="" />
							<span class="label-text">{{ panneMenu[0].label }}</span>
						</div>
						<div class="divied"></div>
						<div :class="`tile ${panneType === panneMenu[1].key ? 'panne-active' : ''}`" @click="onPanneChange(panneMenu[1].key)">
							<img :src="panneType === panneMenu[1].key ? panneMenu[1].active_icon : panneMenu[1].icon" alt="" srcset="" />
							<span class="label-text">{{ panneMenu[1].label }}</span>
						</div>
					</div>
				</div>
				<div class="list-box" v-if="!isTile">
					<position-table
						row-key="userId"
						:columns="columns"
						:data-source="datasource"
						:scroll="{ y: tableScrollY }"
						:pagination="false"
						:loading="loading"
						:on-location="onLocation"
						:cdn-url="CDN_URL"
						:table-scroll-y="tableScrollY"
						:rowSelection="{ columnTitle: '勾选', columnWidth: '90px', onSelect, selectedRowKeys: rowSelectKeys }"
						@dismissChange="onDismissChange"
						@aspireChange="onAsaspireChange"
						@drop-change="
							(keys, records) => {
								onDropChange(keys, records, 1)
							}
						"
					/>
				</div>
				<div class="tile-box" v-else ref="tileList">
					<user-info-box
						v-for="item in datasource"
						:key="item.userId"
						:user-info="item"
						:selected="rowSelectKeys.includes(item.userId)"
						@click="onSelectUserInfo(item)"
					/>
				</div>
			</template>
		</div>
		<div class="confirm">
			<template v-if="isSearch">
				<a-button @click="onReset" type="default" class="reset">重置</a-button>
				<a-button type="primary" @click="onSearch" class="search">搜索</a-button>
			</template>
			<a-button type="primary" @click="onSubmit" v-else>提交</a-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { CDN_URL } from '@/config/env'
import { historyPush } from '@/utils/history'
import { MeScrollInit, functionMap } from '@/utils/utils'
import { getUserList } from '@/apis/sand-table-exercise'
import { getPmsLeader } from '@/apis/search'

import PositionTable from '../components/PositionTable.vue'
import SelectMenu from '../components/SelectMenu.vue'
import Condition from '@/components/Condition.vue'
import UserInfoBox from '../components/UserInfoBox.vue'

import tilePng from '../images/tile.png'
import titleActive from '../images/tile-active.png'
import listPng from '../images/list.png'
import listActive from '../images/list-active.png'
import router from '@/router'

type UserData = {
	userId: number
	userName: string
	headUrl?: string
	currentJob?: string
	currentJobTime?: string
	birthday?: string
	age?: number
	initDegree?: string
	initSchool?: string
	specialty?: string
	cadreIndex?: string
	cadreIndexSort?: string
	userType?: number // 1 - 市管领导，2 - 区管领导，3 - 中层干部
	highestDegree?: string
	select?: boolean
}

const panneMenu = [
	{
		label: '平铺',
		icon: tilePng,
		active_icon: titleActive,
		key: '1',
	},
	{
		label: '列表',
		icon: listPng,
		active_icon: listActive,
		key: '2',
	},
]

const menu = [
	{
		label: '优中选优',
		key: 1,
	},
	{
		label: '指数领先',
		key: 2,
	},
	{
		label: '一贯优秀',
		key: 3,
	},
	{
		label: '当下优秀',
		key: 4,
	},
	{
		label: '条件查询',
		key: 5,
	},
]

const columns = [
	{
		key: 'headUrl',
		dataIndex: 'headUrl',
		align: 'center',
		width: '7%',
		title: '照片',
	},
	{
		key: 'userName',
		dataIndex: 'userName',
		align: 'center',
		width: '10%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},

	// {
	// 	key: 'portrait',
	// 	align: 'center',
	// 	width: '9%',
	// 	title: '干部画像',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'currentJob',
		dataIndex: 'currentJob',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'currentJobTime',
		dataIndex: 'currentJobTime',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initDegree',
		dataIndex: 'initDegree',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initSchool',
		dataIndex: 'initSchool',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'specialty',
	// 	align: 'center',
	// 	width: '12%',
	// 	title: '专业',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'cadreIndex',
		dataIndex: 'cadreIndex',
		align: 'center',
		width: '10%',
		title: '干部指数',
	},
	{
		key: 'cadreIndexSort',
		dataIndex: 'cadreIndexSort',
		align: 'center',
		width: '10%',
		title: '序列排名',
	},
	// {
	// 	key: 'operate',
	// 	dataIndex: 'operate',
	// 	align: 'center',
	// 	width: '15%',
	// 	title: '操作',
	// },
	// {
	// 	key: 'achievement',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '业绩',
	// },
	// {
	// 	key: 'ability',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '能力',
	// },
	// {
	// 	key: 'praise',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '口碑',
	// },
	// {
	// 	key: 'politics',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '政治',
	// 	customization: 'CustomBlock',
	// },
]
const mockData: UserData[] = Array.from({ length: 10 }, (_, index) => ({
	userId: index + 1,
	userName: `用户${index + 1}`,
	headUrl: `https://example.com/avatar${index + 1}.png`,
	pmsJobId: 101 + index,
	jobName: `职务${index + 1}`,
	currentJob: `现任职务${index + 1}`,
	currentJobTime: `2023-01-01`,
	birthday: `1990-01-01`,
	age: 30 + index,
	initDegree: `学历${index + 1}`,
	initSchool: `学校${index + 1}`,
	specialty: `专业${index + 1}`,
	cadreIndex: `干部指数${index + 1}`,
	cadreIndexSort: `干部指数同序列排名${index + 1}`,
	userType: (index % 3) + 1, // Alternating between 1, 2, 3
}))
//
const route = useRoute()
const { mock_id } = route.query as any
// 搜索
const isSearch = ref(false)
//
const loading = ref(false)
//
const checked = ref(false)
//
const condition = ref<any>({})
// 表格滚动值
const scrollTop = ref(0)
// 平铺 、列表切换
const panneType = ref(panneMenu[1].key)
// 表格可滚动
const tableScrollY = ref(0)
// 顶部tab切换
const tabKeys = ref(menu[0].key)
// 数据源
const datasource = ref<Array<UserData>>([])
// 勾选的数据
const selectData: any = ref([])
// 滚动加载
const currentPage = ref(1)
// formState
const formState = ref<any>({})
// 复选框
const checkValue = reactive({
	checked1: false,
	checked2: false,
})
// 屏蔽处分期影响
const filter = computed(() => {
	return checked.value ? '1' : ''
})

// 是否为平铺
const isTile = ref(panneType.value === panneMenu[0].key)

// 表格选中项
const rowSelectKeys = computed(() => {
	return selectData.value.map((item: any) => item.userId)
})

const onLocation = (data: any) => {
	// keepalive.push()

	historyPush(`/cadre-portrait/home?user_id=${data.userId}`)
}

const onPanneChange = (key: any) => {
	panneType.value = key

	isTile.value = panneType.value === panneMenu[0].key

	if (tabKeys.value === menu[4].key) {
		resetStatus()

		setTimeout(() => {
			initUpScroll(key)
		})
	}
}

const onSelectMenuChange = (key: any) => {
	tabKeys.value = key

	isSearch.value = tabKeys.value === 5
	// 切换时先清空数据
	datasource.value = []

	!isSearch.value && loadData()

	currentPage.value = 1
}

// 拟免职务
const onDismissChange = (record: any, data: any) => {
	const { offPmsJobId, offProposedAppointJobSupple } = data
	record.offPmsJobId = offPmsJobId
	record.offProposedAppointJobSupple = offProposedAppointJobSupple
}

const onDropChange = (key: any, record: any, type: any) => {
	record.operation = key
}

// 拟任职务变化
const onAsaspireChange = (record: any, data: any) => {
	const { onPmsJobId, onProposedAppointJobSupple, orgId } = data
	record.onPmsJobId = onPmsJobId
	record.onProposedAppointJobSupple = onProposedAppointJobSupple
	record.orgId = orgId
}

const onSelect = (record: any, selected: any, selectedRows: any, nativeEvent: any) => {
	record.select = !record.select
	if (selected) {
		selectData.value.push(record)
	} else {
		const findIndex = selectData.value.findIndex((item: any) => {
			return item.userId === record.userId
		})

		selectData.value.splice(findIndex, 1)
	}
}

const onSelectUserInfo = (record: any) => {
	if (!rowSelectKeys.value.includes(record.userId)) {
		selectData.value.push(record)
	} else {
		const findIndex = selectData.value.findIndex((item: any) => {
			return item.userId === record.userId
		})

		selectData.value.splice(findIndex, 1)
	}
}
// 条件查询重置
const onReset = () => {
	condition.value.resetForm()
}
// 条件查询
const onSearch = () => {
	resetStatus()

	formState.value = condition.value.formState

	isSearch.value = false
	setTimeout(() => {
		initUpScroll(panneType.value)
	})
	// query(++currentPage.value)
}

const resetStatus = () => {
	currentPage.value = 1

	datasource.value = []

	mescroll1.value.destroy?.()

	mescroll1.value = null
}
const onSubmit = () => {
	sessionStorage.setItem('smart_selection', JSON.stringify(selectData.value))

	router.push({
		path: '/sand-table-exercise/cadre-allocation',
		query: {
			mock_id,
		},
	})
}

const loadUserList = () => {
	resetStatus()

	if (isSearch.value) {
		onSearch()
	} else {
		loadData()
	}
}
/**
 * @description: 上拉加载更多
 * @return {*}
 */
// 滚动容器
const mescroll1 = ref<any>({})

const initUpScroll = (type: string) => {
	let ele = type === '1' ? document.querySelector('.tile-box') : document.querySelector('.ant-table-body')

	console.log('🚀 ~ file: index.vue:668 ~ initUpScroll ~ ele:', ele)

	mescroll1.value = MeScrollInit(ele as any, {
		auto: true,
		// loadFull: { use: true, delay: 500 },
		callback: async () => {
			const res: any = await query(currentPage.value++)
			console.log('🚀 ~ file: index.vue:674 ~ callback: ~ totalPages:', res)
			if (res.code === 0) {
				const { content, totalPages }: any = res.data || {}
				console.log('🚀 ~ file: index.vue:674 ~ callback: ~ totalPages:', totalPages)

				mescroll1.value.endByPage(content, totalPages)
			}
		},
	})
}

const query = (page: number) =>
	new Promise((resolve) => {
		const _formState = formState.value
		const params: any = { ..._formState, page, page_size: 40 }

		if (checkValue.checked1) {
			params.punishment_flag = 1
		}

		if (checkValue.checked2) {
			params.relationship_flag = 1
		}

		if (params.birthday) {
			params.birthday_start = params.birthday[0]
			params.birthday_end = params.birthday[1]
			delete params.birthday
		}
		if (params.join_time) {
			params.join_time_start = params.join_time[0]
			params.join_time_end = params.join_time[1]
			delete params.join_time
		}
		if (params.join_time) {
			params.join_time_start = params.join_time[0]
			params.join_time_end = params.join_time[1]
			delete params.join_time
		}
		if (params.information) {
			params.information = params.information.value
		}
		if (params.responsibilities) {
			params.responsibilities = params.responsibilities.value
		}
		if (params.speciality) {
			params.speciality = params.speciality.value
		}
		if (params.label) {
			params.label = params.label.value
		}
		const _params: any = {}
		Object.entries(params).forEach(([key, value]) => {
			if (Array.isArray(value)) {
				if (value[0] !== undefined) {
					_params[key] = value
				}
				return false
			}
			_params[key] = value
		})
		getPmsLeader(_params).then((res: any) => {
			if (res.code === 0) {
				const { content }: any = res.data || {}

				if (content) {
					datasource.value.push(...content.map((item: any) => functionMap.transformData(item)))
				}

				resolve(res)
			}
		})
	})

const loadData = async () => {
	const res: any = await getUserList(tabKeys.value as any, filter.value as any)
	if (res.code === 0) {
		datasource.value = res.data
	}
}

loadData()

onMounted(() => {
	const head = document.querySelector('.ant-table-thead')
	const body = document.querySelector('.ant-table-body')
	const table = document.querySelector('.list-box')

	tableScrollY.value = table?.offsetHeight - head?.offsetHeight

	body?.addEventListener('scroll', (e: any) => {
		scrollTop.value = e.target.scrollTop
	})
})
</script>

<style lang="less" scoped>
.smart-selection {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.content {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		flex: 1;
		padding: 24px;
		background-color: #ffffff;
		overflow: hidden;
		.search-page {
			flex: 1;
			width: 100%;
			overflow: auto;
			background-color: #ffffff;
		}

		.controll {
			display: flex;
			justify-content: space-between;
			overflow-y: auto;
			.checkbox-custom {
				display: flex;
				align-items: center;
				.select-label {
					margin-left: 8px;
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.85);
					line-height: 20px;
				}
			}
			.panne-c {
				display: flex;
				align-items: center;
				height: 27px;
				overflow: hidden;
				.divied {
					margin: 0px 25px;
					width: 1px;
					height: 18px;
					background: rgba(0, 0, 0, 0.4);
					border-radius: 9px 9px 9px 9px;
					opacity: 1;
				}
				.tile {
					display: flex;
					align-items: center;
					img {
						width: 27px;
						height: 27px;
						object-fit: contain;
					}
					.label-text {
						font-size: 20px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						line-height: 20px;
					}
				}
				.panne-active .label-text {
					color: #008eff;
				}
			}
		}

		.list-box {
			margin-top: 18px;
			flex: 1;
		}
		.tile-box {
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			flex: 1;
			gap: 20px;
			overflow-y: scroll;
			.userinfo-box {
				width: 24%;
			}
		}
	}
	.confirm {
		margin-top: 28px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		width: 100%;
		height: 102px;
		button {
			font-size: 21px;
			padding: 0px;
			width: 150px;
			height: 54px;
			background: #008eff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
		}
		.reset {
			margin-right: 20px;
			width: 111px;
			height: 42px;
			background: #e8f4fd;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #008eff;
		}
		.search {
			width: 111px;
			height: 42px;
			background: #008eff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;

			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #ffffff;
		}
	}
}
</style>
