<template>
	<div class="sand-table">
		<p-header title="沙盘推演" />
		<div class="content">
			<router-view v-slot="{ Component }">
				<component :is="Component" :key="refreshPage" />
			</router-view>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, provide, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

import useMock from '@/store/sandbox'

const mockId = ref<number>()

const mock = useMock()

const refreshPage = ref<number>()
// 控制是否需要清除pinia数据
const isClearPinia = ref<boolean>(true)

const updateMockId = (id: number) => {
	mockId.value = id
}

const onRefreshPage = () => {
	refreshPage.value = Math.random()
}

const notClearPinia = () => {
	isClearPinia.value = false
}

const clearMock = () => {
	mock.setMockId(undefined)
	mock.setMockName(undefined)
}
provide('mockId', mockId)
provide('clearMock', clearMock)
provide('updateMockId', updateMockId)
provide('refreshPage', onRefreshPage)
provide('notClearPinia', notClearPinia)
/**
 * @description: 页面卸载时清除pinia数据
 * @return {*}
 */
onUnmounted(() => {
	if (isClearPinia.value) {
		clearMock()
	}

	isClearPinia.value = true
})
</script>

<style lang="scss" scoped>
.sand-table {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	background-color: #f5f5f5;

	.content {
		flex: 1;
		width: 100%;
		overflow: hidden;
	}
}
</style>
