import { inject } from 'vue'
import { getUserInfoItem } from '@/utils/utils'
import { submitMock, saveMock } from '@/apis/sand-table-exercise'
import { message } from 'ant-design-vue'
import useMock from '@/store/sandbox'
// 拟免职务
export const onDismissChange = (record: any, data: any) => {
	const { offPmsJobId, offProposedAppointJobSupple } = data
	record.offPmsJobId = offPmsJobId
	record.offProposedAppointJobSupple = offProposedAppointJobSupple
}

export const onDropChange = (key: any, record: any, type: any) => {
	record.operation = key
}

// 拟任职务变化
export const onAsaspireChange = (record: any, data: any) => {
	const { onPmsJobId, onProposedAppointJobSupple, orgId } = data
	record.onPmsJobId = onPmsJobId
	record.onProposedAppointJobSupple = onProposedAppointJobSupple
	record.orgId = orgId
}

// 职务修改提交
export const useSubmit = () => {
	const mock = useMock()
	const _uid = getUserInfoItem('_uid')
	return async ({ datasource = [], mockId, mockName, callback }: any) => {
		//

		const adminId = getUserInfoItem('_uid')

		const mockUser = datasource
			.filter(
				({ operation, onPmsJobId, onProposedAppointJobSupple, toMockFlag }: any) =>
					(toMockFlag || operation || onPmsJobId || onProposedAppointJobSupple) !== undefined
			)
			.map((item: any) => {
				const {
					orgId,
					userId,
					userName,
					operation,
					onPmsJobId,
					onProposedAppointJobSupple,
					offPmsJobId,
					offProposedAppointJobSupple,
					toMockFlag,
					primaryUserId,
				} = item
				return {
					orgId,
					userId,
					userName,
					operation,
					onPmsJobId,
					onProposedAppointJobSupple,
					offPmsJobId,
					toMockFlag,
					offProposedAppointJobSupple,
					primaryUserId,
				}
			})

		let params: any = {
			adminId,
			mockId: mockId || mock.mockId,
			mockName,
			mockUser: mockUser,
		}

		params = JSON.stringify(params)

		const res: any = await submitMock(params)

		if (res.code === 0) {
			const mockId = res.data
			mock.setMockId(mockId || '')

			message.success('方案提交成功')

			callback && callback()
		} else {
			message.error(res.message)
		}
	}
}
