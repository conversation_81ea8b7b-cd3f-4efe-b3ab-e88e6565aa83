<template>
	<div class="wait-matched">
		<header-back title="待配名单" />
		<div class="content">
			<div class="table-box">
				<position-table
					:columns="columns"
					:datasource="datasource"
					:scroll="{ y: tableScrollY }"
					:pagination="false"
					:loading="loading"
					:on-location="onLocation"
					:cdn-url="CDN_URL"
					:table-scroll-y="tableScrollY"
					:merge-avatar="true"
					@dismissChange="onDismissChange"
					@aspireChange="onAsaspireChange"
					@drop-change="
						(keys, records) => {
							onDropChange(keys, records, 1)
						}
					"
				/>
			</div>
		</div>

		<div class="confirm">
			<a-button type="primary" @click="onSubmit">提交</a-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { shallowRef, ref, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { CDN_URL } from '@/config/env'
import { useSubmit } from '../mixins/event'
import { historyPush } from '@/utils/history'
import { toSubmitList } from '@/apis/sand-table-exercise'
import useMock from '@/store/sandbox'

import PositionTable from '../components/PositionTable.vue'

type UserData = {
	userId: number
	userName: string
	headUrl?: string
	currentJob?: string
	currentJobTime?: string
	birthday?: string
	age?: number
	initDegree?: string
	initSchool?: string
	specialty?: string
	cadreIndex?: string
	cadreIndexSort?: string
	userType?: number // 1 - 市管领导，2 - 区管领导，3 - 中层干部
	highestDegree?: string
}
const mockUserData1: UserData[] = Array.from({ length: 10 }, (_, index) => ({
	userId: index + 1,
	userName: `用户${index + 1}`,
	headUrl: `https://example.com/avatar${index + 1}.png`,
	currentJob: `现任职务${index + 1}`,
	currentJobTime: `2023-01-01`,
	birthday: `1990-01-01`,
	age: 30 + index,
	initDegree: `全日制学历${index + 1}`,
	initSchool: `全日制学校${index + 1}`,
	specialty: `专业${index + 1}`,
	cadreIndex: `干部指数${index + 1}`,
	cadreIndexSort: `干部指数同序列排名${index + 1}`,
	userType: (index % 3) + 1, // Alternating between 1, 2, 3
	highestDegree: `最高学历${index + 1}`,
}))

const columns = [
	// {
	// 	key: 'head_url',
	// 	dataIndex: 'head_url',
	// 	align: 'center',
	// 	width: '7%',
	// 	title: '头像',
	// },
	{
		key: 'userName',
		dataIndex: 'userName',
		align: 'center',
		width: '7%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},
	{
		key: 'cadreIndex',
		dataIndex: 'cadreIndex',
		align: 'center',
		width: '6%',
		title: '干部指数',
	},
	{
		key: 'cadreIndexSort',
		dataIndex: 'cadreIndexSort',
		align: 'center',
		width: '10%',
		title: '序列排名',
	},

	// {
	// 	key: 'portrait',
	// 	align: 'center',
	// 	width: '9%',
	// 	title: '干部画像',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'currentJob',
		dataIndex: 'currentJob',
		align: 'left',
		width: '10%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'currentJobTime',
		dataIndex: 'currentJobTime',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'highestDegree',
		dataIndex: 'highestDegree',
		align: 'center',
		width: '7%',
		title: '最高学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initDegree',
		dataIndex: 'initDegree',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initSchool',
		dataIndex: 'initSchool',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// // {
	// // 	key: 'specialty',
	// // 	align: 'center',
	// // 	width: '12%',
	// // 	title: '专业',
	// // 	// colClass: blur.value ? 'filter-style' : '',
	// // },

	{
		key: 'operate',
		dataIndex: 'operate',
		align: 'center',
		width: '15%',
		title: '操作',
	},
	// {
	// 	key: 'achievement',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '业绩',
	// },
	// {
	// 	key: 'ability',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '能力',
	// },
	// {
	// 	key: 'praise',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '口碑',
	// },
	// {
	// 	key: 'politics',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '政治',
	// 	customization: 'CustomBlock',
	// },
]
// 路由
const route = useRoute()
// 沙盘数据
const mock = useMock()
//
const submit = useSubmit()
// mock_id
const { mock_id, mock_name } = route.query as any
// 表格加载
const loading = ref(false)
// 表格滚动值
const scrollTop = ref(0)
// 表格可滚动
const tableScrollY = ref(0)

const datasource = ref<Array<UserData>>([])

const onLocation = (data: any) => {
	// keepalive.push()

	historyPush(`/cadre-portrait/home?user_id=${data.userId}`)
}

const loadData = async () => {
	const res: any = await toSubmitList(mock_id)

	if (res.code === 0) {
		datasource.value = res.data
	}
}

// 拟免职务
const onDismissChange = (record: any, data: any) => {
	const { offPmsJobId, offProposedAppointJobSupple } = data
	record.offPmsJobId = offPmsJobId
	record.offProposedAppointJobSupple = offProposedAppointJobSupple
}

const onDropChange = (key: any, record: any, type: any) => {
	record.operation = key
}

// 拟任职务变化
const onAsaspireChange = (record: any, data: any) => {
	const { onPmsJobId, onProposedAppointJobSupple, orgId } = data
	record.onPmsJobId = onPmsJobId
	record.onProposedAppointJobSupple = onProposedAppointJobSupple
	record.orgId = orgId
}

// 提交
const onSubmit = () => {
	submit({
		datasource: datasource.value,
		mockId: mock_id,
		mockName: mock_name,
	})
}

onMounted(() => {
	nextTick(() => {
		const head: any = document.querySelector('.ant-table-thead')
		const body: any = document.querySelector('.ant-table-body')
		const table: any = document.querySelector('.table-box')

		tableScrollY.value = table?.clientHeight - head?.offsetHeight
		console.log(tableScrollY.value)
		body?.addEventListener('scroll', (e: any) => {
			scrollTop.value = e.target.scrollTop
		})
	})
})

loadData()
</script>

<style lang="less" scoped>
.wait-matched {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.content {
		background-color: #ffffff;
		flex: 1;
		width: 100%;
		padding: 27px;
		margin-bottom: 28px;
		.table-box {
			width: 100%;
			height: 100%;
		}
	}
	.confirm {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		flex-shrink: 0;
		width: 100%;
		height: 102px;
		button {
			font-size: 21px;
			padding: 0px;
			width: 150px !important;
			height: 54px !important;
			background: #008eff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
		}
	}
}
</style>
