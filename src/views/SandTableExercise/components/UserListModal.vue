<template>
	<a-modal :visible="true" :title="title" @ok="handleOk" @cancel="onClose" :footer="false" class="user-list-modal">
		<div class="modal-box">
			<div class="tips-box">
				<!-- 本次干部调整共涉及单位个，涉及干部<text-component>521</text-component>人。其中提拔<text-component>124</text-component>人（女性<text-component>21</text-component>人，70后<text-component>32</text-component>人，80后<text-component>25</text-component>人，90后<text-component>16</text-component>人，35岁以下<text-component>31</text-component>人，36岁-40岁<text-component>32</text-component>人，40岁-50岁<text-component>32</text-component>人，50岁以上<text-component>32</text-component>人。全日制大学<text-component>32</text-component>人，全日制研究生<text-component>32</text-component>人）；进一步使用<text-component>32</text-component>人；交流<text-component>32</text-component>人；不再担任领导职务<text-component>32</text-component>人。 -->
				本次干部调整共涉及单位<text-component>{{ mockInfo.orgCount }}</text-component> 个，涉及干部
				<text-component>{{ mockInfo.userCount }}</text-component>
				人。其中提拔
				<text-component>{{ mockInfo.promoteCount }}</text-component>
				人（女性
				<text-component>{{ mockInfo.femaleCount }}</text-component>
				人，70后
				<text-component>{{ mockInfo.age70Count }}</text-component>
				人，80后
				<text-component>{{ mockInfo.age80Count }}</text-component>
				人，90后
				<text-component>{{ mockInfo.age90Count }}</text-component>
				人，35岁以下
				<text-component>{{ mockInfo.ageUnder35Count }}</text-component>
				人，36岁-40岁
				<text-component>{{ mockInfo.age36To39Count }}</text-component>
				人，40岁-50岁
				<text-component>{{ mockInfo.age40To49Count }}</text-component>
				人，50岁以上
				<text-component>{{ mockInfo.ageOver50Count }}</text-component>
				人。全日制大学
				<text-component>{{ mockInfo.undergraduateCount }}</text-component>
				人，全日制研究生
				<text-component>{{ mockInfo.graduateCount }}</text-component>
				人）；进一步使用
				<text-component>{{ mockInfo.furtherUseCount }}</text-component>
				人；交流
				<text-component>{{ mockInfo.communicationCount }}</text-component>
				人；不再担任领导职务
				<text-component>{{ mockInfo.removeCount }}</text-component>
				人。
			</div>
			<div class="change-methods">
				<div class="label">调整方式：</div>
				<div class="check">
					<a-checkbox-group @change="onCheckChange" name="checkboxgroup" :options="typeOptions" />
				</div>
			</div>
			<div class="data-box">
				<position-table
					:datasource="filterData"
					:columns="columns"
					@dismissChange="onDismissChange"
					@aspireChange="onAsaspireChange"
					@drop-change="
						(keys, records) => {
							onDropChange(keys, records, 1)
						}
					"
				>
				</position-table>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { createVNode, computed, ref } from 'vue'
import PositionTable from './PositionTable.vue'
import { mockUserList } from '@/apis/sand-table-exercise'

type MockType = {
	orgCount: number
	userCount: number
	promoteCount: number
	furtherUseCount: number
	communicationCount: number
	removeCount: number
	femaleCount: number
	age70Count: number
	age80Count: number
	age90Count: number
	ageUnder35Count: number
	age36To39Count: number
	age40To49Count: number
	ageOver50Count: number
	undergraduateCount: number
	graduateCount: number
}
// -1-其他 0-提拔 1-进一步使用 2-交流 3-试用期满转正 4-任免兼挂职 5-不再担任领导职务
const typeOptions = [
	{ label: '其他', value: -1 },
	{ label: '提拔', value: 0 },
	{ label: '进一步使用', value: 1 },
	{ label: '交流', value: 2 },
	{ label: '试用期满转正', value: 3 },
	{ label: '任免兼挂职', value: 4 },
	{ label: '不再担任领导职务', value: 5 },
]

const props = defineProps({
	title: {
		type: String,
		default: '',
	},
	mockId: {
		type: Number,
	},
})

const mockInfo = ref<MockType>({} as MockType)

const datasource = ref([])

/**
 * @description: 文本组件
 * @return {*}
 */
const TextComponent: any = {
	props: { text: String },
	render() {
		return createVNode('span', { className: 'text-component' }, this.$slots.default())
	},
}
/**
 * @description: 文本组件
 * @return {*}
 */
const TextBold: any = {
	props: { text: String },
	render() {
		return createVNode('span', { className: 'text-component' }, this.$slots.default())
	},
}
const emits = defineEmits(['close'])

const columns = [
	{
		dataIndex: 'index',
		key: 'index',
		title: '序号',
		width: '10%',
	},
	{
		dataIndex: 'username',
		key: 'username',
		title: '姓名',
		width: '10%',
	},
	{
		dataIndex: 'operation',
		key: 'operation',
		title: '调整方式',
		width: '10%',
	},
	{
		dataIndex: 'currentJob',
		key: 'currentJob',
		title: '现任职务',
		width: '20%',
	},
	{
		dataIndex: 'onJob',
		key: 'onJob',
		title: '拟任职务',
		width: '17%',
	},
	{
		dataIndex: 'offJob',
		key: 'offJob',
		title: '拟免职务',
		width: '17%',
	},
	{
		dataIndex: 'afterMockJob',
		key: 'afterMockJob',
		title: '调整后职务',
		width: '17%',
	},
	// {
	// 	dataIndex: 'operate',
	// 	key: 'operate',
	// 	title: '操作',
	// },
]

//
const handleOk = () => {
	// visible.value = false
	emits('close')
}
// 关闭
const onClose = () => {
	emits('close')
}
// 复选框更改
const check: any = ref([])
const onCheckChange = (checkedValues: any) => {
	check.value = checkedValues
}

const filterData = computed(() => {
	if (check.value.length === 0) {
		return datasource.value
	} else {
		return datasource.value.filter((item: any) => {
			return check.value.includes(Number(item.operation))
		})
	}
})
// 拟免职务
const onDismissChange = (record: any, data: any) => {
	const { offPmsJobId, offProposedAppointJobSupple } = data
	record.offPmsJobId = offPmsJobId
	record.offProposedAppointJobSupple = offProposedAppointJobSupple
}

const onDropChange = (key: any, record: any, type: any) => {
	record.operation = key
}

// 拟任职务变化
const onAsaspireChange = (record: any, data: any) => {
	const { onPmsJobId, onProposedAppointJobSupple, orgId } = data
	record.onPmsJobId = onPmsJobId
	record.onProposedAppointJobSupple = onProposedAppointJobSupple
	record.orgId = orgId
}

const loadData = async () => {
	const res: any = await mockUserList(props.mockId + '')
	if (res.code === 0) {
		mockInfo.value = res.data.count

		console.log(mockInfo.value)

		datasource.value = res.data.list.map((item: any, index: number) => {
			return {
				...item,
				index: index + 1,
			}
		})
	}
}

loadData()
</script>

<style lang="less" scoped>
.text-component {
	font-size: 20px;
	font-weight: Bold;
	color: #008eff;
}
.text-bold {
	font-size: 20px;
	font-weight: Bold;
	color: #333333;
}
</style>

<style lang="less">
.user-list-modal {
	width: 1275px !important;
	height: 80vh;
	overflow: hidden;
	.ant-modal-content {
		height: 100%;
		.ant-modal-body {
			height: calc(100% - 54px);
			.modal-box {
				height: 100%;
				overflow-y: auto;

				.tips-box {
					padding: 23px 24px;
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: #333333;
					background: #f5f5f5;
				}
				.data-box {
					margin-top: 24px;
				}
			}
		}
	}
	.change-methods {
		display: flex;
	}
}
</style>
