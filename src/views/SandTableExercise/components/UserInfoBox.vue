<template>
	<div :class="{ 'userinfo-box': true, selected: selected, 'light-selected': lightSelected }">
		<div class="avatar">
			<img :src="avatar" />
		</div>
		<div class="info">
			<div class="name">
				<span>{{ userInfo.userName || userInfo.username }}</span>
				<span v-if="showConfirm" class="confirm-button" @click="onConfirm">确认配置</span>
			</div>
			<div class="birth">出生年月：{{ userInfo.birthday }}</div>
			<div class="index-box">
				<span>干部指数：{{ userInfo.cadreIndex }}</span>
				<span>序列排名：{{ userInfo.cadreIndexSort }}</span>
			</div>
			<div class="position-box">现任职务：{{ userInfo.currentJob }}</div>
		</div>
		<span class="delete" v-if="showDelete" @click="onDelete"></span>
		<span class="select-icon"></span>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { CDN_URL } from '@/config/env'
import avatarPng from '@/assets/images/avatar.png'

const props = defineProps({
	userInfo: {
		type: Object,
		default: () => ({}),
	},
	lightSelected: {
		type: Boolean,
		default: false,
	},
	selected: {
		type: Boolean,
		default: false,
	},
	showConfirm: {
		type: Boolean,
		default: false,
	},
	showDelete: {
		type: Boolean,
		default: false,
	},
})

const emits = defineEmits(['delete', 'confirm'])

const avatar = computed(() => {
	return props.userInfo.headUrl ? `${CDN_URL}/fr_img/${props.userInfo.headUrl}` : avatarPng
})
// 删除
const onDelete = () => {
	emits('delete', props.userInfo)
}
// 确认配置
const onConfirm = () => {
	emits('confirm', props.userInfo)
}
</script>

<style lang="less" scoped>
.light-selected {
	background-color: rgba(237, 247, 255, 0.9);
	.name,
	.birth,
	.index-box,
	.position-box {
		color: #008eff !important;
	}
}
.userinfo-box {
	position: relative;
	padding: 16px 18px;
	display: flex;
	width: 465px;
	height: 177px;
	background: #ffffff;
	box-shadow: 0px 0 11px 0px rgba(0, 0, 0, 0.06);
	border-radius: 3px 3px 3px 3px;
	opacity: 1;
	.delete {
		position: absolute;
		top: 0;
		right: 0;
		transform: translate(50%, -50%);
		display: inline-block;
		width: 24px;
		height: 24px;
		background: url('../images/delete-grey.png') center / cover no-repeat;
		cursor: pointer;
	}
	.avatar {
		flex-shrink: 0;
		margin-right: 11px;
		width: 89px;
		height: 99px;
		border-radius: 3px 3px 3px 3px;
		opacity: 1;
		img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
	}
	.info {
		.name {
			display: flex;
			align-items: center;
			width: 100%;
			font-size: 21px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			color: #000000;
			line-height: 25px;
			.confirm-button {
				margin-left: 30px;
				padding: 6px 6px;
				display: inline-block;
				font-size: 18px;
				line-height: 18px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
				background: #008eff;
				border-radius: 5px 5px 5px 5px;
				opacity: 1;
			}
		}
		.birth {
			margin-top: 5px;
			font-size: 17px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #000000;
			line-height: 19px;
		}
		.index-box {
			margin-top: 9px;
			font-size: 17px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #000000;
			line-height: 19px;
			span:not(:last-child) {
				margin-right: 24px;
			}
		}
		.position-box {
			margin-top: 6px;
			font-size: 17px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #000000;
			line-height: 22px;
			// 超过两行省略
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
	}
}
.selected {
	background-color: rgba(237, 247, 255, 0.9) !important;
	.name,
	.birth,
	.index-box,
	.position-box {
		color: #008eff !important;
	}

	.select-icon {
		position: absolute;
		top: 0;
		right: 0;
		display: inline-block;
		width: 24px;
		height: 18px;
		background: url('../images/select-icon.png') no-repeat center / cover;
		opacity: 1;
	}
}
</style>
