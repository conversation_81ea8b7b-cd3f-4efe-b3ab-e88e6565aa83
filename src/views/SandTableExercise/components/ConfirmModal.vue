<template>
	<a-modal class="confirm-modal" :visible="visible" title="确认调整方案" @cancel="onClose" :afterClose="onAfterClose">
		<div class="confirm-modal-content">
			<div class="before-box">
				<div class="title">新加入的调整干部</div>
				<div class="table-box">
					<PositionTable
						:org-id="orgId"
						:columns="columns"
						:cdnUrl="CDN_URL"
						:datasource="beforeDataSource"
						@dismissChange="onDismissChange"
						@aspireChange="onAsaspireChange"
						@drop-change="
							(keys, records) => {
								onDropChange(keys, records, 1)
							}
						"
					/>
				</div>
			</div>
			<div class="after-box">
				<div class="title">
					原班子待配干部 <label><a-checkbox v-model:value="check" />保存到待配名单后续调整</label>
				</div>
				<div class="table-box">
					<PositionTable
						:columns="columns"
						:cdnUrl="CDN_URL"
						:datasource="afterDataSource"
						@dismissChange="onDismissChange"
						@aspireChange="onAsaspireChange"
						@drop-change="
							(key, records) => {
								onDropChange(key, records, 2)
							}
						"
					/>
				</div>
			</div>
		</div>
		<template #footer>
			<a-button type="primary" @click="onConfirm">确认</a-button>
		</template>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance } from 'vue'
import { useSubmit } from '../mixins/event'
import PositionTable from './PositionTable.vue'
import VueLazyLoad from 'vue3-lazyload'
import { CDN_URL } from '@/config/env'
import useMock from '@/store/sandbox'
import { submitMock } from '@/apis/sand-table-exercise'
import { message } from 'ant-design-vue'
const app: any = getCurrentInstance()?.appContext.app

app.use?.(VueLazyLoad)

const visible = ref(true)

const emits = defineEmits(['close'])

const props = defineProps({
	// 上面新加入的
	beforeData: {
		type: Array,
		default: () => [],
	},
	// 原班子待配
	afterData: {
		type: Array,
		default: () => [],
	},
	adminId: Number,
	mockId: Number,
	mockName: String,
	orgId: Number, // 当前选中orgId
})

const mock = useMock()

const beforeDataSource = ref([...props.beforeData])
const afterDataSource = ref([...props.afterData])

const submit = useSubmit()

const check = ref(false)
const columns = [
	{
		key: 'headUrl',
		dataIndex: 'headUrl',
		align: 'center',
		width: '7%',
		title: '照片',
	},
	{
		key: 'userName',
		dataIndex: 'userName',
		align: 'center',
		width: '7%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},

	// {
	// 	key: 'portrait',
	// 	align: 'center',
	// 	width: '9%',
	// 	title: '干部画像',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'currentJob',
		dataIndex: 'currentJob',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'currentJobTime',
		dataIndex: 'currentJobTime',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initDegree',
		dataIndex: 'initDegree',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initSchool',
		dataIndex: 'initSchool',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'specialty',
	// 	align: 'center',
	// 	width: '12%',
	// 	title: '专业',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'cadreIndex',
		dataIndex: 'cadreIndex',
		align: 'center',
		width: '6%',
		title: '干部指数',
	},
	{
		key: 'cadreIndexSort',
		dataIndex: 'cadreIndexSort',
		align: 'center',
		width: '6%',
		title: '序列排名',
	},
	{
		key: 'operate',
		dataIndex: 'operate',
		align: 'center',
		width: '15%',
		title: '操作',
	},
	// {
	// 	key: 'achievement',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '业绩',
	// },
	// {
	// 	key: 'ability',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '能力',
	// },
	// {
	// 	key: 'praise',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '口碑',
	// },
	// {
	// 	key: 'politics',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '政治',
	// 	customization: 'CustomBlock',
	// },
]

const onClose = () => {
	visible.value = false
}

const onAfterClose = () => {
	emits('close')
}
// 调整弹窗点击确认
const onConfirm = async () => {
	visible.value = false

	const { mockId, mockName } = props
	const _data2 = afterDataSource.value
		.filter((item: any) => item.userId)
		.map((item: any) => {
			return {
				...item,
				// 保存到待配名单
				toMockFlag: check.value ? 1 : 0,
			}
		})

	const hasSelect: any = beforeDataSource.value.find((item: any) => {
		return item.operation === undefined
	})

	if (!!hasSelect) {
		return message.error(`${hasSelect.userName}未选择调整类型`)
	}

	const data = [...beforeDataSource.value, ..._data2]

	submit({ datasource: data, mockId, mockName })
}
// 拟任职务变化
const onAsaspireChange = (record: any, data: any) => {
	const { onPmsJobId, onProposedAppointJobSupple, orgId } = data
	record.onPmsJobId = onPmsJobId
	record.onProposedAppointJobSupple = onProposedAppointJobSupple
	record.orgId = orgId
}
// 拟免职务
const onDismissChange = (record: any, data: any) => {
	const { offPmsJobId, offProposedAppointJobSupple } = data
	record.offPmsJobId = offPmsJobId
	record.offProposedAppointJobSupple = offProposedAppointJobSupple
}
const onDropChange = (key: any, record: any, type: any) => {
	record.operation = key
}
</script>

<style lang="less">
.confirm-modal {
	width: 1646px !important;
	&-content {
		.title {
			display: flex;
			align-items: center;
			font-size: 24px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			color: #222222;
			line-height: 24px;
			&::before {
				margin-right: 12px;
				content: '';
				display: inline-block;
				width: 6px;
				height: 28px;
				background: url('../images/title-1.png') no-repeat center / cover;
			}
		}
		.before-box {
			.table-box {
				margin: 12px 0px 41px;
			}
		}
		.after-box {
			.title {
				display: flex;
				align-items: center;
				label {
					margin-left: 46px;
					font-weight: 400;
					font-size: 20px;
					color: rgba(0, 0, 0, 0.85);
					.ant-checkbox-inner {
						margin-right: 6px;
					}
				}
			}
			.table-box {
				margin: 12px 0px 0px;
			}
		}
	}
	.ant-modal-title {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 24px;
		color: #222222;
		line-height: 28px;
	}
	.ant-modal-body {
		padding: 18px 24px;
	}
	.ant-modal-footer {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 95px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;

		button {
			padding: 0px;
			width: 150px;
			height: 47px;
			background: #008eff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			font-size: 21px;
		}
	}
}
</style>
