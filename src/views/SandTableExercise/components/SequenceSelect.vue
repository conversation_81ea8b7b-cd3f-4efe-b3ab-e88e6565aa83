<template>
	<div class="sequence-select">
		<a-collapse expand-icon-position="right" ghost>
			<a-collapse-panel :header="item.name" v-for="item in orgList" :key="item.id">
				<div
					:class="`position-item ${selectKey === c.id ? 'position-item-select' : ''}`"
					v-for="c in item.children"
					:key="c.id"
					@click="onSelect(c.id)"
				>
					{{ c.name }}
					<span class="select" v-if="c.select"></span>
				</div>
			</a-collapse-panel>
		</a-collapse>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

defineProps({
	orgList: {
		type: Array,
		default: () => [],
	},
})
const emits = defineEmits(['change'])

const selectKey = ref()
// 数据列表
const dataList = ref<any>([
	{
		id: 1,
		name: '职务名称1',
		children: [
			{
				id: 1,
				name: '职务名称1',
				select: false,
			},
			{
				id: 2,
				name: '职务名称2',
				select: false,
			},
			{
				id: 3,
				name: '职务名称3',
				select: false,
			},
		],
	},
	{
		id: 2,
		name: '职务名称2',
		children: [
			{
				id: 9,
				name: '职务名称1',
			},
			{
				id: 4,
				name: '职务名称2',
			},
			{
				id: 5,
				name: '职务名称3',
			},
		],
	},
	{
		id: 3,
		name: '职务名称3',
		children: [
			{
				id: 6,
				name: '职务名称1',
			},
			{
				id: 7,
				name: '职务名称2',
			},
			{
				id: 8,
				name: '职务名称3',
			},
		],
	},
])

const onSelect = (key: any) => {
	selectKey.value = key
	emits('change', key)
}
</script>

<style lang="less" scoped>
.sequence-select {
	width: 369px;
	height: 100%;
	background: #f4faff;
	:deep(.ant-collapse-content-box) {
		padding-top: 0px !important;
	}
	.position-item {
		display: flex;
		justify-content: space-between;
		margin-top: 3px;
		font-size: 18px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #000000;
		line-height: 18px;
		padding: 9px 0px 9px 20px;
		cursor: pointer;
		transition: all linear 0.2s;
	}
	.position-item-select {
		font-size: 19px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #ffffff;
		background: #008eff;
		.select {
			display: inline-block;
			width: 23px;
			height: 23px;
			background: url('../images/select.png') center / cover no-repeat;
		}
	}
}
:deep(.ant-collapse-header) {
	display: flex;
	align-items: center !important;
	height: 47px;
	// background: #f4faff;
	font-size: 20px;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 500;
	color: #000000 !important;
	&::before {
		margin-right: 8px;
		content: '';
		display: inline-block;
		width: 9px;
		height: 9px;
		background: url('../images/rect.png') center / cover no-repeat;
	}
}
</style>
