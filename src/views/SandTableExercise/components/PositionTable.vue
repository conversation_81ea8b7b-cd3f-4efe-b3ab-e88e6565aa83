<template>
	<a-config-provider :locale="zhCN">
		<a-table
			rowKey="user_id"
			:columns="columns"
			:data-source="datasource"
			:scroll="{ y: tableScrollY }"
			:pagination="false"
			:loading="loading"
			v-bind="$attrs"
		>
			<template #bodyCell="{ column, record }">
				<template v-if="column.key === 'headUrl'">
					<img v-lazy="record.headUrl ? `${cdnUrl}/fr_img/${record.headUrl}` : defaultAvatar" alt="" class="avatar" />
				</template>
				<template v-if="column.key === 'userName'">
					<div class="user-box" v-if="mergeAvatar">
						<img v-lazy="record.headUrl ? `${cdnUrl}/fr_img/${record.headUrl}` : defaultAvatar" alt="" class="avatar" />
						<a @click="onLocation(record)">{{ record.userName }}</a>
					</div>
					<a @click="onLocation(record)" v-else>{{ record.userName }}</a>
				</template>
				<template v-if="column.key === 'initSchool'">
					<span>{{ `${record.initSchool || ''}${record.specialty || ''}` }}</span>
				</template>
				<template v-if="column.key === 'operate'">
					<template v-if="record.userId">
						<!-- <a-dropdown :trigger="['click']">
							<a class="ant-dropdown-link" @click.prevent>
								{{ record['operation'] ? getDropText(record['operation']) : '请选择' }}
								<DownOutlined />
							</a>
							<template #overlay>
								<a-menu>
									<a-menu-item v-for="item in dropList" :key="item.key" @click="onDropSelect(item.key, record)">
										<a>{{ item.label }}</a>
									</a-menu-item>
								</a-menu>
							</template>
						</a-dropdown> -->
						<a class="ant-dropdown-link" @click="onPositionTypeDrawer(record)">
							{{ record['operation'] ? getDropText(record['operation']) : '请选择' }}
						</a>
						<a class="ant-dropdown-link" @click="onAspringApp(record)"> 拟任职务 </a>
						<a class="ant-dropdown-link" @click="onDismissApp(record)"> 拟免职务 </a>
					</template>
					<template v-else> </template>
				</template>
				<template v-if="column.key === 'operation'">
					<span>{{ statusMap.get(record.operation) }}</span>
				</template>
			</template>
		</a-table>
	</a-config-provider>
</template>

<script lang="ts" setup>
import zhCN from 'ant-design-vue/es/locale/zh_CN'

import defaultAvatar from '@/assets/images/avatar.png'
import { DownOutlined } from '@ant-design/icons-vue'
import { dismissDrawer, aspiringDrawer, positionTypeDrawer } from './Drawer'
import useMock from '@/store/sandbox'
// const dismissApp: any = createApp(DismissalPosition)

const props = defineProps({
	columns: {
		type: Array,
		default: () => [],
	},
	datasource: {
		type: Array,
		default: () => [],
	},
	cdnUrl: String,
	onLocation: {
		type: Function,
		default: () => ({}),
	},
	tableScrollY: {
		type: Number,
	},
	loading: {
		type: Boolean,
		default: false,
	},
	mergeAvatar: {
		type: Boolean,
		default: false,
	},
	orgId: {
		type: Number || String,
		default: '',
	},
	mockId: {
		type: Number || String,
		default: '',
	},
})

const emits = defineEmits(['dropChange', 'aspireChange', 'dismissChange'])

const mock = useMock()

const dropList = [
	{
		label: '提拔',
		key: '0',
	},
	{
		label: '进一步使用',
		key: '1',
	},
	{
		label: '交流',
		key: '2',
	},
	{
		label: '试用期满转正',
		key: '3',
	},
	{
		label: '任免兼挂职',
		key: '4',
	},
	{
		label: '不再担任领导职务',
		key: '5',
	},
	{
		label: '其他',
		key: '-1',
	},
]

const statusMap = new Map([
	[-1, '其他'],
	[0, '提拔'],
	[1, '进一步使用'],
	[2, '交流'],
	[3, '试用期满转正'],
	[4, '任免兼挂职'],
	[5, '不再担任领导职务'],
])

const getDropText = (key: any) => {
	return dropList.find((item) => item.key === key)?.label
}
const onDismissApp = (record: any) => {
	dismissDrawer.openDrawer({
		user_id: record.userId,
		orgId: record.orgId,
		mockId: mock.mockId,
		onConfirm: (data: any) => {
			emits('dismissChange', record, data)
		},
	})
}
const onAspringApp = (record: any) => {
	aspiringDrawer.openDrawer({
		user_id: record.userId,
		user: record,
		orgId: props.orgId,
		onConfirm: (data: any) => {
			emits('aspireChange', record, data)
		},
	})
}

const onPositionTypeDrawer = (record: any) => {
	positionTypeDrawer.openDrawer({
		type: record.operation,
		onConfirm: (data: any) => {
			emits('dropChange', data, record)
		},
	})
}
//
const onDropSelect = (key: any, record: any) => {
	emits('dropChange', key, record)
}
</script>

<style lang="less" scoped>
.avatar {
	width: 53px;
	height: 66px;
	background: #c4c4c4;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	object-fit: contain;
}
.user-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	.avatar {
		margin-bottom: 3px;
	}
}
:deep(.ant-table) {
	.ant-table-header,
	.ant-table-body {
		.ant-table-cell {
			font-size: 18px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #3d3d3d;
		}
	}
}
.ant-dropdown-link {
	&:not(:last-child) {
		margin-right: 10px;
	}
}
</style>
