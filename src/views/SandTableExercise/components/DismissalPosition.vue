<template>
	<a-drawer
		class="dismissal-position"
		title="拟免职务"
		:visible="visible"
		@close="onClose"
		@afterVisibleChange="afterVisibleChange"
		:closable="false"
		size="default"
	>
		<div class="inner">
			<div class="tips">职务预览：{{ positionPreview }}</div>
			<div class="position-box">
				<div class="label">现任职务：</div>
				<div class="list">
					<div class="position-item" v-for="item in dataList" :key="item.pmsJobId">
						<div class="position-name">{{ item.jobName }}</div>
						<div class="delete" @click="onDelete(item.pmsJobId)"></div>
					</div>
				</div>
			</div>
			<div class="input-box">
				<div class="label">其他：</div>
				<a-input class="position-input" v-model:value="offProposedAppointJobSupple" placeholder="请输入" />
			</div>
		</div>

		<template #footer>
			<div class="footer">
				<a-button type="primary" @click="onConfirm">确认</a-button>
			</div>
		</template>
	</a-drawer>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { userJob } from '@/apis/sand-table-exercise'
const props = defineProps({
	user_id: {
		type: String,
	},
	mockId: {
		type: Number,
	},
	user: {
		type: Object,
		default: () => ({}),
	},
})
type JobInfo = {
	pmsJobId?: number
	jobName?: string
	orgId?: number
}
const mockJobInfoList: JobInfo[] = Array.from({ length: 10 }, (_, index) => ({
	pmsJobId: index % 2 === 0 ? undefined : 1001 + index, // Set pmsJobId to undefined for every other entry
	jobName: `示例职务${index + 1}`,
	orgId: index % 3 === 0 ? undefined : 201 + index, // Set orgId to undefined for every third entry
}))

const emits = defineEmits(['close', 'confirm'])

const visible = ref(true)
// 数据列表
const dataList = ref<JobInfo[]>([])
// 免除职务id
const offPmsJobId = ref<any>([])
// 免除职务文本
const offProposedAppointJobSupple = ref<any>(props.user.offProposedAppointJobSupple)
//
const positionPreview = computed(() => {
	const data = dataList.value.map((item) => item.jobName)

	offProposedAppointJobSupple.value && data.push(offProposedAppointJobSupple.value)

	return data.join(',')
})
// 关闭
const onClose = () => {
	visible.value = false
}
// 删除
const onDelete = (id: any) => {
	const index = dataList.value.findIndex((item: any) => item.id === id)
	console.log('🚀 ~ file: DismissalPosition.vue:64 ~ onDelete ~ index:', index)

	dataList.value.splice(index, 1)

	offPmsJobId.value.push(id)
}
// 确认
const onConfirm = () => {
	visible.value = false
	emits('confirm', { offPmsJobId: offPmsJobId.value.join(','), offProposedAppointJobSupple: offProposedAppointJobSupple.value })
}
// 过度结束后触发
const afterVisibleChange = (visible: boolean) => {
	visible || emits('close')
}

const loadData = async () => {
	const res: any = await userJob(props.user_id as string, props.mockId as any)

	if (res.code === 0) {
		// 根据pmsJobId 过滤出对应的数据
		const jobList: any = []
		const jobInfo: any = []

		res.data.map((item: any) => {
			if (item.pmsJobId) {
				jobList.push(item)
			} else {
				jobInfo.push(item)
			}
		})
		dataList.value = jobList

		offProposedAppointJobSupple.value = offProposedAppointJobSupple.value || jobInfo.map((item: any) => item.jobName).join(',')
	}
}

loadData()
</script>

<style lang="less">
.dismissal-position {
	.ant-drawer-header {
		height: 85px;
		.ant-drawer-title {
			font-size: 24px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #222222;
		}
	}

	.ant-drawer-body {
		padding: 0;
	}
	.inner {
		.tips {
			padding: 13px 21px;
			width: 100%;
			background: #e4f3ff;
			border-radius: 2px 2px 2px 2px;
			opacity: 1;
			line-height: 20px;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #008eff;
		}
		.position-box {
			padding: 18px;
			.label {
				font-size: 18px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.95);
				line-height: 21px;
			}
			.list {
				margin-top: 12px;
				.position-item {
					padding: 10px 0px;
					display: flex;
					justify-content: space-between;
					.position-name {
						font-size: 18px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
					}
					.delete {
						margin-right: 6px;
						flex-shrink: 0;
						width: 20px;
						height: 20px;
						background: url('../images/position-delete.png') center / cover no-repeat;
						cursor: pointer;
					}
				}
			}
		}
		.input-box {
			padding: 18px;
			.position-input {
				margin-top: 12px;
				height: 47px;
			}
		}
	}
	.footer {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 81px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		button {
			padding: 0px;
			width: 150px;
			height: 47px;
			background: #008eff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
		}
	}
	.ant-drawer-footer {
		border-top-width: 12px;
	}
}
</style>
