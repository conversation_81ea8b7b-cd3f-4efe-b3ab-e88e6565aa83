<template>
	<a-config-provider :locale="zhCN">
		<a-modal class="structure-modal" width="" :visible="visible" @cancel="onClose" :afterClose="onAfterClose">
			<StructureTable :data="data"
		/></a-modal>
	</a-config-provider>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { getTeamStructure } from '@/apis/sand-table-exercise'
import { convertCamelToUnderscore } from '@/utils/utils'

import StructureTable from '@/components/StructureTable.vue'

// import { } from "@/apis/sand-table-exercise"
const emits = defineEmits(['close'])

interface Standards {
	standards: number
	case: any[] // 根据实际情况定义配备情况的类型
}

interface UserVo {
	name: string
	currentJob: string
	age: string
	gender: string
	educational: string
	major: string
	experience: string
	cadreIndex: number
	cadreIndexRank: string
	withdraw: string
}

interface OrgStructure {
	ageStandards: number
	ageCase: Standards[]
	genderStandards: number
	genderCase: Standards[]
	educationalStandards: number
	educationalCase: Standards[]
	majorStandards: number
	majorCase: Standards[]
	experienceStandards: number
	experienceCase: Standards[]
	cadreIndexStandards: number
	cadreIndexCase: Standards[]
	withdrawStandards: number
	withdrawCase: Standards[]
	users: UserVo[]
}

const props = defineProps({
	orgId: {
		type: String,
	},
	mockId: {
		type: String,
	},
})
const data = ref([])

const visible = ref(true)

const onClose = () => {
	visible.value = false
}

const onAfterClose = () => {
	emits('close')
}

const loadData = async () => {
	const _res: any = await getTeamStructure(props.orgId, props.mockId)

	if (_res.code === 0) {
		console.log(_res)
		console.log('🚀 ~ file: StructureTableModal.vue:81 ~ loadData ~ _res:', _res)
		const res = convertCamelToUnderscore(_res.data)

		const allocationInfo = {
			name: '配备情况',
			age: res?.age_case,
			gender: res?.gender_case,
			educational: res?.educational_case,
			major: res?.major_case,
			experience: res?.experience_case,
			cadre_index: res?.cadre_index_case,
			withdraw: res?.withdraw_case,
		}
		const standardsInfo = {
			name: '是否达标',
			age: res?.age_standards,
			gender: res?.gender_standards ? '是' : '否',
			educational: res?.educational_standards,
			major: res?.major_standards,
			experience: res?.experience_standards,
			cadre_index: res?.cadre_index_standards,
			withdraw: res?.withdraw_standards,
		}
		console.log('🚀 ~ file: StructureTableModal.vue:103 ~ loadData ~ res:', res)
		console.log('🚀 ~ file: StructureTableModal.vue:105 ~ loadData ~ res.users:', res.users)
		res.users.unshift(allocationInfo, standardsInfo)
		data.value = res.users
	}
}

loadData()
</script>

<style lang="less">
.structure-modal {
	width: 1689px !important;
}
</style>
