<template>
	<div class="select-menu">
		<div :class="`tab-item ${item.key === tabActiveKey ? 'tab-item-select' : ''}`" v-for="item in menu" :key="item.key" @click="onSelect(item.key)">
			{{ item.label }}
		</div>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	menu: {
		type: Array<{ label: string; key: number }>,
		default: () => [],
	},
	tabActiveKey: {
		type: Number,
	},
})

const emits = defineEmits(['change'])

// 选中
const onSelect = (key: any) => {
	emits('change', key)
}
</script>

<style lang="less" scoped>
.select-menu {
	margin-bottom: 20px;
	display: flex;
	gap: 9px;
	.tab-item {
		width: 117px;
		height: 42px;
		line-height: 42px;
		text-align: center;
		background: #ececec;
		border-radius: 3px 3px 3px 3px;
		opacity: 1;

		font-size: 21px;
		font-family: Source <PERSON>, Source <PERSON>N;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.65);

		cursor: pointer;
	}
	.tab-item-select {
		color: #ffffff;
		background-color: #008eff;
	}
}
</style>
