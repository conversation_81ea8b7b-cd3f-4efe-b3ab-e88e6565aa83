<template>
	<a-modal class="save-plan-modal" title="保存方案" width="" :visible="visible" @cancel="onClose" :afterClose="onAfterClose" :footer="null">
		<div class="inner-box">
			<a-form>
				<a-form-item label="方案名称" :rules="[{ required: true }]">
					<a-input v-model:value="name" placeholder="请输入" />
				</a-form-item>
			</a-form>
			<div class="button-box">
				<a-button type="primary" @click="onSubmit">提交</a-button>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { ref } from 'vue'

const emits = defineEmits(['close'])

const props = defineProps({
	submit: {
		type: Function,
		default: (value: string, close: () => void) => void 0,
	},
	name: {
		type: String,
		default: '',
	},
})

const name = ref(props.name)
const visible = ref(true)

const onClose = () => {
	visible.value = false
}

const onSubmit = async () => {
	if (!name.value) {
		return message.warn('请输入值')
	}

	await props.submit(name.value, onClose)
}

const onAfterClose = () => {
	emits('close')
}
</script>

<style lang="less">
.save-plan-modal {
	width: 708px !important;
	:deep(.ant-modal-title) {
		font-size: 24px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		color: #333333;
		line-height: 28px;
	}
	.inner-box {
		width: 100%;
		height: 100%;

		input {
			height: 54px;
		}
		.button-box {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 122px;

			button {
				padding: 0px;
				width: 150px;
				height: 54px;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
			}
		}
	}
}
</style>
