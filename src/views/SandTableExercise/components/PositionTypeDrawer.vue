<template>
	<a-drawer
		class="position-type-drawer"
		title="调整类别"
		:visible="visible"
		@close="onClose"
		@afterVisibleChange="afterVisibleChange"
		:closable="false"
		:footer-style="{ textAlign: 'center' }"
		size="default"
	>
		<div class="select-list">
			<div class="select-item" :class="{ active: item.key === currentKey }" v-for="item in dropList" :key="item.key" @click="onSelect(item.key)">
				{{ item.label }}
			</div>
		</div>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">取消</a-button>
			<a-button type="primary" @click="onConfirm">确认</a-button>
		</template>
	</a-drawer>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from 'vue'

const props = defineProps({
	type: String || Number,
})

const emits = defineEmits(['close', 'confirm'])

const visible = ref(true)

const currentKey: any = ref(undefined)

const dropList = [
	{
		label: '提拔',
		key: '0',
	},
	{
		label: '进一步使用',
		key: '1',
	},
	{
		label: '交流',
		key: '2',
	},
	{
		label: '试用期满转正',
		key: '3',
	},
	{
		label: '任免兼挂职',
		key: '4',
	},
	{
		label: '不再担任领导职务',
		key: '5',
	},
	{
		label: '其他',
		key: '-1',
	},
]
// 关闭
const onClose = () => {
	visible.value = false
}

const onSelect = (key: any) => {
	currentKey.value = key
}

// 过度结束后触发
const afterVisibleChange = (visible: boolean) => {
	visible || emits('close')
}

const onConfirm = () => {
	emits('confirm', currentKey.value)

	onClose()
}

watchEffect(() => {
	currentKey.value = props.type
})
</script>

<style lang="less">
.position-type-drawer {
	.select-list {
		.select-item {
			margin-top: 15px;
			font-size: 23px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.85);

			display: flex;
			align-items: center;
			justify-content: center;
			height: 61px;
			background: #f9f9f9;
			cursor: pointer;
			&:not(1) {
				margin-top: 15px;
			}
		}
		.active {
			color: #008eff;
			background: #edf7ff;
		}
	}
	button {
		width: 150px;
		height: 47px;
	}
}
</style>
