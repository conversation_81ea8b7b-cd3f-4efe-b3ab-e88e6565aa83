<template>
	<row-table :columns="columns" :datasource="tableData">
		<template #position_should="{ data }">
			<div>应配：{{ data.jobNum }}人 &nbsp; 缺配：{{ data.jobVacancy }}人</div>
		</template>
		<template #age="{ value }">
			<div class="age-col">
				<div class="age-item" v-for="(item, index) in value" :key="index">
					<span :class="`${item.standard ? 'status-success' : 'status-pre-warn'}`"></span>
					<span class="age-item-title">{{ item.info }}</span>
					<!-- <span class="age-item-value">{{ value }}人</span> -->
				</div>
				<!-- <div class="age-item">
								<span class="status-success"></span>
								<span class="age-item-title">35岁以下：</span>
								<span class="age-item-value">{{ value }}人</span>
							</div>
							<div class="age-item">
								<span class="status-pre-warn"></span>
								<span class="age-item-title">35~45岁：</span>
								<span class="age-item-value">{{ value }}人</span>
							</div>
							<div class="age-item">
								<span class="status-success"></span>
								<span class="age-item-title">50岁以上：</span>
								<span class="age-item-value">{{ value }}人</span>
							</div> -->
			</div>
		</template>
		<template #gender="{ value }">
			<div class="age-col">
				<div class="block-common-style" v-for="(item, index) in value" :key="index">
					<span :class="`${item.standard ? 'status-success' : 'status-pre-warn'}`"></span>

					<span class="age-item-title">{{ item.info }}</span>
					<!-- <span class="age-item-title">女干部：</span>
								<span class="age-item-value">{{ value }}人</span> -->
				</div>
			</div>
		</template>
		<template #educational="{ value }">
			<div class="age-col">
				<div class="block-common-style" v-for="(item, index) in value" :key="index">
					<span :class="`${item.standard ? 'status-success' : 'status-pre-warn'}`"></span>

					<span class="age-item-title">{{ item.info }}</span>
					<!-- <span class="age-item-title">（初始学历）大学本科及以上：</span> -->
					<!-- <span class="age-item-value">{{ value }}人</span> -->
				</div>
			</div>
		</template>
		<template #specialty="{ value }">
			<div class="age-col">
				<div class="block-common-style" v-for="(item, index) in value" :key="index">
					<span :class="`${item.standard ? 'status-success' : 'status-pre-warn'}`"></span>

					<span class="age-item-title">{{ item.info }}</span>
					<!-- <span class="age-item-title">农业产业：1人，建设规划：</span>
								<span class="age-item-value">{{ value }}人</span> -->
				</div>
				<!-- <div class="block-common-style" v-for="(item, index) in value" :key="index">
								<span :class="`${item.standard ? 'status-success' : 'status-pre-warn'}`" ></span>
								<span class="age-item-title">{{ item.info }}</span>
								<span class="age-item-title">书记专业：农学，镇长专业：</span>
								<span class="age-item-value">经济</span>
							</div> -->
			</div>
		</template>
		<template #experience="{ value }">
			<div class="age-col">
				<div class="block-common-style" v-for="(item, index) in value" :key="index">
					<span :class="`${item.standard ? 'status-success' : 'status-pre-warn'}`"></span>
					<span class="age-item-title">{{ item.info }}</span>
					<!-- <span class="age-item-title">班子成员具有2年乡镇领导工作经历或者3年乡镇工作经历:</span> -->
					<!-- <span class="age-item-value">{{ value }}人，占比47.5%</span> -->
				</div>
				<!-- <div class="block-common-style">
								<span class="status-success"></span>
								<span class="age-item-title">同一职务任职超过5年：0人, 同一单位工作超过10年：0人</span>
								<span class="age-item-value"></span>
							</div> -->
			</div>
		</template>
		<template #cadreIndex="{ value }">
			<div class="age-col">
				<div class="block-common-style" v-for="(item, index) in value" :key="index">
					<span :class="`${item.standard ? 'status-success' : 'status-pre-warn'}`"></span>
					<span class="age-item-title">{{ item.info }}</span>
					<!-- <span class="age-item-title">"班子成员干部指数在序列内排名后30%：</span> -->
					<!-- <span class="age-item-value">{{ value }}人</span> -->
				</div>
			</div>
		</template>
		<template #withdraw="{ value }">
			<div class="age-col">
				<div class="block-common-style" v-for="(item, index) in value" :key="index">
					<span :class="`${item.standard ? 'status-success' : 'status-pre-warn'}`"></span>
					<span class="age-item-title">{{ item.info }}</span>
					<!-- <span class="age-item-title">"主要社会关系成员在同一单位任职：</span> -->
					<!-- <span class="age-item-value">{{ value }}人</span> -->
				</div>
			</div>
		</template>
	</row-table>
</template>

<script lang="ts" setup>
defineProps({
	columns: {
		type: Array,
		default() {
			return []
		},
	},
	tableData: {
		type: Array,
		default() {
			return []
		},
	},
	type: {
		type: String,
		default: '',
	},
})
</script>

<style lang="less" scoped>
:deep(.table-title-gray) {
	background-color: #f5f5f5;
}
.full-container {
	width: 100%;
	height: 100%;
}
.block-common-style {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
.age-col {
	width: 100%;
	height: 100%;
	.age-item {
		.block-common-style;
	}
}

.status {
	margin-right: 9px;
	width: 18px;
	height: 18px;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	background: no-repeat center / cover;
}
.status-success {
	.status;
	background-image: url('../images/success-green.png');
}
.status-pre-warn {
	.status;
	background-image: url('../images/pre-warn.png');
}
</style>
