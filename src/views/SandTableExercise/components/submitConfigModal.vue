<template>
	<a-modal class="submit-config-modal" title="确认配置" width="" :visible="visible" @cancel="onClose" :afterClose="onAfterClose" :footer="null">
		<div class="inner-box">
			<a-form>
				<a-form-item label="调任方式" :rules="[{ required: true }]">
					<!-- <a-select ref="select" v-model:value="config.operation" style="width: 120px">
						<a-select-option :value="item.key" v-for="item in dropList" :key="item.key">{{ item.label }}</a-select-option>
					</a-select> -->
					<a-button type="link" @click="onPositionTypeModal"> {{ getTypeName(config.operation) || '请选择' }}</a-button>
				</a-form-item>
				<a-form-item label="拟任职务" :rules="[{ required: true }]">
					<div class="position-box">
						<div class="position">{{ job.jobName }}</div>
						<a-input placeholder="输入其他任职信息" v-model:value="config.onProposedAppointJobSupple" />
					</div>
				</a-form-item>
				<a-form-item label="拟免职务" :rules="[{ required: true }]">
					<div class="position-box">
						<div class="position">
							<div class="position-item" v-for="(item, index) in disData.orgJob" :key="index">
								{{ item.jobName }}
								<span @click="onDelete(item)" class="delete-icon"></span>
							</div>
						</div>
						<a-input placeholder="原职务中手动输入的职务文本" v-model:value="config.offProposedAppointJobSupple" />
					</div>
					<!-- <a-input v-model:value="name" placeholder="请输入" /> -->
				</a-form-item>
			</a-form>
			<div class="button-box">
				<a-button type="primary" @click="onSubmit">确认</a-button>
			</div>
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { ref } from 'vue'
import { userJob } from '@/apis/sand-table-exercise'
import { useSubmit } from '../mixins/event'
import { positionTypeDrawer } from './Drawer'

const emits = defineEmits(['close'])

const props = defineProps({
	user: {
		type: Object,
		default: () => ({}),
	},
	job: {
		type: Object,
		default: () => ({}),
	},
	mockId: {
		type: Number,
		default: 0,
	},
	callBack: {
		type: Function,
	},
})

const submit = useSubmit()

const name = ref('')

const config = ref({
	operation: '',
	onProposedAppointJobSupple: '',
	offProposedAppointJobSupple: '',
	offPmsJobId: '',
})
//拟免职务
const disData: any = ref({
	orgJob: [],
	textJob: '',
})

const visible = ref(true)

const dropList = [
	{
		label: '提拔',
		key: '0',
	},
	{
		label: '进一步使用',
		key: '1',
	},
	{
		label: '交流',
		key: '2',
	},
	{
		label: '试用期满转正',
		key: '3',
	},
	{
		label: '任免兼挂职',
		key: '4',
	},
	{
		label: '不再担任领导职务',
		key: '5',
	},
	{
		label: '其他',
		key: '-1',
	},
]

const getTypeName = (key: any) => {
	return dropList.find((item: any) => item.key === key)?.label
}

const onClose = () => {
	visible.value = false
}

const onSubmit = async () => {
	if (config.value.operation === '') {
		message.error('请选择调任方式')
		return
	}

	await submit({
		datasource: [{ ...config.value, userId: props.user.userId, onPmsJobId: props.job.pmsJobId }],
		mockId: props.mockId,
		callback: () => {
			// loadDisData()
			props.callBack?.()

			visible.value = false
		},
	})
}
//
const onAfterClose = () => {
	emits('close')
}

// 加载拟免职务
const loadDisData = async () => {
	const res: any = await userJob(props.user.userId)
	if (res.code === 0) {
		const data = res.data
		data.map((item: any) => {
			if (item.pmsJobId) {
				disData.value.orgJob.push(item)
			} else {
				config.value.offProposedAppointJobSupple = item.jobName
			}
		})
	}
}

const onPositionTypeModal = () => {
	positionTypeDrawer.openDrawer({
		type: config.value.operation,
		onConfirm(key: any) {
			config.value.operation = key
		},
	})
}
// 删除
const onDelete = (job: any) => {
	disData.value.orgJob = disData.value.orgJob.filter((item: any) => item.jobId !== job.jobId)

	config.value.offPmsJobId = job.pmsJobId
}

loadDisData()
</script>

<style lang="less">
.submit-config-modal {
	width: 708px !important;
	:deep(.ant-modal-title) {
		font-size: 24px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		color: #333333;
		line-height: 28px;
	}
	.position-box {
		.position-item {
			margin-bottom: 15px;
			display: flex;
			align-items: center;
			font-size: 18px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #008eff;
			line-height: 21px;
			.delete-icon {
				display: inline-block;
				width: 18px;
				height: 18px;
				background: url('../images/delete-blue.png') center / cover no-repeat;
			}
		}
	}
	.inner-box {
		width: 100%;
		height: 100%;

		input {
			height: 54px;
		}
		.button-box {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 122px;

			button {
				padding: 0px;
				width: 150px;
				height: 54px;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
			}
		}
	}
}
</style>
