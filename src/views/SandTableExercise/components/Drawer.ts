import { createApp } from 'vue'
import AspiringPositionVue from './AspiringPosition.vue'
import DismissalPositionVue from './DismissalPosition.vue'
import UserListModalVue from './UserListModal.vue'
import ConfirmModalVue from './ConfirmModal.vue'
import StructureTableModalVue from './StructureTableModal.vue'
import CompareModalVue from './CompareModal.vue'
import SavePlanModalVue from './SavePlanModal.vue'
import SelectUserDrawerVue from './SelectUserDrawer.vue'
import submitConfigModalVue from './submitConfigModal.vue'
import PositionTypeDrawerVue from './PositionTypeDrawer.vue'

const mountComponent = (component: any, props = {}) => {
	let dismissEle: any = document.createElement('div')

	let app: any = createApp(component, {
		onClose: () => {
			app.unmount()
			dismissEle.remove()

			app = null
			dismissEle = null
		},
		...props,
	})

	app.mount(dismissEle)
}
/**
 * @description: 拟免职务抽屉
 * @return {*}
 */
const dismissDrawer = {
	openDrawer(props?: any) {
		mountComponent(DismissalPositionVue, props)
	},
}
/**
 * @description: 拟任职务抽屉
 * @return {*}
 */
const aspiringDrawer = {
	openDrawer(props?: any) {
		mountComponent(AspiringPositionVue, props)
	},
}
/**
 * @description: 人员调整名单列表
 * @return {*}
 */
const userlistModal = {
	openModal(props?: any) {
		mountComponent(UserListModalVue, props)
	},
}
/**
 * @description: 确认调整方案
 * @return {*}
 */
const confirmModal = {
	openModal(props?: any) {
		mountComponent(ConfirmModalVue, props)
	},
}
/**
 * @description: 班子结构分析表
 * @return {*}
 */
const structTableModal = {
	openModal(props?: any) {
		mountComponent(StructureTableModalVue, props)
	},
}
/**
 * @description: 结构对比modal
 * @return {*}
 */
const compareModal = {
	openModal(props?: any) {
		mountComponent(CompareModalVue, props)
	},
}
/**
 * @description: 保存方案modal
 * @return {*}
 */
const savePlanModal = {
	openModal(props: any) {
		mountComponent(SavePlanModalVue, props)
	},
}
const selectUserDrawer = {
	openDrawer(props?: any) {
		mountComponent(SelectUserDrawerVue, props)
	},
}
const submitConfigModal = {
	openDrawer(props?: any) {
		mountComponent(submitConfigModalVue, props)
	},
}
const positionTypeDrawer = {
	openDrawer(props?: any) {
		mountComponent(PositionTypeDrawerVue, props)
	},
}
export {
	dismissDrawer,
	aspiringDrawer,
	userlistModal,
	confirmModal,
	structTableModal,
	compareModal,
	savePlanModal,
	selectUserDrawer,
	submitConfigModal,
	positionTypeDrawer,
}
