<template>
	<a-drawer
		class="aspiring-position"
		title="拟任职务"
		:visible="visible"
		@close="onClose"
		@afterVisibleChange="afterVisibleChange"
		:closable="false"
		size="default"
	>
		<div class="inner">
			<div class="tips">职务预览：{{ selectJob.jobName }} &nbsp; {{ otherPosition }}</div>
			<div class="search-box">
				<a-input placeholder="请输入关键字" v-model:value="searchText">
					<template #prefix>
						<span class="search-icon"></span>
					</template>
					<template #suffix>
						<span class="search-text" @click="onSearch">搜索</span>
					</template>
				</a-input>
			</div>
			<div class="position-box">
				<a-collapse expand-icon-position="right" ghost>
					<a-collapse-panel :header="item.orgName" v-for="item in dataList" :key="item.orgId">
						<div
							v-for="c in item.jobs"
							:class="`position-item ${selectJob.pmsJobId === c.pmsJobId ? 'position-item-select' : ''}`"
							:key="c.pmsJobId"
							@click="onPositionClick(c, item.orgId)"
						>
							{{ c.jobName }}
							<span class="select" v-if="selectJob.pmsJobId === c.pmsJobId"></span>
						</div>
					</a-collapse-panel>
				</a-collapse>
			</div>
			<div class="input-box">
				<div class="label">其他：</div>
				<a-input class="position-input" v-model:value="otherPosition" placeholder="请输入" />
			</div>
		</div>

		<template #footer>
			<div class="footer">
				<a-button type="primary" @click="onConfirm">确认</a-button>
			</div>
		</template>
	</a-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { jobList } from '@/apis/sand-table-exercise'

type JobInfo = {
	pmsJobId: number
	jobName: string
	select?: boolean
}

type OrgData = {
	orgId: number
	orgName: string
	jobs: JobInfo[]
}

const emits = defineEmits(['close', 'confirm'])

const props = defineProps({
	orgId: {
		type: Number,
	},
	selectJobList: {
		type: Array,
	},
	user: {
		type: Object,
		default: () => ({}),
	},
})

const visible = ref(true)

const otherPosition = ref(props.user.onProposedAppointJobSupple)
// 选中
const selectJob = ref<any>({
	pmsJobId: props.user.onPmsJobId,
	jobName: props.user.onProposedAppointJobSupple,
})
// 搜索框文本
const searchText = ref('')
// 其他
const otherText = ref('')

// Generate ten mock data entries
const mockOrgDataList: OrgData[] = Array.from({ length: 10 }, (_, index) => ({
	orgId: index + 1,
	orgName: `示例机构${index + 1}`,
	jobs: [
		{
			pmsJobId: 1001 + index + (Math.random() * 10).toFixed(5),
			jobName: `领导岗位${index + 1}`,
		},
		{
			pmsJobId: 1002 + index + (Math.random() * 10).toFixed(5),
			jobName: `管理岗位${index + 1}`,
		},
		{
			pmsJobId: 1003 + index + (Math.random() * 10).toFixed(5),
			jobName: `执行岗位${index + 1}`,
		},
	],
}))
// 数据列表
const dataList = ref<OrgData[]>([])
// 关闭
const onClose = () => {
	visible.value = false
}
// 删除
const onDelete = (id: any) => {
	const index = dataList.value.findIndex((item: any) => item.id === id)
	console.log('🚀 ~ file: DismissalPosition.vue:64 ~ onDelete ~ index:', index)

	dataList.value.splice(index, 1)
}
// 点击搜索
const onSearch = () => {
	loadData()
}
// 确认
const onConfirm = () => {
	visible.value = false
	emits('confirm', {
		onProposedAppointJobSupple: otherPosition.value,
		onPmsJobId: selectJob.value.pmsJobId,
		orgId: selectJob.value.orgId,
	})
}
const onPositionClick = (job: any, orgId: any) => {
	selectJob.value = { ...job, orgId }
}
// 过度结束后触发
const afterVisibleChange = (visible: boolean) => {
	visible || emits('close')
}

// 获取职务列表
const loadData = async () => {
	const res: any = await jobList(props.orgId as any, searchText.value)
	if (res.code === 0) {
		dataList.value = res.data
	}
}

loadData()
</script>

<style lang="less">
.aspiring-position {
	.ant-drawer-header {
		height: 85px;
		.ant-drawer-title {
			font-size: 24px;
			line-height: 24px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #222222;
		}
	}

	.ant-drawer-body {
		padding: 0;
	}
	.inner {
		.tips {
			padding: 13px 21px;
			width: 100%;
			background: #e4f3ff;
			border-radius: 2px 2px 2px 2px;
			opacity: 1;
			line-height: 20px;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #008eff;
		}
		.position-box {
			padding: 18px;
			.ant-collapse-content-box {
				padding-top: 0px;
			}
			.position-item {
				display: flex;
				justify-content: space-between;
				margin-top: 6px;
				font-size: 20px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #000000;
				line-height: 23px;
				padding: 9px 0px 9px 50px;
				cursor: pointer;
				transition: all linear 0.2s;
			}
			.position-item-select {
				font-size: 20px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #008eff;
				.select {
					display: inline-block;
					width: 23px;
					height: 23px;
					background: url('../images/select.png') center / cover no-repeat;
				}
			}
		}
		.search-box {
			margin-top: 30px;
			padding: 0px 18px;
			.ant-input-affix-wrapper {
				height: 47px;
			}
			.search-icon {
				width: 24px;
				height: 24px;
				background: url(../images/search.png) center / cover no-repeat;
			}
			input {
				font-size: 18px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.25);
			}
			.search-text {
				height: 20px;
				padding-left: 20px;
				font-size: 18px;
				line-height: 20px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #008eff;
				cursor: pointer;
				border-left: 1px solid #d9d9d9;
			}
		}
		.input-box {
			padding: 18px;

			.label {
				font-size: 18px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.95);
			}
			.position-input {
				margin-top: 12px;
				height: 47px;
			}
		}
	}
	.footer {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 81px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		button {
			padding: 0px;
			width: 150px;
			height: 47px;
			background: #008eff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
		}
	}
	.ant-collapse-header {
		height: 47px;
		// background: #f4faff;
		font-size: 20px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #000000;
		&::before {
			margin-right: 8px;
			content: '';
			display: inline-block;
			width: 32px;
			height: 32px;
			background: url('../images/group.png') center / cover no-repeat;
		}
	}
	.ant-drawer-footer {
		border-top-width: 12px;
	}
}
</style>
