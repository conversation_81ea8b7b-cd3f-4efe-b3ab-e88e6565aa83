<template>
	<div :class="`vacancy-box ${selected ? 'selected' : ''}`">
		<div class="inner-box">
			<div class="avatar-box">
				<img class="avatar" :src="vacancyIcon" />
			</div>
			<div class="info">
				<div class="name">{{ '--' }}</div>
				<div class="position-box">现任职务：{{ job.jobName }}</div>
				<div class="button-box">
					<a-button type="primary" @click="onSelect">岗找人</a-button>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import vacancyIcon from '../images/vacancy-icon.png'
const props = defineProps({
	job: {
		type: Object,
		default: () => ({}),
	},
	selected: {
		type: Boolean,
		default: false,
	},
})

const emits = defineEmits(['select'])
// 选中
const onSelect = () => {
	emits('select', props.job)
}
</script>

<style lang="less" scoped>
.vacancy-box {
	width: 465px;
	height: 177px;
	box-shadow: 0px 0 11px 0px rgba(0, 0, 0, 0.06);
	border-radius: 3px 3px 3px 3px;
	opacity: 1;
	background: url('../images/kongque.png') right top / 60px 60px no-repeat;
	.inner-box {
		width: 100%;
		height: 100%;
		display: flex;
		padding: 16px 18px;
		.avatar-box {
			width: 111px;
			height: 100%;
			background-color: #eeeeee;
			display: flex;
			align-items: center;
			justify-content: center;
			.avatar {
				width: 33px;
				height: 53px;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				object-fit: contain;
			}
		}
	}

	.info {
		margin-left: 21px;
		flex: 1;
		display: flex;
		flex-direction: column;
		.name {
			font-size: 21px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			color: #000000;
			line-height: 25px;
		}
		.button-box {
			width: 100%;
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.position-box {
			margin-top: 6px;
			font-size: 17px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #000000;
			line-height: 22px;
			// 超过两行省略
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
	}
}
.selected {
	background-color: rgb(180, 210, 233);
	.info {
	}
}
</style>
