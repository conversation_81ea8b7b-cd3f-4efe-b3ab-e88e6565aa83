const nameColumns: any = {
	name: '对比维度',
	dataIndex: 'name',
	key: 'name',
	style: {
		backgroundColor: '#F7F8FA',
	},
	align: 'center',
}

const baseInfoColumns = [
	// {
	// 	name: '对比维度',
	// 	dataIndex: 'name',
	// 	key: 'name',
	// 	style: {
	// 		backgroundColor: '#F7F8FA',
	// 	},
	// 	align: 'center',
	// 	config_id: '1',
	// },
	{
		name: '现任职务',
		// 职务
		key: 'position',
		dataIndex: 'position',
		align: 'center',
		config_id: '6',
	},
	{
		name: '现任职务时间',
		// 职务
		key: 'positionStr',
		dataIndex: 'positionStr',
		align: 'center',
		config_id: '10',
	},
	{
		name: '出生年月',
		dataIndex: 'dateOfBirth',
		key: 'dateOfBirth',
		align: 'center',
		config_id: '38',
	},
	{
		name: '性别',
		dataIndex: 'gender',
		key: 'gender',
		align: 'center',
		config_id: '7',
	},

	{
		name: '出生地',
		key: 'birthPlace',
		dataIndex: 'birthPlace',
		config_id: '29',
	},
	{
		name: '民族',
		// 民族
		key: 'ethnic',
		dataIndex: 'ethnic',
		align: 'center',
		config_id: '11',
	},
	{
		name: '初始学历',
		// 学历
		key: 'minEdu',
		dataIndex: 'minEdu',
		align: 'center',
		config_id: '39',
	},
	{
		name: '最高学历',
		// 学历
		key: 'proMaxEdu',
		dataIndex: 'proMaxEdu',
		align: 'center',
		config_id: '39',
	},
	{
		name: '政治面貌',
		key: 'politicalType',
		dataIndex: 'politicalType',
		align: 'center',
		config_id: '15',
	},
	{
		name: '入党时间',
		key: 'partyJoinDate',
		dataIndex: 'partyJoinDate',
		config_id: '31',
	},
	{
		name: '毕业院校(全日制)	',
		key: 'partTimeEducation',
		dataIndex: 'partTimeEducation',
		config_id: '34',
	},
	{
		name: '专业(全日制)',
		key: 'fullTime',
		dataIndex: 'fullTime',
		config_id: '30',
	},
	{
		name: '干部类别',
		key: 'category',
		dataIndex: 'category',
		config_id: '32',
	},
	{
		name: '现任职级时间',
		key: 'currentRankTime',
		dataIndex: 'currentRankTime',
		config_id: '36',
	},
	// {
	// 	name: '党龄',
	// 	// 学历
	// 	key: 'politicalAge',
	// 	dataIndex: 'politicalAge',
	// 	align: 'center',
	// 	config_id: '31',
	// },
]
// 工作简历字段
const resumeColumns = [{ name: '工作简历', key: 'resume', dataIndex: 'resume', config_id: '35' }]

const configList = [
	{
		config_id: '38',
		parentId: '1',
		weight: 1,
		name: '出生年月',
		is_select: true,
	},
	{
		config_id: '4',
		parentId: '1',
		weight: 2,
		name: '籍贯',
		is_select: true,
	},
	{
		config_id: '31',
		parentId: '1',
		weight: 3,
		name: '入党时间',
		is_select: true,
	},
	{
		config_id: '6',
		parentId: '1',
		weight: 4,
		name: '现任职务',
		is_select: true,
	},
	{
		config_id: '7',
		parentId: '1',
		weight: 5,
		name: '性别',
		is_select: true,
	},
	{
		config_id: '39',
		parentId: '1',
		weight: 6,
		name: '学历',
		is_select: true,
	},
	{
		config_id: '13',
		parentId: '1',
		weight: 7,
		name: '工作年限',
		is_select: true,
	},
	{
		config_id: '10',
		parentId: '1',
		weight: 8,
		name: '现任职务时间',
		is_select: true,
	},
	{
		config_id: '11',
		parentId: '1',
		weight: 9,
		name: '民族',
		is_select: true,
	},
	{
		config_id: '30',
		parentId: '1',
		weight: 10,
		name: '专业(全日制)',
		is_select: true,
	},
	{
		config_id: '17',
		parentId: '1',
		weight: 11,
		name: '擅长领域',
		is_select: true,
	},
	{
		config_id: '14',
		parentId: '1',
		weight: 12,
		name: '分管领域',
		is_select: true,
	},
	{
		config_id: '15',
		parentId: '1',
		weight: 13,
		name: '政治面貌',
		is_select: true,
	},
	{
		config_id: '29',
		parentId: '1',
		weight: 14,
		name: '出生地',
		is_select: true,
	},
	{
		config_id: '32',
		parentId: '1',
		weight: 15,
		name: '干部类别',
		is_select: true,
	},
	{
		config_id: '18',
		parentId: '1',
		weight: 16,
		name: '特长爱好',
		is_select: true,
	},
	{
		config_id: '36',
		parentId: '1',
		weight: 17,
		name: '现任职级时间',
		is_select: true,
	},
	{
		config_id: '33',
		parentId: '1',
		weight: 18,
		name: '年度考核(近5年)',
		is_select: true,
	},
	{
		config_id: '34',
		parentId: '1',
		weight: 19,
		name: '毕业院校(全日制)',
		is_select: true,
	},
	{
		config_id: '35',
		parentId: '1',
		weight: 20,
		name: '工作简历',
		is_select: true,
	},

	{
		config_id: '19',
		parentId: '2',
		weight: 1,
		name: '干部指数',
		is_select: true,
	},
	{
		config_id: '20',
		parentId: '2',
		weight: 2,
		name: '风险指数',
		is_select: true,
	},
	{
		config_id: '21',
		parentId: '2',
		weight: 3,
		name: '能力-业绩坐标图',
		is_select: true,
	},
	{
		config_id: '22',
		parentId: '2',
		weight: 4,
		name: '干部指数评测结果',
		is_select: true,
	},
	{
		config_id: '23',
		parentId: '2',
		weight: 5,
		name: '特征标签',
		is_select: true,
	},
	{
		config_id: '24',
		parentId: '2',
		weight: 6,
		name: '一言素描',
		is_select: true,
	},
	{
		config_id: '26',
		parentId: '2',
		weight: 7,
		name: '表扬表彰',
		is_select: true,
	},
	{
		config_id: '27',
		parentId: '2',
		weight: 8,
		name: '风险项',
		is_select: true,
	},
	{
		config_id: '28',
		parentId: '2',
		weight: 9,
		name: '躺平式干部（得票）',
		is_select: true,
	},
	{
		config_id: '25',
		parentId: '2',
		weight: 10,
		name: '干部指数排名（同序列）',
		is_select: true,
	},
	{
		config_id: '37',
		parentId: '2',
		weight: 12,
		name: '头像',
		is_select: true,
	},
]

const columnsConfig: any = [
	{
		name: '对比维度',
		dataIndex: 'name',
		key: 'name',
		style: {
			backgroundColor: '#F7F8FA',
		},
		align: 'center',
	},
	{
		name: '年龄',
		dataIndex: 'age',
		key: 'age',
		align: 'center',
	},
	{
		name: '性别',
		dataIndex: 'gender',
		key: 'gender',
		align: 'center',
	},
	{
		name: '民族',
		// 民族
		key: 'ethnic',
		dataIndex: 'ethnic',
		align: 'center',
	},
	{
		name: '出生地',
		key: 'birthPlace',
		dataIndex: 'birthPlace',
	},
	{
		name: '政治面貌',
		key: 'politicalType',
		dataIndex: 'politicalType',
		align: 'center',
	},
	{
		name: '入党时间',
		key: 'partyJoinDate',
		dataIndex: 'partyJoinDate',
	},
	{
		name: '毕业院校(全日制)	',
		key: 'partTimeEducation',
		dataIndex: 'partTimeEducation',
	},
	{
		name: '专业(全日制)',
		key: 'fullTime',
		dataIndex: 'fullTime',
	},

	{
		name: '干部类别',
		key: 'category',
		dataIndex: 'category',
	},
	{
		name: '现任职级时间',
		key: 'currentRankTime',
		dataIndex: 'currentRankTime',
	},

	{
		name: '现任职务',
		// 职务
		key: 'position',
		dataIndex: 'position',
		align: 'center',
	},
	{
		name: '初始学历',
		// 学历
		key: 'minEdu',
		dataIndex: 'minEdu',
		align: 'center',
	},
	{
		name: '最高学历',
		// 学历
		key: 'proMaxEdu',
		dataIndex: 'proMaxEdu',
		align: 'center',
	},
	{
		name: '党龄',
		// 学历
		key: 'politicalAge',
		dataIndex: 'politicalAge',
		align: 'center',
	},

	{
		name: '现任职务时间',
		// 职务
		key: 'positionStr',
		dataIndex: 'positionStr',
		align: 'center',
	},
	{
		name: '简历',
		// 指数
		key: 'resume',
		dataIndex: 'resume',
		contentStyle: {
			padding: '0px',
		},
	},

	{
		name: '年度考核(近5年)',
		key: 'annualAssessmentResults',
		dataIndex: 'annualAssessmentResults',
	},

	{
		name: '表扬表彰',
		// 荣誉
		key: 'commendInfo',
		dataIndex: 'commendInfo',
	},
	{
		name: '兴趣爱好',
		key: 'hobby',
		dataIndex: 'hobby',
		align: 'center',
	},
	{
		name: '分管领域',
		key: 'branchingArea',
		dataIndex: 'branchingArea',
		align: 'center',
	},
	{
		name: '擅长领域',
		key: 'expertiseArea',
		dataIndex: 'expertiseArea',
		align: 'center',
	},
	{
		name: '特征标签',
		// 特征标签
		key: 'traitTag',
		dataIndex: 'traitTag',
		align: 'center',
	},
	{
		name: '一言素描',
		key: 'sketch',
		dataIndex: 'sketch',
	},
	{
		name: '躺平式干部（得票）',
		// 得票
		key: 'lieFlatTicket',
		dataIndex: 'lieFlatTicket',
		headerClassName: 'lieFlatTicket-header',
	},
	{
		name: '干部指数',
		// 指数
		key: 'leaderIndex',
		dataIndex: 'leaderIndex',
		align: 'center',
		contentStyle: {
			padding: '0px',
		},
	},
	{
		name: '风险指数',
		// 风险指数
		key: 'riskIndex',
		dataIndex: 'riskIndex',
	},
	{
		name: '风险项',
		// 风险
		key: 'riskItems',
		dataIndex: 'riskItems',
	},

	{
		name: '干部指数排名',
		// 指数
		key: 'cadreIndexRank',
		dataIndex: 'cadreIndexRank',
		align: 'center',
	},

	{
		name: '最高学历',
		key: 'maxEdu',
		dataIndex: 'maxEdu',
		align: 'center',
	},
]

const fixedColumnsConfig = [
	{
		name: '能力-业绩坐标图',
		// 能力-业绩坐标图
		key: 'pointVO',
		dataIndex: 'pointVO',
		merge: true,
		headerClassName: 'coordinate-header',
		fixed: true,
	},
	{
		name: '干部测评结果',
		// 能力-业绩坐标图
		key: 'leaderIndexResult',
		dataIndex: 'leaderIndexResult',
		merge: true,
		headerClassName: 'leaderIndexResult-header',
	},
]

export { nameColumns, baseInfoColumns, resumeColumns, configList, columnsConfig, fixedColumnsConfig }
