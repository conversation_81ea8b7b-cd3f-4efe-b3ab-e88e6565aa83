<template>
	<div class="comparison-results-new">
		<div class="header">
			<div class="title" @click="onBack">
				<span class="text"> 对比结果 </span>
			</div>
			<div class="nav">
				<div
					:class="`nav-item ${activeKey === item.key ? 'nav-item-active' : ''}`"
					v-for="item in menuConfig"
					:key="item.key"
					@click="onNavSelect(item.key)"
					:style="{
						'z-index': item.zIndex,
					}"
				>
					<img :src="activeKey === item.key ? item.active_img : item.img" class="icon" />
					{{ item.label }}
				</div>
			</div>
		</div>
		<div class="content">
			<base-info v-if="activeKey === 1" :datasource="dataSource" :columns="baseInfoColumns" />
			<job-resume v-else-if="activeKey === 2" :datasource="dataSource" />
			<qualitative-evaluation v-else-if="activeKey === 3" :datasource="dataSource" />
			<quantitative-evaluation v-else :datasource="dataSource" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, provide } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// api
import { getComparisonResult, getComparisonResult1 } from '@/apis/comparisonResults'
// component
import BaseInfo from './components/BaseInfo.vue'
import JobResume from './components/JobResume.vue'
import QualitativeEvaluation from './components/QualitativeEvaluation.vue'
import QuantitativeEvaluation from './components/QuantitativeEvaluation.vue'
import baseInfoPng from './images/base-info.png'
import baseInfoActivePng from './images/base-info-1.png'
import resumePng from './images/resume.png'
import resumeActivePng from './images/resume-1.png'
import dinxinPng from './images/dinxin.png'
import dinxinActivePng from './images/dinxin-1.png'
import dinliangPng from './images/dinliang.png'
import dinliangActivePng from './images/dinliang-1.png'

import { nameColumns, baseInfoColumns as _baseInfoColumns, resumeColumns, columnsConfig, fixedColumnsConfig } from './columns'
import { message } from 'ant-design-vue'
const router = useRouter()
const route = useRoute()
const { user_id, config_ids }: any = route.query

const config_id = config_ids.split(',')

const baseInfoColumns = _baseInfoColumns
function getImageUrl(name: string) {
	return new URL(`${name}`, import.meta.url).href
}
const menuConfig = ref([
	{
		label: '基础信息',
		key: 1,
		img: baseInfoPng,
		active_img: baseInfoActivePng,
		zIndex: 4,
	},
	{
		label: '工作简历',
		key: 2,
		img: resumePng,
		active_img: resumeActivePng,
		zIndex: 3,
	},
	{
		label: '定性评价',
		key: 3,
		img: dinxinPng,
		active_img: dinxinActivePng,
		zIndex: 2,
	},
	{
		label: '定量评价',
		key: 4,
		img: dinliangPng,
		active_img: dinliangActivePng,
		zIndex: 1,
	},
])

const menuList = ref([
	{
		label: '基础信息',
		value: '1',
	},
	{
		label: '工作简历',
		value: '2',
	},
	{
		label: '定性评价',
		value: '3',
	},
	{
		label: '定量评价',
		value: '4',
	},
])

const menuValue = ref(menuList.value[0].value)
const loading = ref(false)
const tipShow = ref(false)
const columns = ref([])
const dataSource = ref([])
const fixedColumns = ref([])
const activeKey = ref(menuConfig.value[0].key)

const state: any = reactive({
	name: [],
	baseData: [],
	resumeData: [],
})
/**
 * @description: 返回
 * @return {*}
 */
const onBack = () => {
	router.back()
}

const onNavSelect = (key: any) => {
	activeKey.value = key
	// 找到对应位置
	const index = menuConfig.value.findIndex((item: any) => {
		return item.key === key
	})

	const _: any = [...menuConfig.value]

	const maxIndex = _.reduce((pre: number, current: any) => {
		return pre > current.zIndex ? pre : current.zIndex
	}, 0)

	let zIndex = 1
	for (let i = index - 1; i >= 0; i--) {
		if (!_[i].zIndex) continue

		_[i].zIndex = i

		zIndex++
	}
	zIndex = 1
	for (let i = index + 1; i < _.length; i++) {
		if (!_[i].zIndex) continue

		_[i].zIndex = maxIndex - zIndex
		zIndex++
	}

	_[index].zIndex = maxIndex

	menuConfig.value = _
	// // 最大下标
}

const onDeleteUser = (user_id: any) => {
	if (dataSource.value.length === 2) return

	const index = dataSource.value.findIndex((item: any) => {
		return item.userId === user_id
	})

	if (index !== -1) {
		dataSource.value.splice(index, 1)
	}
}

provide('onDelete', onDeleteUser)

const loadData = async () => {
	loading.value = true

	const _config_ids = config_ids.split(',')

	if (_config_ids.includes(8)) {
		_config_ids.push(12, 5)
	}

	const params = {
		user_ids: user_id,
		config_ids: _config_ids,
	}
	try {
		const { data, code, message: _mes } = await getComparisonResult1(params)

		if (code === 0) {
			const _c: any = []
			const _c1: any = []
			data?.map((item: any, index: number) => {
				for (let c of columnsConfig) {
					const col = Object.keys(item).findIndex((key: any) => c.dataIndex === key)
					index === 0 && col !== -1 && _c.push(c)

					if (c.key === 'leaderIndex' && item.leaderIndex) {
						const { cadreIndex, sequencesRank, pyramidIndex } = item.leaderIndex[0] || {}
						const _d = [
							{
								year: '2023',
								cadreIndex: cadreIndex || '-',
								sequencesRank: sequencesRank || '-',
								pyramidIndex: pyramidIndex || '-',
							},
							// {
							// 	year: '2022',
							// 	cadreIndex: '-',
							// 	sequencesRank: '-',
							// 	pyramidIndex: '-',
							// },
							// {
							// 	year: '2021',
							// 	cadreIndex: '-',
							// 	sequencesRank: '-',
							// 	pyramidIndex: '-',
							// },
							// {
							// 	year: '2020',
							// 	cadreIndex: '-',
							// 	sequencesRank: '-',
							// 	pyramidIndex: '-',
							// },
							// {
							// 	year: '2019',
							// 	cadreIndex: '-',
							// 	sequencesRank: '-',
							// 	pyramidIndex: '-',
							// },
						]
						item.leaderIndex = _d
					}
				}
				for (let c of fixedColumnsConfig) {
					const col = Object.keys(item).findIndex((key: any) => c.dataIndex === key)
					index === 0 && col !== -1 && _c1.push(c)

					// if (c.key === 'leaderIndexResult' && item.leaderIndexResult) {
					// 	const leaderIndex = item.leaderIndexResult.other_result?.find((_item: any) => {
					// 		return _item.user_id === item.userId
					// 	})
					// 	leaderIndex && (item.leaderIndexResult = { leaderIndex, xlabel: item.leaderIndexResult.xlabel })
					// }
				}
			})
			// 用户名
			state.name = data.map((item: any) => {
				return {
					name: item.name,
					userId: item.userId,
					headUrl: item.headUrl,
				}
			})
			// 基础信息
			// baseInfoColumns.unshift(nameColumns)

			state.baseData = data.map((item: any) => {
				return {
					data: baseInfoColumns.map((col: any) => {
						col.value = item[col.key]
						return col
					}),
					name: item.name,
					userId: item.userId,
					headUrl: item.headUrl,
				}
			})

			state.resumeData = data.map((item: any) => {
				const _data = resumeColumns.map((col: any) => {
					col.value = item[col.key]
					return col
				})
				return {
					data: _data?.[0]?.value || [],
					name: item.name,
					userId: item.userId,
					headUrl: item.headUrl,
				}
			})
			// 特征标签数据改造，根据数据中相同的个数生成此种数据格式 {label: '', count: ''}
			data.forEach((item: any) => {
				if (item.traitTag?.positive_feature) {
					item.traitTag.positive_feature_map = convertMap(item.traitTag.positive_feature).sort((a: any, b: any) => {
						return b.count - a.count
					})
				}
				if (item.traitTag?.negative_feature) {
					item.traitTag.negative_feature_map = convertMap(item.traitTag.negative_feature).sort((a: any, b: any) => {
						return b.count - a.count
					})
				}

				function convertMap(data: Array<any>) {
					const positive_feature_map: any = []
					data.map((item: any) => {
						const _index = positive_feature_map.find((item1: any) => item1.label === item)
						if (!_index) {
							positive_feature_map.push({
								label: item,
								count: 1,
							})
						} else {
							_index.count++
						}
					})

					return positive_feature_map
				}
			})

			// columns.value = _c
			dataSource.value = data

			// fixedColumns.value = _c1

			// tipShow.value = data.length > 2
		} else {
			message.error(_mes)
		}
	} catch (err) {
		console.log(err)
	} finally {
		loading.value = false
	}
}

loadData()
</script>

<style lang="less" scoped>
.comparison-results-new {
	display: flex;
	flex-direction: column;
	padding: 21px 17px;
	width: 100%;
	height: 100%;
	background: url('./images/bg-image.png') top / cover no-repeat;
	background-color: #f6f8fc;
	user-select: none;
	.header {
		display: flex;
		justify-content: space-between;
		:deep(.ant-radio-button-wrapper:focus-within) {
			box-shadow: none;
		}
		.nav {
			display: flex;
			align-items: flex-end;
			.nav-item {
				position: relative;
				margin-right: -138px;
				display: flex;
				align-items: center;
				justify-content: center;

				font-size: 24px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				color: #626466;
				line-height: 33px;

				width: 468px;
				height: 66px;
				background: url('./images/nav-bg.png') no-repeat center/ cover;
				opacity: 1;
				filter: drop-shadow(0px 0 18px rgba(0, 142, 255, 0.21));
				cursor: pointer;
				user-select: none;

				.icon {
					margin-right: 12px;
					font-size: 0px;
					height: 30px;
					width: 30px;
				}
				&:last-child {
					margin-right: 0;
				}
			}
			.nav-item-active {
				z-index: 99 !important;
				height: 83px;
				background: url('./images/nav-bg-1.png') no-repeat center/ cover;
				color: #008eff;
			}
			.active {
				color: #008eff;
			}
		}
		.title {
			display: flex;
			align-items: center;
			cursor: pointer;
			height: 60px;
			.text {
				font-size: 24px;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				color: #000000;
			}
			&::before {
				margin-right: 12px;
				content: '';
				display: inline-block;
				width: 30px;
				height: 30px;
				background: url('@/assets/images/arrow-left.png') no-repeat center / contain;
			}
		}
		* {
			--webkit-tap-highlight-color: transparent;
			outline: none;
		}
		.back {
			display: inline-block;
			width: 40px;
			height: 40px;
			background: url('@/assets/images/arrow-left.png') no-repeat center / contain;
			cursor: pointer;
		}
	}
	.content {
		position: relative;
		z-index: 990;
		margin-top: -1px;
		display: flex;
		padding: 27px;
		flex: 1;
		width: 100%;
		background-color: #ffffff;
		overflow: hidden;
	}
}
</style>
