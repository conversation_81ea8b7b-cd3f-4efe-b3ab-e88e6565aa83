<template>
	<container :data="datasource" bg-color>
		<template #default="{ data, theme }">
			<div class="base-info">
				<template v-for="(item, index) in columnsGroup" :key="index">
					<div class="label-group">
						<div class="label-box" v-for="(item1, index1) in item" :key="index1">
							<div class="label" :style="{ '--color': theme.color }">{{ item1.name }}</div>
							<div class="value" :style="{ color: theme.color }">
								{{ render(data[item1.key]) }}
							</div>
						</div>
					</div>
				</template>
			</div>
		</template>
	</container>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import Container from './Container.vue'

const list = [
	{
		label: '现任职务',
		value: '某某镇党委组织委员',
	},
	{
		label: '任现职务时间',
		value: '2020.10',
	},
	{
		label: '出生年月',
		value: '1981.10（42岁）',
	},
	{
		label: '性别',
		value: '女',
	},
	{
		label: '学历',
		value: '大学本科  |  经济管理研究生',
	},
	{
		label: '毕业院校',
		value: 'xxxxx大学',
	},
	{
		label: '籍贯',
		value: '重庆丰都',
	},
	{
		label: '出生地',
		value: '重庆丰都',
	},
	{
		label: '民族',
		value: '汉',
	},
	{
		label: '干部类别',
		value: '乡镇副职',
	},
	{
		label: '现职级任职时间',
		value: '2020.10',
	},
]

const props = defineProps({
	datasource: {
		type: Array<any>,
		default: () => [],
	},
	columns: {
		type: Array<any>,
		default: () => [],
	},
})

const columnsGroup = computed(() => {
	const _: any = []
	if (Array.isArray(props.columns)) {
		for (let i = 0; i < props.columns.length; i++) {
			// 两两分组
			if (i % 2 === 0) {
				_.push(props.columns.slice(i, i + 2))
			}
		}
	}

	return _
})

const render = (value: any) => {
	return Array.isArray(value) ? value.join(',') : value
}
</script>

<style lang="less" scoped>
.base-info {
	gap: 30px 0;
	padding: 35px 30px;
	.label-box {
		.label {
			display: flex;
			align-items: center;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			line-height: 23px;
			&::before {
				margin-right: 6px;
				content: '';
				display: inline-block;
				width: 6px;
				height: 6px;
				background: var(--color);
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
			}
		}
		.value {
			padding-left: 12px;
			margin-top: 12px;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			color: #008eff;
			line-height: 23px;
		}
	}
	.label-group {
		padding: 17px 0px;
		display: grid;
		grid-template-columns: 2fr 1fr;
		border-bottom: 1px solid rgba(0, 0, 0, 0.06);

		&:last-child {
			border-bottom: none;
		}
	}
}
</style>
