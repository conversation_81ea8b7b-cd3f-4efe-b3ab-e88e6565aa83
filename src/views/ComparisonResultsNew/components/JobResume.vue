<template>
	<container :data="datasource" bg-color>
		<template #default="{ data, theme }">
			<div class="resume-list">
				<div class="resume-item" v-for="(resume, index) in data.resume || []" :key="index">
					<div class="time" :style="{ color: theme.color, '--color': theme.color }">
						<span class="text">
							{{ resume.timeDifference }}
						</span>
					</div>
					<div class="text">{{ resume.remark }}</div>
				</div>
			</div>
		</template>
	</container>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import Container from './Container.vue'
const props = defineProps({
	datasource: {
		type: Array<any>,
		default: () => [],
	},
})
const resumeColumns = { name: '工作简历', key: 'resume', dataIndex: 'resume', config_id: '35' }

const _data = computed(() => {
	return props.datasource.map((item: any) => {
		return [{}]
	})
})
</script>

<style lang="less" scoped>
.resume-list {
	padding: 32px 38px;
	width: 100%;
	.resume-item {
		width: 100%;
		display: flex;
		&:nth-child(1) {
			.time {
				&::before {
					top: 0px !important;
				}
			}
		}
		.time {
			flex: 0 0 230px;
			position: relative;
			font-size: 21px;
			line-height: 21px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			color: var(--color);
			.text {
				margin-right: 20px;
			}
			&::before {
				content: '';
				position: absolute;
				top: 4px;
				right: 0px;
				width: 15px;
				height: 15px;
				background-color: #fff;
				border: 2px solid var(--color);
				border-radius: 50%;
				z-index: 1;
			}
			&::after {
				content: '';
				position: absolute;
				top: 2px;
				right: 0px;
				height: 100%;
				width: 1px;
				transform: translate(-7px);
				background-color: var(--color);
			}
		}
		.text {
			margin-left: 23px;
			font-size: 21px;
			line-height: 25px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #333333;
			padding-bottom: 32px;
		}
	}
}
</style>
