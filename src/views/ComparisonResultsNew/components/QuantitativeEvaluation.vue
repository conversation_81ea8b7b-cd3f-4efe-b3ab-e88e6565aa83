<template>
	<container :data="datasource" class="quantitative-evaluation">
		<template #full>
			<div class="abilityAnalysis">
				<Coordinate :data="coordinateData" />
			</div>
			<div class="abilityAnalysis">
				<AbilityAnalysis :data="leaderIndexResult" />
			</div>
		</template>
		<template #default="{ data, index, theme }">
			<!-- <div class="cadre" :style="{ backgroundColor: theme.bgColor }">
				<CadreBox :leader_index="data.leaderIndex?.[0]?.cadreIndex" :leader_index_rank="data.cadreIndexRank" :risk_index="data.riskIndex" />
			</div> -->
			<InfoCard class="margin-top-27" title="业绩分析" :color="theme.color" :bg-color="theme.bgColor">
				<data-box :color="theme.color" :bg-color="theme.performance" :data="data.achievement" />
			</InfoCard>
			<InfoCard class="margin-top-27" title="年度考核" :color="theme.color" :bg-color="theme.bgColor">
				<data-box :color="theme.color" type="2" :bg-color="theme.performance" :data="data.annualAssessmentResults" :key="index" />
			</InfoCard>
			<InfoCard class="margin-top-27" title="个人监督" :color="theme.color" :bg-color="theme.bgColor">
				<div class="reporting-situation">
					<div class="common-title" :style="{ '--color': theme.color }">信访举报情况</div>
					<template v-if="data.complaintReport?.length">
						<div class="reporting margin-top-25" v-for="(item, index) in data.complaintReport" :key="index">{{ item }}</div>
					</template>

					<a-empty v-else />
				</div>
				<div class="reporting-situation margin-top-37">
					<div class="common-title" :style="{ '--color': theme.color }">个人事项报告核查情况</div>
					<template v-if="true">
						<div class="evaluate">
							<div class="label" :style="{ color: theme.color }"><span type="label">查核时间</span> <span type="sub_prefix">：</span></div>
							<div class="text">{{ data.mattersCheck?.check_date }}</div>
						</div>
						<div class="evaluate">
							<div class="label" :style="{ color: theme.color }"><span type="label">查核类型</span> <span type="sub_prefix">：</span></div>
							<div class="text">{{ data.mattersCheck?.type }}</div>
						</div>
						<div class="evaluate">
							<div class="label" :style="{ color: theme.color }"><span type="label">查核结果</span> <span type="sub_prefix">：</span></div>
							<div class="text">{{ data.mattersCheck?.result }}</div>
						</div>
					</template>
					<a-empty v-else />
				</div>
				<div class="supervision margin-top-37" :style="{ '--color': theme.color }">
					<div class="common-title">其他监督</div>
					<template v-if="true">
						<div class="year-box">{{ data.supervision?.supervision_date }}征求意见</div>
						<div class="reporting" style="line-height: 1.8">征求意见单位：{{ data.supervision?.consultation_org }}</div>
						<div class="reporting" style="line-height: 1.8">反馈意见情况：{{ data.supervision?.condition }}</div>
					</template>
					<a-empty v-else />
				</div>
			</InfoCard>
			<InfoCard class="margin-top-27" title="表彰表扬" :color="theme.color" :bg-color="theme.bgColor">
				<template v-if="data.reward?.length">
					<div :class="`commendation ${index > 0 ? 'm-top-20' : ''}`" v-for="(item, index) in data.reward" :key="index">
						<div class="year rank">{{ item.year }}年-{{ item.type }}</div>
						<div class="desc">
							{{ item.content }}
						</div>
					</div>
				</template>

				<a-empty v-else />
			</InfoCard>
			<InfoCard class="margin-top-27" title="负面清单" :color="theme.color" :bg-color="theme.bgColor">
				<template v-if="data.punishment?.length">
					<div class="negevative" v-for="(item, index) in data.punishment" :key="index">
						<div class="year doc">{{ item.punishment_time }}</div>
						<div class="desc">
							{{ item.content }}
						</div>
					</div>
				</template>
				<a-empty v-else />
			</InfoCard>
		</template>
	</container>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import Container from './Container.vue'
import AbilityAnalysis from './AbilityAnalysis.vue'
import Coordinate from './Coordinate.vue'
import CadreBox from './CadreBox.vue'
import InfoCard from './InfoCard.vue'
import DataBox from './data-box.vue'
const props = defineProps({
	datasource: {
		type: Array<any>,
		default: () => [],
	},
	bgColor: {
		type: Boolean,
		default: false,
	},
})

const leaderIndexResult = computed(() => {
	return [props.datasource[0]?.leaderIndexResult || {}]
})

const coordinateData = computed(() => {
	return props.datasource.map((item: any) => {
		return item.pointVO
	})
})
</script>

<style lang="less" scoped>
.common-title {
	display: flex;
	align-items: center;
	font-size: 21px;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.9);
	line-height: 18px;
	&::before {
		margin-right: 9px;
		content: '▶';
		color: var(--color);
		font-size: 16px;
	}
}
:deep(.inner) {
	padding: 0px 0px 100px !important;
}
.cadre {
	padding: 34px 35px;
}
.card-box {
	padding: 36px 35px;
}

.reporting-situation {
	display: flex;
	flex-direction: column;

	.reporting {
		margin-left: 26px;
		padding: 13px 14px 13px 0px;
		font-size: 21px;
		line-height: 25px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.9);
		border-bottom: 1px solid rgba(0, 0, 0, 0.06);
	}

	.evaluate {
		padding-left: 26px;
		display: flex;
		&:not(:first-child) {
			margin-top: 23px;
		}
		.label {
			flex-shrink: 0;
			margin-right: 4px;
			font-size: 21px;
			font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
			font-weight: bold;
			text-align-last: justify;
			white-space: nowrap;
			span[type='label'] {
				display: inline-block;
				width: 80px;
				font-weight: bold;
				text-align-last: justify;
				font-size: 21px;
			}
			span[type='sub_prefix'] {
				font-weight: bold;
				text-align-last: justify;
			}
		}
		.text {
			font-size: 21px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #000000;
		}
	}
}
.supervision {
	.year-box {
		margin-top: 20px;
		padding-left: 26px;
		font-size: 22px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		color: #008eff;
	}
	.reporting {
		margin-top: 20px;
		display: flex;
		padding-left: 26px;
		font-size: 21px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.9);
		line-height: 25px;
		&::before {
			margin-top: 10px;
			content: '';
			margin-right: 9px;
			font-size: 26px;
			line-height: 26px;
			display: inline-block;
			width: 9px;
			height: 9px;
			background: var(--color);
			flex-shrink: 0;
		}
	}
}
.doc {
	&::before {
		content: '';
		background: url('../images/doc.png') no-repeat center / cover;
	}
}
.rank {
	&::before {
		content: '';
		background: url('../images/rank.png') no-repeat center / cover;
	}
}
.commendation,
.negevative {
	.year {
		display: flex;
		align-items: center;
		font-weight: bold;
		font-size: 22px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		color: rgba(0, 0, 0, 0.9);
		line-height: 21px;
		&::before {
			margin-right: 17px;
			content: '';
			display: inline-block;
			width: 22px;
			height: 23px;
		}
	}
	.desc {
		margin-top: 8px;
		padding-left: 37px;
		font-size: 21px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #000000;
		line-height: 1.8;
	}
}
.negevative {
}
.abilityAnalysis {
	width: 100%;
	:deep(.card-header) {
		background-color: #d6f0ff;
	}
}
.margin-top-27 {
	margin-top: 27px;
}
.margin-top-25 {
	margin-top: 25px;
}
.margin-top-37 {
	margin-top: 37px;
}
</style>
