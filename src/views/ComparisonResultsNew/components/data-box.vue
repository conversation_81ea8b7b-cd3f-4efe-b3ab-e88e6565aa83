<template>
	<div class="data-box" :style="{ '--color': color, '--bg-color': bgColor }">
		<div class="data-item" v-for="item in data" :key="item">
			<div class="year">{{ item.year }}年</div>
			<div class="data" v-if="type === '1'">
				<div class="index">{{ item.score }}</div>
				<div class="label">同序列平均值</div>
				<div class="avg-data">{{ item.avg }}</div>
			</div>
			<div class="data type1" v-else>
				<div class="data-desc">{{ item.score }}</div>
				<div class="rank">
					<div class="rank-label">排名</div>
					<div class="rank-line"></div>
					<div class="rank-text">{{ item.rank }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
defineProps({
	data: {
		type: Array<any>,
		default: () => [],
	},
	type: {
		type: String,
		default: '1', // 1为业绩， 2为考核
	},
	color: String,
	bgColor: String,
})
</script>

<style lang="less" scoped>
.data-box {
	display: grid;
	// flex-wrap: wrap;
	grid-template-columns: 2fr 2fr;
	gap: 22px;
	.data-item {
		flex: 1 1 50%;
		border-radius: 6px;
		overflow: hidden;
		background-color: rgba(255, 255, 255, 0.5);
		box-shadow: 0px 0 12px 0px rgba(0, 0, 0, 0.06);
		.year {
			height: 55px;
			background: var(--bg-color);
			box-shadow: 0px 0 12px 0px rgba(0, 0, 0, 0.06);
			text-align: center;

			font-size: 24px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			color: rgba(0, 0, 0, 0.85);
			line-height: 55px;
		}
		.data {
			padding: 16px 0px;
			.index {
				text-align: center;
				font-size: 24px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: var(--color);
				line-height: 28px;
			}
			.label {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 16px;
				font-size: 21px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.85);
				line-height: 25px;
				text-align: center;
				&::before {
					content: '';
					display: inline-block;
					margin-right: 3px;
					background: var(--color);
					font-size: 26px;
					line-height: 26px;
					width: 6px;
					height: 6px;
				}
			}
			.data-desc {
				text-align: center;
				font-size: 22px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				color: var(--color);
			}
			.rank {
				width: 100%;
				margin-top: 16px;
				display: flex;
				align-items: center;
				justify-content: center;
				&::before {
					margin-right: 6px;
					content: '';
					display: inline-block;
					width: 6px;
					height: 6px;
					background-color: var(--color);
				}
				.rank-label {
					font-size: 21px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.85);
					line-height: 19px;
				}
				.rank-line {
					margin: 0px 15px;
					width: 60px;
					height: 1px;
					background-color: var(--color);
				}
				.rank-text {
					color: var(--color);
					line-height: 19px;
					font-size: 22px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
				}
			}
			.avg-data {
				margin-top: 10px;
				font-size: 24px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				color: #60ca71;
				text-align: center;
			}
		}
	}
}
.type1 {
	padding-bottom: 34px !important;
}
</style>
