<template>
	<div :class="`line-block-${size} line-block ${type && `bg-type-${type}`}`" :style="{ '--color': color, '--bg-color': bgColor }">{{ label }}</div>
</template>

<script lang="ts" setup>
import { ref, PropType } from 'vue'
defineProps({
	size: {
		type: String as PropType<'default' | 'small'>,
		default: 'default',
	},
	label: {
		type: String,
		default: '',
	},
	value: {
		type: String,
		default: '',
	},
	color: {
		type: String,
		default: '',
	},
	bgColor: {
		type: String,
		default: '',
	},
	type: {
		type: Number, //1， 2, size 为 small 可传
		required: false,
	},
})
</script>

<style lang="scss" scoped>
.line-block-default {
	position: relative;
	width: 100%;
	padding: 10px 23px;
	background-color: var(--bg-color);
	font-size: 21px;
	font-family: Source <PERSON>, Source <PERSON> Sans CN;
	font-weight: 400;
	color: #000000;
	line-height: 25px;
	&::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		display: inline-block;
		height: 100%;
		width: 5px;
		background: var(--color);
		border-radius: 0px 2px 2px 0px;
		opacity: 1;
	}
}
.line-block-small {
	padding: 5px 15px;
	width: 100%;
	background: url('../images/line-block.png') center / 100% 100% no-repeat !important;

	font-size: 21px;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.9);
	line-height: 25px;
}

.bg-type-1 {
	background: url('../images/line-block-1.png') center / 100% 100% no-repeat !important;
}
</style>
