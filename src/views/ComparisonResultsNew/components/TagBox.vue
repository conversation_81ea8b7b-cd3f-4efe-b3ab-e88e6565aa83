<template>
	<div class="tag-box">
		<div class="tag" v-for="(item, index) in data" :key="index">
			<div class="inner" :style="{ backgroundColor: color }">{{ item.label }}</div>
			<div class="count" v-if="item.count > 1">
				<span class="icon">x</span> <span class="number">{{ item.count }}</span>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const props = defineProps({
	data: {
		type: Array<any>,
		default: () => [],
	},
	color: {
		color: String,
		default: '',
	},
})
</script>

<style lang="less" scoped>
.tag-box {
	display: flex;
	flex-wrap: wrap;
	gap: 20px;
	.tag {
		display: flex;
		.inner {
			padding: 4px 25px !important;
			font-size: 18px;
			text-align: center;
			font-family: Source <PERSON>, Source <PERSON>;
			font-weight: 400;
			color: #ffffff;
			border-radius: 5px 5px 5px 5px;
		}
		.count {
			margin-left: 15px;
			display: flex;
			align-items: center;
			.icon {
				margin-right: 5px;
				padding-bottom: 2px;
				font-size: 23px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #f02a2a;
				line-height: 23px;
			}
			.number {
				font-size: 38px;
				font-family: Rany, Rany;
				font-weight: normal;
				color: #f02a2a;
				line-height: 30px;
			}
		}
	}
}
</style>
