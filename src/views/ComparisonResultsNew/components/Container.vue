<template>
	<div class="container">
		<div class="container-box" @scroll="onScroll" ref="scroll">
			<div class="user-header">
				<div
					class="avatar-box"
					v-for="(item, index) in data"
					:key="index"
					@touchstart="onTouchStart(item, $event)"
					@touchend="onTouchEnd"
					@mousedown="onTouchStart(item, $event)"
					@mouseup="onTouchEnd"
				>
					<div :class="`avatar-item ${(index + 1) % 2 === 0 ? 'fushu' : ''}`">
						<!-- <img class="delete-icon" :src="deleteIcon" @click="onDelete(item)" /> -->
						<div class="avatar">
							<CodeAvatar :head_url="item.headUrl">
								<template #avatar="{ avatar }">
									<img :src="avatar" alt="" />
								</template>
							</CodeAvatar>
						</div>
						<div class="user-info">
							<div class="username">
								<div class="label">
									{{ item.name }}
								</div>
								<div class="line"></div>
							</div>
							<div class="cadre-index index-common">
								<div class="label">干部指数</div>
								<div class="index-box">
									<span class="text">同序列</span>
									<span class="text">{{ item.leaderIndex?.[0]?.sequencesRank }}</span>
								</div>
								<div class="dashed"></div>
								<div class="number-value">{{ item.leaderIndex?.[0]?.cadreIndex }}</div>
							</div>
							<div class="risk-index index-common">
								<div class="label">风险指数</div>
								<div class="dashed"></div>
								<div class="number-value">{{ item.riskIndex || '0.00' }}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="full">
				<slot name="full"></slot>
			</div>
			<div class="content">
				<div :class="`user-box ${bgColor ? 'bg-color' : ''}`" v-for="(item, index) in data" :key="index">
					<div class="inner" ref="elRef">
						<slot :data="item" :index="index" :theme="index % 2 === 0 ? theme.origin : theme.blue"></slot>
					</div>
				</div>
			</div>
		</div>
		<template v-if="data.length > 3">
			<div class="left-icon" @click="onChange('1')"></div>
			<div class="right-icon" @click="onChange('2')"></div>
		</template>
	</div>
	<a-modal v-model:visible="visible" title="确认删除" @ok="onModalOk" cancelText="我再想想" class="delete-modal">
		<div class="modal-content">
			<div class="modal-text">是否确认删除人员“{{ currentUser.name }}”？</div>
			<!-- <div class="button-box">
				<button>我再想想</button>
				<button>确认删除</button>
			</div> -->
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref, inject, onUpdated, onMounted, nextTick } from 'vue'
import defaultAvatar from '@/assets/images/avatar.png'
import deleteIcon from '@/assets/images/delete-1.png'
import { CDN_URL } from '@/config/env'
import { convertPxToRem, debounce, setEqualHeightInGroups, throttle } from '@/utils/utils'
import CodeAvatar from '@/components/CodeAvatar.vue'

const props = defineProps({
	data: {
		type: Array<any>,
		default: () => [],
	},
	bgColor: {
		type: Boolean,
		default: false,
	},
})

const scroll: any = ref()
const visible = ref(false)
const currentUser = ref<any>({})

const theme = {
	blue: {
		color: '#FF6B09',
		bgColor: '#FFFAF7',
		subBgColor: '#FFECDB',
		performance: 'rgba(255,107,9,0.08)', // 业绩
	},
	origin: {
		color: '#008EFF',
		bgColor: '#F6F9FC',
		subBgColor: '#E7EFFF',
		performance: 'rgba(0,142,255,0.08)', // 业绩
	},
}

const onDeleteUser: any = inject('onDelete')

const left = ref(0)

const onScroll = (e: any) => {
	const target = e.target

	left.value = target.scrollLeft

	const header = document.querySelector('.user-header')

	if (target.scrollTop > 0) {
		header?.classList.add('shadow')
	} else {
		header?.classList.remove('shadow')
	}
}

const onVisible = () => {
	visible.value = !visible.value
}

const onModalOk = () => {
	onVisible()

	onDeleteUser(currentUser.value.userId)
}

const onDelete = (item: any) => {
	onDeleteUser(item.userId)
}

const onChange = throttle((type: string) => {
	const el = scroll.value
	const left = el?.scrollLeft
	const unit = convertPxToRem(594 + 24)

	const num = Math.floor(left / unit)

	el.scrollTo({
		left: (type === '1' ? (num === 0 ? 0 : num - 1) : num + 1) * unit,
		behavior: 'smooth',
	})
}, 500)

let timer: any = null

const onTouchStart = (item: any, event: any) => {
	event.preventDefault()

	if (props.data.length <= 2) return

	timer = setTimeout(() => {
		currentUser.value = item

		onVisible()
	}, 800)
}

const onTouchEnd = (e) => {
	e.preventDefault()

	if (timer) {
		clearTimeout(timer)

		timer = null
	}
}

const elRef = ref()

onMounted(() => {
	nextTick(() => {
		setEqualHeightInGroups(elRef.value)
	})
})
</script>

<style lang="less" scoped>
.container {
	position: relative;
	width: 100%;
	height: 100%;
	.container-box {
		width: 100%;
		height: 100%;
		overflow: auto;
	}
	.user-header {
		margin-bottom: 6px;
		display: flex;
		height: 195px;
		position: sticky;
		top: 0;
		z-index: 99;
		background-color: #fff;
		min-width: 100%;
		width: fit-content !important;
		& > * {
			user-select: none !important;
		}
		.avatar-box {
			margin-right: 24px;
			display: flex;
			justify-content: center;
			flex: 0 0 594px;
			background-color: #fff;

			.avatar-item {
				position: relative;
				width: 100%;
				height: 190px;
				padding: 19px 30px 23px;
				background: linear-gradient(180deg, rgba(0, 142, 255, 0.1) 0%, rgba(0, 142, 255, 0.03) 100%);
				display: flex;
				.delete-icon {
					position: absolute;
					top: 0px;
					right: 0px;
					transform: translate(50%, -50%);
					width: 18px;
					height: 18px;
					cursor: pointer;
					background-color: #fff;
					border-radius: 50%;
				}
				.avatar {
					img {
						width: 132px !important;
						height: 148px !important;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
						object-fit: contain;
					}
				}
				.user-info {
					margin-left: 10px;
					flex: 1;
					.username {
						margin-top: 8px;
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-size: 27px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 600;
						color: rgba(0, 0, 0, 0.9);
						line-height: 32px;
						.line {
							width: 260px;
							height: 15px;
							background: url('../images/line-1.png') center / contain no-repeat;
						}
					}
					.index-common {
						display: flex;
						align-items: center;
						.label {
							margin-left: 8px;
							font-size: 23px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 500;
							color: rgba(0, 0, 0, 0.75);
							line-height: 23px;
						}
						&::before {
							content: '';
							display: inline-block;
							width: 8px;
							height: 8px;
							border-radius: 50%;
							opacity: 1;
						}

						.index-box {
							margin-left: 15px;
							padding: 5px 5px;
							border-radius: 3px 3px 3px 3px;
							opacity: 1;
							border: 1px solid #60ca71;
							span.text {
								font-size: 21px;
								line-height: 21px;
								font-family: Source Han Sans CN, Source Han Sans CN;
								font-weight: 500;
								color: #66cf77;
							}
						}
						.dashed {
							margin-left: 15px;
							flex: 1;
							height: 1px;
						}
						.number-value {
							font-size: 26px;
							font-family: Rany-Medium;
							font-weight: 500;
							color: #60ca71;
							line-height: 30px;
							width: 78px;
							text-align: right;
						}
					}
					.cadre-index {
						margin-top: 20px;
						&::before {
							background: #60ca71;
						}
					}
					.risk-index {
						margin-top: 14px;
						&::before {
							background: #ffa300;
						}
						.number-value {
							color: #ffa300;
						}
					}
				}
			}
			.fushu {
				background: linear-gradient(180deg, rgba(255, 126, 43, 0.1) 0%, rgba(253, 109, 12, 0.03) 100%);
				.user-info {
					.username {
						.line {
							background: url('../images/line-2.png') center / contain no-repeat;
						}
					}
				}
			}
		}
	}
	.full {
		width: 100%;
		position: sticky;
		left: 0px;
	}
	.content {
		display: flex;
		.user-box {
			margin-right: 24px;
			flex: 0 0 594px;
			.inner {
				width: 100%;
				border-radius: 6px 6px 6px 6px;
			}
		}
		.bg-color {
			.inner {
				// background: linear-gradient(180deg, rgba(0, 142, 255, 0.1) 0%, rgba(0, 142, 255, 0.03) 100%);

				background: linear-gradient(180deg, rgba(0, 142, 255, 0.1) 6%, rgba(0, 142, 255, 0.03) 100%);
			}
			&:nth-child(2n) .inner {
				// background: linear-gradient(180deg, rgba(255, 126, 43, 0.1) 0%, rgba(253, 109, 12, 0.03) 100%);
				background: linear-gradient(180deg, rgba(255, 126, 43, 0.1) 6%, rgba(253, 109, 12, 0.03) 100%);
			}
		}
	}
	.left-icon,
	.right-icon {
		width: 83px;
		height: 83px;
		background: no-repeat center / 100% 100%;
		border-radius: 50%;
		position: fixed;
		top: 45vh;
	}
	.left-icon {
		left: 27px;
		background-image: url('../images/left-icon.png');
	}
	.right-icon {
		right: 27px;
		background-image: url('../images/right-icon.png');
	}
}
.dashed {
	background-image: linear-gradient(to right, #cccccc 50%, #fff 50%);
	background-repeat: repeat;
	background-size: 4px 1px;
}
.shadow {
	box-shadow: 0px 0 30px 0px rgba(57, 131, 227, 0.2);
}
</style>
<style lang="less">
.modal-text {
	display: flex;
	align-items: center;
	font-size: 24px;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 400;
	color: rgba(0, 0, 0, 0.9);
	line-height: 24px;
	&::before {
		margin-right: 8px;
		content: '';
		display: inline-block;
		width: 23px;
		height: 23px;
		background: url('../images/warn.png') center / 100% no-repeat;
	}
}
.ant-modal-header {
	padding: 27px 24px;
}
.ant-modal-title {
	font-size: 24px;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: bold;
	color: rgba(0, 0, 0, 0.9);
	line-height: 24px;
}
.modal-content {
	display: flex;
	flex-direction: column;
	height: 100%;
	.modal-text {
	}
	.button-box {
		display: flex;
		justify-content: flex-end;
		padding: 10px;
		button {
			height: 64px;
			width: 120px;
			font-size: 17px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			color: rgba(0, 0, 0, 0.9);
			line-height: 17px;
			&:nth-child(1) {
				margin-right: 10px;
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid #d9d9d9;
				color: rgba(0, 0, 0, 0.9);
			}
			&:nth-child(2) {
				background: #008eff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				color: rgba(255, 255, 255, 0.9);
			}
		}
	}
}
.delete-modal {
	width: 600px !important;
	.ant-modal-content {
		width: 100% !important;
		height: 100% !important;
	}
	.ant-modal-body {
		height: 200px;
	}
	.ant-btn {
		height: 64px;
		width: 120px;
		padding-top: 0px;
		padding-bottom: 0px;
		font-size: 17px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		border-width: 1px;
		font-size: 22px;
		&:nth-child(1) {
			margin-right: 10px;
		}
	}
}
</style>
