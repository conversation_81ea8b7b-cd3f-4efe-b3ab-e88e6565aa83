<template>
	<div class="cadre-box">
		<div class="left-box">
			<div class="top">
				<span>干部指数</span>
			</div>
			<div class="middle">
				<div class="inline-box">
					<div class="index">{{ leader_index || '0.0' }}</div>
					<div class="rank">
						<div class="text">同序列</div>
						<div class="index_rank">{{ leader_index_rank }}</div>
					</div>
				</div>
			</div>
			<div class="bottom">
				<div class="progress" :style="`width:${leaderIndexPercent}%`"></div>
			</div>
		</div>
		<div class="right-box" :style="`background-image: ${riskColor.backgroundColor}`">
			<div class="top">
				<span>风险指数</span>
			</div>
			<div class="middle">
				<div class="inline-box">
					<span class="index" :style="`color: ${riskColor.textColor}`">{{ riskIndex || '0.0' }}</span>
					<span class="rank" :style="`background-color: ${riskColor.textColor}`"></span>
				</div>
			</div>
			<div class="bottom">
				<div class="progress" :style="`width:${riskIndexPercent}%;background-color: ${riskColor.textColor}`"></div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import useUser from '@/store/user.ts'
export default defineComponent({
	props: {
		userInfo: {
			type: Object,
			default: () => ({}),
		},
		risk_index: {
			type: Number,
			default: 0,
		},
		leader_index: {
			type: Number,
			default: 0,
		},
		leader_index_rank: {
			type: String,
			default: '',
		},
	},
	setup(props) {
		const user: any = useUser()
		const openRuleModal: any = inject('openRuleModal')

		const riskIndexPercent = computed(() => (props.risk_index ? (props.risk_index > 100 ? 100 : props.risk_index) : 0))
		const leaderIndexPercent = computed(() => (props.leader_index ? (props.leader_index > 100 ? 100 : props.leader_index) : 0))

		const riskColor = computed(() => {
			const riskIndex = props.risk_index || 0
			const colorMap = {
				textColor: '',
				backgroundColor: '',
			}
			if (riskIndex >= 0 && riskIndex < 10) {
				colorMap.textColor = '#60CA71'
				colorMap.backgroundColor = 'linear-gradient(305deg, #DFFFE0 0%, #FFFFFF 100%);'
			} else if (riskIndex >= 10 && riskIndex < 40) {
				colorMap.textColor = '#F6DD00'
				colorMap.backgroundColor = 'linear-gradient(305deg, #FFFFDD 0%, #FFFFFF 100%)'
			} else if (riskIndex >= 40 && riskIndex < 70) {
				colorMap.textColor = '#FF9900'
				colorMap.backgroundColor = 'linear-gradient(305deg, #FEF0DA 0%, #FFFFFF 100%)'
			} else {
				colorMap.textColor = '#FF0000'
				colorMap.backgroundColor = 'linear-gradient(305deg, #FFE2E2 0%, #FFFFFF 100%)'
			}
			return colorMap
		})
		const riskIndex = computed(() => {
			return props.risk_index == -1 ? '一票否决' : props.risk_index
		})
		return {
			user,
			riskColor,
			riskIndex,
			openRuleModal,
			riskIndexPercent,
			leaderIndexPercent,
		}
	},
})
</script>

<style scoped lang="less">
.cadre-box {
	display: flex;
	justify-content: space-between;
	width: 100%;
	.left-box,
	.right-box {
		padding: 33px 32px;
		width: 49%;
		height: 162px;
		background: linear-gradient(305deg, #e5fee6 0%, #ffffff 100%);
		border-radius: 8px 8px 8px 8px;
		opacity: 1;
		box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
		.top {
			font-size: 20px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #333333;
			line-height: 28px;
		}
		.middle {
			width: 100%;
			margin-top: 18px;
			color: #60ca71;
			.inline-box {
				display: flex;
				align-items: flex-end;
				width: 100%;
				height: 100%;
				span {
				}
			}
			.index {
				line-height: 37px;
				font-size: 37px;
				font-weight: bold;
				font-family: 'BAKHUM';
			}
			.rank {
				display: flex;
				flex-direction: column;
				justify-content: center;
				margin-left: 27px;
				padding: 4px 0px;
				width: 96px;
				text-align: center;
				background: #60ca71;
				border-radius: 2px 2px 2px 2px;
				opacity: 1;
				color: #ffffff;
				.text {
					font-size: 14px;
					line-height: 1;
				}
				.index_rank {
					margin-top: 4px;
					font-size: 15px;
					line-height: 15px;
				}
			}
		}
		.bottom {
			margin-top: 16px;
			width: 100%;
			height: 8px;
			background: #f5f5f5;
			border-radius: 23px;
			opacity: 1;
			overflow: hidden;
			.progress {
				height: 8px;
				width: 50%;
				background: #60ca71;
				border-radius: 30px 30px 30px 30px;
				opacity: 1;
			}
		}
	}
	.right-box {
		.middle {
			.inline-box {
				align-items: flex-end;
			}
			.index {
				line-height: 37px !important;
			}
			.rank {
				width: 79px;
				height: 24px;
			}
		}
		.bottom {
			.progress {
				width: 79px;
				height: 8px;
				border-radius: 30px 30px 30px 30px;
				opacity: 1;
			}
		}
	}
	.icon {
		margin-left: 12px;
		display: inline-block;
		width: 40px;
		height: 40px;
		background: url('@/assets/images/question-mark.png') no-repeat center center / 100% 100%;
		vertical-align: middle;
		cursor: pointer;
	}
}
</style>
