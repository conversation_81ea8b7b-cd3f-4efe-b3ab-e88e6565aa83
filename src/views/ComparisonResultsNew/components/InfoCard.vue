<template>
	<div :class="`card-box`" :style="{ backgroundColor: bgColor }">
		<div class="title-box" :style="{ '--color': color }">{{ title }}</div>
		<div class="content-box">
			<slot></slot>
		</div>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	title: {
		type: String,
		default: '',
	},
	color: {
		type: String,
		default: 'blue',
	},
	bgColor: {
		type: String,
		default: '',
	},
})
</script>

<style lang="less" scoped>
.card-box {
	padding: 27px 34px 25px !important;
	.title-box {
		display: flex;
		align-items: center;
		font-size: 22px;
		font-family: Source <PERSON>, Source Han Sans CN;
		font-weight: bold;
		color: #333333;
		line-height: 22px;
		&::before {
			content: '';
			margin-right: 12px;
			display: inline-block;
			width: 11px;
			height: 11px;
			border-radius: 50%;
			background-color: var(--color);
		}
	}
	.content-box {
		padding: 30px 20px 0px;
		font-size: 20px;
		font-family: Source <PERSON>, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
	}
}
</style>
