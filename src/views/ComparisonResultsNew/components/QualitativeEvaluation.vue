<template>
	<container :data="datasource" class="qualitative-evaluation-container">
		<template #default="{ data, index, theme }">
			<div class="common-box m-top-18" :style="{ backgroundColor: theme.bgColor }">
				<info-card :color="theme.color" :bg-color="theme.bgColor" title="干部特点" @click="handleClick(data)">
					<TagBox :color="theme.color" :data="data.traitTag?.positive_feature_map?.slice(0, 4)"
				/></info-card>
			</div>
			<div class="common-box m-top-18" :style="{ backgroundColor: theme.bgColor }">
				<info-card :color="theme.color" :bg-color="theme.bgColor" title="主要不足" @click="handleClick(data)">
					<TagBox :color="theme.color" :data="data.traitTag?.negative_feature_map?.slice(0, 4)"
				/></info-card>
			</div>
			<div class="common-box m-top-18" :style="{ backgroundColor: theme.bgColor }">
				<info-card :color="theme.color" :bg-color="theme.bgColor" title="一言素描">
					<line-block
						v-for="(item, index) in data.sketch"
						:key="index"
						:color="theme.color"
						:bg-color="theme.subBgColor"
						:label="item"
						:class="{ 'm-top-23': index > 0 }"
					/>
				</info-card>
			</div>
			<div class="common-box m-top-18" :style="{ backgroundColor: theme.bgColor }">
				<info-card :color="theme.color" :bg-color="theme.bgColor" title="熟悉领域和特长">
					<line-block
						:color="theme.color"
						:bg-color="theme.subBgColor"
						size="small"
						:type="index % 2"
						:label="data.expertiseArea"
						v-if="data.expertiseArea"
					/>
				</info-card>
				<info-card :color="theme.color" :bg-color="theme.bgColor" title="分管领域" class="margin-top-37">
					<line-block
						:color="theme.color"
						:bg-color="theme.subBgColor"
						:label="data.branchingArea"
						size="small"
						:type="index % 2"
						v-if="data.branchingArea"
					/>
				</info-card>
			</div>
			<div class="common-box m-top-18" :style="{ backgroundColor: theme.bgColor }">
				<info-card
					:color="theme.color"
					:bg-color="theme.bgColor"
					:title="`${item.year}年度班子回访调研评价`"
					v-for="(item, index) in data.returnVisit"
					:key="index"
				>
					<div class="evaluate">
						<div class="label" :style="{ color: theme.color }"><span type="label">时任职务</span> <span type="sub_prefix">：</span></div>
						<div class="text">{{ item.job }}</div>
					</div>
					<div class="evaluate">
						<div class="label" :style="{ color: theme.color }"><span type="label">民主测评</span> <span type="sub_prefix">：</span></div>
						<div class="text">{{ item.evaluation }}</div>
					</div>
					<div class="evaluate">
						<div class="label" :style="{ color: theme.color }"><span type="label">评价</span> <span type="sub_prefix">：</span></div>
						<div class="text">{{ item.assessment }}</div>
					</div>
				</info-card>
			</div>
			<div class="common-box m-top-18" :style="{ backgroundColor: theme.bgColor }">
				<info-card :color="theme.color" :bg-color="theme.bgColor" title="躺平式干部得票"> {{ data.lieFlatTicket }} </info-card>
			</div>
		</template>
	</container>

	<a-modal class="tag-ant-modal" :visible="tagVisible" @cancel="onTagModal" title="特征标签" :footer="null">
		<div class="modal-content">
			<time-line :times="tagReactive.tagYearList" v-model:value="tagReactive.tagYear" />
			<div class="tag-content">
				<div class="tag-inner">
					<!-- <template v-for="item in userInfo.feature_list">
							<TagBox color="#008EFF" :data="item.positive_feature_map" />
							<TagBox color="#FF6A16" :data="item.negative_feature_map" />
						</template> -->
					<TagBox color="#008EFF" :data="tagMap.positiveTag" />
					<TagBox color="#FF6A16" :data="tagMap.negativeTag" />
				</div>
				<div class="legend">
					<div class="tag-1">干部特点</div>
					<div class="tag-2">主要不足</div>
				</div>
			</div>
			<div class="other-content">
				<div class="title">其他标签</div>
				<div class="inner" style="border-bottom: none">
					<!-- <template v-for="item in userInfo.feature_list">
							<TagBox color="#008EFF" :data="item.positive_feature_map" />
							<TagBox color="#FF6A16" :data="item.negative_feature_map" />
						</template> -->
					<TagBox color="#60CA71" :data="tagMap.otherTag" />
				</div>
			</div>
			<div class="example" v-if="loading">
				<a-spin :indicator="indicator" size="large" />
			</div>
			<!-- <div class="content-item">
							<div class="tag-box">
								<div class="tag-item">担当型</div>
							</div>
							<div class="user-name">评价人：王五</div>
						</div>
						<div class="content-item">
							<div class="tag-box">
								<div class="tag-item">担当型</div>
							</div>
							<div class="user-name">评价人：王五</div>
						</div> -->
		</div>
	</a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, h } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { getLeaderBaseInfo, getFeature } from '@/apis/cadre-portrait/home'
import Container from './Container.vue'
import InfoCard from './InfoCard.vue'
import TagBox from './TagBox.vue'
import LineBlock from './LineBlock.vue'
import TimeLine from '@/components/Timeline.vue'

defineProps({
	datasource: {
		type: Array<any>,
		default: () => [],
	},
})

const indicator = h(LoadingOutlined, {
	style: {
		fontSize: '24px',
	},
	spin: true,
})

const tagVisible = ref(false)

const loading = ref(false)

// 初始化特征标签
const tagReactive = reactive({
	tagYearList: [],
	tagYear: '',
	feature: [],
})
const convertMap = (data: Array<any>) => {
	const positive_feature_map: any = []
	data.map((item: any) => {
		const _index = positive_feature_map.find((item1: any) => item1.label === item)
		if (!_index) {
			positive_feature_map.push({
				label: item,
				count: 1,
			})
		} else {
			_index.count++
		}
	})

	return positive_feature_map
}
// 获取标签数据
const tagMap = computed(() => {
	const _data: any = tagReactive.feature.find((item: any) => item.time === tagReactive.tagYear)
	/**
 *  negativeTag	list	-	Y	负面标签
	positiveTag	list	-	Y	正面标签
	otherTag	list	-	Y	其他
 */
	const tags: any = {}
	if (_data?.negativeTag) {
		tags.negativeTag = convertMap(_data.negativeTag).sort((a: any, b: any) => b.count - a.count)
	}
	if (_data?.positiveTag) {
		tags.positiveTag = convertMap(_data.positiveTag).sort((a: any, b: any) => b.count - a.count)
	}
	if (_data?.otherTag) {
		tags.otherTag = convertMap(_data.otherTag).sort((a: any, b: any) => b.count - a.count)
	}

	return tags
})
const handleClick = (data: any) => {
	if (!data.traitTag?.positive_feature_map && !data.traitTag?.other_feature_map) {
		return
	}
	tagReactive.feature = []
	tagReactive.tagYear = ''
	tagReactive.tagYearList = []

	initFeature(data.userId)

	onTagModal()
}

const onTagModal = () => {
	tagVisible.value = !tagVisible.value
}

let controller = null

const initFeature = async (user_id: any) => {
	if (controller) {
		controller.abort()
	}

	controller = new AbortController()

	loading.value = true

	const res = await getFeature(
		{ user_id },
		{
			signal: controller.signal,
		}
	)
	if (res.code === 0) {
		tagReactive.feature = res.data

		tagReactive.tagYear = res.data[0]?.time

		tagReactive.tagYearList = res.data.map((item: any) => item.time)
	} else {
		message.error(res.message)
	}
	loading.value = false

	controller = null
}
</script>

<style lang="less" scoped>
:deep(.inner) {
	padding: 0px !important;
}
.qualitative-evaluation {
	.common-box:not(:first-child) {
		margin-top: 27px;
	}
}
.common-box {
	.line-block:not(:last-child) {
		margin-bottom: 23px;
	}

	.evaluate {
		display: flex;
		&:not(:first-child) {
			margin-top: 23px;
		}
		.label {
			flex-shrink: 0;
			margin-right: 20px;
			font-size: 21px;
			font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
			font-weight: bold;
			text-align-last: justify;
			white-space: nowrap;
			span[type='label'] {
				display: inline-block;
				width: 80px;
				font-weight: bold;
				text-align-last: justify;
			}
			span[type='sub_prefix'] {
				font-weight: bold;
				text-align-last: justify;
			}
		}
		.text {
			font-size: 21px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #000000;
		}
	}
}
.margin-top-37 {
	margin-top: 37px;
}
</style>
<style lang="less">
.tag-ant-modal {
	width: 1300px !important;
	.ant-modal-title {
		font-size: 24px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		text-align: center;
	}
	.modal-content {
		position: relative;

		.example {
			display: flex;
			align-items: center;
			justify-content: center;
			position: absolute;
			inset: 0;
			background: rgba(255, 255, 255, 0.6);
			border-radius: 4px;
		}
	}
	.tag-content,
	.other-content {
		background: #f7fbff;
		.tag-inner {
			padding: 24px 24px 50px;
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
		}
		.legend {
			padding: 12px 0px;
			display: flex;
			justify-content: center;
			gap: 30px;
			.tag-1,
			.tag-2 {
				display: flex;
				align-items: center;
				font-size: 16px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				&::before {
					content: '';
					display: inline-block;
					width: 8px;
					height: 8px;
					margin-right: 4px;
				}
			}
			.tag-1 {
				&::before {
					background: #008eff;
				}
			}
			.tag-2 {
				&::before {
					background: #ff6a16;
				}
			}
		}
	}
	.other-content {
		padding: 24px;
		margin-top: 20px;
		height: 220px;
		background: #f6fff7;
		border-radius: 8px 8px 8px 8px;
		.title {
			display: flex;
			align-items: center;
			font-size: 22px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			line-height: 22px;
			&::before {
				content: '';
				margin-right: 7px;
				display: inline-block;
				width: 8px;
				height: 24px;
				background: url('@/assets/images/card-icon.png') no-repeat center / cover;
			}
		}
	}
}
</style>
