<template>
	<min-card title="能力-业绩坐标图" header size-type="3">
		<div class="coordinate">
			<v-chart :option="option" autoresize ref="echartsRef"></v-chart>
		</div>
	</min-card>
</template>

<script lang="ts">
import { defineComponent, ref, computed, toRefs } from 'vue'
import { decreaseOpacity, convertPxToRem, debounce } from '@/utils/utils'
// 0-全区, 1-本单位, 2-同序列, 3-全部乡镇/部门
const menu = [
	{
		label: '本单位',
		key: 1,
	},
	{
		label: '同序列',
		key: 2,
	},
	{
		label: '乡镇（部门）',
		key: 3,
	},
	{
		label: '全区',
		key: 0,
	},
]
// const self = [91, 28.8]
export default defineComponent({
	name: 'Coordinate',
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	setup(props) {
		const { data } = toRefs(props)
		const echartsRef = ref<any>(null)

		const option = computed(() => {
			const color = ['#6AAEFB', '#FE533A', '#FFA300', '#43C8D0', '#C7A7FF', '#46DA9C', '#939DFF', '#C7AD26', '#FF84CE', '#C97818']
			let x_avg = 0
			let _data: any = []
			// 处理数据
			let series = data.value
				?.filter((item: any) => item.mine?.x)
				.map((item: any, index: number) => {
					const { mine } = item

					const data = [mine.x, mine.y, mine.name, mine.user_id]
					x_avg += mine.x

					_data.push(data)

					return {
						name: mine.name,
						type: 'scatter',
						data: [data],
						itemStyle: {
							color: color[index],
						},
						// markLine: {
						// 	symbol: 'none',
						// 	label: { show: false },
						// 	silent: true,
						// 	data: [
						// 		{
						// 			type: 'average',
						// 			name: '平均值',
						// 		},
						// 		{
						// 			xAxis: x_avg,
						// 		},
						// 	],
						// },
					}
				})
			x_avg = x_avg / data.value.length
			const _option = {
				color: ['#FE533A'],
				grid: {
					top: '13%',
					left: '4%',
					right: '5%',
					bottom: '10%',
					containLabel: true,
					show: false,
				},
				legend: {
					show: true,
					// selectedMode: false,
					textStyle: {
						color: '#333333',
						fontSize: convertPxToRem(16),
					},
					icon: 'circle',
					itemGap: 40,
					itemWidth: convertPxToRem(12),
					itemHeight: convertPxToRem(12),
					itemStyle: {
						borderWidth: convertPxToRem(4),
					},
				},
				tooltip: {
					trigger: 'item',
					showDelay: 0,
					show: true,
					formatter: function (name: any) {
						const { value } = name
						return `
							<div class="tooltips-box">
								<div class="data-name">${value[2]}</div>
								<div class="data-item">业绩：${value[1] || ''}</div>
								<div class="data-item">能力+口碑：${value[0] || ''}</div>
							</div>
						`
					},
				},
				xAxis: {
					name: '能力+口碑',
					type: 'value',
					scale: true,
					axisLabel: {
						color: '#333333',
						formatter: (value: any) => Number(value).toFixed(2),
						showMaxLabel: false,
						fontSize: convertPxToRem(16),
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#666666',
						},
						symbol: ['none', 'arrow'],
						symbolOffset: 7,
						symbolSize: [7, 10],
					},
					splitLine: {
						show: false,
					},
					min: (value: any) => (value.min === 0 ? 0 : value.min - 0.01),
					max: (value: any) => value.max + 0.01,
					// min: 50,
					// max: 100,
					// splitNumber: 4,
					// 刻度线
					axisTick: {
						show: true,
					},
					nameTextStyle: {
						color: '#333333',
						// align: 'left',
						padding: [7, 0, 0, -40],
						verticalAlign: 'top',
					},
				},
				yAxis: {
					name: '业绩',
					type: 'value',
					// scale: true,
					axisLabel: {
						color: '#666666',
						fontSize: convertPxToRem(16),
						formatter: (value: any) => {
							value = Number(value)
							return value > 100 ? '' : value.toFixed(2)
						},
						showMaxLabel: false,
					},
					splitLine: {
						show: true,
						lineStyle: {
							color: '#CAC9C9',
							type: 'dashed',
						},
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#666666',
						},
						symbol: ['none', 'arrow'],
						symbolOffset: 7,
						symbolSize: [7, 10],
					},
					axisTick: {
						show: false,
					},
					nameTextStyle: {
						color: '#333333',
						// align: 'right',
						padding: [0, 20, -5, -20],
					},
					min: (value: any) => {
						return value.min - 0.05
					},
					max: (value: any) => {
						return value.max + 0.05
					},
					// splitNumber: 7,
				},
				series: [
					...series,
					{
						name: '',
						type: 'scatter',
						data: _data,
						symbol: () => {
							return 'none'
						},
						markLine: {
							symbol: 'none',
							label: { show: false },
							silent: true,
							data: [
								{
									type: 'average',
									name: '平均值',
								},
								{
									xAxis: x_avg,
								},
							],
						},
					},
				],
			}

			return _option
		})

		return {
			menu,
			option,
			echartsRef,
		}
	},
})
</script>

<style scoped lang="less">
:deep(.card-header) {
	border-radius: 6px 6px 0px 0px;
}
.data-menu {
	display: flex;
	.menu-item {
		margin-left: 15px;
		font-size: 14px;
		font-weight: 400;
		color: #9e9e9e;
		cursor: pointer;
	}
	.menu-active {
		color: #00eaff;
	}
}

.coordinate {
	width: 100%;
	height: 329px;
}
</style>
