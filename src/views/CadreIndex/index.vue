<template>
	<div style="width: 100%; height: 100%">
		<layout title="干部画像">
			<div class="container">
				<div class="title"><span class="back" @click="onBack"></span> 干部指数</div>
				<div class="table-container">
					<div class="btn-box">
						<a-button @click="showModal" :disabled="selectedRowKeys.length < 2 || selectedRowKeys.length > 10">对比分析</a-button>
						<a-button @click="onCollect" :disabled="selectedRowKeys.length === 0">收藏</a-button>
					</div>
					<div id="table">
						<a-table
							rowKey="user_id"
							:columns="columns"
							:data-source="datasource"
							:row-selection="{ hideDefaultSelections: true, columnTitle: '勾选', onSelect: onSelect, selectedRowKeys: selectedRowKeys }"
							:scroll="{ y: tableScrollY }"
							:pagination="false"
							:loading="loading"
						>
							<template #bodyCell="{ column, record }">
								<template v-if="column.key === 'head_url'">
									<CodeAvatar :head_url="record.head_url" />
								</template>
								<template v-if="column.key === 'name'">
									<a @click="onLocation(record)">{{ record.name }}</a>
								</template>
								<template v-if="column.key === 'same_rank'">
									{{
										homeStore.coor_menu_selected === TOWN || homeStore.coor_menu_selected === COUNTY
											? `${record.rank}/${datasource.length}`
											: record.same_rank
									}}
								</template>
							</template>
						</a-table>
					</div>
				</div>
			</div>
			<!-- <a-modal v-model:visible="visible" width="70%" title="分析维度" @ok="handleOk" class="search-modal">
				<div class="veidoo">
					<div class="veidoo-item">
						<div class="title">基础信息</div>
						<div class="data">
							<div class="check-item" v-for="(item, index) in basicMessage" :key="index">
								<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
							</div>
						</div>
					</div>
					<div class="veidoo-item">
						<div class="title">干部画像</div>
						<div class="data">
							<div class="check-item" v-for="(item, index) in cadreMessage" :key="index">
								<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
							</div>
						</div>
					</div>
				</div>
			</a-modal> -->
			<a-modal class="folder-modal" :closable="false" :visible="collectVisible" width="" destroyOnClose :footer="null" @cancel="onClose">
				<Folder @close="onClose" @success="onSuccess" modal-type="collect" :source-ids="selectedRowKeys.join(',')" />
			</a-modal>
		</layout>
	</div>
</template>
<script lang="ts">
export default {
	name: 'CadreIndex',
}
</script>
<script lang="ts" setup>
import { shallowRef, ref, unref, onDeactivated, onMounted, onActivated, computed } from 'vue'
import { useRouter } from 'vue-router'
import { historyPush } from '@/utils/history'
import { CDN_URL } from '@/config/env'
import { message } from 'ant-design-vue'
import { getDimensionConfig } from '@/apis/cadre-portrait/home'
import { Base64, getUserInfo, appendParamsToUrl } from '@/utils/utils'
import { useComparative } from '@/store/comparative'

import useKeepalive from '@/store/keepalive'
import Layout from '@/layout/index.vue'
import { useHomeStore, TOWN, COUNTY } from '@/store/home'

const router = useRouter()
const keepalive = useKeepalive()
const homeStore = useHomeStore()
const comparative = useComparative()

const _d = sessionStorage.getItem('cadre_data') || '[]'

const selectedRowKeys = ref<Array<string>>([])
const basicMessage = ref<Array<any>>([])
const cadreMessage = ref<Array<any>>([])
const loading = ref(true)
const collectVisible = ref(false)
const datasource = shallowRef([])

setTimeout(() => {
	datasource.value = JSON.parse(_d)
	loading.value = false
}, 500)

const columns = computed(() => {
	const typeMap: any = {
		[TOWN]: '指数乡镇（部门）排名',
		[COUNTY]: '指数全县排名',
	}
	return [
		{
			key: 'rank',
			dataIndex: 'rank',
			align: 'center',
			width: '6%',
			title: '排名',
		},
		{
			key: 'head_url',
			dataIndex: 'head_url',
			align: 'center',
			width: '7%',
			title: '头像',
		},
		{
			key: 'name',
			dataIndex: 'name',
			align: 'center',
			width: '7%',
			title: '姓名',
			// colClass: blur.value ? 'filter-style' : '',
			colClass: 'cursor-pointer',
			colClick: (_data: any, event: any) => {
				event.stopPropagation()
			},
		},

		// {
		// 	key: 'portrait',
		// 	align: 'center',
		// 	width: '9%',
		// 	title: '干部画像',
		// 	// colClass: blur.value ? 'filter-style' : '',
		// },
		{
			key: 'current_position',
			dataIndex: 'current_position',
			align: 'left',
			width: '20%',
			title: '现任职务',
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'birthday',
			dataIndex: 'birthday',
			align: 'center',
			width: '10%',
			title: '出生年月（年龄）',
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'current_position_time',
			dataIndex: 'current_position_time',
			align: 'center',
			width: '10%',
			title: '任现职务时间',
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'diploma',
			dataIndex: 'diploma',
			align: 'center',
			width: '10%',
			title: '全日制学历',
			// colClass: blur.value ? 'filter-style' : '',
		},
		{
			key: 'school',
			dataIndex: 'school',
			align: 'left',
			width: '10%',
			title: '毕业院校及专业',
			// colClass: blur.value ? 'filter-style' : '',
		},
		// {
		// 	key: 'specialty',
		// 	align: 'center',
		// 	width: '12%',
		// 	title: '专业',
		// 	// colClass: blur.value ? 'filter-style' : '',
		// },
		{
			key: 'cadre_index',
			dataIndex: 'cadre_index',
			align: 'center',
			width: '6%',
			title: '干部指数',
		},
		{
			key: 'same_rank',
			dataIndex: 'same_rank',
			align: 'center',
			width: '10%',
			title: [TOWN, COUNTY].includes(homeStore.coor_menu_selected) ? typeMap[homeStore.coor_menu_selected] : '指数同序列排名',
		},
		// {
		// 	key: 'achievement',
		// 	align: 'center',
		// 	width: '8%',
		// 	title: '业绩',
		// },
		// {
		// 	key: 'ability',
		// 	align: 'center',
		// 	width: '8%',
		// 	title: '能力',
		// },
		// {
		// 	key: 'praise',
		// 	align: 'center',
		// 	width: '8%',
		// 	title: '口碑',
		// },
		// {
		// 	key: 'politics',
		// 	align: 'center',
		// 	width: '8%',
		// 	title: '政治',
		// 	customization: 'CustomBlock',
		// },
	]
})

const onSelect = (record: any) => {
	const index = selectedRowKeys.value.findIndex((item: any) => {
		return item === record.user_id
	})
	if (index === -1) {
		selectedRowKeys.value.push(record.user_id)
	} else {
		selectedRowKeys.value.splice(index, 1)
	}
}

const onLocation = (data: any) => {
	keepalive.push('CadreIndex')

	historyPush(`/cadre-portrait/home?user_id=${data.user_id}`)
}

const visible = ref(false)

const showModal = () => {
	if (unref(selectedRowKeys).length > 10) {
		return message.error('最多可选择10人')
	}

	handleOk()
}

const goPage = ({ path, ...parmas }: any) => {
	const userInfo = JSON.stringify(getUserInfo())

	keepalive.push('CadreIndex')

	router.push({
		path,
		query: {
			...parmas,
		},
	})
}

const handleOk = () => {
	const config_ids: any[] = []
	basicMessage.value.forEach((element: any) => {
		if (element.is_select) {
			config_ids.push(element.config_id)
		}
	})
	cadreMessage.value.forEach((element: any) => {
		if (element.is_select) {
			config_ids.push(element.config_id)
		}
	})
	goPage({ path: '/comparison-results', user_id: selectedRowKeys.value.join(','), config_ids: comparative.config_ids.join(',') })
}

const onClose = () => {
	collectVisible.value = false
}
const onSuccess = () => {
	collectVisible.value = false
}
const onCollect = () => {
	collectVisible.value = true
}
const onBack = () => {
	keepalive.remove('CadreIndex')
	router.back()
}
onDeactivated(() => {
	visible.value = false
})
const tableScrollY = ref(0)
const scrollTop = ref(0)

onMounted(() => {
	const head = document.querySelector('.ant-table-thead')
	const table = document.querySelector('#table')
	const body = document.querySelector('.ant-table-body')

	tableScrollY.value = table?.offsetHeight - head?.offsetHeight

	body?.addEventListener('scroll', (e: any) => {
		scrollTop.value = e.target.scrollTop
	})
	getDimensionConfig({}).then(({ data, code }: any) => {
		if (code == 0) {
			data.map((item: any) => {
				if (item.name == '基础信息') {
					basicMessage.value = item.children
				} else {
					cadreMessage.value = item.children
				}
			})
		}
	})
})
onActivated(() => {
	const body = document.querySelector('.ant-table-body')
	body?.scrollTo(0, scrollTop.value)
})
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100%;
	padding: 32px 16px;
	display: flex;
	flex-direction: column;
	background-color: #f6f8fc;
	.title {
		display: flex;
		align-items: center;
		font-size: 26px;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #000000;
		line-height: 26px;
		.back {
			margin-right: 7px;
			display: inline-block;
			width: 25px;
			height: 25px;
			background: url('@/assets/images/arrow-left.png') no-repeat center / 100%;
			cursor: pointer;
		}
	}
	.table-container {
		display: flex;
		flex-direction: column;
		padding: 24px;
		margin-top: 16px;
		flex: 1;
		width: 100%;
		background-color: #ffffff;
		.btn-box {
			display: flex;
			justify-content: flex-end;
			margin-bottom: 16px;
			button {
				padding: 0px;
				width: 86px;
				height: 40px;
				margin-left: 20px;
				background: #e5251b;
				border-radius: 4px;
				font-size: 16px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
				outline: none;
				border: none;
			}
			button[disabled] {
				background: #ebebeb;
				color: #999999;
			}
		}
		#table {
			flex: 1;
			.ant-table-wrapper {
				height: 100%;
			}
		}
		.avatar {
			width: 53px;
			height: 66px;
			background: #c4c4c4;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			object-fit: contain;
		}
	}
}
:deep(.ant-table) {
	.ant-table-thead {
		& > tr > th {
			background-color: #f3f3f3;
		}
	}
	.ant-table-cell {
		padding: 12px 16px;
	}
	.ant-table-body {
		.ant-table-cell {
			font-size: 18px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.9);
			line-height: 21px;
		}
	}
	.ant-checkbox-checked .ant-checkbox-inner {
		background-color: #ee391f;
		border-color: #ee391f;
	}

	.ant-checkbox-checked::after {
		border-color: #ee391f;
	}
	.ant-checkbox-wrapper:hover .ant-checkbox-inner,
	.ant-checkbox:hover .ant-checkbox-inner,
	.ant-checkbox-input:focus + .ant-checkbox-inner {
		border-color: #ee391f !important;
	}

	.ant-table .ant-checkbox-wrapper:hover .ant-checkbox-inner,
	.ant-table .ant-checkbox:hover .ant-checkbox-inner,
	.ant-table .ant-checkbox-input:focus + .ant-checkbox-inner {
		border-color: #ee391f !important;
	}
}

.veidoo {
	margin-top: 10px;
	width: 100%;
	.top-content {
		display: flex;
		margin-top: 49px;
		margin-bottom: 31px;

		.label {
			display: flex;
			align-items: center;
			font-size: 18px;
			font-weight: bold;
			color: #00fff6;
			line-height: 18px;
			text-shadow: 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2);
			background: linear-gradient(0deg, #3bdeff 6.1279296875%, #d1fbff 55.4443359375%, #ddf9ff 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;

			&::after {
				margin-left: 7px;
				content: '';
				display: inline-block;
				width: 24px;
				height: 16px;
				background: url('@/assets/images/label-icon.png') no-repeat center / cover;
			}
		}
	}
	.top-content-two {
		margin-bottom: 22px;
	}
	.veidoo-item {
		display: flex;
		border: 1px solid #ebebeb;
		.title {
			display: flex;
			align-items: center;
			justify-content: center;

			width: 100px;
			background: #f3f3f3;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;

			font-size: 16px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #222222;
			line-height: 24px;
		}

		.data {
			flex: 1;
			padding: 28px 84px;

			display: flex;
			flex-wrap: wrap;
			.check-item {
				width: 25%;
			}
		}

		.two-title {
			display: flex;
			width: 169px;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 68px 0;
		}

		div {
			font-size: 14px;
			font-family: Source Han Sans CN;
			font-weight: 500;
			color: #00d2ff;
		}
	}
}
:deep(.ant-table-selection-column) {
	white-space: nowrap;
}
</style>
<style lang="scss">
.folder-modal {
	width: 752px;
	.ant-modal-body {
		padding: 0px !important;
	}
}

.search-modal {
	margin-top: 24px;
	padding: 24px;
	width: 100%;
	.ant-checkbox-wrapper {
		font-size: 20px;
	}
	button {
		font-size: 19px;
		height: 40px;
		width: 80px;
	}
}
</style>
