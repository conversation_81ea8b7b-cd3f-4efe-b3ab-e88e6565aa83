<template>
	<div class="table-list">
		<div class="table" v-if="type === '1'">
			<a-table :columns="columns" row-key="user_id" :dataSource="data" :pagination="false">
				<template #bodyCell="{ column, index, record }">
					<template v-if="column.key === 'rank'">
						<span>{{ index + 1 }}</span>
					</template>
					<template v-if="column.key === 'head_url'">
						<CodeAvatar :head_url="record.head_url">
							<template #avatar="{ avatar }">
								<img v-lazy="avatar" alt="" class="avatar" />
							</template>
						</CodeAvatar>
					</template>
					<template v-if="column.key === 'name'">
						<a @click="onLocation(record)">{{ record.name }}</a>
					</template>
					<template v-if="column.key === 'current_job_time'">
						<span>{{ convertDay(record.current_job_time) }}</span>
					</template>
				</template>
			</a-table>
		</div>
		<div class="card-box" v-else>
			<user-card type="collect" v-for="user in data" :key="user.user_id" :user="user" @card-click="onLocation" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, toRefs } from 'vue'
import { historyPush } from '@/utils/history'
import { CDN_URL } from '@/config/env'
import UserCard from './user-card.vue'
import defaultAvatar from '@/assets/images/avatar.png'
import dayJs from 'dayjs'
import CodeAvatar from '@/components/CodeAvatar.vue'
const props = defineProps({
	// 决定组件展示类型， 1 表格，2为依次排列
	type: {
		type: String,
		default: '1',
	},
	// 是否是中层干部
	middle: Boolean,
	data: {
		type: Array,
		default: () => [],
	},
	vacancy: {
		type: Array,
		default: () => [],
	},
	sliderStatus: {
		type: Boolean,
		default: () => {
			return false
		},
	},
})

// 	@icon-click="onIconClick"
// @card-click="onCardClick"

const emit = defineEmits(['icon-click', 'card-click'])

const onIconClick = async (data: any) => {
	emit('icon-click', data)
}

const onCardClick = (data: any) => {
	emit('card-click', data)
}

const columns = [
	{
		key: 'rank',
		dataIndex: 'rank',
		align: 'center',
		width: '6%',
		title: '序号',
	},
	{
		key: 'head_url',
		dataIndex: 'head_url',
		align: 'center',
		width: '7%',
		title: '照片',
	},
	{
		key: 'name',
		dataIndex: 'name',
		align: 'center',
		width: '7%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},
	{
		key: 'current_job',
		dataIndex: 'current_job',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'current_job_time',
		dataIndex: 'current_job_time',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'full_time_diploma',
		dataIndex: 'full_time_diploma',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'school_department',
		dataIndex: 'school_department',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'specialty',
	// 	align: 'center',
	// 	width: '12%',
	// 	title: '专业',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'cadre_index',
		dataIndex: 'cadre_index',
		align: 'center',
		width: '6%',
		title: '干部指数',
	},
	{
		key: 'cadre_index_rank',
		dataIndex: 'cadre_index_rank',
		align: 'center',
		width: '10%',
		title: '指数同序列排名',
	},
]

const onLocation = (data: any) => {
	if (!data.user_id) return
	if (props.middle) {
		historyPush(`/cadre-portrait/cadre-table?user_id=${data.user_id}`)
	} else {
		historyPush(`/cadre-portrait/home?user_id=${data.user_id}`)
	}
}

const getAvatar = (head_url?: string) => {
	return head_url ? `${CDN_URL}/fr_img/${head_url}` : defaultAvatar
}

const convertDay = (time: string) => {
	return time ? dayJs(time).format('YYYY.MM') : '-'
}
</script>

<style lang="less" scoped>
.table-list {
	width: 100%;
	.table {
		width: 100%;
	}
	.avatar {
		width: 53px;
		height: 66px;
		background: #c4c4c4;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		object-fit: contain;
	}

	.card-box {
		padding: 27px 0px;
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		gap: 16px 20px;

		:deep(.small) {
			margin: 0px;
		}
	}
}
:deep(.ant-table-cell) {
	font-size: 18px !important;
}
</style>
