<script lang="ts" setup>
import { reactive, ref, onMounted, unref, onDeactivated, onActivated, onUnmounted } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { getPmsLeader } from '@/apis/search'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import { Base64, getUserInfo, appendParamsToUrl } from '@/utils/utils'
import { getDimensionConfig } from '@/apis/cadre-portrait/home'
import usePage from '@/store/page'

interface FormState {
	birthday: [string, string]
	ethic: [string, string]
	gender: [string, string]
	political: [string, string]
	join_time: [string, string]
	cadre_category: [string, string]
	identity: [string, string]
	full_time_education: [string, string]
	on_job_education: [string, string]
	full_time_school: [string, string]
	current_rank: [string, string]
	major: [string, string]
	profession_specialty: string
	technical_position: string
	current_job: string
	current_job_time_gte: string
	current_job_time_lte: string
	current_rank_time_gte: string
	current_rank_time_lte: string
}

const route = useRoute()

const router = useRouter()

const page = usePage()

const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 18 },
}
const formRef = ref<FormInstance>()
const selectedRowKeys = ref<any[]>([])

const pagination = reactive({
	showSizeChanger: false,
	showQuickJumper: true,
	showTotal: (total: number) => `共${total}条数据`,
} as any)
const visible = ref<boolean>(false)
const drawerVisible = ref<boolean>(false)
const basicMessage = ref<any>([])
const cadreMessage = ref<any>([])
const dataSource = ref<any>([])
const tableLoading = ref<boolean>(false)
const columns = [
	{
		title: '姓名',
		dataIndex: 'username',
		width: 80,
		align: 'center',
	},
	{
		title: '现任职务',
		dataIndex: 'current_job',
	},
	{
		title: '出生年月',
		dataIndex: 'birthday',
		width: 100,
	},
	{
		title: '全日制学历',
		dataIndex: 'diploma',
		width: 120,
		align: 'center',
	},
	{
		title: '全日制学历毕业院校及专业',
		dataIndex: 'school',
	},
	{
		title: '干部指数',
		dataIndex: 'cadre_index',
		width: 100,
		align: 'center',
	},
	{
		title: '操作',
		key: 'action',
		align: 'center',
		width: 220,
	},
]

onMounted(() => {
	onFinish({})
	getDimensionConfig({}).then(({ data, code }: any) => {
		if (code == 0) {
			data.map((item: any) => {
				if (item.name == '基础信息') {
					basicMessage.value = item.children
				} else {
					cadreMessage.value = item.children
				}
			})
		}
	})
})
onActivated(() => {
	onFinish({})
})
onDeactivated(() => {
	visible.value = false
})
const resetForm = () => {
	formRef?.value?.resetFields()
	/* setTimeout(() => {
		onFinish({})
	}, 100) */
}

const showModal = () => {
	if (unref(selectedRowKeys).length > 10) {
		return message.error('最多可选择10人')
	}
	visible.value = true
}

const handleOk = () => {
	const config_ids: any[] = []

	if (selectedRowKeys.value.length === 0) {
		return message.error('请选择人员')
	}

	basicMessage.value.forEach((element: any) => {
		if (element.is_select) {
			config_ids.push(element.config_id)
		}
	})
	cadreMessage.value.forEach((element: any) => {
		if (element.is_select) {
			config_ids.push(element.config_id)
		}
	})

	if (config_ids.length === 0) {
		return message.error('请选择分析维度')
	}
	goPage({ path: '/comparison-results', user_id: selectedRowKeys.value.join(','), config_ids: config_ids.join(',') })
}

const goPage = ({ path, ...parmas }: any) => {
	routeAction = true

	const userInfo = JSON.stringify(getUserInfo())

	const _h = Base64.encode(userInfo)
	router.push({
		path,
		query: {
			...parmas,
			_h,
		},
	})
}
let routeAction = false

// onUnmounted(() => {
// 	if(routeAction) {

// 	}else {

// 	}
// })
// location 跳转
const onLocation = ({ path, ...parmas }: any) => {
	const userInfo = JSON.stringify(getUserInfo())

	const _h = Base64.encode(userInfo)

	const url = appendParamsToUrl(path, { ...parmas, _h: _h })

	// window.location.href = url
	window.open(url)
}

const handlePaginationChange = (params: any) => {
	onFinish({ page: params.current })
}

const onSelectChange = (rowKeys: any[]) => {
	selectedRowKeys.value = rowKeys
}
let _selectedRowKeys: any = []

const onSelect = (record: any) => {
	const index = _selectedRowKeys.findIndex((item: any) => {
		return item === record.user_id
	})
	if (index == -1 && _selectedRowKeys.length < 10) {
		_selectedRowKeys.push(record.user_id)
	} else {
		if (index == -1) {
			return void 0
		}
		_selectedRowKeys.splice(index, 1)
	}
	selectedRowKeys.value = [..._selectedRowKeys]
}
// 删除选中项中的数据
const deleteName = (index: number) => {
	selectedRowKeys.value.splice(index, 1)
	_selectedRowKeys.splice(index, 1)
}
const onFinish = ({ page = 1 }: any) => {
	const formState = JSON.parse(sessionStorage.getItem('search_params') || '{}')

	const params: any = { ...formState, page }

	getPmsLeader(params).then((res: any) => {
		tableLoading.value = false
		if (res.code === 0) {
			const { content, totalElements }: any = res.data || {}
			dataSource.value = content || []
			pagination.total = totalElements
		}
	})
}
</script>

<template>
	<div class="search-wrap">
		<div class="form-wrap">
			<div class="comparison-box">
				<a-button @click="visible = true" type="primary" size="small">对比分析</a-button>
			</div>
			<div class="search-res">
				<a-table
					rowKey="user_id"
					:loading="tableLoading"
					:columns="columns"
					:data-source="dataSource"
					:pagination="pagination"
					@change="handlePaginationChange"
					:row-selection="{ selectedRowKeys: selectedRowKeys, onSelect: onSelect, columnTitle: ' ' }"
				>
					<template #bodyCell="{ column, record }">
						<template v-if="column.key === 'action'">
							<a @click="goPage({ path: '/cadre-form', user_id: record.user_id })">干部任免审批表</a>
							<a-divider type="vertical" />
							<a @click="goPage({ path: '/cadre-portrait/home', user_id: record.user_id })">干部画像</a>
						</template>
					</template>
				</a-table>
			</div>
		</div>
		<a-modal v-model:visible="visible" width="70%" height="100%" title="分析维度" @ok="handleOk" class="search-modal">
			<div class="veidoo">
				<div class="veidoo-item">
					<div class="title">基础信息</div>
					<div class="data">
						<div class="check-item" v-for="(item, index) in basicMessage" :key="index">
							<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
						</div>
					</div>
				</div>
				<div class="veidoo-item">
					<div class="title">干部画像</div>
					<div class="data">
						<div class="check-item" v-for="(item, index) in cadreMessage" :key="index">
							<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
						</div>
					</div>
				</div>
			</div>
		</a-modal>
	</div>
</template>

<style lang="less" scoped>
.search-wrap {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 100%;
	overflow-y: auto;
	// background: #f6f9fc url(./back.png) no-repeat top center/contain;
	.form-wrap {
		position: relative;
		width: 90%;
		.comparison-box {
			margin-bottom: 20px;
			width: 100%;
			display: flex;
			justify-content: flex-end;
		}
	}
	.search-form {
		user-select: none;
		::v-deep(.ant-card-body) {
			padding: 24px 24px 5px !important;
		}
		width: 100%;
		.ant-card {
			// margin-bottom: 16px;
			.ant-row {
				margin-bottom: 10px;
			}
			.item-h {
				align-items: center;
				margin: 0 !important;
				.ant-form-item {
					margin-bottom: 0;
				}
			}
		}
		.ant-checkbox-group {
			.ant-checkbox-wrapper {
				margin-left: 0;
				margin-right: 10px;
			}
		}
		// .cadre_category {
		// 	::v-deep(.ant-checkbox-wrapper) {
		// 		margin-bottom: 10px;
		// 	}
		// }
	}
	.search-title {
		font-size: 20px;
		font-weight: bold;
	}
	.search-result {
		width: 100%;
		height: 100%;
	}

	.ant-btn {
		height: 36px;
		padding: 0 16px;
		font-size: 16px;
	}
	.btn-wrap {
		margin-bottom: 10px;
	}
}
.search-modal {
	margin-top: 24px;
	padding: 24px;
	width: 100%;
	background: #ffffff;
	.veidoo {
		margin-top: 10px;
		width: 100%;
		.top-content {
			display: flex;
			margin-top: 49px;
			margin-bottom: 31px;

			.label {
				display: flex;
				align-items: center;
				font-size: 18px;
				font-weight: bold;
				color: #00fff6;
				line-height: 18px;
				text-shadow: 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2),
					0 0 4px rgba(35, 219, 252, 0.2);
				background: linear-gradient(0deg, #3bdeff 6.1279296875%, #d1fbff 55.4443359375%, #ddf9ff 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;

				&::after {
					margin-left: 7px;
					content: '';
					display: inline-block;
					width: 24px;
					height: 16px;
					background: url('@/assets/images/label-icon.png') no-repeat center / cover;
				}
			}
		}
		.top-content-two {
			margin-bottom: 22px;
		}
		.veidoo-item {
			display: flex;
			border: 1px solid #ebebeb;
			.title {
				display: flex;
				align-items: center;
				justify-content: center;

				width: 100px;
				height: 192px;
				background: #f3f3f3;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;

				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #222222;
				line-height: 24px;
			}

			.data {
				flex: 1;
				padding: 28px 84px;

				display: flex;
				flex-wrap: wrap;
				.check-item {
					width: 25%;
				}
			}

			.two-title {
				display: flex;
				width: 169px;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 68px 0;
			}

			div {
				font-size: 14px;
				font-family: Source Han Sans CN;
				font-weight: 500;
				color: #00d2ff;
			}
		}
	}
}
</style>
