<template>
	<div class="table-list">
		<div class="table" v-if="type === '1'">
			<a-table :columns="columns" row-key="user_id" :dataSource="sortData" :pagination="false">
				<template #bodyCell="{ column, record }">
					<template v-if="column.key === 'head_url'">
						<CodeAvatar :user_id="record.user_id" :head_url="record.head_url">
							<template #avatar="{ avatar }">
								<img :src="avatar" :key="avatar" alt="" class="avatar" decoding="async" />
							</template>
						</CodeAvatar>
					</template>
					<template v-if="column.key === 'user_name'">
						<a @click="onLocation(record)">{{ record.user_name }}</a>
					</template>
					<template v-if="column.key === 'current_job_time'">
						<span>{{ convertDay(record.current_job_time) }}</span>
					</template>
					<template v-if="column.key === 'birthday'">
						<span>{{ record.birthday }}</span>
					</template>
				</template>
			</a-table>
		</div>
		<div class="card-box" v-else>
			<template v-for="user in dataSource" :key="user.user_id + user.position">
				<UserCard
					:user="user"
					@card-click="onLocation(user)"
					:empty="!!user.user_id"
					:style="{ width: sliderStatus ? '12.9%' : '11.38%' }"
					:middle="middle"
					:showPosition="showPosition"
				/>
			</template>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, toRefs } from 'vue'
import { historyPush } from '@/utils/history'
import { CDN_URL } from '@/config/env'
import UserCard from '../../components/user-card.vue'
import defaultAvatar from '@/assets/images/avatar.png'
import CodeAvatar from '@/components/CodeAvatar.vue'
import dayJs from 'dayjs'
const props = defineProps({
	// 决定组件展示类型， 1 表格，2为依次排列
	type: {
		type: String,
		default: '1',
	},
	// 是否是中层干部
	middle: Boolean,
	data: {
		type: Array,
		default: () => [],
	},
	vacancy: {
		type: Array,
		default: () => [],
	},
	sliderStatus: {
		type: Boolean,
		default: () => {
			return false
		},
	},
	showPosition: {
		type: Boolean,
		default: true,
	},
	sort: {
		type: Object,
		default: () => ({}),
	},
})

const dataSource: any = computed(() => {
	const vacancy = props.vacancy

	const data = props.data

	const _data = [
		...data,
		...vacancy.map((item: any) => {
			return { position: item }
		}),
	]

	return _data.map((item: any, index: any) => {
		return {
			...item,
			rank: index + 1,
		}
	})
})

const sortData: any = computed(() => {
	const vacancy = props.vacancy
	const { data, sort } = props
	const { key, type } = sort
	console.log('🚀 ~ constsortData:any=computed ~ type:', type)
	let tmp = [...data]
	console.log('---------------------------------')
	if (key) {
		tmp.sort((a: any, b: any) => {
			if (key === 'current_job_time') {
				a[key] = new Date(a[key])
				b[key] = new Date(b[key])
			}
			return type === 'up' ? a[key] - b[key] : b[key] - a[key]
		})
	}

	const _data = [
		...tmp,
		...vacancy.map((item: any) => {
			return { position: item }
		}),
	]
	return _data.map((item: any, index: any) => {
		return {
			...item,
			rank: index + 1,
		}
	})
})

const _columns = [
	{
		key: 'rank',
		dataIndex: 'rank',
		align: 'center',
		width: '6%',
		title: '序号',
	},
	{
		key: 'head_url',
		dataIndex: 'head_url',
		align: 'center',
		width: '7%',
		title: '头像',
	},
	{
		key: 'user_name',
		dataIndex: 'user_name',
		align: 'center',
		width: '7%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},
	{
		key: 'position',
		dataIndex: 'position',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'current_job_time',
		dataIndex: 'current_job_time',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'diploma',
		dataIndex: 'diploma',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'major',
		dataIndex: 'major',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'specialty',
	// 	align: 'center',
	// 	width: '12%',
	// 	title: '专业',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'cadre_index',
		dataIndex: 'cadre_index',
		align: 'center',
		width: '6%',
		title: '干部指数',
	},
	{
		key: 'cadre_index_rank',
		dataIndex: 'cadre_index_rank',
		align: 'center',
		width: '10%',
		title: '指数同序列排名',
	},
]

const columns = computed(() => {
	const _: any = []
	_columns.map((item: any) => {
		if (!props.showPosition && item.key == 'position') {
			return
		}
		_.push(item)
	})
	return _
})
const onLocation = (data: any) => {
	if (!data.user_id) return
	if (props.middle) {
		sessionStorage.setItem('user_type', 'shiguanganbu')

		historyPush(`/cadre-portrait/cadre-table?user_id=${data.user_id}`)
	} else {
		sessionStorage.removeItem('user_type')

		historyPush(`/cadre-portrait/home?user_id=${data.user_id}`)
	}
}

// const getAvatar = (head_url?: string) => {
// 	return head_url ? `${CDN_URL}/fr_img/${head_url}` : defaultAvatar
// }
const getAvatar = (head_url?: string) => {
	return head_url || defaultAvatar
}

const convertDay = (time: string) => {
	return time ? dayJs(new Date(time)).format('YYYY.MM') : '-'
}
</script>

<style lang="less" scoped>
.table-list {
	width: 100%;
	.table {
		width: 100%;
	}
	.avatar {
		width: 53px;
		height: 66px;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		object-fit: contain;
	}

	.card-box {
		padding: 27px 0px;
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		gap: 16px 20px;

		:deep(.small) {
			margin: 0px;
		}
	}
}
:deep(.ant-table-cell) {
	font-size: 18px !important;
}
</style>
