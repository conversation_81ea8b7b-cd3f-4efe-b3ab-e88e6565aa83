<template>
	<div class="structural-analysis">
		<div class="left-box">
			<div class="town-header">
				<span class="town-icon" @click="changePage(2)"></span>
				<span class="town-title">{{ orgName }}班子结构分析</span>
			</div>
			<div class="content-box">
				<div class="data-card—top">
					<div class="total-position">
						<img src="../images/base-icon.png" alt="" />
						<div class="text-data">
							<div class="text">干部总数：</div>
							<div class="top">
								<span class="number">{{ data.user_count }}</span>
								<span class="label">人</span>
							</div>
						</div>
					</div>
					<div class="card-content">
						<div class="total-data">
							<span class="total-data-title">总职数：</span>
							<span class="total-data-num">{{ data.job_count }}</span>
						</div>
						<!-- 正职 -->
						<div class="position-data">
							<span class="position-title">正职：</span>
							<span class="position-label">已配</span>
							<span class="position-num">{{ data.positive_assigned_number }}</span>
							<span class="position-label">/应配{{ data.positive_number }}</span>
						</div>
						<div class="position-data">
							<span class="position-title">副职：</span>
							<span class="position-label">已配</span>
							<span class="position-num">{{ data.deputy_assigned_number }}</span>
							<span class="position-label">/应配{{ data.deputy_number }}</span>
						</div>
					</div>
				</div>
				<div class="middle-box">
					<div class="flex">
						<!-- <Box class="flex-1 h-488" title="性别结构">
							<Tips :level="data.gender?.standard" text="配备1~3名女干部" />
							<Tips1 :number="item.standard" class="m-top-21" v-for="(item, index) in data.gender?.detail || 1" :key="index" />
							<div class="gender-ec-box m-top-27">
								<v-chart :option="genderOption" class="chart2" autoresize />
							</div>
						</Box> -->
						<Box class="flex-1 h-1037" title="年龄结构">
							<div class="top">
								<div>
									<Tips class="w-265" :level="data.gender?.standard" text="35岁以下至少有1名" />
									<Tips1 class="m-left-46" :number="item.standard" v-for="(item, index) in data.gender?.detail || 1" :key="index" />
								</div>
								<div class="label-box-line">
									<span class="label">平均年龄</span>
									<div class="line"></div>
									<span class="label-suffix"><span class="number">44</span>岁</span>
								</div>
								<div class="age1-ec-box">
									<v-chart :option="ageOption1" class="chart2" autoresize />
								</div>
							</div>
						</Box>
						<Box class="flex-1 h-488" title="专业结构">
							<Tips :level="data.gender?.standard" text="专业对口干部不低于50%" />
							<div class="left-label-box">
								<span class="text">
									对口专业（建筑学、法学）<br />
									已配备： <span class="specil">2</span>人
								</span>
							</div>
							<div class="zhuanye-ec-box m-top-27">
								<v-chart :option="genderOption" class="chart2" autoresize />
							</div>
						</Box>
					</div>
					<div class="flex">
						<Box class="flex-1 h-836" title="学历结构（全日制）">
							<Tips :level="1" text="（初始学历）大学本科及以上不低于30%" />
							<div class="edu-bottom-box m-top-41">
								<div class="left-label-box">
									<span class="text"> 大学及以上： <span class="specil">2</span>人 </span>
								</div>
								<div class="education-ec-box">
									<v-chart :option="eduOption" class="chart2" autoresize />
								</div>
							</div>
							<div class="dashed-line"></div>
							<div class="education1-ec-box">
								<v-chart :option="eduOption1" class="chart2" autoresize />
							</div>
						</Box>
						<Box class="flex-1 h-689" title="经历结构">
							<Tips :level="1" text="班子成员具有2年乡镇领导工作经历或者3年乡镇工作经历的达到70%" />
							<Tips class="m-top-30" :level="1" text="同一单位工作10年以上：0人" />
							<Tips class="m-top-30" :level="1" text="同一职务任职5年以上：0人" />
							<div class="dashed-line"></div>
							<div class="exp-bottom-box">
								<div class="left-label-box">
									<span class="text"> 具有2年乡镇领导工作经历或<br />者3年乡镇工作经历： <span class="specil">2</span>人 </span>
								</div>
								<div class="exp-ec-box">
									<v-chart :option="expOption" class="chart2" autoresize />
								</div>
							</div>
						</Box>
					</div>
				</div>
				<!-- <div class="age-education flex">
					<div class="box gray">
						<div class="title">年龄结构</div>
						<div class="inner">
							<div class="flex justify-between">
								<div class="status">
									<img src="../images/success.png" alt="" />
									35岁以下至少有1名
								</div>
								<div class="status">
									<img src="../images/warn.png" alt="" />
									35~45岁至少有3名
								</div>
								<div class="status">
									<img src="../images/success.png" alt="" />
									50岁以上不超过30%
								</div>
							</div>
							<div class="label-box-line">
								<span class="label">平均年龄</span>
								<div class="line"></div>
								<span class="label-suffix"><span class="number">44</span>岁</span>
							</div>
							<div class="echarts-box">
								<v-chart :option="ageOption" class="chart2" autoresize />
							</div>
						</div>
					</div>
					<div class="box gray">
						<div class="title">学历结构（全日制）</div>
						<div class="inner">
							<div class="status">
								<img src="../images/warn.png" alt="" />
								（初始学历）大学本科及以上不低于30%
							</div>
							<b-progress :data="data.education" class="progress" />
							<div class="echarts-box">
								<v-chart :option="ageOption" class="chart2" autoresize />
							</div>
						</div>
					</div>
				</div> -->
				<!-- <div class="professional-structure box gray">
					<div class="title">专业结构</div>
					<div class="inner">
						<div class="flex">
							<div class="status m-right-38">
								<img src="../images/success.png" alt="" />
								班子成员具有2年乡镇领导工作经历或者3年乡镇工作经历的达到70%
							</div>
							<div class="status m-right-38">
								<img src="../images/warn.png" alt="" />
								35~同一单位工作10年以上
							</div>
							<div class="status">
								<img src="../images/success.png" alt="" />
								同一职务任职5年以上
							</div>
						</div>
						<div class="echarts-box">
							<v-chart :option="professionalOption" class="chart2" autoresize />
						</div>
						<div class="sub-title">乡镇经历2年以上占比</div>
					</div>
				</div> -->
				<!-- <Box class="experience" title="专业结构">
					<div class="inner">
						<div>
							<Tips :level="1" text="同时具备农学、建设、法学三类专业" />
							<Tips class="m-top-30" :level="0" text="党政主要领导专业要差异化" />
						</div>
						<div class="tab-box flex">
							<div class="tab m-right-60 flex">
								<div class="active">书记</div>
								<div>农学</div>
							</div>
							<div class="tab flex">
								<div class="active">镇长</div>
								<div>经济</div>
							</div>
						</div>
						<div class="echarts-box">
							<v-chart :option="barOption" autoresize />
						</div>
					</div>
				</Box> -->
				<Box title="干部指数" class="cadre-index">
					<div class="inner">
						<div class="status">
							<Tips :level="1" text="班子成员干部指数在序列内排名后30%的人数不能超过一半" />
						</div>
						<div class="bottom m-top-42">
							<div class="cadre-left-box">
								<div class="left-label-box">
									<span class="text">
										干部指数在序列内排名 <br />
										后30%：<span class="specil">2</span>人
									</span>
								</div>
								<div class="left-ec-box">
									<v-chart :option="expOption" class="chart2" autoresize />
								</div>
							</div>
							<div class="echarts-box">
								<v-chart :option="indexOption" autoresize />
							</div>
						</div>
					</div>
				</Box>
			</div>
		</div>
		<div class="right-box">
			<div class="struct-table" @click="structureVisible = true">
				<span class="icon"></span>
				<span class="text">班子结构分析表</span>
				<svg width="9" height="16" viewBox="0 0 13 22" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path
						id="Vector"
						d="M11.3207 12.2334L12.0625 11.4927L1.85187 1.27897C1.65491 1.0839 1.3889 0.974463 1.11169 0.974463C0.834479 0.974463 0.568474 1.0839 0.371511 1.27897C0.273815 1.37597 0.196258 1.49133 0.143301 1.61841C0.0903443 1.74549 0.0630319 1.88178 0.0629345 2.01945C0.0628371 2.15713 0.0899569 2.29346 0.142734 2.42061C0.195511 2.54777 0.272905 2.66324 0.370464 2.76038L9.09758 11.4906L0.370464 20.2136C0.272869 20.3105 0.195415 20.4259 0.142562 20.5529C0.0897095 20.6799 0.0624999 20.8162 0.0625 20.9537C0.0625 21.0913 0.0897095 21.2276 0.142563 21.3546C0.195415 21.4816 0.272869 21.5969 0.370464 21.6939C0.567195 21.8896 0.83327 21.9996 1.11074 22C1.38821 22.0004 1.65459 21.8911 1.85187 21.696L11.2778 12.2743L11.3207 12.2324L11.3207 12.2334Z"
						fill="#3D3D3D"
					/>
				</svg>
			</div>
			<div class="tip-list">
				<div :class="`tip-item structure-${item.type - 1}`" v-for="(item, index) in departmentalData" :key="index">
					<svg t="1697450179918" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4025">
						<path
							d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
							:fill="cardColor[item.type - 1]"
							p-id="4026"
						></path>
					</svg>

					<span class="text">
						{{ item.name }}
					</span>
				</div>
			</div>
		</div>
	</div>
	<a-modal v-model:visible="structureVisible" width="" class="structure-modal" :footer="null" destroyOnClose>
		<!-- 班子结构表 -->
		<structure-table :data="structureTable" :close="onClose" />
	</a-modal>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
import bProgress from './comp/bProgress.vue'
import { convertPxToRem } from '@/utils/utils'
import { getTeamStructure, getTeamBasicInfo, getTeamStructureAnalyze } from '@/apis/statistics'
import Box from './comp/Box.vue'
import Tips from './comp/Tips.vue'
import Tips1 from './comp/Tips1.vue'
const props = defineProps({
	changePage: {
		type: Function,
		required: true,
	},
	orgId: {
		type: Number,
		required: true,
	},
	orgName: {
		type: String,
		required: true,
	},
})

const barData = [
	{
		name: '农学',
		value: '1',
	},
	{
		name: '建设',
		value: '1',
	},
	{
		name: '经济',
		value: '1',
	},
	{
		name: '法学',
		value: '2',
	},
	{
		name: '社会学',
		value: '3',
	},
	{
		name: '某某专业',
		value: '4',
	},
	{
		name: '某某专业',
		value: '5',
	},
]
const bar2Data = [
	{
		name: '前10%',
		value: '1',
	},
	{
		name: '10%',
		value: '1',
	},
	{
		name: '20%',
		value: '1',
	},
	{
		name: '30%',
		value: '2',
	},
	{
		name: '40%',
		value: '3',
	},
	{
		name: '50%',
		value: '4',
	},
	{
		name: '60%',
		value: '5',
	},
	{
		name: '70%',
		value: '5',
	},
	{
		name: '80%',
		value: '5',
	},
	{
		name: '90%',
		value: '5',
	},
	{
		name: '100%',
		value: '5',
	},
]
const cardColor = ['#60ca71', '#f6dd00', '#ffa300', '#ff473e']

const structureVisible = ref(false)

const departmentalData = ref<any>([
	{
		name: '性别结构',
		type: 1,
	},
	{
		name: '年龄结构',
		type: 2,
	},
	{
		name: '学历结构',
		type: 3,
	},
	{
		name: '专业结构',
		type: 4,
	},
	{
		name: '经历结构',
		type: 3,
	},
	{
		name: '干部指数',
		type: 2,
	},
	{
		name: '任职回避',
		type: 1,
	},
])
// 柱状图
const barOption = ref<any>()
// 指数
const indexOption = ref({})
// 专业结构
const professionalOption = ref({})

const structureTable = ref([])

const data = ref<any>({
	user_count: 12,
	job_count: 12,
	positive_number: 3,
	positive_assigned_number: 3,
	deputy_number: 9,
	deputy_assigned_number: 9,
	age: {
		standard: 1,
		detail: [
			{
				standard: 0,
				info: '35岁以下至少有1名',
				data: '4',
			},
			{
				standard: 1,
				info: '35~45岁至少有3名',
				data: '2',
			},
			{
				standard: 1,
				info: '50岁以上不超过30%',
				data: '33.33',
			},
		],
		data: {
			under35: 4,
			to40: 1,
			to45: 1,
			to49: 2,
			up50: 4,
			avg: '51',
		},
	},
	gender: {
		standard: 0,
		detail: [
			{
				standard: 0,
				info: '配备1~3名女干部',
				data: '3',
			},
		],
		data: {
			men: 9,
			women: 3,
		},
	},
	educational: {
		standard: 0,
		detail: [
			{
				standard: 0,
				info: '（初始学历）大学本科及以上不低于30%',
				data: '41.67',
			},
		],
		data: {
			graduate: 1,
			undergraduate: 4,
			junior: 2,
			technical: 5,
		},
	},
	experience: {
		standard: 1,
		detail: [
			{
				standard: 1,
				info: '班子成员具有2年乡镇领导工作经历或者3年乡镇工作经历的达到70%',
				data: '8',
			},
			{
				standard: 0,
				info: '同一职务任职超过5年',
				data: '0',
			},
			{
				standard: 1,
				info: '同一单位工作超过10年',
				data: '7',
			},
		],
		data: {
			rate: '0.08',
			count: 1,
		},
	},
	cadre_index: {
		standard: 0,
		detail: [
			{
				standard: 0,
				info: '班子成员干部指数在序列内排名后30%的人数不能超过一半',
				data: '1',
			},
		],
		data: {
			to10: 3,
			to20: 4,
			to30: 1,
			to40: 0,
			to50: 2,
			to60: 0,
			to70: 1,
			to80: 1,
			to90: 0,
			to100: 0,
		},
	},
	withdraw: {
		standard: 0,
		detail: [
			{
				standard: 0,
				info: '主要社会关系成员在同一单位任职的',
				data: null,
			},
		],
		data: null,
	},
	specialty: {
		standard: 0,
		detail: [
			{
				standard: 0,
				info: '同时具备农业产业、规划建设、法学三类专业',
				data: null,
			},
			{
				standard: 0,
				info: '党政主要领导专业要差异化',
				data: null,
			},
		],
		data: {
			无: 3,
			动力工程专业: 1,
			机械制造专业: 1,
			汉语言文学: 1,
			'环境工程（环境规划）专业': 1,
			市场营销专业: 1,
			农业经济管理专业: 1,
			公共事业管理专业: 1,
			林业专业: 1,
			地球物理学专业: 1,
		},
	},
})
const pieSeries: any = {
	type: 'pie',
	radius: ['0%', '80%'],
	center: ['30%', '50%'],
	itemStyle: {
		borderWidth: 2,
		borderColor: '#fff',
	},
	label: {
		show: false,
		formatter: '{c}个',
		fontSize: convertPxToRem(16),
		fontWeight: 500,
		color: '#000',
	},
	labelLine: {
		show: true,
	},
	clockWise: false,
	startAngle: 180,
	data: [],
}
const age = [
	{
		proportion: 6.4,
		value: 54,
		name: '35岁以下',
	},
	{
		proportion: 18.84,
		value: 159,
		name: '35-40岁',
	},
	{
		proportion: 23.34,
		value: 197,
		name: '40-45岁',
	},
	{
		proportion: 26.9,
		value: 227,
		name: '45-55岁',
	},
	{
		proportion: 24.53,
		value: 207,
		name: '55岁以上',
	},
]
const series = { ...JSON.parse(JSON.stringify(pieSeries)), radius: ['65%', '90%'], center: ['25%', '50%'] }
const legendData: any = []
series.data = age.map((item: any) => {
	legendData.push(item.name)
	return item
})

const legend = {
	orient: 'vertical',
	top: 'center',
	right: '20%',
	itemWidth: 9,
	itemHeight: 9,
	textStyle: {
		fontSize: convertPxToRem(23),
		fontWeight: 500,
		color: '#000',
		rich: {
			a: {
				width: convertPxToRem(90),
			},
			b: {
				width: convertPxToRem(50),
			},
			c: {
				width: convertPxToRem(70),
			},
		},
	},
}
const pieColor = ['#1992FF', '#2ECF61', '#FFCF15', '#FF4B67', '#14B3D9', '#FF8943', '#9656F1', '#3EF589']

const _data = [
	{ value: 260, name: '总数' },
	{ value: 243, name: '在岗' },
]

const onClose = () => {
	structureVisible.value = false
}

// 年龄
const ageOption1 = ref({})

const loadAgeOption1 = () => {
	ageOption1.value = {
		legend: {
			data: legendData,
			orient: 'vertical',
			top: 'center',
			right: '20%',
			itemWidth: 9,
			itemHeight: 9,
			itemGap: 20,
			textStyle: {
				fontSize: convertPxToRem(23),
				fontWeight: 500,
				color: '#000',
				rich: {
					a: {
						width: convertPxToRem(90),
					},
					b: {
						width: convertPxToRem(50),
					},
					c: {
						width: convertPxToRem(70),
					},
				},
			},
			formatter: function (name: string) {
				const item = age.filter((item: any) => item.name === name)[0]
				return `{a|${name}}{b|${item.value}人}{c|(${item.proportion}%)}`
			},
		},
		color: pieColor,
		series: {
			...series,
			radius: ['60%', '80%'],
		},
	}
}
loadAgeOption1()

const emptyPie = (total: any, value: any) => {
	return {
		value: total - value,
		name: 'invisible',
		itemStyle: {
			normal: {
				color: '#DBEBFF',
			},
			emphasis: {
				color: '#DBEBFF',
			},
		},
	}
}

const getTitle = (params = {}) => {
	return {
		x: '50%',
		y: 'center',
		show: true,
		text: `{a|0} {b|%}`,
		textAlign: 'center',
		...params,
		textStyle: {
			rich: {
				a: {
					fontSize: convertPxToRem(36),
					color: '#000',
					fontWeight: '600',
					textAlign: 'center',
					fontFamily: 'iconfont',
				},
				b: {
					fontSize: convertPxToRem(36),
					color: '#000',
					textAlign: 'center',
				},
			},
		},
	}
}

// 年龄
const ageOption = ref({})

const loadAgeOption = () => {
	const _series = {
		...series,
		startAngle: 180,
		clockWise: false,
		hoverAnimation: false,
		data: [{ name: '21', value: 50 }],
		center: ['center', 'center'],
	}
	ageOption.value = {
		legend: {
			show: false,
		},
		title: {
			x: '50%',
			y: 'center',
			show: true,
			text: `{a|0} {b|%}`,
			textAlign: 'center',
			textStyle: {
				rich: {
					a: {
						fontSize: convertPxToRem(36),
						color: '#000',
						fontWeight: '600',
						textAlign: 'center',
						fontFamily: 'iconfont',
					},
					b: {
						fontSize: convertPxToRem(36),
						color: '#000',
						textAlign: 'center',
					},
				},
			},
		},
		color: pieColor,
		series: _series,
	}
}
loadAgeOption()

const genderOption = ref({})
// 性别结构
const loadGenderOption = ({ men = 0, women = 0 }: any) => {
	const total = women + men

	const _series = {
		...series,
		data: [
			{ name: '男', value: men },
			{
				name: '女',
				value: women,
			},
		],
	}
	genderOption.value = {
		series: _series,
		color: pieColor,
		legend: {
			...legend,
			formatter: function (name: string) {
				const item = _series.data.filter((item: any) => item.name === name)[0]

				const proportion = Math.round((item.value / total) * 100)

				return `{a|${name}}{b|${item.value}人}{c|(${proportion}%)}`
			},
		},
	}
}

const zhuanyeOption = ref({})
// 性别结构
const loadZhuanyeOption = ({ men = 0, women = 0 }?: any) => {
	const total = women + men

	const _series = {
		...series,
		data: [
			{ name: '建筑学', value: men },
			{
				name: '法学',
				value: women,
			},
		],
	}
	zhuanyeOption.value = {
		series: _series,
		color: ['#2ECF61', '#90ECA5', '#DBEBFF'],
		legend: {
			...legend,
			position: 'bottom',
			formatter: function (name: string) {
				const item = _series.data.filter((item: any) => item.name === name)[0]

				const proportion = Math.round((item.value / total) * 100)

				return `{a|${name}}{b|${item.value}人}{c|(${proportion}%)}`
			},
		},
	}
}
loadZhuanyeOption()

// 教育
const eduOption = ref({})

const eduOption1 = ref({})

const loadEduOption = () => {
	eduOption.value = {
		legend: {
			show: false,
		},
		hoverAnimation: false,
		color: ['#2ECF61'],
		title: getTitle(),
		series: {
			...series,
			hoverAnimation: false,
			radius: ['60%', '80%'],
			center: ['50%', '50%'],
			data: [{ name: '大学及以上', value: 2 }, emptyPie(5, 2)],
		},
	}

	eduOption1.value = {
		legend: {
			...legend,
		},
		hoverAnimation: false,
		color: pieColor,
		title: getTitle({
			x: '30%',
		}),
		series: {
			...series,
			hoverAnimation: false,
			radius: ['60%', '80%'],
			center: ['30%', '50%'],
			data: [
				{ name: '大学及以上', value: 2 },
				{ name: '大学及以上', value: 2 },
			],
		},
	}
}

loadEduOption()
//
const loadBarOption = (type = '1') => {
	;(type === '1' ? barOption : indexOption).value = {
		legend: {
			show: false,
		},
		color: ['#008EFF'],
		grid: {
			left: '0%',
			right: '4%',
			bottom: '3%',
			containLabel: true,
		},
		xAxis: [
			{
				type: 'category',
				data: (type === '1' ? barData : bar2Data).map((item) => item.name),
			},
		],
		yAxis: [
			{
				show: false,
				type: 'value',
			},
		],
		series: [
			{
				type: 'bar',
				stack: 'Ad',
				emphasis: {
					focus: 'series',
				},
				barWidth: convertPxToRem(30),
				label: {
					show: true,
					position: 'top',
					formatter: ({ value }: any) => {
						return value ? `${value}个` : ''
					},
					color: '#666666',
				},
				data: type === '1' ? barData : bar2Data,
			},
		],
	}
}
// 经历
const expOption = ref({})
const loadExpOption = () => {
	expOption.value = {
		legend: {
			show: false,
		},
		hoverAnimation: false,
		color: ['#2ECF61'],
		title: getTitle(),
		series: {
			...series,
			hoverAnimation: false,
			radius: ['60%', '80%'],
			center: ['50%', '50%'],
			data: [{ name: '大学及以上', value: 2 }, emptyPie(5, 2)],
		},
	}
}
loadExpOption()

const loadProfessionalOption = () => {
	professionalOption.value = {
		title: {
			x: 'center',
			y: 'center',
			textStyle: {
				rich: {
					a: {
						fontSize: convertPxToRem(30),
						color: '#000',
						fontWeight: '600',
						textAlign: 'center',
						fontFamily: 'ArTarumianBakhum-Regular, ArTarumianBakhum',
					},
					b: {
						fontSize: convertPxToRem(20),
						color: '#000',
						textAlign: 'center',
					},
				},
			},
			text: `{a|67}{b|%}`,
		},
		series: [
			{
				color: pieColor,
				type: 'pie',
				radius: ['55%', '80%'],
				center: ['50%', '50%'],
				itemStyle: {
					borderWidth: 2,
					borderColor: '#fff',
				},
				label: {
					show: false,
					formatter: '{b} {c}个',
					fontSize: convertPxToRem(16),
					fontWeight: 500,
					color: '#000',
				},
				labelLine: {
					show: true,
				},
				data: _data,
			},
		],
	}
}
loadBarOption('1')
loadBarOption('2')
loadProfessionalOption()

loadGenderOption({ men: 10, women: 20 })
// 班子结构table
const getStructureTable = async (org_id: number) => {
	const res = await getTeamStructure({ org_id })
	const allocationInfo = {
		name: '配备情况',
		age: res.data?.age_case,
		gender: res.data?.gender_case,
		educational: res.data?.educational_case,
		major: res.data?.major_case,
		experience: res.data?.experience_case,
		cadre_index: res.data?.cadre_index_case,
		withdraw: res.data?.withdraw_case,
	}
	const standardsInfo = {
		name: '是否达标',
		age: res.data?.age_standards,
		gender: res.data?.gender_standards ? '是' : '否',
		educational: res.data?.educational_standards,
		major: res.data?.major_standards,
		experience: res.data?.experience_standards,
		cadre_index: res.data?.cadre_index_standards,
		withdraw: res.data?.withdraw_standards,
	}
	res.data?.users?.unshift(standardsInfo, allocationInfo)
	if (res.code === 0) {
		structureTable.value = res.data?.users
	} else {
		message.error(res.message)
	}
}

getStructureTable(props.orgId)

const initBaseInfo = async (org_id: number) => {
	const res = await getTeamStructureAnalyze({ org_id })
	if (res.code === 0) {
		data.value = res.data
	} else {
		message.error(res.message)
	}
}

initBaseInfo(props.orgId)
</script>

<style lang="scss" scoped>
$small-font: 17px;
$base-font: 24px;
$large-font: 44px;

.flex-direction {
	display: flex;
	flex-direction: column;
}
.align-center {
	align-items: center;
}
.structural-analysis {
	position: absolute;
	inset: 0px;
	width: 100%;
	height: 100%;
	z-index: 999;
	overflow: auto;
	display: flex;
	.left-box {
		margin-right: 18px;
		flex: 1;
		height: fit-content;
		min-height: 100%;
		background-color: #ffffff;
		.town-header {
			padding: 24px 0px 0px 41px;
			display: flex;
			align-items: center;
			& > span {
				cursor: pointer;
			}
			.town-icon {
				margin-right: 9px;
				display: inline-block;
				width: 24px;
				height: 24px;
				background: #000000;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				background: url('@/assets/images/back-3.png') no-repeat center / cover;
			}
			.town-title {
				display: inline-block;
				font-size: 21px;
				font-family: PingFang SC-Heavy, PingFang SC;
				font-weight: bold;
				color: #000000;
			}
		}
		.content-box {
			padding: 10px 41px;
			.data-card—top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				position: relative;
				padding: 29px 41px;
				width: 100%;
				height: 120px;
				background: linear-gradient(305deg, #e1f2ff 0%, #ffffff 100%);
				box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
				border-radius: 8px 8px 8px 8px;
				&::after {
					content: '';
					position: absolute;
					top: 50%;
					right: 54px;
					height: 52px;
					width: 709px;
					background: url('../images/lineBack.png') no-repeat center / cover;
					transform: translateY(-50%);
					z-index: 0;
				}
				.total-position {
					display: flex;
					align-items: center;
					img {
						width: 48px;
						height: 48px;
					}
					.text-data {
						display: flex;
						align-items: center;
						margin-left: 18px;
						.top {
							text-align: center;
							vertical-align: bottom;
							.number {
								font-size: 44px;
								font-family: ArTarumianBakhum, ArTarumianBakhum;
								font-weight: 400;
								color: #008eff;
								line-height: 24px;
							}
							.label {
								font-size: 28px;
								font-family: ArTarumianBakhum, ArTarumianBakhum;
								font-weight: 400;
								color: #008eff;
								line-height: 24px;
							}
						}
						.text {
							text-align: center;
							font-size: 20px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: bold;
							color: rgba(0, 0, 0, 0.9);
							line-height: 24px;
						}
					}
				}
				.card-title {
					font-size: $base-font;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: bold;
					color: #333333;
					line-height: 19px;
				}
				.card-content {
					margin-left: 200px;
					flex: 1;
					position: relative;
					z-index: 1;
					// margin-left: 146px;
					display: flex;
					align-items: center;
					justify-content: space-around;
					height: 16px;
					.total-data {
						display: flex;
						align-items: center;
						height: 100%;
						&-title {
							font-size: $base-font;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #000000;
						}
						&-num {
							// margin: -10px 0px 0px 1px;
							font-size: $large-font;
							font-family: ArTarumianBakhum-Regular, ArTarumianBakhum;
							font-weight: 400;
							color: #008eff;
						}
					}
					.position-data {
						margin-left: 85px;
						display: flex;
						align-items: center;
						height: 100%;
						font-size: $base-font;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);

						.position-title,
						.position-label {
							font-size: $base-font;
							line-height: $base-font;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
						}
						.position-num {
							margin: 0px 4px;
							vertical-align: bottom;
							font-size: $base-font;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #008eff;
						}
					}
				}
			}
			.middle-box {
				width: 100%;
				display: flex;
				justify-content: space-between;
				.flex {
					width: 49.6%;
					display: flex;
					flex-direction: column;
				}
				.row {
					height: 54px;
					display: flex;
					align-items: center;
					width: 100%;
				}
				.gender-ec-box {
					width: 100%;
					height: 200px;
				}
				.age-ec-box {
					width: 100%;
					height: 261px;
				}
				.edu-bottom-box {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 261px;
					.left-label-box {
						display: flex;
						align-items: center;
						.text {
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 23px;
							color: #000000;
							line-height: 23px;

							&::before {
								content: '';
								display: inline-block;
								width: 12px;
								height: 12px;
								background: #2ecf61;
								margin-right: 10px;
							}
						}
					}
					.zhuanye-ec-box {
						width: 100%;
						height: 425px;
					}
					.education-ec-box {
						width: 261px;
						height: 261px;
					}
				}
				.left-label-box {
					display: flex;
					align-items: center;
					.text {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 23px;
						color: #000000;
						line-height: 23px;

						&::before {
							content: '';
							display: inline-block;
							width: 12px;
							height: 12px;
							background: #2ecf61;
							margin-right: 10px;
						}
					}
				}
				.education1-ec-box {
					width: 100%;
					height: 260px;
				}
				.age1-ec-box {
					width: 100%;
					height: 319px;
				}
				.exp-bottom-box {
					height: 261px;
					width: 100%;
					display: flex;
					justify-content: space-between;
					display: flex;
					align-items: center;
					.text {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 23px;
						color: #000000;
						line-height: 23px;

						&::before {
							content: '';
							display: inline-block;
							width: 12px;
							height: 12px;
							background: #2ecf61;
							margin-right: 10px;
						}
					}
					.exp-ec-box {
						width: 261px;
						height: 261px;
					}
				}
				.text-label {
					.icon {
						display: inline-block;
						width: 12px;
						height: 12px;
						background: #2ecf61;
					}
				}
			}

			.label-box-line {
				display: flex;
				margin-top: 66px;
				align-items: center;
				.label {
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: rgba(0, 0, 0, 0.9);
				}
				.line {
					margin: 0px 31px 0px 31px;
					flex: 1;
					height: 1px;
					background: linear-gradient(to left, transparent 0%, transparent 50%, #008eff 50%, #008eff 100%);
					background-size: 10px 1px;
					background-repeat: repeat-x;
				}
				.label-suffix {
					font-size: 18px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					line-height: 21px;
					.number {
						font-size: 30px;
						font-family: ArTarumianBakhum, ArTarumianBakhum;
						font-weight: 400;
						color: #008eff;
						line-height: 35px;
					}
				}
			}
			.box {
				margin-top: 27px;
				padding: 18px;
				.title {
					font-size: 20px;
					font-family: Source Han Sans CN-Bold, Source Han Sans CN;
					font-weight: bold;
					color: #333333;
					&::before {
						content: '';
						display: inline-block;
						margin-right: 15px;
						width: 12px;
						height: 12px;
						background-color: #008eff;
						border-radius: 50%;
					}
				}

				.inner {
					padding: 0 24px;
					.status {
						margin-top: 36px;
						font-size: 18px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #000000;

						display: flex;
						align-items: center;
						img {
							margin-right: 9px;
							width: 18px;
							height: 18px;
						}
					}
				}
				:deep(.label) {
					width: auto;
				}
			}

			.age-education {
				justify-content: space-between;
				.box {
					width: calc(50% - 15px) !important;
				}

				.echarts-box {
					margin-top: 57px;
					height: 224px;
					width: 100%;
				}
				.progress {
					margin-top: 35px;
					padding-top: 0px;
				}
			}
			.professional-structure {
				.echarts-box {
					width: 100%;
					height: 266px;
				}
				.sub-title {
					font-size: 18px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					text-align: center;
				}
			}

			.experience {
				.tab-box {
					margin-top: 26px;
					.tab {
						width: 291px;
						height: 62px;
						border-radius: 6px;
						background-color: #ffffff;
						overflow: hidden;
						box-shadow: 0px 0px 3px #ccc;
						& > div {
							display: flex;
							align-items: center;
							justify-content: center;
							flex: 1;
							font-size: 20px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 500;
							color: #000000;
							cursor: pointer;
						}
						.active {
							color: #ffffff;

							background-color: #4b99ff;
						}
					}
				}
				.echarts-box {
					width: 100%;
					height: 357px;
				}
			}
			.cadre-index {
				width: 100%;
				.sub_title {
					margin-top: 26px;
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: #000000;
					line-height: 23px;
				}

				.bottom {
					display: flex;
					width: 100%;
					.cadre-left-box {
						.left-label-box {
							display: flex;
							align-items: flex-start;
							.text {
								font-family: Source Han Sans CN, Source Han Sans CN;
								font-weight: 400;
								font-size: 23px;
								color: #000000;
								line-height: 25px;
							}

							&::before {
								content: '';
								display: inline-block;
								width: 12px;
								height: 12px;
								background: #2ecf61;
								margin-right: 21px;
								transform: translate(0, 50%);
							}
						}
						.left-ec-box {
							width: 261px;
							height: 261px;
						}
					}
					.echarts-box {
						flex: 1;
						height: 327px;
					}
				}
			}
		}
	}
	.right-box {
		width: 270px;
		.struct-table {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 80px;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			color: rgba(0, 0, 0, 0.9);
			background-color: #ffffff;
			border-radius: 8px 0px 0px 8px;
			.icon {
				margin-right: 9px;
				display: inline-block;
				width: 17px;
				height: 17px;
				background: #ff241a;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				border-radius: 50%;
			}
			.text {
				margin-right: 27px;
			}
		}
		.tip-list {
			.tip-item {
				margin-top: 23px;
				border-radius: 8px 0px 0px 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 270px;
				height: 80px;
				font-size: 20px;
				line-height: 20px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: rgba(0, 0, 0, 1);

				&:nth-child(1) {
					margin-top: 47px;
				}

				svg {
					width: 39px;
					height: 39px;
				}
				.text {
					margin-left: 18px;
				}
			}
			.structure {
				box-shadow: 0px 0 11px 0px rgba(0, 0, 0, 0.1);
				&-0 {
					background: linear-gradient(305deg, #e5fee6 0%, #ffffff 100%);
				}
				&-1 {
					background: linear-gradient(305deg, #ffffdd 0%, #ffffff 100%);
				}
				&-2 {
					background: linear-gradient(305deg, #fef0da 0%, #ffffff 100%);
				}
				&-3 {
					background: linear-gradient(305deg, #ffe2e2 0%, #ffffff 100%);
				}
			}
		}
	}

	.flex {
		display: flex;
	}
	.justify-between {
		justify-content: space-between;
	}
	.m-right-38 {
		margin-right: 38px;
	}
	.m-right-60 {
		margin-right: 60px;
	}
}
.gray {
	background-color: #f6f8fc;
}
.dashed-line {
	margin: 31px 0px;
	flex: 1;
	width: 100%;
	height: 1px;
	background: linear-gradient(to left, transparent 0%, transparent 50%, #008eff 50%, #008eff 100%);
	background-size: 10px 1px;
	background-repeat: repeat-x;
}
</style>

<style>
.structure-modal {
	width: 1689px;
}
</style>
