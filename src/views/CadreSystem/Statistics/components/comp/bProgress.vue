<script lang="ts" setup>
import { PropType } from 'vue'
type Link = {
	name: string
	value: number
	proportion: number
}
defineProps({
	vertical: {
		type: Boolean,
		default: true,
	},
	data: {
		type: Array as unknown as PropType<[Link]>,
		default: () => [],
	},
})

const emits = defineEmits(['nameClick'])

const onClick = (item: any) => {
	emits('nameClick', item)
}
</script>

<template>
	<div class="progress">
		<div v-if="vertical" class="progress-vertical">
			<div class="value-wrap">
				<div class="value" v-for="item in data" :key="item.name" @click="onClick(item)">
					<span>{{ item.name }}：</span>
					<span>{{ item.value }}人 &nbsp;&nbsp;</span>
					<span>({{ item.proportion }}%)</span>
				</div>
			</div>
			<div :class="{ 'progress-wrap': data.length }">
				<span
					class="proportion"
					:style="{
						width: `${data.length && data[0].proportion}%`,
					}"
				/>
			</div>
		</div>
		<div v-else class="progress">
			<div class="flex" v-for="(item, index) in data" :key="item.name" @click="onClick(item)">
				<div class="label">{{ item.name }}</div>
				<div class="progress-wrap">
					<span
						class="proportion"
						:class="'proportion-' + index"
						:style="{
							width: `${item.proportion}%`,
						}"
					/>
				</div>
				<div class="value">
					<span>{{ item.value }}人 &nbsp;&nbsp;</span>
					<span>({{ item.proportion }}%)</span>
				</div>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.progress {
	padding: 16px 0;
	.flex {
		display: flex;
		align-items: center;
	}
	.progress-wrap {
		position: relative;
		flex: 1;
		width: 100%;
		height: 24px;
		background-color: #dcebff;
		.proportion {
			position: absolute;
			left: 0;
			top: 0;
			height: 100%;
			background-color: #008eff;
			&-1 {
				background-color: #2ecf61;
			}
		}
	}
	.progress {
		font-size: 16px;
		font-family: Source Han Sans CN-Regular, Source Han Sans CN;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.9);
		> div:first-child {
			margin-bottom: 32px;
		}
		.label {
			margin-right: 10px;
			width: 100px;
			text-align: right;
		}
		.value {
			width: 150px;
			text-align: right;
			font-size: 16px;
			font-weight: 500;
			color: #000000;
		}
	}
	&-vertical {
		.value-wrap {
			display: flex;
			justify-content: space-between;
			font-size: 16px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #000000;
		}
		.progress-wrap {
			margin-top: 11px;
			background-color: #2ecf61;
		}
	}
}
</style>
