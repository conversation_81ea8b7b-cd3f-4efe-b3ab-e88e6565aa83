<template>
	<MinCard title="班子生态/班子业绩散点图" header>
		<template v-slot:right>
			<NavMenu />
		</template>
		<div class="echarts-box">
			<div class="echarts">
				<v-chart :option="option" autoresize ref="echartsRef"></v-chart>
			</div>
			<div class="type-selection">
				<a-select :bordered="false" :dropdownMatchSelectWidth="false" @change="onSelectChange" :value="selectValue">
					<a-select-option v-for="item in selectOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
					<template #suffixIcon><span class="select-icon"></span></template>
				</a-select>
			</div>
		</div>
	</MinCard>
</template>

<script lang="ts" setup>
import { ref, computed, watch, toRefs, watchEffect } from 'vue'
import { useCadreSystem } from '@/store/cadreSystem'
import { debounce, convertPxToRem } from '@/utils/utils'

import NavMenu from './Nav.vue'

const props = defineProps({ pointData: { type: Object, default: () => ({}) }, selectValue: { type: String, default: '0' } })
const emits = defineEmits(['selectChange'])

const { pointData } = toRefs(props)

const echartsRef = ref<any>(null)
const currentMinePoint = ref([])
const store = useCadreSystem()
const { coor_user_id } = toRefs(store)

const refreshOption = ref()

const selectOption = [
	{
		label: '生态+口碑',
		value: '0',
	},
	{
		label: '生态',
		value: '1',
	},
	{
		label: '口碑',
		value: '2',
	},
]

window.addEventListener(
	'resize',
	debounce(() => {
		refreshOption.value = Math.random()
	}, 100)
)
const option = computed(() => {
	{
		refreshOption.value
		coor_user_id.value
	}

	if (!pointData.value.mine) {
		return
	}
	const { mine, others } = pointData.value
	// 处理数据
	let otherData = []
		.concat(others, mine)
		.filter((item: any) => {
			return item.x > 0 && item.y > 0
		})
		.map((item: any) => {
			return [Number(item.x), Number(item.y), item.name, item.org_id]
		})
	// 求otherData中x的平均值
	const x_avg = otherData.reduce((pre, current) => pre + current[0], 0) / otherData.length

	const minePoint = [currentMinePoint.value].filter((item: any) => {
		return item[0] > 0 && item[1] > 0
	})

	const _option = {
		// color: ['#'],
		grid: {
			top: '9%',
			left: '1%',
			right: '5%',
			bottom: '2%',
			containLabel: true,
			show: false,
		},
		tooltip: {
			trigger: 'item',
			showDelay: 0,
			show: true,
			formatter: function (name: any) {
				const { value } = name
				const currentOption = selectOption.find((item: any) => item.value === props.selectValue)
				return `
							<div class="tooltips-box">
								<div class="data-name">${value[2]}</div>
								<div class="data-item">业绩：${value[1]}</div>
								<div class="data-item">${currentOption?.label}：${value[0]}</div>
							</div>
						`
			},
		},
		xAxis: {
			// 名称配置为函数
			// name: '生态',
			// nameTextStyle: {
			// 	color: '#000000',
			// 	// align: 'left',
			// 	padding: [30, 0, 0, -30],
			// 	verticalAlign: 'top',
			// 	fontSize: convertPxToRem(18),
			// },
			type: 'value',
			axisLabel: {
				formatter: (value: any) => {
					value = Number(value)
					return value > 100 ? '' : value.toFixed(2)
				},
				color: '#666666',
				fontSize: convertPxToRem(16),
			},
			splitLine: {
				//x轴网格线
				show: true,
				lineStyle: {
					color: '#EEEEEE',
					type: 'dotted',
				},
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: '#666666',
				},
				symbol: ['none', 'arrow'],
				symbolOffset: 7,
				symbolSize: [7, 10],
			},
			axisTick: {
				show: false,
			},
			min: (value: any) => (value.min === 0 ? 0 : value.min - 0.1),
			max: (value: any) => value.max + 0.1,
			// max,
			// splitNumber: 7,
		},
		yAxis: {
			name: '业绩',
			type: 'value',
			scale: true,
			nameTextStyle: {
				color: '#000000',
				// align: 'left',
				padding: [0, 0, 0, -50],
				verticalAlign: 'top',
				fontSize: convertPxToRem(18),
			},
			axisLabel: {
				color: '#666666',
				formatter: (value: any) => {
					return Number(value).toFixed(2)
				},
				fontSize: convertPxToRem(16),
				fontFamily: 'PingFang SC',
				showMaxLabel: false,
			},
			splitLine: {
				lineStyle: {
					type: 'dashed',
					color: '#EEEEEE',
				},
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: '#666666',
				},
				symbol: [
					'none',
					// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
					'arrow',
				],
				symbolOffset: 7,
				symbolSize: [7, 10],
			},
			// min: 20,
			min: (value: any) => (value.min === 0 ? 0 : value.min - 0.1),
			max: (value: any) => value.max + 0.1,
			// splitNumber: 7,
			// 刻度线
			axisTick: {
				show: false,
			},
		},
		series: [
			{
				type: 'scatter',
				data: otherData,
				symbol: (value: Array<number>) => {
					if (value[3] === currentMinePoint.value[3]) {
						return 'none'
					} else {
						return 'circle'
					}
				},
				itemStyle: {
					color: '#60CA71',
				},
				markLine: {
					symbol: 'none',
					label: { show: false },
					lineStyle: {
						//x/y轴平均值线
						color: '#FE533A',
						type: 'dashed',
					},
					animation: false,
					silent: true,
					data: [
						{
							type: 'average',
							name: '平均值',
						},
						{
							xAxis: x_avg,
						},
					],
				},
			},
			{
				type: 'scatter',
				// 分别为原点，自己，x轴
				data: minePoint,
				itemStyle: {
					color: '#FE533A',
				},
				z: 10,
			},
		],
	}
	return _option
})

const onSelectChange = (value: any) => {
	emits('selectChange', value)
}
watchEffect(() => {
	const { mine } = pointData.value
	if (mine) {
		currentMinePoint.value = [mine.x, mine.y, mine.name, mine.org_id]
	}
})
watch(coor_user_id, (newVal) => {
	if (newVal) {
		const { others = [], mine = {} } = pointData.value
		const current = [mine, ...others].find((item: any) => item.org_id === newVal)
		current && (currentMinePoint.value = [current.x, current.y, current.name, current.org_id])
	}
})
// echarts 绑定点击事件
watch(echartsRef, () => {
	echartsRef.value.chart.on('click', (params: any) => {
		store.updateCoorUserId(params.data?.[3])
		currentMinePoint.value = params.data
	})
})
</script>

<style scoped lang="less">
// .first-coord {
// 	padding-bottom: 45px;
// 	position: relative;
// 	.type-selection {
// 		position: absolute;
// 		bottom: 0px;
// 		right: 0px;
// 	}
// }
.data-menu {
	display: flex;
	align-items: center;
	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		line-height: 18px;
		font-weight: 500;
		color: #666666;
		// line-height: 28px;
		cursor: pointer;
		line-height: 28px;
	}
	.menu-active {
		color: #333333;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #ff2121;
		border-radius: 2px 2px 2px 2px;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translateX(-50%);
	}
}
.echarts-box {
	.echarts {
		width: 100%;
		height: 441px;
	}
}
::v-deep(.ant-select-arrow) {
	width: 16px;
	height: 16px;
}
.select-icon {
	margin-left: 4px;
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url('@/assets/images/select.png') no-repeat center / 100%;
}
::v-deep(.ant-select-selection-item) {
	min-width: 130px;
	color: #000000;
	font-size: 20px;
	text-align: right;
}
.pupop-custom {
	width: 200px;
}
.type-selection {
	padding-right: 20px;
	display: flex;
	justify-content: flex-end;
}

.select-icon {
	margin-left: 4px;
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url('@/assets/images/select.png') no-repeat center / 100%;
}
</style>
