<template>
	<MinCard title="班子运行指数金字塔" header>
		<template v-slot:right>
			<nav-menu />
		</template>
		<div class="pyramid">
			<div :class="`pyramid-item pyramid-${item.key}`" v-for="item in pyramidList" :key="item.key" @click="onActive(item.key)">
				<div class="pyramid-img">
					<div class="pyramid-label">{{ item.label }}</div>
					<img :src="item.img" alt="" />
					<div class="select" v-if="pyramidData.pyramid_index == item.key - 1">
						<div class="select-line"></div>
						<div class="text">{{ pyramidData.name }} {{ pyramidData.reindex }}</div>
					</div>
					<div class="splitLine">
						<div class="line-box">
							<div class="left-line"></div>
							<div class="right-line"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</MinCard>
</template>

<script lang="ts" setup>
import { ref, toRefs } from 'vue'
import ImgPyramid1 from '@/assets/images/pyramid-1.png'
import ImgPyramid2 from '@/assets/images/pyramid-2.png'
import ImgPyramid3 from '@/assets/images/pyramid-3.png'
import ImgPyramid4 from '@/assets/images/pyramid-4.png'

import NavMenu from './Nav.vue'

const props = defineProps({
	pyramidData: {
		type: Object,
		default: () => ({}),
	},
})

const { pyramidData } = toRefs(props)

const activeRow = ref(1)

const pyramidList = [
	{
		img: ImgPyramid1,
		label: '10%',
		key: 1,
	},
	{
		img: ImgPyramid2,
		label: '20%',
		key: 2,
	},
	{
		img: ImgPyramid3,
		label: '30%',
		key: 3,
	},
	{
		img: ImgPyramid4,
		label: '40%',
		key: 4,
	},
]

const onActive = (key: number) => {
	activeRow.value = key
}
</script>

<style scoped lang="less">
.data-menu {
	display: flex;
	align-items: center;
	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		font-weight: 500;
		color: #666666;
		// line-height: 28px;
		cursor: pointer;
	}
	.menu-active {
		color: #333333;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #ff2121;
		border-radius: 2px 2px 2px 2px;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translate(-50%, 0);
	}
}

::v-deep(.pyramid) {
	padding: 0px 29px 27px 10px;
}

.pyramid {
	height: 491px;
	display: flex;
	flex-direction: column;
	align-items: center;
	user-select: none;
	.pyramid-item {
		position: relative;
		transform: translate(-60px);

		.pyramid-img {
			position: relative;
			margin: 0 auto;
			.pyramid-label {
				position: absolute;
				top: 40%;
				left: -20px;
				font-size: 22px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #666666;
				line-height: 26px;
			}
			img {
				width: 100%;
				height: 100%;
			}
			.select {
				position: absolute;
				top: 0;
				left: 100%;
				display: flex;
				align-items: center;
				width: 341px;
				height: 75.5px;
				-webkit-clip-path: polygon(0 0, 100% 0%, 100% 100%, 7% 100%);
				clip-path: polygon(0 0, 100% 0%, 100% 100%, 12% 100%);
				.select-line {
					height: 1px;
					background: #278236;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
				.text {
					margin-left: 10px;
					font-size: 22px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #278236;
					line-height: 26px;
				}
			}
			.splitLine {
				position: absolute;
				inset: 0;
				// background-color: rgba(0, 0, 0, 0.9);
				.line-box {
					.left-line {
					}
					.right-line {
					}
				}
			}
		}
		&:nth-child(2) {
			margin-top: -10px;
		}
		&:nth-child(3) {
			margin-top: -20px;
		}
		&:nth-child(4) {
			margin-top: -20px;
		}
	}
	.pyramid-1 {
		.pyramid-img {
			width: 74px;
			height: 86px;

			.pyramid-label {
				transform: translateX(-15px);
			}
		}
		.select {
			width: 341px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-40px);
			.select-line {
				width: 191px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-2 {
		.pyramid-img {
			width: 162.23px;
			height: 93.9px;
			.pyramid-label {
				left: -30px !important;
			}
		}
		.select {
			width: 271px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-40px);
			.select-line {
				width: 146px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-3 {
		.pyramid-img {
			width: 265.23px;
			height: 112px;
		}
		.select {
			width: 271px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-45px, 10px);
			.select-line {
				width: 126px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-4 {
		.pyramid-img {
			width: 384.87px;
			height: 140.51px;
		}
		.select {
			width: 271px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-50px, 20px);
			.select-line {
				width: 66px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
}
</style>
