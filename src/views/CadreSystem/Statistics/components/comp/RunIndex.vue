<template>
	<MinCard title="班子运行指数" header>
		<div class="personal-analysis">
			<div class="box-content__line">
				<v-chart :option="option" autoresize ref="echarts"></v-chart>
			</div>
			<div class="box-content__table">
				<DataTable :data-source="tableData" :columns="columns"> </DataTable>
			</div>
		</div>
	</MinCard>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import DataTable from '@/components/Table.vue'
import { decreaseOpacity, convertPxToRem, debounce } from '@/utils/utils'

const props = defineProps({
	lineData: {
		type: Object,
		default: () => ({}),
	},
	tableData: {
		type: Array<any>,
		default: () => [],
	},
	columns: {
		type: Array<any>,
		default: () => [],
	},
})

// 用于刷新option
const refreshOption = ref(0)
// 刷新option
window.addEventListener(
	'resize',
	debounce(() => {
		refreshOption.value++
	}, 100)
)

// 几条平均线
const option = computed(() => {
	{
		refreshOption.value
	}

	if (!props.lineData) return

	const { xlabel, org_data, sequence_avg, department_avg } = props.lineData

	const dataArray = [
		{
			data: org_data?.map((item: any) => item || 0),
			label: '本单位',
			color: '#60CA71',
			lineType: 'solid',
		},
		{
			data: sequence_avg?.map((item: any) => item || 0),
			// data: down,
			label: '序列平均值',
			color: '#6AAEFB',
			lineType: 'dashed',
		},
		{
			data: department_avg?.map((item: any) => item || 0),
			// data: down,
			label: '乡镇(部门)平均值',
			color: '#FE533A',
			lineType: 'dashed',
		},
	]

	const datas: any[] = []
	// 基础线
	dataArray.map((item) => {
		const { data, label, color, lineType } = item
		datas.push({
			name: label,
			symbolSize: convertPxToRem(12),
			symbol: 'circle',
			type: 'line',
			yAxisIndex: 1,
			data: data,
			smooth: false,
			itemStyle: {
				borderWidth: convertPxToRem(4),
				borderColor: color,
				color: '#ffffff',
				shadowColor: decreaseOpacity(color, 0.5),
				shadowBlur: 5,
			},
			lineStyle: {
				color: color,
				width: convertPxToRem(3),
				type: lineType,
			},
		})
	})

	const option = {
		color: ['#17EBC7'],
		grid: {
			left: '0%',
			top: '23%',
			bottom: '0%',
			right: '3%',
			containLabel: true,
		},
		legend: {
			show: true,
			top: 15,
			left: 'center',
			// type: 'scroll',
			icon: 'circle',
			itemWidth: convertPxToRem(12),
			itemHeight: convertPxToRem(12),
			textStyle: {
				color: '#333333',
				fontSize: convertPxToRem(18),
			},
			itemStyle: {
				borderWidth: convertPxToRem(4),
			},
			itemGap: convertPxToRem(24),
			// selectedMode: false,
		},
		yAxis: [
			{
				type: 'value',
				position: 'right',
				splitLine: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
			},
			{
				type: 'value',
				position: 'left',
				scale: true,
				nameTextStyle: {
					color: '#00FFFF',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',
						color: '#EEEEEE',
					},
				},
				// min: 60,
				// max: 100,
				// splitNumber: 7,
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					formatter: (value: any) => {
						value = Number(value)
						return value.toFixed(2)
					},
					color: '#666666',
					fontSize: convertPxToRem(16),
					// interval: 5,
					showMaxLabel: false,
				},
			},
		],
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#333333',
					},
					symbol: [
						'none',
						// 'path://M384 883.08c-8.19 0-16.38-3.12-22.62-9.38-12.5-12.5-12.5-32.75 0-45.25L677.83 512 361.38 195.55c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L734.4 478.07c18.72 18.72 18.72 49.16 0 67.88L406.62 873.7c-6.24 6.25-14.43 9.38-22.62 9.38z m305.14-359.77h0.31-0.31z',
						'arrow',
					],
					symbolOffset: 7,
					symbolSize: [7, 10],
				},
				axisLabel: {
					inside: false,
					textStyle: {
						color: '#666666', // x轴颜色
						fontWeight: 'normal',
						fontSize: convertPxToRem(16),
						lineHeight: convertPxToRem(22),
					},
					interval: 0,
				},
				data: xlabel,
			},
			{
				type: 'category',
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				splitArea: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				//-----
			},
		],
		series: datas,
	}
	return option
})

const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
	const columns: Array<any> = []

	list.forEach((item: any, index: number) => {
		const key = `name`
		if (index === 0) {
			columns[index] = {
				key,
				title,
				width: '15%',
			}
		}
		columns[index + 1] = {
			key: `${key}${index + 1}`,
			title: item,
			width: 100 / (list.length + 1) + '%',
			align: 'center',
		}
	})
	return columns
}

const columns1 = computed(() => {
	{
		props.lineData
	}
	if (!props.lineData.xlabel) return []
	const _columns = dynamicTableHeader(props.lineData.xlabel, '#00ffff')

	return _columns
})

const colorList = ['#15EAC2', '#24c5e5', '#FDA22C']

const rowColor = (_record: any, index: number) => {
	return colorList[index]
}
</script>

<style scoped lang="less">
.personal-analysis {
	display: flex;
	flex-direction: column;
	.box-content__line {
		height: 294px;
	}
	.box-content__table {
		margin-top: 30px;
		flex: 1;
	}
}
</style>
