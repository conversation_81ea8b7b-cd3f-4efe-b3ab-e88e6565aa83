<template>
	<MinCard title="班子运行指数" header>
		<template v-slot:right>
			<div class="menu-box">
				<nav-menu />
				<button class="look-all" @click="onLookAll">查看全部</button>
			</div>
		</template>
		<div class="personal-analysis" ref="topBoxRef">
			<data-table :data-source="teamData" :columns="columns" body-scroll ref="table" row-id="org_id" :rowClick="onTableRowClick">
				<template v-slot:rank="{ value }">
					<span v-if="value > 3">{{ value }}</span>
					<span v-else :class="`rank-icon rank-${value}`"></span>
				</template>
				<template v-slot:name="{ value, data }">
					<span
						@click.stop="
							() => {
								props.onOrgSelect?.(data.org_id, data)
							}
						"
						>{{ value }}</span
					>
				</template>
				<template v-slot:cadre_index_rank="{ value }">
					<span v-if="value > 3">{{ value }}</span>
					<span v-else :class="`rank-icon rank-${value}`"></span>
				</template>
				<template v-slot:structure="{ value }">
					<span :class="`structure-icon structure-${value}`"></span>
				</template>
			</data-table>
		</div>
	</MinCard>
	<Teleport to="body">
		<Modal :visible="visibleTable" @close="onTableModal" class="table-modal">
			<div class="data-modal">
				<data-table :data-source="teamData" :columns="columns" body-scroll ref="tableModal" row-id="org_id" :rowClick="onTableRowClick">
					<template v-slot:rank="{ value }">
						<span v-if="value > 3">{{ value }}</span>
						<span v-else :class="`rank-icon rank-${value}`"></span>
					</template>
					<template v-slot:name="{ value, data }">
						<span
							@click.stop="
								() => {
									props.onOrgSelect?.(data.org_id, data)
									onTableModal()
								}
							"
							>{{ value }}</span
						>
					</template>
					<template v-slot:cadre_index_rank="{ value }">
						<span v-if="value > 3">{{ value }}</span>
						<span v-else :class="`rank-icon rank-${value}`"></span>
					</template>
					<template v-slot:structure="{ value }">
						<span :class="`structure-icon structure-${value}`"></span>
					</template>
				</data-table>
			</div>
		</Modal>
	</Teleport>
</template>

<script setup lang="ts">
import { watch, toRefs, ref } from 'vue'
import NavMenu from './Nav.vue'
import DataTable from '@/components/Table.vue'
import { useCadreSystem } from '@/store/cadreSystem'

const props = defineProps({ teamData: { type: Array<any>, default: () => [] }, onOrgSelect: Function })

const columns = [
	{
		key: 'name',
		align: 'center',
		width: '6%',
		title: '班子名称',
	},
	{
		key: 'index',
		align: 'center',
		width: '6%',
		title: '班子运行指数',
	},
	{
		key: 'rank',
		align: 'center',
		width: '6%',
		title: '运行指数排名',
	},
	{
		key: 'cadre_index',
		align: 'center',
		width: '6%',
		title: '班子配备指数',
	},
	{
		key: 'cadre_index_rank',
		align: 'center',
		width: '6%',
		title: '配备指数排名',
	},
	{
		key: 'ecology',
		align: 'center',
		width: '6%',
		title: '政治生态',
	},
	{
		key: 'ability',
		align: 'center',
		width: '6%',
		title: '班子业绩',
	},
	{
		key: 'reputation',
		align: 'center',
		width: '6%',
		title: '班子口碑',
	},
	{
		key: 'structure',
		align: 'center',
		width: '6%',
		title: '班子结构',
	},
]
const table = ref()
const tableModal = ref()
const store = useCadreSystem()
const visibleTable = ref(false)

const { coor_user_id } = toRefs(store)

// 在表格内点击行不触发滚动
let emitScroll = true

const onTableRowClick = (data: any) => {
	// if (blur.value) return
	emitScroll = false
	store.updateCoorUserId(data.org_id)
}

const onTableModal = () => {
	visibleTable.value = false
}

const onLookAll = () => {
	visibleTable.value = true
}
watch(coor_user_id, (newVal, oldVal) => {
	if (!emitScroll) {
		emitScroll = true
	} else if (oldVal !== newVal) {
		table.value?.scrollToElementById(newVal)
	}
	table.value?.rowHighLight(newVal)

	visibleTable.value && (tableModal.value.rowHighLight(newVal), table.value?.scrollToElementById(newVal))
})
</script>

<style scoped lang="less">
.personal-analysis {
	display: flex;
	flex-direction: column;
	// margin-top: 16px;
	overflow: auto;
	padding: 0px 0px;
	max-height: 414.5px;
	// 隐藏滚动条
	&::-webkit-scrollbar {
		display: none;
	}

	.box-content__line {
		height: 414.5px;
	}

	.box-content__table {
		flex: 1;
	}
}
.menu-box {
	display: flex;
	align-items: center;
	.look-all {
		margin-left: 20px;
		padding: 8px 11px;
		text-align: center;
		font-size: 14px;
		font-family: Source Han Sans CN;
		font-weight: 400;
		color: #ffffff;
		border-radius: 4px;
		line-height: 14px;
		background: #008eff;
		cursor: pointer;
	}
}
.data-menu {
	// padding-right: 21px;
	display: flex;
	align-items: center;

	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		font-family: PingFang SC-Medium, PingFang SC;
		font-weight: 500;
		color: #666666;
		// font-size: 14px;
		// line-height: 28px;
		// font-weight: 400;
		// color: #9e9e9e;
		cursor: pointer;
		text-align: center;
	}

	.menu-active {
		color: #e5231a;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #ff2121;
		border-radius: 2px 2px 2px 2px;
		opacity: 1;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translateX(-50%);
	}

	.contrast-button {
		margin-left: 17px;

		.button-box {
			padding: 8px 11px;
			text-align: center;
			font-size: 14px;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
			border-radius: 4px;
			line-height: 19px;
			background: #e5251b;
			cursor: pointer;
		}
	}
	.data-btn {
		margin-left: 37px;
	}
}

.contrast-box {
	width: 1200px;
	min-height: 100%;
	// overflow-y: auto;
	// height: 1215px;
	// border: 1px solid #23dbfc;
	::v-deep(.card-box) {
		padding: 0;
		.title {
			font-size: 20px;
		}
	}
	.chosse-box {
		padding: 24px;
		background-color: #ffffff;
		.choose {
			margin-top: 24px;
			display: flex;
			justify-content: space-between;
		}
		.contrast-seek {
			width: 706px;
			display: flex;
			flex-direction: column;
			margin-right: 12px;

			.seek-table {
				height: 500px;
				::v-deep(.ant-table-thead) {
					.ant-table-cell {
						background-color: #f3f3f3;
					}
				}
				::v-deep(.ant-table-tbody) {
					.ant-table-cell {
						font-size: 16px;
					}
				}
			}

			.seek-title {
				padding: 21px 20px;
				display: flex;
				align-items: center;
				margin-bottom: 17px;
				width: 100%;
				height: 76px;
				background: #f7f8fa;
				border-radius: 4px 4px 4px 4px;
				.input-box {
					display: flex;
					align-items: center;
					height: 40px;
					span {
						font-size: 16px;
						font-family: Source Han Sans CN;
						font-weight: 400;
						color: #222222;
						white-space: nowrap;
					}
					input {
						width: 160px;
					}
				}
				.look-up {
					width: 80px;
					height: 36px;
					background: #e5231a;
					border-radius: 4px 4px 4px 4px;
					opacity: 1;
					font-size: 16px;
					line-height: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #ffffff;
					outline: none;
				}
				.reset {
					width: 80px;
					height: 36px;
					background: #ededed;
					border-radius: 4px 4px 4px 4px;
					font-size: 16px;
					line-height: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #666666;
					opacity: 1;
					outline: none;
				}
				button {
					margin-right: 10px;
					width: 80px;
					height: 36px;
					font-size: 16px;
					font-weight: 400;
				}
				span {
					margin-right: 11px;
				}

				div {
					margin-right: 24px;
				}
				// button {
				// 	outline: none;
				// }
			}

			// input {
			// 	background: #fff;
			// 	border: 1px solid #00d2ff;
			// 	border-radius: 2px;
			// 	width: 120px;
			// 	height: 24px;
			// 	color: #fff;
			// }

			// button {
			// 	width: 62px;
			// 	height: 24px;
			// 	border: 1px solid #00d2ff;
			// 	border-radius: 4px;
			// 	padding: 0px;
			// 	font-size: 14px;
			// 	font-family: Source Han Sans CN;
			// 	font-weight: 400;
			// 	color: #00d2ff;
			// }
		}

		.already-have {
			width: 408px;
			display: flex;
			flex-direction: column;

			.have-nubmer {
				display: flex;
				align-items: center;
				padding: 28px 20px;
				width: 408px;
				height: 76px;
				background: #f7f8fa;
				border-radius: 4px 4px 4px 4px;
				opacity: 1;
				font-size: 14px;
				font-weight: 400;
				color: #00d2ff;
				.label {
					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #222222;
					line-height: 24px;
				}
				.select-number {
					margin-left: 10px;
					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #2462ff;
				}
			}

			.have-name {
				margin-top: 20px;
				padding: 20px;
				height: 551px;
				background: #f7f8fa;
				border-radius: 4px 4px 4px 4px;
				opacity: 1;
				.select-box {
					display: flex;
					justify-content: space-between;
				}
				div {
					margin-bottom: 33px;
					cursor: pointer;
				}

				span {
					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #222222;
				}

				.have-delete {
					width: 24px;
					height: 24px;
					display: inline-block;
					background-image: url('@/assets/images/delete.png');
					background-size: 100% 100%;
					margin-left: 15px;
					vertical-align: middle;
				}
			}
		}
	}

	.veidoo-box {
		margin-top: 24px;
		padding: 24px;
		width: 100%;
		background: #ffffff;
		.veidoo {
			margin-top: 24px;
			width: 100%;
			.top-content {
				display: flex;
				margin-top: 49px;
				margin-bottom: 31px;

				.label {
					display: flex;
					align-items: center;
					font-size: 18px;
					font-weight: bold;
					color: #00fff6;
					line-height: 18px;
					text-shadow: 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2),
						0 0 4px rgba(35, 219, 252, 0.2);
					background: linear-gradient(0deg, #3bdeff 6.1279296875%, #d1fbff 55.4443359375%, #ddf9ff 100%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;

					&::after {
						margin-left: 7px;
						content: '';
						display: inline-block;
						width: 24px;
						height: 16px;
						background: url('@/assets/images/label-icon.png') no-repeat center / cover;
					}
				}
			}
			.veidoo-item {
				display: flex;
				border: 1px solid #ebebeb;
				.title {
					display: flex;
					align-items: center;
					justify-content: center;

					width: 100px;
					background: #f3f3f3;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;

					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #222222;
					line-height: 24px;
				}

				.data {
					flex: 1;
					padding: 28px 44px;

					display: flex;
					flex-wrap: wrap;
					.check-item {
						width: 25%;
						white-space: nowrap;
						::v-deep(.ant-checkbox-wrapper) {
							font-size: 16px;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #222222;
						}
					}
				}

				.two-title {
					display: flex;
					width: 169px;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 68px 0;
				}

				div {
					font-size: 14px;
					font-family: Source Han Sans CN;
					font-weight: 500;
					color: #00d2ff;
				}
			}
		}
	}
	::v-deep(.ant-checkbox-checked) {
		.ant-checkbox-inner {
			background-color: #0a58f6;
			border-color: #0a58f6;
		}
	}
	.start-analyse {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 16px;
		width: 1200px;
		height: 88px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		button {
			width: 160px;
			height: 48px;
			background: #e5231a;
			border-radius: 4px 4px 4px 4px;
			opacity: 1;
			font-size: 16px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #ffffff;
			outline: none;
			border: 0;
			&:active {
				opacity: 0.7;
			}
		}
	}
}

// .start-analyse {
// 	display: flex;
// 	justify-content: center;
// 	margin: 51px 0 36px 0;

// 	.analyse {
// 		background: rgba(21, 25, 105, 0.4);
// 		border: 1px solid #00fff6;
// 		box-shadow: inset 0 0 25px rgba(0, 255, 246, 0.4);
// 		border-radius: 4px;
// 		font-size: 16px;
// 		font-family: Source Han Sans CN;
// 		font-weight: 400;
// 		color: #ffffff;
// 		height: unset;
// 		padding: 10px 53px;
// 	}
// }

// ::v-deep(.ant-table-thead th) {
// 	background: #1a1e72;
// 	font-size: 14px;
// 	font-family: Source Han Sans CN;
// 	font-weight: 400;
// 	color: #00d2ff;
// 	border-bottom: 1px solid #333aa2 !important;
// }

::v-deep(.ant-table-thead .ant-table-selection) {
	display: none !important;
}

// ::v-deep(.ant-table) {
// 	max-height: 551px;
// 	// height: 100%;
// 	height: 551px;
// 	background: #1a1e72;
// 	max-height: 551px;
// }

::v-deep(.ant-table::-webkit-scrollbar) {
	display: none;
}

// ::v-deep(.ant-table-tbody td) {
// 	background: #1a1e72 !important;
// 	border-bottom: 1px solid #333aa2 !important;
// 	border: 0px;
// 	font-size: 14px;
// 	font-family: Source Han Sans CN;
// 	font-weight: 400;
// 	color: #00d2ff;
// }

// ::v-deep(.ant-checkbox-wrapper span) {
// 	font-size: 14px;
// 	font-family: Source Han Sans CN;
// 	font-weight: 400;
// 	color: #00d2ff;
// }

::v-deep(.ant-checkbox-wrapper .ant-checkbox-inner) {
	// width: 10px !important;
	// height: 10px !important;
	// top: -2px;
}

::v-deep(.ant-checkbox-checked .ant-checkbox-inner::after) {
	// left: 0;
}

::v-deep(.ant-checkbox-wrapper) {
	width: 25%;
	margin-left: 0;
	margin-bottom: 14px;
	position: relative;
}

::v-deep(.ant-table-tbody .ant-checkbox-wrapper) {
	position: relative;
}

::v-deep(.ant-table-tbody .ant-checkbox) {
	position: absolute;
	top: 10px;
}

::v-deep(.filter-style) {
	position: relative;
	user-select: none;
	filter: blur(7px);
	&::after {
		content: '';
		position: absolute;
		inset: 0;
	}
}
::v-deep(.table) {
	user-select: none;
}
::v-deep(.modal-content) {
	height: 90%;
	overflow-y: auto;
	// 隐藏滚动条
	::-webkit-scrollbar {
		display: none;
	}
}

.rank-icon {
	display: inline-block;
	width: 28px;
	height: 28px;
	vertical-align: middle;
}

.rank-1 {
	background: url('@/assets/images/rank-1.png') no-repeat center / contain;
}
.rank-2 {
	background: url('@/assets/images/rank-2.png') no-repeat center / contain;
}
.rank-3 {
	background: url('@/assets/images/rank-3.png') no-repeat center / contain;
}

.structure-icon {
	display: inline-block;
	width: 24px;
	height: 24px;
	vertical-align: middle;
}

.structure-0 {
	background: url('../../images/structure-0.png') no-repeat center / contain;
}
.structure-1 {
	background: url('../../images/structure-1.png') no-repeat center / contain;
}
.structure-2 {
	background: url('../../images/structure-2.png') no-repeat center / contain;
}
.structure-3 {
	background: url('../../images/structure-3.png') no-repeat center / contain;
}
.custom-portrait {
	display: inline-block;
	width: 27.43px;
	height: 27.43px;
	background: url('@/assets/images/portrait.png') no-repeat center center;
	background-size: 100%, 100%;
	cursor: pointer;
	font-size: 0px;
	vertical-align: middle;
}
::v-deep(.cursor-pointer) {
	cursor: pointer;
}
.table-modal {
	height: 100%;
	::v-deep(.modal-content) {
		// height: auto;
		// min-height: 50%;
		// max-height: 90%;
		background-color: #ffffff;
	}
	.data-modal {
		height: 100%;
		width: 1800px;
	}
}
</style>
