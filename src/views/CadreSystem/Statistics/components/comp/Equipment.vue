<template>
	<div class="equipment">
		<div class="top">
			<a-radio-group v-model:value="mode1">
				<a-radio-button v-for="item in menu" :key="item.key" :value="item.key">{{ item.label }}</a-radio-button>
			</a-radio-group>
			<div class="tree-box">
				<TeamMembers :mode="mode1" :org_id="org_id" v-if="mode1 === '1'" />

				<a-table class="m-top-20" v-else :dataSource="datasource" :columns="columns" :scroll="{ y: 400 }" :pagination="false"></a-table>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch } from 'vue'
import { getCadreIndex } from '@/apis/statistics'

import TeamMembers from './TeamMembers.vue'

const props = defineProps({
	org_id: {
		type: Number,
	},
})

const menu = [
	{
		label: '班子',
		key: '1',
	},
	{
		label: '同序列',
		key: '2',
	},
	{
		label: '乡镇（部门）',
		key: '3',
	},
]
const columns = [
	{
		title: '排名',
		dataIndex: 'index',
		align: 'center',
	},
	{
		title: '班子名称',
		dataIndex: 'org_name',
		align: 'center',
	},
	{
		title: '班子配备',
		dataIndex: 'cadre_index_avg',
		align: 'center',
	},
]

const mode1 = ref(menu[0].key)

const datasource = ref([])

const initCadreIndex = async () => {
	const res: any = await getCadreIndex({ org_id: props.org_id, flag: Number(mode1.value) - 1 })

	if (res.code === 0) {
		datasource.value = res.data.map((item: any, index: number) => {
			return {
				...item,
				index: index + 1,
			}
		})
	}
}

initCadreIndex()

watch(mode1, (newV) => {
	if (newV == '1') return

	datasource.value = []

	initCadreIndex()
})
</script>

<style lang="less" scoped>
.equipment {
	.top {
		.menu-box {
			display: flex;
			justify-content: space-between;
			.menu-item {
				width: 100px;
				height: 40px;
				line-height: 40px;
				text-align: center;
				border: 1px solid #ccc;
				border-radius: 5px;
				cursor: pointer;
			}
		}
	}
}
</style>
