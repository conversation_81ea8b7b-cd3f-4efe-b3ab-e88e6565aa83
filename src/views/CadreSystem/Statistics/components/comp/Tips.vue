<template>
	<div class="tips">
		<span :class="`icon icon-${level}`"></span>
		<span class="text">
			{{ text }}
		</span>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	text: String,
	level: {
		type: Number,
		required: true,
		default: 0,
	},
})
</script>

<style lang="less" scoped>
.tips {
	display: flex;
	align-items: center;
	.icon {
		flex-shrink: 0;
		display: inline-block;
		height: 24px;
		width: 24px;
		background-size: cover;
	}
	.icon-0 {
		background-image: url('../../images/success.png');
	}
	.icon-1 {
		background-image: url('../../images/warn.png');
	}
	.text {
		margin-left: 12px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 24px;
		color: rgba(0, 0, 0, 0.9);
		line-height: 28px;
		letter-spacing: 1px;
	}
}
</style>
