<template>
	<div class="box-1">
		<div class="header">
			<div class="icon"></div>
			<div class="text">{{ title }}</div>
		</div>
		<div class="content">
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Box',
	props: {
		title: String,
	},
}
</script>

<style scoped>
.box-1 {
	margin-top: 25px;
	width: 100%;
	display: flex;
	flex-direction: column;
}

.header {
	padding: 0px 42px;
	display: flex;
	align-items: center;
	height: 83px;
	background-color: #eef3ff;
}

.icon {
	width: 40px;
	height: 40px;
	background-image: url('../../images/header-icon.png');
	background-size: cover;
}

.text {
	font-size: 24px;
	margin-left: 12px;
}

.content {
	flex: 1;
	padding: 39px;
	background: #f6f8fc;
	border-radius: 6px 6px 6px 6px;
}
</style>
