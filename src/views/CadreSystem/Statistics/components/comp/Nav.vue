<template>
	<div class="data-menu">
		<div
			:class="['menu-item', coor_menu_selected === item.key && 'menu-active']"
			v-for="item in store.getMenu"
			:key="item.key"
			@click="onMenu(item.key)"
		>
			{{ item.label }}
		</div>
	</div>
</template>

<script lang="ts" setup>
import { toRefs } from 'vue'

import { useCadreSystem } from '@/store/cadreSystem'

const store = useCadreSystem()

const { coor_menu_selected } = toRefs(store)

const emit = defineEmits(['change'])

const onMenu = (key: number) => {
	// currentIndex.value = key
	store.updateCoorMenuSelected(key)

	emit('change', key)
}
</script>

<style lang="less" scoped>
.data-menu {
	display: flex;
	align-items: center;
	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		font-weight: 500;
		color: #666666;
		// line-height: 28px;
		cursor: pointer;
	}
	.menu-active {
		color: #008eff;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #008eff;
		border-radius: 2px 2px 2px 2px;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translate(-50%, 0);
	}
}
</style>
