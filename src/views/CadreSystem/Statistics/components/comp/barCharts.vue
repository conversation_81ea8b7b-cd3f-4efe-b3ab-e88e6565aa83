<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
})
const option = ref<any>({
	legend: {},
	color: ['#008EFF', '#2ECF61'],
	grid: {
		left: '3%',
		right: '4%',
		bottom: '3%',
		containLabel: true,
	},
	xAxis: [
		{
			type: 'category',
			data: [],
		},
	],
	yAxis: [
		{
			type: 'value',
			name: '单位：个',
		},
	],
	series: [
		{
			name: '乡镇',
			type: 'bar',
			stack: 'Ad',
			emphasis: {
				focus: 'series',
			},
			label: {
				show: true,
				position: 'inside',
				formatter: ({ value }: any) => {
					return value ? `${value}个` : ''
				},
				color: '#fff',
			},
			data: [],
		},
		{
			name: '部门',
			type: 'bar',
			stack: 'Ad',
			barWidth: 54,
			emphasis: {
				focus: 'series',
			},
			label: {
				show: true,
				formatter: ({ value }: any) => {
					return value ? `${value}个` : ''
				},
				color: '#fff',
			},
			data: [],
		},
	],
})

const setOptions = (data: any = []) => {
	if (data.length) {
		option.value.xAxis[0].data = []
		option.value.series[0].data = []
		option.value.series[1].data = []
		data.forEach((item: any) => {
			option.value.xAxis[0].data.push(item.axis_name)
			option.value.series[0].data.push(item.township)
			option.value.series[1].data.push(item.department)
		})
	}
}

watchEffect(() => {
	setOptions(props.data)
})

const emit = defineEmits(['onChangeModal'])
const click = ({ seriesIndex, dataIndex, name }: any) => {
	const type = seriesIndex === 1 ? seriesIndex : 2
	const { axis_id: ids }: any = props.data[dataIndex] || {}
	emit('onChangeModal', type, ids, name)
}
</script>

<template>
	<v-chart :option="option" style="height: 300px; width: 100%" @click="click" autoresize />
</template>

<style lang="scss" scoped></style>
