<template>
	<div class="tips-1">
		<span class="label">已配备</span>
		<span class="number">
			<span class="text">{{ number }}</span>
			<span class="unit">人</span>
		</span>
	</div>
</template>

<script lang="ts" setup>
defineProps({
	number: Number,
})
</script>

<style lang="less" scoped>
.tips-1 {
	padding: 11px 41px 11px 27px;
	display: flex;
	width: 276px;
	justify-content: space-between;
	background: url('../../images/tips1-bg.png') center / 100% no-repeat;
	.label {
		font-family: Source <PERSON>, Source <PERSON>;
		font-weight: 400;
		font-size: 23px;
		color: rgba(0, 0, 0, 0.9);
		line-height: 26px;
	}
	.number {
		.text {
			font-family: ArTarumianBakhum, ArTarumianBakhum;
			font-weight: 400;
			font-size: 36px;
			color: #008eff;
			line-height: 23px;
		}
		.unit {
			font-family: Source <PERSON>, Source <PERSON>;
			font-weight: 400;
			font-size: 23px;
			color: rgba(0, 0, 0, 0.9);
			line-height: 23px;
		}
	}
}
</style>
