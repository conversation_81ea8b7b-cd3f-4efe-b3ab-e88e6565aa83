export const svgIcon = (color = 'black') => {
	return `
    <svg width="100%" height="100%" viewBox="0 0 36 36" fill="${color}" xmlns="http://www.w3.org/2000/svg">
								<g id="&#229;&#136;&#151;&#232;&#161;&#168;&#230;&#168;&#161;&#229;&#188;&#143; 1" clip-path="url(#clip0_1007_11774)">
									<g id="Group 427323171">
										<path
											id="Vector"
											d="M18.3422 22.5073C18.5549 22.2945 18.8416 22.1722 19.1425 22.1658C19.4433 22.1594 19.735 22.2695 19.9566 22.4731L19.9922 22.5073L25.0003 27.5155L30.0084 22.5081C30.2211 22.2954 30.5079 22.1731 30.8087 22.1668C31.1095 22.1604 31.4011 22.2705 31.6226 22.4741L31.6582 22.5081C31.871 22.7209 31.9933 23.0077 31.9996 23.3085C32.006 23.6093 31.8958 23.901 31.6922 24.1225L31.6582 24.1581L25.8251 29.9903C25.6123 30.203 25.3256 30.3254 25.0248 30.3317C24.724 30.3381 24.4324 30.228 24.2109 30.0245L24.1753 29.9903L18.3422 24.1571C18.1234 23.9384 18.0005 23.6416 18.0005 23.3322C18.0005 23.0229 18.1234 22.7261 18.3422 22.5073Z"
											fill="${color}"
										/>
										<path
											id="Vector_2"
											d="M25.0004 13.9997C25.3022 13.9997 25.5923 14.1167 25.8097 14.3261C26.0271 14.5355 26.1549 14.821 26.1662 15.1226L26.167 15.1664V29.1658C26.1673 29.4717 26.0475 29.7654 25.8334 29.9837C25.6193 30.2021 25.328 30.3277 25.0223 30.3334C24.7165 30.3392 24.4207 30.2246 24.1986 30.0144C23.9764 29.8042 23.8457 29.5152 23.8345 29.2096L23.8338 29.1658V15.1664C23.8338 14.857 23.9567 14.5602 24.1755 14.3414C24.3942 14.1226 24.691 13.9997 25.0004 13.9997ZM15.6674 16.333C15.9732 16.3327 16.2669 16.4526 16.4852 16.6667C16.7035 16.8808 16.8291 17.1721 16.8348 17.4778C16.8405 17.7836 16.7259 18.0793 16.5157 18.3015C16.3056 18.5236 16.0166 18.6543 15.711 18.6654L15.6674 18.6662H5.1678C4.86197 18.6665 4.56826 18.5467 4.34989 18.3326C4.13151 18.1185 4.00594 17.8273 4.00021 17.5215C3.99447 17.2157 4.10903 16.9199 4.31922 16.6978C4.52941 16.4756 4.81843 16.3449 5.12405 16.3338L5.1678 16.333H15.6674ZM25.0004 7C25.3062 6.99975 25.5999 7.11957 25.8182 7.33369C26.0365 7.54781 26.1621 7.83909 26.1678 8.14484C26.1735 8.45059 26.0589 8.74636 25.8487 8.96848C25.6385 9.1906 25.3495 9.32131 25.0439 9.33247L25.0004 9.33325H5.1678C4.86197 9.33355 4.56826 9.21376 4.34989 8.99966C4.13151 8.78556 4.00594 8.49427 4.00021 8.1885C3.99447 7.88273 4.10903 7.58694 4.31922 7.3648C4.52941 7.14266 4.81843 7.01194 5.12405 7.00078L5.1678 7H25.0004ZM13.3342 25.666C13.64 25.6657 13.9337 25.7854 14.1521 25.9995C14.3704 26.2136 14.496 26.5049 14.5017 26.8107C14.5075 27.1165 14.3929 27.4123 14.1827 27.6344C13.9725 27.8565 13.6835 27.9873 13.3779 27.9984L13.3342 27.9992H5.1678C4.86197 27.9995 4.56826 27.8797 4.34989 27.6656C4.13151 27.4515 4.00594 27.1602 4.00021 26.8545C3.99447 26.5487 4.10903 26.2529 4.31922 26.0308C4.52941 25.8086 4.81843 25.6779 5.12405 25.6667L5.1678 25.666H13.3342Z"
											fill="${color}"
										/>
									</g>
								</g>
								<defs>
									<clipPath id="clip0_1007_11774">
										<rect width="36" height="36" fill="white" />
									</clipPath>
								</defs>
							</svg>
    `
}
export const svgIconUp = (color = 'black') => {
	return `
    <svg width="100%" height="100%" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
		<g id="Frame" clip-path="url(#clip0_1007_11784)">
			<g id="Group 427323171">
				<path id="Vector" d="M15.6674 16.333C15.9732 16.3327 16.2669 16.4526 16.4852 16.6667C16.7035 16.8808 16.8291 17.1721 16.8348 17.4778C16.8405 17.7836 16.7259 18.0793 16.5157 18.3015C16.3056 18.5236 16.0166 18.6543 15.711 18.6654L15.6674 18.6662H5.1678C4.86197 18.6665 4.56826 18.5467 4.34989 18.3326C4.13151 18.1185 4.00594 17.8273 4.00021 17.5215C3.99447 17.2157 4.10903 16.9199 4.31922 16.6978C4.52941 16.4756 4.81843 16.3449 5.12405 16.3338L5.1678 16.333H15.6674ZM25.0004 7C25.3062 6.99975 25.5999 7.11957 25.8182 7.33369C26.0365 7.54781 26.1621 7.83909 26.1678 8.14484C26.1735 8.45059 26.0589 8.74636 25.8487 8.96848C25.6385 9.1906 25.3495 9.32131 25.0439 9.33247L25.0004 9.33325H5.1678C4.86197 9.33355 4.56826 9.21376 4.34989 8.99966C4.13151 8.78556 4.00594 8.49427 4.00021 8.1885C3.99447 7.88273 4.10903 7.58694 4.31922 7.3648C4.52941 7.14266 4.81843 7.01194 5.12405 7.00078L5.1678 7H25.0004ZM13.3342 25.666C13.64 25.6657 13.9337 25.7854 14.1521 25.9995C14.3704 26.2136 14.496 26.5049 14.5017 26.8107C14.5075 27.1165 14.3929 27.4123 14.1827 27.6344C13.9725 27.8565 13.6835 27.9873 13.3779 27.9984L13.3342 27.9992H5.1678C4.86197 27.9995 4.56826 27.8797 4.34989 27.6656C4.13151 27.4515 4.00594 27.1602 4.00021 26.8545C3.99447 26.5487 4.10903 26.2529 4.31922 26.0308C4.52941 25.8086 4.81843 25.6779 5.12405 25.6667L5.1678 25.666H13.3342Z" fill="${color}"/>
				<g id="Group 427323172">
					<path id="Vector_2" d="M31.6582 21.8257C31.4455 22.0385 31.1587 22.1608 30.8579 22.1672C30.557 22.1736 30.2654 22.0635 30.0438 21.8599L30.0082 21.8257L25.0001 16.8175L19.992 21.8249C19.7792 22.0376 19.4925 22.1599 19.1917 22.1663C18.8909 22.1726 18.5993 22.0625 18.3778 21.8589L18.3422 21.8249C18.1294 21.6121 18.0071 21.3254 18.0007 21.0245C17.9944 20.7237 18.1045 20.432 18.3082 20.2105L18.3422 20.1749L24.1753 14.3427C24.388 14.13 24.6747 14.0077 24.9755 14.0013C25.2763 13.9949 25.568 14.105 25.7895 14.3085L25.8251 14.3427L31.6582 20.1759C31.877 20.3946 31.9999 20.6914 31.9999 21.0008C31.9999 21.3102 31.877 21.6069 31.6582 21.8257Z" fill="${color}"/>
					<path id="Vector_3" d="M25 30.3335C24.6982 30.3335 24.4081 30.2165 24.1907 30.0071C23.9733 29.7978 23.8455 29.5123 23.8342 29.2106L23.8334 29.1669L23.8334 15.1674C23.8331 14.8616 23.9529 14.5679 24.167 14.3495C24.3811 14.1311 24.6724 14.0055 24.9781 13.9998C25.2839 13.9941 25.5797 14.1086 25.8018 14.3188C26.024 14.529 26.1547 14.818 26.1658 15.1237L26.1666 15.1674L26.1666 29.1669C26.1666 29.4763 26.0437 29.773 25.8249 29.9918C25.6061 30.2106 25.3094 30.3335 25 30.3335Z" fill="${color}"/>
				</g>
			</g>
		</g>
		<defs>
			<clipPath id="clip0_1007_11784">
			<rect width="36" height="36" fill="white"/>
			</clipPath>
		</defs>
	</svg>
    `
}
export const svgIconPing = (color = 'black') => {
	return `
	<svg width="100%" height="100%" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
		<g
			id="&#228;&#186;&#145;&#231;&#155;&#152; - &#229;&#185;&#179;&#233;&#147;&#186;&#232;&#167;&#134;&#229;&#155;&#190; 1">
			<path id="Vector"
				d="M13.8075 16.375H7.5675C6.88656 16.375 6.2335 16.1045 5.752 15.623C5.2705 15.1415 5 14.4884 5 13.8075V7.5675C5 6.88656 5.2705 6.2335 5.752 5.752C6.2335 5.2705 6.88656 5 7.5675 5H13.8075C14.4884 5 15.1415 5.2705 15.623 5.752C16.1045 6.2335 16.375 6.88656 16.375 7.5675V13.8075C16.375 14.4884 16.1045 15.1415 15.623 15.623C15.1415 16.1045 14.4884 16.375 13.8075 16.375ZM7.5675 6.625C7.31753 6.625 7.07781 6.7243 6.90105 6.90105C6.7243 7.07781 6.625 7.31753 6.625 7.5675V13.8075C6.625 14.0575 6.7243 14.2972 6.90105 14.4739C7.07781 14.6507 7.31753 14.75 7.5675 14.75H13.8075C14.0575 14.75 14.2972 14.6507 14.4739 14.4739C14.6507 14.2972 14.75 14.0575 14.75 13.8075V7.5675C14.75 7.31753 14.6507 7.07781 14.4739 6.90105C14.2972 6.7243 14.0575 6.625 13.8075 6.625H7.5675ZM13.8075 31H7.5675C6.88656 31 6.2335 30.7295 5.752 30.248C5.2705 29.7665 5 29.1134 5 28.4325V22.1925C5 21.5116 5.2705 20.8585 5.752 20.377C6.2335 19.8955 6.88656 19.625 7.5675 19.625H13.8075C14.4884 19.625 15.1415 19.8955 15.623 20.377C16.1045 20.8585 16.375 21.5116 16.375 22.1925V28.4325C16.375 29.1134 16.1045 29.7665 15.623 30.248C15.1415 30.7295 14.4884 31 13.8075 31ZM7.5675 21.25C7.31753 21.25 7.07781 21.3493 6.90105 21.5261C6.7243 21.7028 6.625 21.9425 6.625 22.1925V28.4325C6.625 28.6825 6.7243 28.9222 6.90105 29.0989C7.07781 29.2757 7.31753 29.375 7.5675 29.375H13.8075C14.0575 29.375 14.2972 29.2757 14.4739 29.0989C14.6507 28.9222 14.75 28.6825 14.75 28.4325V22.1925C14.75 21.9425 14.6507 21.7028 14.4739 21.5261C14.2972 21.3493 14.0575 21.25 13.8075 21.25H7.5675ZM28.4325 16.375H22.1925C21.5116 16.375 20.8585 16.1045 20.377 15.623C19.8955 15.1415 19.625 14.4884 19.625 13.8075V7.5675C19.625 6.88656 19.8955 6.2335 20.377 5.752C20.8585 5.2705 21.5116 5 22.1925 5H28.4325C29.1134 5 29.7665 5.2705 30.248 5.752C30.7295 6.2335 31 6.88656 31 7.5675V13.8075C31 14.4884 30.7295 15.1415 30.248 15.623C29.7665 16.1045 29.1134 16.375 28.4325 16.375ZM22.1925 6.625C21.9425 6.625 21.7028 6.7243 21.5261 6.90105C21.3493 7.07781 21.25 7.31753 21.25 7.5675V13.8075C21.25 14.0575 21.3493 14.2972 21.5261 14.4739C21.7028 14.6507 21.9425 14.75 22.1925 14.75H28.4325C28.6825 14.75 28.9222 14.6507 29.0989 14.4739C29.2757 14.2972 29.375 14.0575 29.375 13.8075V7.5675C29.375 7.31753 29.2757 7.07781 29.0989 6.90105C28.9222 6.7243 28.6825 6.625 28.4325 6.625H22.1925ZM28.4325 31H22.1925C21.5116 31 20.8585 30.7295 20.377 30.248C19.8955 29.7665 19.625 29.1134 19.625 28.4325V22.1925C19.625 21.5116 19.8955 20.8585 20.377 20.377C20.8585 19.8955 21.5116 19.625 22.1925 19.625H28.4325C29.1134 19.625 29.7665 19.8955 30.248 20.377C30.7295 20.8585 31 21.5116 31 22.1925V28.4325C31 29.1134 30.7295 29.7665 30.248 30.248C29.7665 30.7295 29.1134 31 28.4325 31ZM22.1925 21.25C21.9425 21.25 21.7028 21.3493 21.5261 21.5261C21.3493 21.7028 21.25 21.9425 21.25 22.1925V28.4325C21.25 28.6825 21.3493 28.9222 21.5261 29.0989C21.7028 29.2757 21.9425 29.375 22.1925 29.375H28.4325C28.6825 29.375 28.9222 29.2757 29.0989 29.0989C29.2757 28.9222 29.375 28.6825 29.375 28.4325V22.1925C29.375 21.9425 29.2757 21.7028 29.0989 21.5261C28.9222 21.3493 28.6825 21.25 28.4325 21.25H22.1925Z"
				fill="${color}" />
		</g>
	</svg>
    `
}
export const svgIconTable = (color = 'black') => {
	return `
	<svg width="100%" height="100%" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="&#229;&#136;&#151;&#232;&#161;&#168;&#230;&#168;&#161;&#229;&#188;&#143; 1">
        <g id="Group 427322237">
            <path id="Vector" d="M30.6951 26.5579H30.6972V26.5449L30.6951 26.5579Z" fill="${color}" fill-opacity="0.85" />
            <path id="Vector_2"
                d="M12.2793 5.3999H5.46038C4.43474 5.3999 3.6001 6.23461 3.6001 7.25921V14.0786C3.6001 15.1043 4.43474 15.9379 5.46038 15.9379H12.2793C13.3045 15.9379 14.1392 15.1043 14.1392 14.0786V7.25917C14.1391 6.23458 13.3044 5.3999 12.2793 5.3999ZM12.3654 7.25917V14.0786C12.3654 14.1257 12.3264 14.1657 12.2793 14.1657H5.46038C5.41328 14.1657 5.37231 14.1257 5.37231 14.0786V7.25917C5.37231 7.2105 5.41224 7.17212 5.46038 7.17212H12.2793C12.3275 7.17212 12.3654 7.21053 12.3654 7.25917Z"
                fill="${color}" fill-opacity="0.85" />
            <path id="Vector_3"
                d="M12.2793 20.0078H5.46038C4.43474 20.0078 3.6001 20.8435 3.6001 21.8675V28.687C3.6001 29.7131 4.43474 30.5468 5.46038 30.5468H12.2793C13.3045 30.5468 14.1392 29.7131 14.1392 28.687V21.8675C14.1391 20.8435 13.3044 20.0078 12.2793 20.0078ZM12.3654 21.8675V28.687C12.3654 28.7352 12.3264 28.774 12.2793 28.774H5.46038C5.41328 28.774 5.37231 28.7352 5.37231 28.687V21.8675C5.37231 21.8194 5.41224 21.7805 5.46038 21.7805H12.2793C12.3275 21.7805 12.3654 21.8194 12.3654 21.8675Z"
                fill="${color}" fill-opacity="0.85" />
            <path id="Vector_4"
                d="M18.2771 7.87293C18.3027 7.88985 18.3334 7.90112 18.3662 7.90112H18.4287C18.444 7.90317 18.4603 7.90317 18.4768 7.90112H31.4163C31.4245 7.90216 31.4336 7.90317 31.4408 7.90317C31.449 7.90317 31.4572 7.90216 31.4644 7.90112H31.5156C31.5433 7.90112 31.5689 7.8919 31.5903 7.87909C32.0134 7.80585 32.3257 7.44488 32.3257 7.01834C32.3257 6.59793 32.0235 6.24203 31.6098 6.16167C31.5832 6.14172 31.5505 6.12891 31.5156 6.12891H18.3661C18.3344 6.12891 18.3037 6.14378 18.2781 6.16477C17.8695 6.24772 17.5674 6.60311 17.5674 7.01838C17.5674 7.43465 17.8695 7.79001 18.2771 7.87293Z"
                fill="${color}" fill-opacity="0.85" />
            <path id="Vector_5"
                d="M18.2637 14.4378C18.2903 14.4619 18.3262 14.4777 18.3651 14.4777H31.5145C31.5463 14.4777 31.575 14.4639 31.6006 14.4449C32.0164 14.3677 32.3236 14.0097 32.3236 13.5862C32.3236 13.1597 32.0123 12.7997 31.5924 12.7266C31.5689 12.7127 31.5422 12.7056 31.5145 12.7056L31.49 12.7096C31.4695 12.7045 31.4387 12.7025 31.4091 12.7056L18.5014 12.7096C18.4798 12.7045 18.4501 12.7025 18.4204 12.7056H18.3651C18.3365 12.7056 18.3109 12.7138 18.2872 12.7307C17.8726 12.8095 17.5674 13.1669 17.5674 13.5863C17.5674 13.9974 17.8603 14.3497 18.2637 14.4378Z"
                fill="${color}" fill-opacity="0.85" />
            <path id="Vector_6"
                d="M18.2648 23.2051C18.2914 23.2285 18.3272 23.2429 18.3651 23.2429H31.5145C31.5289 23.2429 31.5422 23.2408 31.5566 23.2367C32.0235 23.2153 32.3994 22.8272 32.3994 22.353C32.3994 21.8665 32.0021 21.4692 31.5145 21.4692C31.5074 21.4692 31.4992 21.4702 31.49 21.4713H18.4747C18.4593 21.4692 18.444 21.4692 18.4266 21.4713H18.3651C18.3355 21.4713 18.3088 21.4804 18.2852 21.4968C17.8726 21.5767 17.5674 21.9341 17.5674 22.353C17.5674 22.7648 17.8623 23.117 18.2648 23.2051Z"
                fill="${color}" fill-opacity="0.85" />
            <path id="Vector_7"
                d="M31.5719 27.3262C31.5546 27.319 31.534 27.3149 31.5145 27.3149H18.3651C18.3313 27.3149 18.2985 27.3312 18.274 27.3548C17.8664 27.4398 17.5674 27.7941 17.5674 28.2089C17.5674 28.6277 17.8726 28.9852 18.2852 29.0651C18.3088 29.0793 18.3355 29.0865 18.3651 29.0865L18.3908 29.0825C18.401 29.0845 18.4163 29.0896 18.483 29.0865L31.4531 29.0825C31.4695 29.0865 31.5003 29.0927 31.5145 29.0927C32.0021 29.0927 32.3994 28.6954 32.3994 28.2089C32.3994 27.7399 32.0338 27.3568 31.5719 27.3262Z"
                fill="${color}" fill-opacity="0.85" />
        </g>
    </g>
</svg>
    `
}
