<script lang="ts" setup>
import { reactive, ref } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

interface FormState {
	birthday: [string, string]
	ethic: [string, string]
	gender: [string, string]
	political: [string, string]
	join_time: [string, string]
	cadre_category: [string, string]
	identity: [string, string]
	full_time_education: [string, string]
	on_job_education: [string, string]
	full_time_school: [string, string]
	current_rank: [string, string]
	major: [string, string]
	profession_specialty: string
	technical_position: string
	current_job: string
	current_job_time_gte: string
	current_job_time_lte: string
	current_rank_time_gte: string
	current_rank_time_lte: string
}
const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 18 },
}
const formRef = ref<FormInstance>()
const formState = reactive({} as FormState)

const resetForm = () => {
	formRef?.value?.resetFields()
	/* setTimeout(() => {
		onFinish({})
	}, 100) */
}

const onFinish = ({ page = 1 }: any) => {
	const params: any = { ...formState, page }
	if (formState.birthday) {
		params.birthday_start = formState.birthday[0]
		params.birthday_end = formState.birthday[1]
		delete params.birthday
	}
	if (formState.join_time) {
		params.join_time_start = formState.join_time[0]
		params.join_time_end = formState.join_time[1]
		delete params.join_time
	}
	if (formState.join_time) {
		params.join_time_start = formState.join_time[0]
		params.join_time_end = formState.join_time[1]
		delete params.join_time
	}
	const _params: any = {}
	Object.entries(params).forEach(([key, value]) => {
		if (Array.isArray(value)) {
			if (value[0] !== undefined) {
				_params[key] = value
			}
			return false
		}
		_params[key] = value
	})

	sessionStorage.setItem('search_params', JSON.stringify(_params))

	router.push({
		name: 'cadre-system-search-result',
	})
}
</script>

<template>
	<div class="search-wrap">
		<div class="form-wrap">
			<a-form ref="formRef" v-bind="layout" name="advanced_search" class="search-form" :model="formState" @finish="onFinish">
				<a-card :bordered="false">
					<div class="search-title">基本信息</div>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="性别" name="gender">
								<a-checkbox-group v-model:value="formState.gender">
									<a-checkbox value="1">男性</a-checkbox>
									<a-checkbox value="2">女性</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="出生年月" name="birthday">
								<a-range-picker picker="month" v-model:value="formState['birthday']" value-format="YYYY-MM-DD" />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="民族" name="ethic">
								<a-checkbox-group v-model:value="formState.ethic">
									<a-checkbox value="1">汉族</a-checkbox>
									<a-checkbox value="2">少数民族</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="政治面貌" name="political">
								<a-checkbox-group v-model:value="formState.political">
									<a-checkbox value="1">中共党员</a-checkbox>
									<a-checkbox value="2">非党员</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="入党时间" name="join_time">
								<a-range-picker picker="month" v-model:value="formState.join_time" value-format="YYYY-MM-DD" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="专业技术职务" name="technical_position">
								<a-input v-model:value="formState.technical_position" placeholder="请输入" />
							</a-form-item>
						</a-col>
					</a-row>
				</a-card>
				<a-card :bordered="false">
					<div class="search-title">职务信息</div>
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="干部类别" name="cadre_category" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
								<a-checkbox-group v-model:value="formState.cadre_category" class="cadre_category">
									<a-checkbox value="1">市管领导</a-checkbox>
									<a-checkbox value="2">区管正职</a-checkbox>
									<a-checkbox value="3">区管副职</a-checkbox>
									<a-checkbox value="200108">乡镇正职</a-checkbox>
									<a-checkbox value="200101">乡镇副职</a-checkbox>
									<a-checkbox value="200102">部门正职</a-checkbox>
									<a-checkbox value="200103">部门副职</a-checkbox>
									<a-checkbox value="200104">企业正职</a-checkbox>
									<a-checkbox value="200105">企业副职</a-checkbox>
									<a-checkbox value="200106">街道正职</a-checkbox>
									<a-checkbox value="200107">街道副职</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="现任职务" name="current_job">
								<a-input v-model:value="formState.current_job" placeholder="请输入" />
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="任现职务时间" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
								<a-row :gutter="24" class="item-h">
									<span>大于&nbsp;</span>
									<a-form-item name="current_job_time_gte">
										<a-input-number v-model:value="formState.current_job_time_gte" />
									</a-form-item>
									<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
									<a-form-item name="current_job_time_lte">
										<a-input-number v-model:value="formState.current_job_time_lte" />
									</a-form-item>
									<span>&nbsp;年</span>
								</a-row>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="干部职级" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }" name="current_rank">
								<a-checkbox-group v-model:value="formState.current_rank">
									<a-checkbox value="1">正处</a-checkbox>
									<a-checkbox value="200301">副处</a-checkbox>
									<a-checkbox value="200302">保留副处</a-checkbox>
									<a-checkbox value="20030">正科</a-checkbox>
									<a-checkbox value="5">保留正科</a-checkbox>
									<a-checkbox value="6">副科</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="现职级任职时间" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
								<a-row :gutter="24" class="item-h">
									<span>大于&nbsp;</span>
									<a-form-item name="current_rank_time_gte">
										<a-input-number v-model:value="formState.current_rank_time_gte" />
									</a-form-item>
									<span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
									<a-form-item name="current_rank_time_lte">
										<a-input-number v-model:value="formState.current_rank_time_lte" />
									</a-form-item>
									<span>&nbsp;年</span>
								</a-row>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="干部身份" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }" name="identity">
								<a-checkbox-group v-model:value="formState.identity">
									<a-checkbox value="200201">行政</a-checkbox>
									<a-checkbox value="200202">事业</a-checkbox>
									<a-checkbox value="200203">参公</a-checkbox>
									<a-checkbox value="200204">国企</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
				</a-card>
				<a-card :bordered="false">
					<div class="search-title">教育信息</div>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="初始学历" name="full_time_education">
								<a-checkbox-group v-model:value="formState.full_time_education">
									<a-checkbox value="1">研究生</a-checkbox>
									<a-checkbox value="2">大学本科</a-checkbox>
									<a-checkbox value="3">大学专科</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="最高学历" name="on_job_education">
								<a-checkbox-group v-model:value="formState.on_job_education">
									<a-checkbox value="1">研究生</a-checkbox>
									<a-checkbox value="2">大学本科</a-checkbox>
									<a-checkbox value="4">大学专科</a-checkbox>
								</a-checkbox-group>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row :gutter="24">
						<a-col :span="12">
							<a-form-item label="全日制院校" name="full_time_school">
								<a-input v-model:value="formState.full_time_school" placeholder="请输入" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="专业" name="major">
								<a-input v-model:value="formState.major" placeholder="请输入" />
							</a-form-item>
						</a-col>
					</a-row>
				</a-card>
				<a-card :bordered="false">
					<a-row>
						<a-col :span="24" style="text-align: center" class="btn-wrap">
							<a-button style="margin-right: 60px" @click="resetForm">重置</a-button>
							<a-button type="primary" html-type="submit">查询</a-button>
						</a-col>
					</a-row>
				</a-card>
			</a-form>
		</div>
	</div>
</template>

<style lang="less" scoped>
.search-wrap {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 100%;
	overflow-y: auto;
	// background: #f6f9fc url(./back.png) no-repeat top center/contain;
	.form-wrap {
		position: relative;
		width: 90%;
	}
	.search-form {
		user-select: none;
		::v-deep(.ant-card-body) {
			padding: 24px 24px 5px !important;
		}
		width: 100%;
		.ant-card {
			// margin-bottom: 16px;
			.ant-row {
				margin-bottom: 10px;
			}
			.item-h {
				align-items: center;
				margin: 0 !important;
				.ant-form-item {
					margin-bottom: 0;
				}
			}
		}
		.ant-checkbox-group {
			.ant-checkbox-wrapper {
				margin-left: 0;
				margin-right: 10px;
			}
		}
		// .cadre_category {
		// 	::v-deep(.ant-checkbox-wrapper) {
		// 		margin-bottom: 10px;
		// 	}
		// }
	}
	.search-title {
		font-size: 20px;
		font-weight: bold;
		display: flex;
		align-items: center;
		&::before {
			margin-right: 8px;
			content: '';
			display: inline-block;
			width: 8px;
			height: 24px;
			background: url(@/assets/images/left-header-icon.png) center / cover no-repeat;
		}
	}
	.search-result {
		width: 100%;
		height: 100%;
	}

	.ant-btn {
		height: 38px;
		padding: 0 30px;
		font-size: 18px;
	}
	.btn-wrap {
		margin-bottom: 10px;
	}
}
.search-modal {
	margin-top: 24px;
	padding: 24px;
	width: 100%;
	background: #ffffff;
	.veidoo {
		margin-top: 10px;
		width: 100%;
		.top-content {
			display: flex;
			margin-top: 49px;
			margin-bottom: 31px;

			.label {
				display: flex;
				align-items: center;
				font-size: 18px;
				font-weight: bold;
				color: #00fff6;
				line-height: 18px;
				text-shadow: 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2),
					0 0 4px rgba(35, 219, 252, 0.2);
				background: linear-gradient(0deg, #3bdeff 6.1279296875%, #d1fbff 55.4443359375%, #ddf9ff 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;

				&::after {
					margin-left: 7px;
					content: '';
					display: inline-block;
					width: 24px;
					height: 16px;
					background: url('@/assets/images/label-icon.png') no-repeat center / cover;
				}
			}
		}
		.top-content-two {
			margin-bottom: 22px;
		}
		.veidoo-item {
			display: flex;
			border: 1px solid #ebebeb;
			.title {
				display: flex;
				align-items: center;
				justify-content: center;

				width: 100px;
				height: 192px;
				background: #f3f3f3;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;

				font-size: 16px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #222222;
				line-height: 24px;
			}

			.data {
				flex: 1;
				padding: 28px 84px;

				display: flex;
				flex-wrap: wrap;
				.check-item {
					width: 25%;
				}
			}

			.two-title {
				display: flex;
				width: 169px;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 68px 0;
			}

			div {
				font-size: 14px;
				font-family: Source Han Sans CN;
				font-weight: 500;
				color: #00d2ff;
			}
		}
	}
}
</style>
