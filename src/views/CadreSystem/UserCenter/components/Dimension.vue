<template>
	<div class="dimension">
		<div class="dimension-header">
			<div class="left">共有{{ total }}条标注信息</div>
			<div class="right-box">
				<Search @search="onSearch" @clear="onClear" placeholder="请输入干部姓名或标注内容" />
			</div>
		</div>
		<div class="dimension-list" ref="scrollContainer">
			<div class="inner-box">
				<div class="dimension-item" v-for="(item, index) in list" :key="index">
					<div class="left">
						<div class="d-item">
							<div class="base">
								<span class="icon user"></span>
								<span class="name"
									><span type="limit">{{ item.sourceName }}</span
									>：</span
								>
							</div>
							<span class="detail">{{ item.currentJob }}</span>
						</div>
						<div class="d-item">
							<div class="base">
								<span class="icon list"></span>
								<span class="name"><span type="limit">标注内容</span>：</span>
							</div>
							<span class="detail">{{ item.data }}</span>
						</div>
						<div class="d-item">
							<div class="base">
								<div class="icon time"></div>
								<span class="name"><span type="limit">标注时间</span>：</span>
							</div>
							<span class="detail">{{ item.updateTime || item.createTime }}</span>
						</div>
					</div>
					<div class="right">
						<a-button type="primary" @click="onCadre(item)">干部画像</a-button>
						<a-popconfirm title="确认删除?" ok-text="确认" cancel-text="取消" @confirm="onDelete(item)">
							<a-button type="primary">删除标注</a-button>
						</a-popconfirm>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onActivated, watchEffect, onUpdated } from 'vue'

import Search from './Search.vue'
import BScroll from '@better-scroll/core'
import Pullup from '@better-scroll/pull-up'
import MouseWheel from '@better-scroll/mouse-wheel'

import { getUserInfoItem, throttle } from '@/utils/utils'
import { getCalloutList, getUsersByIds, deleteCallout } from '@/apis/user-center'
import { historyPush } from '@/utils/history'

BScroll.use(Pullup)
BScroll.use(MouseWheel)

const props = defineProps({
	activeKey: {
		type: String,
		required: true,
	},
})

const user_id = getUserInfoItem('_uid')

const list = ref<any>([])
// 分页
const page = ref(1)
// 总数据
const total = ref(0)
// 搜索框内容
const content = ref('')
// 滚动容器
const scrollContainer = ref()
// 是否下拉
const loadEnd = ref(false)
// 清楚搜索框内容
const onClear = () => {
	content.value = ''
}
// 干部画像
const onCadre = (record: any) => {
	historyPush(`/cadre-portrait/home?user_id=${record.sourceId}`)
}
// 删除标注
const onDelete = async (record: any) => {
	await deleteCallout(record.calloutId)

	loadDataByPage(page.value)
}

const onSearch = (value: string) => {
	content.value = value

	initData(2, page.value)
}
/**
 * @description:
 * @param {*} type 1 初始化 2 搜索
 * @param {*} page
 * @return {*}
 */
const initData = async (type: 1 | 2, pageNum: number) => {
	// 搜索需要去除所有状态
	if (type === 2) {
		page.value = 1
		pageNum = 1
		loadEnd.value = false
	}

	const res: any = await getCalloutList(user_id, pageNum, content.value)

	if (res.data.length === 0) {
		// 没有数据了
		loadEnd.value = true
	}

	await loadPosition(res.data)

	if (type === 2) {
		list.value = res.data
	} else {
		list.value.push(...res.data)
	}
	total.value = res.total
}

const loadPosition = async (list: Array<any>) => {
	const user_ids = list.map((item) => item.sourceId).join(',')

	if (!user_ids) return

	const res = await getUsersByIds({
		user_ids,
	})
	if (res.code === 0) {
		const data = res.data
		list.forEach((item) => {
			const user = data.find((user: any) => user.user_id === item.sourceId)
			if (user) {
				item.currentJob = user.currentJob
			}
		})
	}
}
// 根据分页循环加载所有数据

const loadDataByPage = async (page: number) => {
	const data = []
	for (let i = 1; i <= page; i++) {
		const res: any = await getCalloutList(user_id, i, content.value)
		if (res.data.length !== 0) {
			data.push(...res.data)
			i === 1 && (total.value = res.total)
		}
	}
	await loadPosition(data)

	list.value = data
}

let bscroll: any = null

onMounted(() => {
	bscroll = new BScroll(scrollContainer.value, {
		probeType: 3,
		pullUpLoad: true,
		mouseWheel: true,
		click: true,
	})

	bscroll.on('pullingUp', async () => {
		if (loadEnd.value) {
			return
		}

		const _page = page.value + 1

		await initData(1, _page)

		page.value = _page

		bscroll.finishPullUp()

		bscroll.refresh()
	})
})
onUpdated(() => {
	bscroll?.refresh()
})

watchEffect(() => {
	props.activeKey

	bscroll?.refresh()
})

onActivated(() => {
	loadEnd.value = false

	loadDataByPage(page.value)

	bscroll?.refresh()
})
loadDataByPage(page.value)
// initData(1, page.value)
</script>

<style lang="scss" scoped>
.dimension {
	height: 100%;
	&::-webkit-scrollbar {
		display: none;
	}
	.dimension-header {
		display: flex;
		justify-content: space-between;
		.left {
			font-size: 22px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 142, 255, 0.9);
			line-height: 26px;
		}
		.right-box {
			width: 560px;
		}
	}
	.dimension-list {
		margin-top: 20px;
		height: 100%;
		overflow: hidden;
		.inner-box {
			padding-bottom: 40px;
		}
		.dimension-item {
			margin-top: 16px;
			display: flex;
			justify-content: space-between;
			flex-wrap: nowrap;
			width: 100%;
			padding: 36px 20px;
			background: #f4f9fc;
			.left {
				.d-item {
					display: flex;
					align-items: flex-start;
					margin-top: 10px;
					.base {
						display: flex;
						flex-shrink: 0;
						align-items: center;
					}
					&:first-child {
						margin-top: 0px;
					}
					.icon {
						margin-right: 11px;
						display: inline-block;
						width: 26px;
						height: 26px;
						background: no-repeat center / contain;
					}
					.user {
						background-image: url('../../image/user-icon.png');
					}
					.list {
						background-image: url('../../image/list.png');
					}
					.time {
						background-image: url('../../image/time.png');
					}
					.name {
						font-size: 22px;
						line-height: 26px;
						font-family: Source Han Sans CN-Medium, Source Han Sans CN;
						font-weight: 500;
						color: rgba(0, 0, 0, 0.9);
						white-space: nowrap;
						span[type='limit'] {
							display: inline-block;
							width: 98px;
							text-align-last: justify;
						}
					}
					.detail {
						font-size: 22px;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						line-height: 26px;
					}
				}
			}
			.right {
				width: 250px;
				display: flex;
				align-items: center;
				button {
					padding: 0px;
					width: 120px;
					height: 46px;
					&:first-child {
						margin-right: 10px;
					}
				}
			}
		}
	}
}
</style>
