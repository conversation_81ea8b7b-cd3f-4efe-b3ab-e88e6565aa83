<template>
	<div class="user-center-password">
		<div class="form-box">
			<div class="title">{{ status === 'login' ? '提示' : '修改密码' }}</div>
			<div class="inner-wrapper" v-if="status === 'login'">
				<div class="label">
					<label>请输入密码</label>
				</div>
				<div class="input-box">
					<a-input-password
						v-model:value="password"
						readonly
						@focus="
							(e) => {
								e.target.removeAttribute('readonly')
							}
						"
						autocomplete="new-password"
						placeholder="请输入"
					/>
				</div>
				<div class="submit-btn">
					<a-button type="primary" @click="submitPassword" :loading="loading">确定</a-button>
				</div>
				<span class="modify" @click="onModify">修改密码</span>
			</div>
			<div class="inner-wrapper" v-if="status === 'forget'">
				<a-form ref="formRef" layout="vertical" :model="forgetPassword" :rules="rules">
					<div class="label">
						<label>请输入旧密码</label>
					</div>
					<div class="input-box">
						<a-form-item name="password_old" :rules="rules.password_old">
							<a-input-password
								v-model:value="forgetPassword.password_old"
								readonly
								@focus="
									(e) => {
										e.target.removeAttribute('readonly')
									}
								"
								autocomplete="new-password"
								placeholder="请输入"
							/>
						</a-form-item>
					</div>
					<div class="label">
						<label>请输入新密码</label>
					</div>
					<div class="input-box">
						<a-form-item name="password" :rules="rules.password">
							<a-input-password
								v-model:value="forgetPassword.password"
								readonly
								@focus="
									(e) => {
										e.target.removeAttribute('readonly')
									}
								"
								autocomplete="new-password"
								placeholder="请输入"
							/>
						</a-form-item>
					</div>
					<div class="label">
						<label>请再次输入新密码</label>
					</div>
					<div class="input-box">
						<a-form-item name="confirm_password" :rules="rules.confirm_password">
							<a-input-password
								v-model:value="forgetPassword.confirm_password"
								readonly
								@focus="
									(e) => {
										e.target.removeAttribute('readonly')
									}
								"
								autocomplete="new-password"
								placeholder="请输入"
							/>
						</a-form-item>
					</div>
					<div class="submit-btn">
						<a-button type="primary" @click="onModifyPassword" :loading="loading">确定</a-button>
					</div>
				</a-form>
				<span class="modify" @click="onBackInput">返回输入密码</span>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { loginPersonal, updatePersonalPassword } from '@/apis/user-center'
import { getUserInfoItem } from '@/utils/utils'
import md5 from 'js-md5'

const loading = ref(false)

const emits = defineEmits(['login'])

const user_id = getUserInfoItem('_uid')

const password = ref('')

const forgetPassword = reactive({
	confirm_password: '',
	password_old: '',
	password: '',
})

const validateByRegex = (password) => {
	// 定义正则表达式
	const passwordRegex =
		/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,30}$/

	return passwordRegex.test(password)
}

const validatePassword = async (_rule, value: string, text: string) => {
	if (value === '') {
		return Promise.reject(text || '请输入')
	} else {
		const res = validateByRegex(value)

		if (res) {
			return Promise.resolve()
		} else {
			return Promise.reject('密码应包含大小写字母、数字和特殊字符中的至少三种，且长度为8-30个字符。')
		}
	}
}

const rules = {
	password_old: [{ required: true, message: '请输入旧密码!' }],
	password: [
		{
			required: true,
			trigger: 'change',
			validator: (_rule, value) => {
				return validatePassword(_rule, value, '请输入新密码!')
			},
		},
	],
	confirm_password: [
		{
			required: true,
			trigger: 'change',
			validator: (_rule, value) => {
				if (!value.trim()) {
					return Promise.reject('再次确认新密码!')
				} else {
					return value !== forgetPassword.password ? Promise.reject('两次密码不一致!') : Promise.resolve()
				}
			},
		},
	],
}

const status = ref('login')

const onModify = () => {
	status.value = 'forget'
}

const onBackInput = () => {
	status.value = 'login'
	forgetPassword.confirm_password = ''
	forgetPassword.password_old = ''
	forgetPassword.password = ''
	loading.value = false
}
const submitPassword = async () => {
	if (password.value.trim()) {
		try {
			loading.value = true

			const res = await loginPersonal({
				sys_user_id: user_id,
				password: md5(password.value),
			})

			if (res.code === 0) {
				const { personal_token } = res.data

				sessionStorage.setItem('personal_token', personal_token)

				// 登录成功
				emits('login')
			} else {
				message.error(res.message)
			}
		} catch (error) {
		} finally {
			loading.value = false
		}
	}
}
const formRef = ref()

const onModifyPassword = async () => {
	try {
		const { password, password_old, confirm_password } = forgetPassword
		formRef.value.validate().then(async () => {
			loading.value = true

			const res = await updatePersonalPassword({
				sys_user_id: user_id,
				password_old: md5(password_old),
				password: md5(password),
			})

			if (res.code === 0) {
				message.success('修改密码成功')

				status.value = 'login'
			} else {
				message.error(res.message)
			}
		})
	} catch (err) {
	} finally {
		loading.value = false
	}
}
</script>

<style lang="less" scoped>
.user-center-password {
	position: absolute;
	inset: 0;
	background: rgb(249, 251, 255);
	overflow: auto;
	.form-box {
		padding: 80px 100px;
		width: 800px;
		min-height: 550px;
		margin: 8vh auto 0px;
		background: #ffffff;
		box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
		border-radius: 10px;
		.title {
			font-size: 30px;
			display: flex;
			align-items: center;
			color: #000000;
			font-weight: 600;
			&::before {
				margin-right: 10px;
				content: '';
				display: inline-block;
				height: 30px;
				width: 5px;
				background: rgb(47, 147, 258);
			}
		}
		.inner-wrapper {
			padding-left: 10px;
			margin-top: 20px;
			.label {
				font-size: 20px;
				font-weight: bold;
			}
			.modify {
				display: inline-block;
				margin-top: 20px;
				font-size: 16px;
				color: #1890ff;
			}
			.input-box {
				margin-top: 20px;
				.ant-input-password {
					height: 50px;
					border-radius: 15px;
					background-color: rgb(245, 246, 250);
					border: none;

					:deep(input) {
						background-color: transparent !important;
						color: #000000;
						font-weight: 500;
						font-size: 18px;
					}
				}
			}
			.submit-btn {
				margin-top: 50px;
				width: 100%;
				button {
					width: 100%;
					height: 50px;
					font-size: 20px;
					border-radius: 10px;
				}
			}
		}
	}
}
</style>
