<template>
	<div class="user-center">
		<Password @login="onLoginSuccess" v-if="!passwordAuth" />

		<a-tabs v-model:activeKey="activeKey" v-else>
			<a-tab-pane key="2">
				<template #tab>
					<span class="tab-item">我的收藏</span>
				</template>
				<Collection />
			</a-tab-pane>
			<a-tab-pane key="1">
				<template #tab>
					<span class="tab-item">我的标注</span>
				</template>
				<Dimension :activeKey="activeKey" />
			</a-tab-pane>
		</a-tabs>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

import Collection from './components/Collection.vue'
import Dimension from './components/Dimension.vue'
import Password from './components/Password.vue'

const activeKey = ref('2')
const storedValue = JSON.parse(sessionStorage.getItem('passwordAuth') || 'false')
const passwordAuth = ref(storedValue ? JSON.parse(storedValue) : false)

const onLoginSuccess = () => {
	passwordAuth.value = true
}
</script>

<style lang="scss" scoped>
.user-center {
	position: relative;
	width: calc(100% - 40px);
	height: 100%;
	margin: 0 auto;
	padding: 20px;
	background-color: #ffffff;
	&::webkit-scroll {
		display: none;
	}
	.tab-item {
		display: inline-block;
		width: 200px;
		text-align: center;
		font-size: 24px;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
	}
	::v-deep(.ant-tabs) {
		height: 100%;
		.ant-tabs-content {
			height: 100%;
		}
	}
}
</style>
