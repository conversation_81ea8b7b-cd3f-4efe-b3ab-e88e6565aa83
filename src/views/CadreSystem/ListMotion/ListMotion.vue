<template>
	<div class="list-motion">
		<div style="width: 100%" class="inner-box">
			<div class="user-box" v-if="listMotion.length">
				<template v-for="(user, index) in listMotion" :key="user.user_id">
					<UserCard :user="user" @card-click="userCardClick" style="width: 14%" />
				</template>
			</div>
			<a-empty :image="simpleImage" style="margin-top: 90px" v-else />
			<!-- <div class="user-box">
						<template v-for="(user, index) in userlist.leader_info" :key="user.user_id">
							<UserCard :user="user" v-if="user.rank === 2" @card-click="userCardClick" :style="{ width: sliderStatus ? '17%' : '14.15%' }" />
						</template>
					</div> -->
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import UserCard from '../components/user-card.vue'
import { getMotionList } from '@/apis/cadreSystem'
import { message } from 'ant-design-vue'

import { Empty } from 'ant-design-vue'
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE

const router = useRouter()

const listMotion = ref([])

const initData = async () => {
	const res = await getMotionList()

	if (res.code === 0) {
		listMotion.value = res.data
	} else {
		message.error(res.msg)
	}
}
const userCardClick = (item: any) => {
	router.push(`/cadre-portrait/home?user_id=${item.user_id}`)
}

initData()
</script>

<style lang="scss" scoped>
.list-motion {
	padding: 20px;
	width: 100%;
	height: 100%;
	background-color: #fff;
	overflow-y: auto;
	&::-webkit-scrollbar {
		display: none;
	}
	.user-box {
		display: flex;
		flex-wrap: wrap;
	}
}
</style>
