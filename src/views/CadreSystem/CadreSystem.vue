<template>
	<div class="cadre-system">
		<p-header :title="$route.meta.title || ($route.meta.hiddenMenu ? '动议名单' : '区管干部')" />
		<div class="menu" v-if="!$route.meta.hiddenMenu">
			<div
				:class="`menu-item ${activeRef === item.key ? 'active-menu-item' : ''}`"
				v-for="(item, index) in menu"
				:key="index"
				@click="handeClickMenu(item)"
			>
				<div class="menu-item__inner">
					<img class="icon" :src="activeRef === item.key ? item.activeIcon : item.icon" />
					<span class="text">{{ item.label }}</span>
				</div>
			</div>
		</div>
		<div class="content">
			<router-view v-slot="{ Component }">
				<!-- <transition :name="transitionName" mode="in-out"> -->
				<keep-alive :exclude="['UserCenter']">
					<component :is="Component" />
				</keep-alive>
				<!-- </transition> -->
			</router-view>
			<!-- <router-view /> -->
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref, watch, onActivated, onDeactivated } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import userPng from './image/user.png'
import userActivePng from './image/user-active.png'
import chaxunPng from './image/chaxun.png'
import chaxunActivePng from './image/chaxun-active.png'
import tongjiPng from './image/tongji.png'
import tongjiActivePng from './image/tongji-active.png'
import usericon from './image/person-icon.png'
import usericonActive from './image/person-icon-active.png'
import useKeepAlive from '@/store/keepalive'
import { useComparative } from '@/store/comparative'

const activeRef = ref(0)

const route = useRoute()

const router = useRouter()
//
const comparative = useComparative()
comparative.initConfigIds()

const keepalive = useKeepAlive()
// 过渡名称
const transitionName = ref()
// 清空页面路由缓存

keepalive.push('CadreSystem')

const menu = [
	{
		label: '干部信息',
		path: '/cadre-system/information',
		icon: userPng,
		activeIcon: userActivePng,
		key: 0,
	},
	{
		label: '干部查询',
		path: '/cadre-system/search',
		icon: chaxunPng,
		activeIcon: chaxunActivePng,
		key: 1,
	},
	{
		label: '统计分析',
		path: '/cadre-system/statistics',
		icon: tongjiPng,
		activeIcon: tongjiActivePng,
		key: 2,
	},
	{
		label: '个人中心',
		path: '/cadre-system/user-center',
		icon: usericon,
		activeIcon: usericonActive,
		key: 3,
	},
]

const handeClickMenu = (item: any) => {
	activeRef.value = item.key
	sessionStorage.setItem('passwordAuth', 'false') //取消存储密码
	sessionStorage.setItem('detailVisible', 'false') // 存储详情页
	sessionStorage.removeItem('current_collection') //切换tab取消这个存储
	router.replace({
		path: item.path,
		query: {
			_h: route.query._h,
		},
	})
}
const confirm = () => {
	sessionStorage.setItem('userInfo', '')

	message.info('退出成功')

	router.replace('/login')
}

const cancel = (e: MouseEvent) => {
	console.log(e)
}
const _menu = menu.concat([
	{
		label: '搜索结果',
		path: '/cadre-system/search-result',
		key: 1,
	},
] as any)

watch(
	router.currentRoute,
	(newV, oldV) => {
		const { path } = newV
		const menuItem = _menu.find((item: any) => {
			return item.path === path
		}) as { key: number }
		// 老路由
		const oldMenu = _menu.find((item: any) => {
			return item.path === oldV?.path
		}) as { key: number }

		activeRef.value = menuItem?.key

		if (oldMenu?.key !== undefined) {
			transitionName.value = menuItem?.key < oldMenu?.key ? 'a_right' : 'a_left'
		} else {
			transitionName.value = undefined
		}
	},
	{
		immediate: true,
	}
)

onDeactivated(() => {
	transitionName.value = undefined
})

onActivated(() => {
	keepalive.remove('CadrePortrait')

	comparative.updateComparative([])
})
</script>

<style scoped lang="less">
.cadre-system {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
	.header {
		display: flex;
		align-items: center;
		padding: 0 32px;
		width: 100%;
		height: 68px;
		background: url(./image/header.png) center / cover no-repeat;

		.title {
			flex: 1;
			text-align: center;
			font-size: 27px;
			line-height: 32px;
			font-weight: 600;
			color: #ffffff;
		}
		.common {
			display: flex;
			align-items: center;
			cursor: pointer;
			.icon {
				margin-right: 10px;
				display: inline-block;
				width: 24px;
				height: 24px;
			}
			.text {
				font-size: 22px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
			}
		}
		.back {
			.icon {
				background: url(./image/back.png) center / cover no-repeat;
			}
		}
		.login-out {
			justify-content: flex-end;
			.icon {
				width: 20px;
				height: 20px;
				background: url(./image/login-out.png) center / contain no-repeat;
			}
		}
	}
	.menu {
		width: 100%;
		display: flex;
		height: 72px;
		background: #ffffff;
		.menu-item {
			display: flex;
			align-items: center;
			justify-content: center;
			border-bottom: 4px solid transparent;
			flex: 1;
			height: 72px;
			cursor: pointer;
			&:nth-child(2) > div {
				border-left: 1px solid #e8e8e8;
				border-right: 1px solid #e8e8e8;
			}
			&__inner {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;

				.icon {
					margin-right: 7px;
					width: 36px;
					height: 36px;
				}
				.text {
					// font-size: 26px;
					font-size: 23px;
					font-family: PingFang SC-Medium, PingFang SC;
					color: #222222;
					line-height: 23px;
				}
			}
		}
		.active-menu-item {
			border-bottom-color: #008eff;

			background: linear-gradient(180deg, rgba(0, 142, 255, 0) 0%, rgba(0, 142, 255, 0.13) 100%);
			.text {
				font-weight: bold;
				color: #008eff;
			}
		}
	}
	.content {
		position: relative;
		margin: 20px 0 0px 0;
		flex: 1;
		width: 100%;
		overflow: hidden;
	}
	.a_right-enter-active,
	.a_right-leave-active,
	.a_left-enter-active,
	.a_left-leave-active {
		transition: all 0.25s cubic-bezier(0.93, -0.01, 0.74, 0.79);
	}
	.a_right-enter,
	.a_right-leave-to {
		transform: translateX(100%);
	}
	.a_left-enter,
	.a_left-leave-to {
		transform: translateX(-100%);
	}
}
</style>
