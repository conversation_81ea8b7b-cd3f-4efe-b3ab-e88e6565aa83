export type UserInfo = {
	user_id: number
	name: string
	birthday_age: string
	education: string
	current_position: string
	position_time: string
	leader_index: number
	leader_index_rank: string
	risk_index: number
	risk_index_avg: number
	feature: string[]
	goods_range: string
	// like: string[]
	like: string
	sketch: string
	areas_responsibility: Array<any>
	feature_list: any[]
	sketch_list: any[]
	avatar?: string
	promotion_type: number
	user_type: number | undefined
}

export type RadarDataType = {
	user_index: Array<{ label: string; value: number }>
	avg_index: Array<{ label: string; value: number }>
}

export type CoordinateType = {
	user_id: number
	name: string
	x: number
	y: number
}

export type CoordinateDataType = {
	others: Array<CoordinateType>
	mine: CoordinateType
	x_avg: number
	y_avg: number
	type?: number
}
// 个人评测分析
export type PersonalDataType = {
	xlabel: string[]
	upper: number[]
	same: number[]
	down: number[]
}
// 能力测评分析
export type AbilityDataType = {
	xlabel: string[]
	other_result: any[]
	my_result: AvgList
	avg_list: AvgList
}

export interface Pokedex {
	xlabel: string[]
	other_result: any[]
	my_result: AvgList
	avg_list: AvgList
}

export interface AvgList {
	user_id: number
	name: string
	list: number[]
}
