import Mock from 'mockjs'
// 自动导入
const multifiles = import.meta.glob('./**/*.ts')
// import cadrePortrait from './cadrePortrait'
const loadMockFile = async () => {
	for (const path in multifiles) {
		const services: any = await multifiles[path]()
		for (const key in services) {
			Mock.mock(services[key].path, services[key].method, services[key].data)
		}
	}

	Mock.setup({
		timeout: 1000,
	})
}

// Mock.mock(cadrePortrait.path, cadrePortrait.method, cadrePortrait.data)
loadMockFile()
