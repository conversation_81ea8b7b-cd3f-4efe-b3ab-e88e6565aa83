const user_id = import.meta.env.VITE_MOCK_USER_ID

export default {
	path: '/owsz/user/leader-screen/baseinfo?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		timestamp: '2023-07-12T10:32:07.675+00:00',
		message: 'success',
		data: {
			user_id: 12345,
			name: '向成言',
			birthday_age: ' 1974.10 (48岁)',
			education: '大专',
			current_position: '虎威镇党委统战委员、副镇长一级主任科员',
			position_time: '2022.01',
			leader_index: 8.5,
			leader_index_rank: '1/7',
			risk_index: 6.2,
			risk_index_avg: 5.8,
			feature: ['担当型', '学习型', '务实肯干'],
			goods_range: ['农产品加工专业', '征地拆迁'],
			like: ['看书', '跑步'],
			sketch: `敢担当，能攻坚，分管场镇建设、征地拆迁成效好，农村工作经验丰
			富，干群口碑较好。不足：性格直，快人快语。`,
			areas_responsibility: `负责统战、民族宗教、侨务、对台、外事、工商联工作。分管提案办
			理、安全生产、应急管理、交通、市场监督管理、场镇管理（五城同
			创）、生态环境、规划和自然资源、住房和城乡建设、复垦、地质灾
			害防治、民防、民营经济等工作。分管应急办、规环办、执法办、综
			合行政执法大队。联系商会、市场监督管理所、国土所、污水处理厂、
			垃圾中转站、大池社区、回龙村。`,
			sketch_list: [
				{
					hobby: '一言素描',
					user_id: 12345,
					name: '张三',
				},
				{
					hobby: '一言素描',
					user_id: 12345,
					name: '张三',
				},
			],
			feature_list: [
				{
					feature: ['爱好', '爱好', '爱好', '爱好', '爱好', '爱好'],
					user_id: 12345,
					name: '张三',
				},
				{
					feature: ['爱好', '爱好', '爱好', '爱好'],
					user_id: 12345,
					name: '张三',
				},
			],
		},
		status: 200,
		time_string: '2023-07-12 10:32:07.007',
	},
}
const RadarChart = {
	path: '/owsz/user/leader-screen/assert?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		timestamp: '2023-07-12T10:33:56.067+00:00',
		message: 'success',
		data: {
			user_index: [
				{
					label: '政治三力',
					value: 90,
				},
				{
					label: '学习力',
					value: 90,
				},
				{
					label: '决策力',
					value: 90,
				},
				{
					label: '执行力',
					value: 90,
				},
				{
					label: '群众工作能力',
					value: 90,
				},
				{
					label: '担当',
					value: 90,
				},
				{
					label: '责任',
					value: 90,
				},
				{
					label: '正派',
					value: 90,
				},
				{
					label: '学习力',
					value: 90,
				},
			],
			avg_index: [
				{
					label: '政治三力',
					value: 88,
				},
				{
					label: '学习力',
					value: 88,
				},
				{
					label: '决策力',
					value: 88,
				},
				{
					label: '执行力',
					value: 88,
				},
				{
					label: '群众工作能力',
					value: 88,
				},
				{
					label: '担当',
					value: 88,
				},
				{
					label: '责任',
					value: 88,
				},
				{
					label: '正派',
					value: 88,
				},
				{
					label: '学习力',
					value: 88,
				},
			],
		},
		status: 200,
		time_string: '2023-07-12 10:33:56.056',
	},
}

const coordinate = {
	path: '/owsz/user/leader-screen/point?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		timestamp: '2023-07-12T10:35:02.067+00:00',
		message: 'success',
		data: {
			others: [
				{
					user_id: 748012828,
					name: '1121',
					x: 84,
					y: 28.6,
				},
				{
					user_id: 7480128281,
					name: '1131',
					x: 85,
					y: 28.8,
				},
				{
					user_id: 7480128282,
					name: '1141',
					x: 86,
					y: 28.9,
				},
			],
			mine: {
				user_id: 283157451515,
				name: '用户1',
				x: 89,
				y: 29,
			},
			x_avg: 88,
			y_avg: 29,
		},
	},
}
const personalAnalysis = {
	path: '/owsz/user/leader-screen/result?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		timestamp: '2023-07-12T10:35:02.067+00:00',
		message: 'success',
		data: {
			xlabel: ['政治三力', '学习力', '决策力', '执行力', '群众工作能力', '担当', '责任', '正派'],
			upper: [10, 20, 50, 50, 50, 30, 10, 20],
			same: [30, 30, 50, 50, 50, 30, 20, 20],
			down: [10, 10, 50, 50, 50, 30, 20, 10],
		},
	},
}
const abilityAnalysis = {
	path: '/owsz/user/leader-screen/all-result?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		data: {
			xlabel: ['政治三力', '学习力', '决策力', '执行力', '群众工作能力', '担当', '责任', '正派'],
			other_result: [
				{ user_id: 283157451515, name: '用户1', list: [50, 50, 50, 50, 50, 50, 50, 50] },
				{ user_id: 283157451515, name: '用户2', list: [50, 50, 50, 50, 50, 50, 50, 50] },
				{ user_id: 283157451515, name: '用户3', list: [50, 50, 50, 50, 50, 50, 50, 50] },
				{ user_id: 283157451515, name: '用户4', list: [50, 50, 50, 50, 50, 50, 50, 50] },
				{ user_id: 283157451515, name: '用户5', list: [50, 50, 50, 50, 50, 50, 50, 50] },
			],
			my_result: {
				user_id: 283157451515,
				name: '用户6',
				list: [50, 50, 50, 50, 50, 50, 50, 50],
			},
			avg_list: {
				user_id: -1000,
				name: '平均值',
				list: [50, 50, 50, 50, 50, 50, 50, 50],
			},
		},
	},
}

const achievement = {
	path: '/owsz/user/leader-screen/achievement?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		timestamp: '2023-07-12T10:36:53.459+00:00',
		message: 'success',
		data: {
			year: [2022, 2021, 2020],
			achievement: [0, 0, 0],
			avg: [0, 0, 0],
		},
		status: 200,
		time_string: '2023-07-12 10:36:53.053',
	},
}
const yearEval = {
	path: '/owsz/user/leader-screen/eval?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		timestamp: '2023-07-12T10:37:49.347+00:00',
		message: 'success',
		data: {
			year: [2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014, 2013],
			achievement: ['-', '-', '-', '-', '优秀', '优秀', '-', '-', '-', '-'],
			rank: ['-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
		},
		status: 200,
		time_string: '2023-07-12 10:37:49.049',
	},
}

const gloryData = {
	path: '/owsz/user/leader-screen/glory?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		timestamp: '2023-07-12T10:37:49.347+00:00',
		message: 'success',
		data: [
			{
				item: '2021年07月,因33，获得444',
				type: '国家部委',
				score: '+0.4',
			},
			{
				item: '2023年07月,因333，获得33333333',
				type: '国家部委',
				score: '+0.4',
			},
		],
		status: 200,
		time_string: '2023-07-12 10:37:49.049',
	},
}
const negativeData = {
	path: '/owsz/user/leader-screen/negative?user_id=' + user_id,
	method: 'get',
	data: {
		code: 0,
		timestamp: '2023-07-12T10:37:49.347+00:00',
		message: 'success',
		data: [
			{
				time: '2019-04-01',
				type: '班子考核年度',
				content: '111二分',
				user_id: 283157451515,
			},
		],
		status: 200,
		time_string: '2023-07-12 10:37:49.049',
	},
}
export { RadarChart, coordinate, personalAnalysis, abilityAnalysis, achievement, yearEval, gloryData, negativeData }
