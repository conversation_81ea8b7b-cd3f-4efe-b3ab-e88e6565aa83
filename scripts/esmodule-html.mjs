import { readFileSync, writeFileSync } from 'fs'
import { parse } from 'node-html-parser'

const htmlFilePath = './dist/index.html' // 替换为HTML文件路径

try {
	const data = readFileSync(htmlFilePath, 'utf8')

	// 使用node-html-parser解析HTML内容
	const root = parse(data)

	// 去掉带有module属性的script标签
	const moduleScripts = root.querySelectorAll('script[type="module"]')
	moduleScripts.forEach((script) => script.remove())

	// 去掉script标签中的nomodule和crossorigin属性
	const nomoduleScripts = root.querySelectorAll('script[nomodule]')
	nomoduleScripts.forEach((script) => {
		script.removeAttribute('nomodule')
		script.removeAttribute('crossorigin')
	})

	// 将标签data-src属性改为src
	const dataSrcScripts = root.querySelectorAll('script[data-src]')
	dataSrcScripts.forEach((script) => {
		script.setAttribute('src', script.getAttribute('data-src'))
		script.removeAttribute('data-src')
	})

	// 将处理后的HTML保存到新文件
	writeFileSync(htmlFilePath, root.toString())
	console.log('[esmodule-html.mjs]: pad端打包 html 格式化完成，输出：', htmlFilePath)
} catch (err) {
	console.error('Error:', err)
}
