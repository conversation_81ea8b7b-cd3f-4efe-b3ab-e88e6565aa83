import { defineConfig, loadEnv } from 'vite'
import UnoCSS from 'unocss/vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import postcssPxToViewport from 'postcss-px-to-viewport'
import Components from 'unplugin-vue-components/vite'
import legacy from '@vitejs/plugin-legacy'
import { VantResolver, AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import { env } from 'process'
// https://vitejs.dev/config/
export default ({ mode }) => {
	const _env = loadEnv(mode, process.cwd(), 'VITE_')

	// pad 打包
	const isPad = env.RUN_MODE === 'PAD'
	return defineConfig({
		base: _env.VITE_URL_PREFIX || (isPad ? './' : '/'),
		plugins: [
			legacy({
				// targets: ['defaults', 'not IE 11'],
				// targets: ['chrome 52'],
			}),
			UnoCSS(),
			vue(),
			vueJsx(),
			Components({
				resolvers: [AntDesignVueResolver({ importStyle: false, importCss: false, importLess: false })],
			}),
		],
		define: {
			'process.env': {
				APP_VERSION: env.APP_VERSION,
				MODE: mode,
			},
		},
		resolve: {
			//文件系统路径的别名, 绝对路径
			alias: {
				'@': '/src',
				store: '@/store',
				config: '@/config',
				components: '@/components',
				assets: '@/assets',
				utils: '@/utils',
				api: '@/api',
				pages: '@/pages',
			},
		},
		server: {
			hmr: true,
			port: 3000,
			proxy: {
				'/api': {
					target: 'http://192.168.10.31:8888/',
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/api/, ''), // 不可以省略rewrite
				},
			},
		},
		css: {
			postcss: {
				plugins: [
					postcssPxToViewport({
						unitToConvert: 'px', // 要转化的单位
						viewportWidth: 1920, // UI设计稿的宽度  webpack.resourcePath.includes(path.join('node_modules', 'vant')) ? 375 : 750;
						unitPrecision: 6, // 转换后的精度，即小数点位数
						propList: ['*'], // 指定转换的css属性，*代表全部css属性都进行转换
						viewportUnit: 'vw', // 指定需要转换成的视窗单位，默认vw
						fontViewportUnit: 'vw', // 指定字体需要转换成的视窗单位，默认vw
						// minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
						mediaQuery: true, // 是否在媒体查询的css代码中也进行转换，默认false
						replace: false, // 是否转换后直接更换属性值
						selectorBlackList: ['ignore'], // 自定义包含某个字符串(如：ignore)不转换为视窗单位的类
						exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
						landscape: false, // 是否处理横屏情况
					}),
				],
			},
			preprocessorOptions: {
				less: {
					// 支持内联 JavaScript
					javascriptEnabled: true,
					modifyVars: {
						'font-size-base': '14px',
					},
				},
			},
		},
	})
}
