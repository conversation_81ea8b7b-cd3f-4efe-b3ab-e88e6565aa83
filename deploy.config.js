/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-11-19 17:45:05
 * @LastEditTime: 2024-04-25 15:15:23
 * @LastEditors: lhl
 */
const path = require('path')

const config = {
	test: {
		host: '**************',
		user: 'node',
		password: 'aaJakqsCDxm&D6Z3',
		remotePath: '/data/www/fd-big-screen', // 服务器文件目录
		build: 'build:test',
		isStatic: true,
	},
	pad_test: {
		host: '**************',
		user: 'node',
		password: 'aaJakqsCDxm&D6Z3',
		remotePath: '/data/www/fd-big-screen', // 服务器文件目录
		build: 'build:dev',
		isStatic: true,
	},
	master: {
		build: 'build',
		zipName: 'fd-big-screen.zip',
	},
	pad: {
		build: 'build',
		zipName: 'fd-big-screen-pad.zip',
	},
}

Object.keys(config).forEach((env) => {
	const baseCofig = {
		zipName: `${path.basename(process.cwd())}-${env}.zip`, // zip 压缩后的临时文件名
		files: ['dist/assets', 'dist/index.html'], // 需要压缩的文件夹和文件
	}
	config[env] = { ...baseCofig, ...config[env] }
})
config.default = config.master
module.exports = config
