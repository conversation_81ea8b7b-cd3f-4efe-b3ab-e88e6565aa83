{"name": "my-vue-app", "private": true, "version": "0.0.0", "scripts": {"start": "node scripts/build.mjs", "dev": "vite --host", "build": "vite build --mode production && npm run html && npm run filemove", "build:dev": "vite build --mode development && npm run html && npm run filemove", "build:test_sw": "vite build --mode test_sw && npm run html && npm run filemove", "build:test": "vite build --mode development", "build:off_line": "vite build --mode off_line &&  npm run html && npm run filemove", "build:xuncha-prod": "vite build --mode xuncha-prod", "preview": "vite --host --mode production", "html": "node scripts/esmodule-html.mjs", "filemove": "node scripts/file-move.mjs"}, "dependencies": {"@better-scroll/core": "^2.5.1", "ant-design-vue": "^3.2.20", "assign-deep": "^1.0.1", "axios": "^1.4.0", "docx-preview": "^0.1.20", "echarts": "^5.4.2", "js-md5": "^0.7.3", "json-formatter-js": "^2.5.18", "mammoth": "^1.6.0", "mescroll.js": "^1.4.2", "mockjs": "^1.1.0", "pinia": "^2.1.4", "relation-graph": "^2.0.34", "swiper": "^11.1.12", "vue": "^3.2.45", "vue-echarts": "^6.6.0", "vue-router": "^4.2.2", "vue3-lazyload": "^0.3.8"}, "devDependencies": {"@better-scroll/mouse-wheel": "^2.5.1", "@better-scroll/pull-up": "^2.5.1", "@inquirer/prompts": "^5.0.1", "@inquirer/select": "^2.3.1", "@types/better-scroll": "^1.12.6", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.7", "@types/node": "^20.8.4", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.60.1", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "cross-env": "^7.0.3", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-config-standard-with-typescript": "^34.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.9.0", "fs-extra": "^11.1.1", "inquirer": "^7.3.3", "inquirer-select-line": "^1.1.3", "less": "^4.2.0", "node-html-parser": "^6.1.10", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.8.4", "sass": "^1.58.3", "terser": "^5.19.2", "typescript": "^4.9.5", "unocss": "^0.58.4", "unplugin-vue-components": "^0.24.0", "vite": "^4.1.0", "vue-tsc": "^1.0.24", "yargs": "^17.7.2"}}